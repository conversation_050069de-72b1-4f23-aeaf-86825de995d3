// TOTP (Time-based One-Time Password) service implementation
// Provides two-factor authentication using TOTP

use crate::configuration::security_config::TotpConfig;
use otpauth::TOTP;
use rand::Rng;

pub struct TotpService {
    config: TotpConfig,
}

impl TotpService {
    pub fn new(config: TotpConfig) -> Self {
        TotpService { config }
    }

    /// Generate a new TOTP secret for a user
    pub fn generate_secret(&self) -> Result<String, TotpError> {
        let mut rng = rand::rng();
        let secret: Vec<u8> = (0..32).map(|_| rng.random()).collect();
        Ok(base32::encode(
            base32::Alphabet::Rfc4648 { padding: false },
            &secret,
        ))
    }

    /// Generate TOTP instance for a user
    pub fn create_totp(&self, secret: &str, _account_name: &str) -> Result<TOTP, TotpError> {
        let totp = TOTP::new(secret.to_string());
        Ok(totp)
    }

    /// Generate current TOTP code for a secret
    pub fn generate_code(&self, secret: &str, account_name: &str) -> Result<String, TotpError> {
        let totp = self.create_totp(secret, account_name)?;
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map_err(|_| TotpError::SystemTimeError)?
            .as_secs();

        let code = totp.generate(self.config.period_seconds, timestamp);
        Ok(format!(
            "{:0width$}",
            code,
            width = self.config.digits as usize
        ))
    }

    /// Verify a TOTP code against a secret
    pub fn verify_code(
        &self,
        secret: &str,
        account_name: &str,
        code: &str,
    ) -> Result<bool, TotpError> {
        let totp = self.create_totp(secret, account_name)?;
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map_err(|_| TotpError::SystemTimeError)?
            .as_secs();

        // Check current time window and adjacent windows (accounting for skew)
        let time_steps = [
            timestamp.saturating_sub(self.config.period_seconds * self.config.skew_tolerance),
            timestamp,
            timestamp + (self.config.period_seconds * self.config.skew_tolerance),
        ];

        for &time_step in &time_steps {
            let expected_code = totp.generate(self.config.period_seconds, time_step);
            let expected_code_str = format!(
                "{:0width$}",
                expected_code,
                width = self.config.digits as usize
            );
            if expected_code_str == code {
                return Ok(true);
            }
        }

        Ok(false)
    }

    /// Generate QR code URL for TOTP setup
    pub fn generate_qr_code_url(
        &self,
        secret: &str,
        account_name: &str,
    ) -> Result<String, TotpError> {
        let totp = self.create_totp(secret, account_name)?;
        Ok(totp.to_uri(account_name, &self.config.issuer))
    }

    /// Generate backup codes for account recovery
    pub fn generate_backup_codes(&self, count: usize) -> Vec<String> {
        let mut rng = rand::rng();
        (0..count)
            .map(|_| {
                // Generate 8-character alphanumeric codes
                (0..8)
                    .map(|_| {
                        let chars = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                        chars[rng.random_range(0..chars.len())] as char
                    })
                    .collect()
            })
            .collect()
    }

    /// Verify a backup code (in a real implementation, this would check against stored hashes)
    pub fn verify_backup_code(&self, stored_codes: &[String], provided_code: &str) -> bool {
        // In a real implementation, you would:
        // 1. Hash the provided code
        // 2. Compare against stored hashed backup codes
        // 3. Mark the code as used if valid
        // 4. Ensure codes can only be used once

        stored_codes.contains(&provided_code.to_uppercase())
    }

    /// Get remaining time in current TOTP window
    pub fn get_remaining_time(&self) -> Result<u64, TotpError> {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map_err(|_| TotpError::SystemTimeError)?
            .as_secs();

        let remaining = self.config.period_seconds - (timestamp % self.config.period_seconds);
        Ok(remaining)
    }

    /// Validate TOTP configuration
    pub fn validate_config(&self) -> Result<(), TotpError> {
        if self.config.period_seconds == 0 {
            return Err(TotpError::InvalidConfiguration(
                "Period must be greater than 0".to_string(),
            ));
        }

        if self.config.digits != 6 && self.config.digits != 8 {
            return Err(TotpError::InvalidConfiguration(
                "Digits must be 6 or 8".to_string(),
            ));
        }

        if self.config.issuer.is_empty() {
            return Err(TotpError::InvalidConfiguration(
                "Issuer cannot be empty".to_string(),
            ));
        }

        Ok(())
    }
}

#[derive(Debug)]
pub struct TotpSetupInfo {
    pub secret: String,
    pub qr_code_url: String,
    pub backup_codes: Vec<String>,
    pub period_seconds: u64,
    pub digits: u32,
}

impl TotpService {
    /// Complete TOTP setup for a user
    pub fn setup_totp(&self, account_name: &str) -> Result<TotpSetupInfo, TotpError> {
        self.validate_config()?;

        let secret = self.generate_secret()?;
        let qr_code_url = self.generate_qr_code_url(&secret, account_name)?;
        let backup_codes = self.generate_backup_codes(10); // Generate 10 backup codes

        Ok(TotpSetupInfo {
            secret,
            qr_code_url,
            backup_codes,
            period_seconds: self.config.period_seconds,
            digits: self.config.digits,
        })
    }
}

#[derive(Debug, thiserror::Error)]
pub enum TotpError {
    #[error("Invalid secret: {0}")]
    InvalidSecret(String),

    #[error("System time error")]
    SystemTimeError,

    #[error("Code generation error: {0}")]
    GenerationError(String),

    #[error("Invalid configuration: {0}")]
    InvalidConfiguration(String),

    #[error("Verification failed")]
    VerificationFailed,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::configuration::SecurityConfig;

    fn test_totp_config() -> TotpConfig {
        SecurityConfig::test_config().mfa.totp
    }

    #[test]
    fn test_generate_secret() {
        let service = TotpService::new(test_totp_config());
        let secret = service.generate_secret().unwrap();

        assert!(!secret.is_empty());
        assert!(secret.len() >= 32); // Base32 encoded 32-byte secret
    }

    #[test]
    fn test_generate_and_verify_code() {
        let service = TotpService::new(test_totp_config());
        let secret = service.generate_secret().unwrap();
        let account_name = "<EMAIL>";

        let code = service.generate_code(&secret, account_name).unwrap();
        assert_eq!(code.len(), 6); // Default 6 digits

        // Verify the code
        let is_valid = service.verify_code(&secret, account_name, &code).unwrap();
        assert!(is_valid);

        // Invalid code should fail
        let is_invalid = service
            .verify_code(&secret, account_name, "000000")
            .unwrap();
        assert!(!is_invalid);
    }

    #[test]
    fn test_generate_qr_code_url() {
        let service = TotpService::new(test_totp_config());
        let secret = service.generate_secret().unwrap();
        let account_name = "<EMAIL>";

        let url = service.generate_qr_code_url(&secret, account_name).unwrap();
        assert!(url.starts_with("otpauth://totp/"));
        // The otpauth library might not URL encode the issuer in the path
        assert!(url.contains("AuthService") && url.contains("Test")); // Less strict check
        assert!(url.contains(account_name));
    }

    #[test]
    fn test_generate_backup_codes() {
        let service = TotpService::new(test_totp_config());
        let codes = service.generate_backup_codes(5);

        assert_eq!(codes.len(), 5);
        for code in &codes {
            assert_eq!(code.len(), 8);
            assert!(code.chars().all(|c| c.is_alphanumeric()));
        }

        // All codes should be unique
        let unique_codes: std::collections::HashSet<_> = codes.iter().collect();
        assert_eq!(unique_codes.len(), codes.len());
    }

    #[test]
    fn test_verify_backup_code() {
        let service = TotpService::new(test_totp_config());
        let backup_codes = vec!["ABC12345".to_string(), "XYZ67890".to_string()];

        assert!(service.verify_backup_code(&backup_codes, "ABC12345"));
        assert!(service.verify_backup_code(&backup_codes, "abc12345")); // Case insensitive
        assert!(!service.verify_backup_code(&backup_codes, "INVALID"));
    }

    #[test]
    fn test_get_remaining_time() {
        let service = TotpService::new(test_totp_config());
        let remaining = service.get_remaining_time().unwrap();

        assert!(remaining > 0);
        assert!(remaining <= 30); // Default period is 30 seconds
    }

    #[test]
    fn test_setup_totp() {
        let service = TotpService::new(test_totp_config());
        let setup_info = service.setup_totp("<EMAIL>").unwrap();

        assert!(!setup_info.secret.is_empty());
        assert!(!setup_info.qr_code_url.is_empty());
        assert_eq!(setup_info.backup_codes.len(), 10);
        assert_eq!(setup_info.period_seconds, 30);
        assert_eq!(setup_info.digits, 6);
    }

    #[test]
    fn test_validate_config() {
        let service = TotpService::new(test_totp_config());
        assert!(service.validate_config().is_ok());

        let mut invalid_config = test_totp_config();
        invalid_config.period_seconds = 0;
        let invalid_service = TotpService::new(invalid_config);
        assert!(invalid_service.validate_config().is_err());
    }
}
