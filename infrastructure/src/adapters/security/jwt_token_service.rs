// JWT token service implementation
// Provides JWT token creation, validation, and management

use crate::configuration::security_config::{JwtAlgorithm, JwtConfig};
use jsonwebtoken::{Al<PERSON><PERSON><PERSON>, Decod<PERSON><PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Val<PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

pub struct JwtTokenService {
    config: JwtConfig,
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    validation: Validation,
}

impl JwtTokenService {
    pub fn new(config: JwtConfig) -> Result<Self, TokenError> {
        let algorithm = match config.algorithm {
            JwtAlgorithm::HS256 => Algorithm::HS256,
            JwtAlgorithm::HS384 => Algorithm::HS384,
            JwtAlgorithm::HS512 => Algorithm::HS512,
            JwtAlgorithm::RS256 => Algorithm::RS256,
            JwtAlgorithm::RS384 => Algorithm::RS384,
            JwtAlgorithm::RS512 => Algorithm::RS512,
            JwtAlgorithm::ES256 => Algorithm::ES256,
            JwtAlgorithm::ES384 => Algorithm::ES384,
        };

        let encoding_key = EncodingKey::from_secret(config.secret_key.as_bytes());
        let decoding_key = DecodingKey::from_secret(config.secret_key.as_bytes());

        let mut validation = Validation::new(algorithm);
        validation.set_issuer(&[&config.issuer]);
        validation.set_audience(&[&config.audience]);

        Ok(JwtTokenService {
            config,
            encoding_key,
            decoding_key,
            validation,
        })
    }

    /// Create an access token
    pub fn create_access_token(
        &self,
        user_id: &str,
        roles: &[String],
    ) -> Result<String, TokenError> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| TokenError::SystemTimeError)?
            .as_secs();

        let claims = AccessTokenClaims {
            sub: user_id.to_string(),
            iss: self.config.issuer.clone(),
            aud: self.config.audience.clone(),
            exp: now + self.config.access_token_ttl_seconds,
            iat: now,
            nbf: now,
            roles: roles.to_vec(),
            token_type: "access".to_string(),
        };

        let header = Header::new(self.validation.algorithms[0]);
        jsonwebtoken::encode(&header, &claims, &self.encoding_key).map_err(TokenError::JwtError)
    }

    /// Create a refresh token
    pub fn create_refresh_token(&self, user_id: &str) -> Result<String, TokenError> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| TokenError::SystemTimeError)?
            .as_secs();

        let claims = RefreshTokenClaims {
            sub: user_id.to_string(),
            iss: self.config.issuer.clone(),
            aud: self.config.audience.clone(),
            exp: now + self.config.refresh_token_ttl_seconds,
            iat: now,
            nbf: now,
            token_type: "refresh".to_string(),
        };

        let header = Header::new(self.validation.algorithms[0]);
        jsonwebtoken::encode(&header, &claims, &self.encoding_key).map_err(TokenError::JwtError)
    }

    /// Validate and decode an access token
    pub fn validate_access_token(&self, token: &str) -> Result<AccessTokenClaims, TokenError> {
        // First, decode as generic JSON to check token type
        let generic_token =
            jsonwebtoken::decode::<serde_json::Value>(token, &self.decoding_key, &self.validation)
                .map_err(TokenError::JwtError)?;

        // Check token type before attempting to parse as AccessTokenClaims
        if let Some(token_type) = generic_token
            .claims
            .get("token_type")
            .and_then(|v| v.as_str())
        {
            if token_type != "access" {
                return Err(TokenError::InvalidTokenType);
            }
        } else {
            return Err(TokenError::InvalidTokenFormat);
        }

        // Now decode as AccessTokenClaims
        let token_data =
            jsonwebtoken::decode::<AccessTokenClaims>(token, &self.decoding_key, &self.validation)
                .map_err(TokenError::JwtError)?;

        Ok(token_data.claims)
    }

    /// Validate and decode a refresh token
    pub fn validate_refresh_token(&self, token: &str) -> Result<RefreshTokenClaims, TokenError> {
        // First, decode as generic JSON to check token type
        let generic_token =
            jsonwebtoken::decode::<serde_json::Value>(token, &self.decoding_key, &self.validation)
                .map_err(TokenError::JwtError)?;

        // Check token type before attempting to parse as RefreshTokenClaims
        if let Some(token_type) = generic_token
            .claims
            .get("token_type")
            .and_then(|v| v.as_str())
        {
            if token_type != "refresh" {
                return Err(TokenError::InvalidTokenType);
            }
        } else {
            return Err(TokenError::InvalidTokenFormat);
        }

        // Now decode as RefreshTokenClaims
        let token_data =
            jsonwebtoken::decode::<RefreshTokenClaims>(token, &self.decoding_key, &self.validation)
                .map_err(TokenError::JwtError)?;

        Ok(token_data.claims)
    }

    /// Extract user ID from token without full validation (for logging, etc.)
    pub fn extract_user_id(&self, token: &str) -> Result<String, TokenError> {
        // Decode without verification for extraction purposes
        let mut validation = Validation::new(self.validation.algorithms[0]);
        validation.validate_exp = false;
        validation.validate_nbf = false;
        validation.validate_aud = false;
        validation.required_spec_claims.clear(); // Remove all required claims
        validation.insecure_disable_signature_validation();

        let token_data =
            jsonwebtoken::decode::<serde_json::Value>(token, &self.decoding_key, &validation)
                .map_err(TokenError::JwtError)?;

        token_data
            .claims
            .get("sub")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
            .ok_or(TokenError::InvalidTokenFormat)
    }

    /// Check if token is expired (without full validation)
    pub fn is_token_expired(&self, token: &str) -> bool {
        match self.extract_token_expiry(token) {
            Ok(exp) => {
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .map(|d| d.as_secs())
                    .unwrap_or(0);
                exp <= now
            }
            Err(_) => true, // Consider invalid tokens as expired
        }
    }

    /// Extract token expiry timestamp
    fn extract_token_expiry(&self, token: &str) -> Result<u64, TokenError> {
        let mut validation = Validation::new(self.validation.algorithms[0]);
        validation.validate_exp = false;
        validation.validate_nbf = false;
        validation.validate_aud = false;
        validation.required_spec_claims.clear(); // Remove all required claims
        validation.insecure_disable_signature_validation();

        let token_data =
            jsonwebtoken::decode::<serde_json::Value>(token, &self.decoding_key, &validation)
                .map_err(TokenError::JwtError)?;

        token_data
            .claims
            .get("exp")
            .and_then(|v| v.as_u64())
            .ok_or(TokenError::InvalidTokenFormat)
    }

    /// Create token pair (access + refresh)
    pub fn create_token_pair(
        &self,
        user_id: &str,
        roles: &[String],
    ) -> Result<TokenPair, TokenError> {
        let access_token = self.create_access_token(user_id, roles)?;
        let refresh_token = self.create_refresh_token(user_id)?;

        Ok(TokenPair {
            access_token,
            refresh_token,
            expires_in: self.config.access_token_ttl_seconds,
            token_type: "Bearer".to_string(),
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessTokenClaims {
    pub sub: String,        // Subject (user ID)
    pub iss: String,        // Issuer
    pub aud: String,        // Audience
    pub exp: u64,           // Expiration time
    pub iat: u64,           // Issued at
    pub nbf: u64,           // Not before
    pub roles: Vec<String>, // User roles
    pub token_type: String, // Token type
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RefreshTokenClaims {
    pub sub: String,        // Subject (user ID)
    pub iss: String,        // Issuer
    pub aud: String,        // Audience
    pub exp: u64,           // Expiration time
    pub iat: u64,           // Issued at
    pub nbf: u64,           // Not before
    pub token_type: String, // Token type
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenPair {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64,
    pub token_type: String,
}

#[derive(Debug, thiserror::Error)]
pub enum TokenError {
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),

    #[error("System time error")]
    SystemTimeError,

    #[error("Invalid token type")]
    InvalidTokenType,

    #[error("Invalid token format")]
    InvalidTokenFormat,

    #[error("Token expired")]
    TokenExpired,

    #[error("Invalid signature")]
    InvalidSignature,

    #[error("Configuration error: {0}")]
    ConfigurationError(String),
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::configuration::SecurityConfig;

    fn test_jwt_config() -> JwtConfig {
        SecurityConfig::test_config().jwt
    }

    #[test]
    fn test_create_access_token() {
        let service = JwtTokenService::new(test_jwt_config()).unwrap();
        let roles = vec!["user".to_string(), "admin".to_string()];

        let token = service.create_access_token("user_123", &roles).unwrap();
        assert!(!token.is_empty());

        // Validate the token
        let claims = service.validate_access_token(&token).unwrap();
        assert_eq!(claims.sub, "user_123");
        assert_eq!(claims.roles, roles);
        assert_eq!(claims.token_type, "access");
    }

    #[test]
    fn test_create_refresh_token() {
        let service = JwtTokenService::new(test_jwt_config()).unwrap();

        let token = service.create_refresh_token("user_123").unwrap();
        assert!(!token.is_empty());

        // Validate the token
        let claims = service.validate_refresh_token(&token).unwrap();
        assert_eq!(claims.sub, "user_123");
        assert_eq!(claims.token_type, "refresh");
    }

    #[test]
    fn test_create_token_pair() {
        let service = JwtTokenService::new(test_jwt_config()).unwrap();
        let roles = vec!["user".to_string()];

        let pair = service.create_token_pair("user_123", &roles).unwrap();
        assert!(!pair.access_token.is_empty());
        assert!(!pair.refresh_token.is_empty());
        assert_eq!(pair.token_type, "Bearer");
        assert!(pair.expires_in > 0);
    }

    #[test]
    fn test_extract_user_id() {
        let service = JwtTokenService::new(test_jwt_config()).unwrap();
        let token = service.create_access_token("user_456", &[]).unwrap();

        let user_id = service.extract_user_id(&token).unwrap();
        assert_eq!(user_id, "user_456");
    }

    #[test]
    fn test_invalid_token() {
        let service = JwtTokenService::new(test_jwt_config()).unwrap();

        let result = service.validate_access_token("invalid.token.here");
        assert!(result.is_err());
    }

    #[test]
    fn test_wrong_token_type() {
        let service = JwtTokenService::new(test_jwt_config()).unwrap();
        let refresh_token = service.create_refresh_token("user_123").unwrap();

        // Try to validate refresh token as access token
        let result = service.validate_access_token(&refresh_token);
        assert!(matches!(result, Err(TokenError::InvalidTokenType)));
    }

    #[test]
    fn test_token_expiry_check() {
        let service = JwtTokenService::new(test_jwt_config()).unwrap();
        let token = service.create_access_token("user_123", &[]).unwrap();

        // Token should not be expired immediately after creation
        assert!(!service.is_token_expired(&token));

        // Invalid token should be considered expired
        assert!(service.is_token_expired("invalid.token"));
    }
}
