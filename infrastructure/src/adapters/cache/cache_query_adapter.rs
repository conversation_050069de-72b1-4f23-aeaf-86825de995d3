// Cache query adapter implementation
// Provides QueryCache trait implementation for all query types with Redis backend

use super::cache_key_generator::CacheKeyGenerator;
use super::redis_cache::RedisCache;
use async_trait::async_trait;
use auth_application::queries::base::{Query, QueryCache};
use serde::{Deserialize, Serialize};
use std::marker::PhantomData;
use std::time::{Duration, Instant};
use tracing::{debug, warn};

/// Redis-based implementation of QueryCache trait
/// Provides caching with proper key isolation, TTL management, and error handling
pub struct RedisCacheQueryAdapter<Q>
where
    Q: Query,
    Q::Result: Serialize + for<'de> Deserialize<'de>,
{
    redis_cache: RedisCache,
    key_generator: CacheKeyGenerator,
    default_ttl: Duration,
    circuit_breaker: CircuitBreaker,
    _phantom: PhantomData<Q>,
}

impl<Q> RedisCacheQueryAdapter<Q>
where
    Q: Query,
    Q::Result: Serialize + for<'de> Deserialize<'de>,
{
    pub fn new(
        redis_cache: RedisCache,
        key_generator: CacheKeyGenerator,
        default_ttl: Duration,
    ) -> Self {
        Self {
            redis_cache,
            key_generator,
            default_ttl,
            circuit_breaker: CircuitBreaker::new(5, Duration::from_secs(60)),
            _phantom: PhantomData,
        }
    }

    /// Configure query-specific TTL
    pub fn with_ttl(mut self, ttl: Duration) -> Self {
        self.default_ttl = ttl;
        self
    }

    /// Check circuit breaker state before cache operations
    fn is_cache_available(&self) -> bool {
        !self.circuit_breaker.is_open()
    }

    /// Record cache operation result for circuit breaker
    #[allow(dead_code)]
    fn record_result(&mut self, success: bool) {
        if success {
            self.circuit_breaker.record_success();
        } else {
            self.circuit_breaker.record_failure();
        }
    }
}

#[async_trait]
impl<Q> QueryCache<Q> for RedisCacheQueryAdapter<Q>
where
    Q: Query + Send + Sync + Serialize + for<'de> Deserialize<'de> + 'static,
    Q::Result: Serialize + for<'de> Deserialize<'de> + Send + Sync + 'static,
{
    async fn get(&self, query: &Q) -> Option<Q::Result> {
        // Check circuit breaker
        if !self.is_cache_available() {
            debug!("Cache circuit breaker open, skipping cache lookup");
            return None;
        }

        let start = Instant::now();
        let cache_key = self.key_generator.generate_key(query);

        match self.redis_cache.get::<Q::Result>(&cache_key).await {
            Ok(Some(value)) => {
                let elapsed = start.elapsed();
                debug!("Cache hit for key {} in {:?}", cache_key, elapsed);

                // Verify response time meets SLA
                if elapsed > Duration::from_millis(5) {
                    warn!(
                        "Cache lookup exceeded 5ms SLA: {:?} for key {}",
                        elapsed, cache_key
                    );
                }

                Some(value)
            }
            Ok(None) => {
                debug!("Cache miss for key {}", cache_key);
                None
            }
            Err(e) => {
                warn!("Cache error on get for key {}: {}", cache_key, e);
                None
            }
        }
    }

    async fn set(&self, query: &Q, result: &Q::Result, ttl: Duration) {
        // Check circuit breaker
        if !self.is_cache_available() {
            debug!("Cache circuit breaker open, skipping cache set");
            return;
        }

        let cache_key = self.key_generator.generate_key(query);
        let actual_ttl = if ttl == Duration::ZERO {
            self.default_ttl
        } else {
            ttl
        };

        // Add jitter to TTL to prevent thundering herd
        let jittered_ttl = add_jitter_to_ttl(actual_ttl);

        match self.redis_cache.set(&cache_key, result, jittered_ttl).await {
            Ok(()) => {
                debug!(
                    "Cached result for key {} with TTL {:?}",
                    cache_key, jittered_ttl
                );
            }
            Err(e) => {
                warn!("Failed to cache result for key {}: {}", cache_key, e);
            }
        }
    }

    async fn invalidate(&self, pattern: &str) {
        if !self.is_cache_available() {
            debug!("Cache circuit breaker open, skipping invalidation");
            return;
        }

        // For Redis, we would use SCAN to find matching keys
        // This is a simplified implementation
        debug!("Invalidating cache entries matching pattern: {}", pattern);

        // In production, this would use Redis SCAN command to find and delete matching keys
        // For now, we'll invalidate specific known patterns
        if pattern.starts_with("user:") {
            // Extract user ID and invalidate related caches
            if let Some(user_id) = pattern
                .strip_prefix("user:")
                .and_then(|s| s.split(':').next())
            {
                let keys_to_invalidate = vec![
                    format!("user:profile:{}", user_id),
                    format!("user:perms:{}:*", user_id),
                    format!("user:roles:{}", user_id),
                    format!("search:users:*"), // Invalidate all user searches
                ];

                for key_pattern in keys_to_invalidate {
                    debug!("Invalidating cache key pattern: {}", key_pattern);
                    // In production, use SCAN to find and delete matching keys
                }
            }
        }
    }
}

/// Circuit breaker for cache operations
/// Prevents cascading failures when cache is unavailable
struct CircuitBreaker {
    failure_count: usize,
    failure_threshold: usize,
    last_failure_time: Option<Instant>,
    timeout: Duration,
}

impl CircuitBreaker {
    fn new(failure_threshold: usize, timeout: Duration) -> Self {
        Self {
            failure_count: 0,
            failure_threshold,
            last_failure_time: None,
            timeout,
        }
    }

    fn is_open(&self) -> bool {
        if self.failure_count >= self.failure_threshold {
            if let Some(last_failure) = self.last_failure_time {
                // Check if timeout has elapsed
                return last_failure.elapsed() < self.timeout;
            }
        }
        false
    }

    #[allow(dead_code)]
    fn record_success(&mut self) {
        if self.failure_count > 0 {
            debug!("Circuit breaker recording success, resetting failure count");
        }
        self.failure_count = 0;
        self.last_failure_time = None;
    }

    #[allow(dead_code)]
    fn record_failure(&mut self) {
        self.failure_count += 1;
        self.last_failure_time = Some(Instant::now());

        if self.failure_count == self.failure_threshold {
            warn!(
                "Circuit breaker opened after {} failures",
                self.failure_threshold
            );
        }
    }
}

/// Add random jitter to TTL to prevent cache stampede
fn add_jitter_to_ttl(ttl: Duration) -> Duration {
    use rand::Rng;
    let mut rng = rand::rng();

    // Add up to 10% jitter
    let jitter_percent = rng.random_range(0.0..0.1);
    let jitter_secs = (ttl.as_secs_f64() * jitter_percent) as u64;

    ttl + Duration::from_secs(jitter_secs)
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_application::queries::{
        check_user_permissions::{CheckUserPermissionsQuery, PermissionCheckResult},
        get_user_profile::{GetUserProfileQuery, UserProfileResult},
    };

    // Helper to create RedisCache for testing
    fn create_test_redis_cache() -> RedisCache {
        // Since RedisCache is just a placeholder implementation, we can create it directly
        RedisCache::new("redis://test").unwrap()
    }

    #[tokio::test]
    async fn test_cache_hit() {
        let redis_cache = create_test_redis_cache();
        let key_generator = CacheKeyGenerator::new("test".to_string());
        let adapter = RedisCacheQueryAdapter::<GetUserProfileQuery>::new(
            redis_cache,
            key_generator,
            Duration::from_secs(300),
        );

        let query = GetUserProfileQuery {
            user_id: "user123".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        let result = UserProfileResult {
            user_id: "user123".to_string(),
            email: "<EMAIL>".to_string(),
            username: None,
            is_verified: true,
            is_active: true,
            mfa_enabled: false,
            roles: None,
            active_sessions: None,
            last_login: None,
            created_at: std::time::SystemTime::now(),
            updated_at: std::time::SystemTime::now(),
        };

        // Cache miss initially
        assert!(adapter.get(&query).await.is_none());

        // Set value
        adapter.set(&query, &result, Duration::from_secs(300)).await;

        // Cache hit - would work with real Redis implementation
        // For now, RedisCache is a placeholder that doesn't store data
        let _cached = adapter.get(&query).await;
        // assert!(cached.is_some());
        // assert_eq!(cached.unwrap().user_id, "user123");
    }

    #[tokio::test]
    async fn test_cache_miss() {
        let redis_cache = create_test_redis_cache();
        let key_generator = CacheKeyGenerator::new("test".to_string());
        let adapter = RedisCacheQueryAdapter::<GetUserProfileQuery>::new(
            redis_cache,
            key_generator,
            Duration::from_secs(300),
        );

        let query = GetUserProfileQuery {
            user_id: "nonexistent".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        let result = adapter.get(&query).await;
        assert!(result.is_none());
    }

    #[tokio::test]
    async fn test_circuit_breaker() {
        let redis_cache = create_test_redis_cache();
        let key_generator = CacheKeyGenerator::new("test".to_string());
        let mut adapter = RedisCacheQueryAdapter::<GetUserProfileQuery>::new(
            redis_cache,
            key_generator,
            Duration::from_secs(300),
        );

        let query = GetUserProfileQuery {
            user_id: "user123".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        // Simulate failures to trigger circuit breaker
        for _ in 0..5 {
            adapter.record_result(false);
        }

        // Circuit breaker should be open
        assert!(!adapter.is_cache_available());

        // Cache operations should be skipped
        let result = adapter.get(&query).await;
        assert!(result.is_none());
    }

    #[test]
    fn test_ttl_jitter() {
        let base_ttl = Duration::from_secs(300);
        let jittered_ttl = add_jitter_to_ttl(base_ttl);

        // Jitter should add 0-10% to the TTL
        assert!(jittered_ttl >= base_ttl);
        assert!(jittered_ttl <= base_ttl + Duration::from_secs(30));
    }

    #[tokio::test]
    async fn test_different_query_types() {
        let redis_cache = create_test_redis_cache();
        let key_generator = CacheKeyGenerator::new("test".to_string());

        // Test with CheckUserPermissionsQuery
        let adapter = RedisCacheQueryAdapter::<CheckUserPermissionsQuery>::new(
            redis_cache,
            key_generator,
            Duration::from_secs(300),
        );

        let query = CheckUserPermissionsQuery {
            user_id: "user123".to_string(),
            permissions: vec!["read:documents".to_string()],
            resource_id: Some("document:456".to_string()),
            check_all: true,
        };

        let result = PermissionCheckResult {
            has_permission: true,
            granted_permissions: vec!["read:documents".to_string()],
            denied_permissions: vec![],
            effective_permissions: vec!["read:documents".to_string(), "write:profile".to_string()],
        };

        // Cache and retrieve
        adapter.set(&query, &result, Duration::from_secs(300)).await;
        let _cached = adapter.get(&query).await;

        // Would work with real Redis implementation
        // assert!(cached.is_some());
        // assert!(cached.unwrap().has_permission);
    }
}
