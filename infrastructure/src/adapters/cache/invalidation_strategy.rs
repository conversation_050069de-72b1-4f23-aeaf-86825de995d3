// Cache invalidation strategy
// Implements event-driven cache invalidation for consistency

use super::cache_key_generator::CacheKeyGenerator;
use super::redis_cache::RedisCache;
use async_trait::async_trait;
use auth_domain::events::DomainEvent;
use std::sync::Arc;
use tracing::{debug, warn};

/// Trait for handling cache invalidation based on domain events
#[async_trait]
pub trait CacheInvalidator: Send + Sync {
    async fn invalidate_for_event(&self, event: &DomainEvent);
}

/// Redis-based cache invalidation strategy
pub struct RedisCacheInvalidator {
    redis_cache: Arc<RedisCache>,
    key_generator: <PERSON>acheKeyGenerator,
}

impl RedisCacheInvalidator {
    pub fn new(redis_cache: Arc<RedisCache>, key_generator: CacheKeyGenerator) -> Self {
        Self {
            redis_cache,
            key_generator,
        }
    }

    /// Invalidate cache entries based on user changes
    async fn invalidate_user_caches(&self, user_id: &str) {
        debug!("Invalidating cache entries for user: {user_id}");

        let patterns = self.key_generator.invalidation_pattern_for_user(user_id);
        for pattern in patterns {
            if let Err(e) = self.invalidate_pattern(&pattern).await {
                warn!("Failed to invalidate cache pattern {}: {}", pattern, e);
            }
        }
    }

    /// Invalidate cache entries based on role changes
    async fn invalidate_role_caches(&self, role_id: &str) {
        debug!("Invalidating cache entries for role: {role_id}");

        let patterns = self.key_generator.invalidation_pattern_for_role(role_id);
        for pattern in patterns {
            if let Err(e) = self.invalidate_pattern(&pattern).await {
                warn!("Failed to invalidate cache pattern {}: {}", pattern, e);
            }
        }
    }

    /// Invalidate cache entries based on session changes
    async fn invalidate_session_caches(&self, session_id: &str) {
        debug!("Invalidating cache entries for session: {session_id}");

        let patterns = self
            .key_generator
            .invalidation_pattern_for_session(session_id);
        for pattern in patterns {
            if let Err(e) = self.invalidate_pattern(&pattern).await {
                warn!("Failed to invalidate cache pattern {}: {}", pattern, e);
            }
        }
    }

    /// Invalidate all search caches
    async fn invalidate_search_caches(&self) {
        debug!("Invalidating all search cache entries");

        let pattern = format!("{}:*:search:users:*", self.key_generator.namespace());
        if let Err(e) = self.invalidate_pattern(&pattern).await {
            warn!("Failed to invalidate search cache pattern: {}", e);
        }
    }

    /// Invalidate audit log caches
    async fn invalidate_audit_caches(&self) {
        debug!("Invalidating audit log cache entries");

        let patterns = self.key_generator.invalidation_pattern_for_audit();
        for pattern in patterns {
            if let Err(e) = self.invalidate_pattern(&pattern).await {
                warn!(
                    "Failed to invalidate audit cache pattern {}: {}",
                    pattern, e
                );
            }
        }
    }

    /// Invalidate cache entries matching a pattern
    /// In production, this would use Redis SCAN to find and delete matching keys
    async fn invalidate_pattern(&self, pattern: &str) -> Result<(), String> {
        // For now, we'll use a simplified approach
        // In production, use SCAN to find all matching keys and delete them
        debug!("Invalidating cache pattern: {pattern}");

        // If pattern ends with *, it's a prefix match
        if pattern.ends_with('*') {
            let prefix = pattern.trim_end_matches('*');
            // In production: Use SCAN to find all keys with this prefix
            // For now, we'll just log
            debug!("Would invalidate all keys with prefix: {prefix}");
        } else {
            // Exact key match
            match self.redis_cache.delete(pattern).await {
                Ok(deleted) => {
                    if deleted {
                        debug!("Deleted cache key: {pattern}");
                    }
                }
                Err(e) => {
                    return Err(format!("Failed to delete key {pattern}: {e}"));
                }
            }
        }

        Ok(())
    }
}

#[async_trait]
impl CacheInvalidator for RedisCacheInvalidator {
    async fn invalidate_for_event(&self, event: &DomainEvent) {
        match event {
            // User events
            DomainEvent::UserRegistered { user_id, .. } => {
                self.invalidate_user_caches(user_id.as_str()).await;
                self.invalidate_search_caches().await;
            }

            DomainEvent::UserLoggedIn {
                user_id: _,
                session_id,
                ..
            } => {
                // Login doesn't invalidate profile cache, but might affect session cache
                self.invalidate_session_caches(session_id.as_str()).await;
            }

            DomainEvent::UserVerified { user_id, .. } => {
                self.invalidate_user_caches(user_id.as_str()).await;
                self.invalidate_search_caches().await;
            }

            DomainEvent::EmailChanged { user_id, .. } => {
                self.invalidate_user_caches(user_id.as_str()).await;
                self.invalidate_search_caches().await;
            }

            DomainEvent::PasswordChanged { user_id } => {
                self.invalidate_user_caches(user_id.as_str()).await;
            }

            DomainEvent::UserDeactivated { user_id } => {
                self.invalidate_user_caches(user_id.as_str()).await;
                self.invalidate_search_caches().await;
            }

            DomainEvent::UserActivated { user_id } => {
                self.invalidate_user_caches(user_id.as_str()).await;
                self.invalidate_search_caches().await;
            }

            // Session events
            DomainEvent::SessionRefreshed {
                session_id,
                user_id: _,
            } => {
                self.invalidate_session_caches(session_id.as_str()).await;
            }

            DomainEvent::SessionExpired {
                session_id,
                user_id,
            } => {
                self.invalidate_session_caches(session_id.as_str()).await;
                self.invalidate_user_caches(user_id.as_str()).await;
            }

            // Role/Permission events
            DomainEvent::PermissionAdded { role_id, .. } => {
                self.invalidate_role_caches(role_id.as_str()).await;
            }

            DomainEvent::PermissionRemoved { role_id, .. } => {
                self.invalidate_role_caches(role_id.as_str()).await;
            }

            // Organization events
            DomainEvent::MemberAdded {
                org_id: _, user_id, ..
            } => {
                self.invalidate_user_caches(user_id.as_str()).await;
                self.invalidate_search_caches().await;
            }

            DomainEvent::MemberRemoved {
                org_id: _, user_id, ..
            } => {
                self.invalidate_user_caches(user_id.as_str()).await;
                self.invalidate_search_caches().await;
            }

            DomainEvent::OwnershipTransferred {
                org_id: _,
                old_owner,
                new_owner,
            } => {
                self.invalidate_user_caches(old_owner.as_str()).await;
                self.invalidate_user_caches(new_owner.as_str()).await;
            }
        }

        // All events generate audit logs, so invalidate audit cache
        self.invalidate_audit_caches().await;
    }
}

/// Batch invalidation strategy for performance optimization
pub struct BatchCacheInvalidator {
    invalidator: Arc<RedisCacheInvalidator>,
    #[allow(dead_code)]
    batch_size: usize,
    #[allow(dead_code)]
    batch_timeout: std::time::Duration,
}

impl BatchCacheInvalidator {
    pub fn new(
        invalidator: Arc<RedisCacheInvalidator>,
        batch_size: usize,
        batch_timeout: std::time::Duration,
    ) -> Self {
        Self {
            invalidator,
            batch_size,
            batch_timeout,
        }
    }

    /// Process a batch of events for invalidation
    pub async fn process_batch(&self, events: Vec<DomainEvent>) {
        debug!(
            "Processing batch of {} events for cache invalidation",
            events.len()
        );

        // Group events by type for efficient invalidation
        let mut user_ids = std::collections::HashSet::new();
        let mut role_ids = std::collections::HashSet::new();
        let mut session_ids = std::collections::HashSet::new();
        let mut invalidate_search = false;
        let mut invalidate_audit = false;

        for event in &events {
            match event {
                DomainEvent::UserRegistered { user_id, .. } => {
                    user_ids.insert(user_id.as_str().to_string());
                    invalidate_search = true;
                }
                DomainEvent::UserVerified { user_id, .. } => {
                    user_ids.insert(user_id.as_str().to_string());
                    invalidate_search = true;
                }
                DomainEvent::EmailChanged { user_id, .. } => {
                    user_ids.insert(user_id.as_str().to_string());
                    invalidate_search = true;
                }
                DomainEvent::UserDeactivated { user_id } => {
                    user_ids.insert(user_id.as_str().to_string());
                    invalidate_search = true;
                }
                DomainEvent::UserActivated { user_id } => {
                    user_ids.insert(user_id.as_str().to_string());
                    invalidate_search = true;
                }
                DomainEvent::PasswordChanged { user_id } => {
                    user_ids.insert(user_id.as_str().to_string());
                }
                DomainEvent::SessionRefreshed { session_id, .. } => {
                    session_ids.insert(session_id.as_str().to_string());
                }
                DomainEvent::SessionExpired {
                    session_id,
                    user_id,
                } => {
                    session_ids.insert(session_id.as_str().to_string());
                    user_ids.insert(user_id.as_str().to_string());
                }
                DomainEvent::PermissionAdded { role_id, .. } => {
                    role_ids.insert(role_id.as_str().to_string());
                }
                DomainEvent::PermissionRemoved { role_id, .. } => {
                    role_ids.insert(role_id.as_str().to_string());
                }
                DomainEvent::MemberAdded { user_id, .. } => {
                    user_ids.insert(user_id.as_str().to_string());
                    invalidate_search = true;
                }
                DomainEvent::MemberRemoved { user_id, .. } => {
                    user_ids.insert(user_id.as_str().to_string());
                    invalidate_search = true;
                }
                DomainEvent::OwnershipTransferred {
                    old_owner,
                    new_owner,
                    ..
                } => {
                    user_ids.insert(old_owner.as_str().to_string());
                    user_ids.insert(new_owner.as_str().to_string());
                }
                _ => {
                    invalidate_audit = true;
                }
            }
        }

        // Perform batch invalidations
        for user_id in user_ids {
            self.invalidator.invalidate_user_caches(&user_id).await;
        }

        for role_id in role_ids {
            self.invalidator.invalidate_role_caches(&role_id).await;
        }

        for session_id in session_ids {
            self.invalidator
                .invalidate_session_caches(&session_id)
                .await;
        }

        if invalidate_search {
            self.invalidator.invalidate_search_caches().await;
        }

        if invalidate_audit {
            self.invalidator.invalidate_audit_caches().await;
        }
    }
}

/// Cache warming strategy for critical queries
#[derive(Default)]
pub struct CacheWarmer {
    // Would contain logic to pre-populate cache with frequently accessed data
}

impl CacheWarmer {
    pub fn new() -> Self {
        Self::default()
    }
}

impl CacheWarmer {
    /// Warm cache for a specific user
    pub async fn warm_user_cache(&self, user_id: &str) {
        debug!("Warming cache for user: {user_id}");
        // In production, this would:
        // 1. Fetch user profile
        // 2. Fetch user roles
        // 3. Fetch user permissions
        // 4. Pre-populate cache with common queries
    }

    /// Warm cache for system startup
    pub async fn warm_system_cache(&self) {
        debug!("Warming system cache");
        // In production, this would:
        // 1. Load frequently accessed roles
        // 2. Load system permissions
        // 3. Pre-populate common search results
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::value_objects::{Email, RoleId, SessionId, UserId};

    // Mock Redis cache for testing
    #[allow(dead_code)]
    struct MockRedisCache {
        deleted_keys: std::sync::Arc<std::sync::Mutex<Vec<String>>>,
    }

    #[allow(dead_code)]
    impl MockRedisCache {
        fn new() -> Self {
            Self {
                deleted_keys: std::sync::Arc::new(std::sync::Mutex::new(Vec::new())),
            }
        }

        async fn delete(&self, key: &str) -> Result<bool, String> {
            let mut deleted = self.deleted_keys.lock().unwrap();
            deleted.push(key.to_string());
            Ok(true)
        }

        fn get_deleted_keys(&self) -> Vec<String> {
            self.deleted_keys.lock().unwrap().clone()
        }
    }

    #[tokio::test]
    async fn test_user_event_invalidation() {
        let redis_cache = Arc::new(RedisCache::new("redis://test").unwrap());
        let key_generator = CacheKeyGenerator::new("test".to_string());
        let invalidator = RedisCacheInvalidator::new(redis_cache.clone(), key_generator);

        let event = DomainEvent::UserRegistered {
            user_id: UserId::from_string("user123".to_string()).unwrap(),
            email: Email::new("<EMAIL>").unwrap(),
            occurred_at: auth_domain::value_objects::DateTime::now(),
            registration_ip: Some("127.0.0.1".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
        };

        invalidator.invalidate_for_event(&event).await;

        // Check that user-related patterns would be invalidated
        // In real implementation, this would use SCAN to find matching keys
    }

    #[tokio::test]
    async fn test_permission_event_invalidation() {
        let redis_cache = Arc::new(RedisCache::new("redis://test").unwrap());
        let key_generator = CacheKeyGenerator::new("test".to_string());
        let invalidator = RedisCacheInvalidator::new(redis_cache.clone(), key_generator);

        let event = DomainEvent::PermissionAdded {
            role_id: RoleId::from_string(
                "role_abcdef1234567890abcdef1234567890abcdef12".to_string(),
            )
            .unwrap(),
            permission: auth_domain::entities::role::Permission::Custom(
                "read:documents".to_string(),
            ),
        };

        invalidator.invalidate_for_event(&event).await;
    }

    #[tokio::test]
    async fn test_batch_invalidation() {
        let redis_cache = Arc::new(RedisCache::new("redis://test").unwrap());
        let key_generator = CacheKeyGenerator::new("test".to_string());
        let invalidator = Arc::new(RedisCacheInvalidator::new(
            redis_cache.clone(),
            key_generator,
        ));
        let batch_invalidator =
            BatchCacheInvalidator::new(invalidator, 10, std::time::Duration::from_secs(1));

        let events = vec![
            DomainEvent::UserRegistered {
                user_id: UserId::from_string("user1".to_string()).unwrap(),
                email: Email::new("<EMAIL>").unwrap(),
                occurred_at: auth_domain::value_objects::DateTime::now(),
                registration_ip: None,
                user_agent: None,
            },
            DomainEvent::UserRegistered {
                user_id: UserId::from_string("user2".to_string()).unwrap(),
                email: Email::new("<EMAIL>").unwrap(),
                occurred_at: auth_domain::value_objects::DateTime::now(),
                registration_ip: None,
                user_agent: None,
            },
            DomainEvent::MemberAdded {
                org_id: auth_domain::value_objects::OrganizationId::from_string(
                    "org_abcdef1234567890abcdef1234567890abcdef12".to_string(),
                )
                .unwrap(),
                user_id: UserId::from_string("user1".to_string()).unwrap(),
                role: auth_domain::entities::organization::OrganizationRole::Member,
                invited_by: UserId::from_string("admin".to_string()).unwrap(),
            },
        ];

        batch_invalidator.process_batch(events).await;
    }

    #[tokio::test]
    async fn test_session_invalidation() {
        let redis_cache = Arc::new(RedisCache::new("redis://test").unwrap());
        let key_generator = CacheKeyGenerator::new("test".to_string());
        let invalidator = RedisCacheInvalidator::new(redis_cache.clone(), key_generator);

        let event = DomainEvent::SessionExpired {
            session_id: SessionId::from_string(
                "sess_abcdef123456789012345678901234567890".to_string(),
            )
            .unwrap(),
            user_id: UserId::from_string("user123".to_string()).unwrap(),
        };

        invalidator.invalidate_for_event(&event).await;
    }

    #[test]
    fn test_cache_warmer_creation() {
        let _warmer = CacheWarmer::new();
        // Basic creation test
        // In production, would test actual warming logic
    }
}
