// Cache key generation strategy
// Provides consistent, secure cache key generation with proper isolation

use auth_application::queries::base::Query;
use serde::Serialize;
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};

/// Generates cache keys for queries with security isolation and consistency
#[derive(Clone)]
pub struct CacheKeyGenerator {
    namespace: String,
    version: u32,
}

impl CacheKeyGenerator {
    pub fn new(namespace: String) -> Self {
        Self {
            namespace,
            version: 1, // Version for cache invalidation on schema changes
        }
    }

    /// Get the namespace for pattern matching
    pub fn namespace(&self) -> &str {
        &self.namespace
    }

    /// Generate a cache key for a query
    /// Keys are namespaced and versioned to prevent collisions and enable bulk invalidation
    pub fn generate_key<Q>(&self, query: &Q) -> String
    where
        Q: Query + Serialize,
    {
        let query_type = std::any::type_name::<Q>()
            .split("::")
            .last()
            .unwrap_or("Unknown");

        // For specific query types, use optimized key patterns
        match query_type {
            "GetUserProfileQuery" => self.generate_user_profile_key(query),
            "CheckUserPermissionsQuery" => self.generate_permissions_key(query),
            "ListUserRolesQuery" => self.generate_user_roles_key(query),
            "GetCurrentSessionQuery" => self.generate_session_key(query),
            "SearchUsersQuery" => self.generate_search_key(query),
            "GetAuditLogQuery" => self.generate_audit_key(query),
            "GetRoleDetailsQuery" => self.generate_role_key(query),
            _ => self.generate_generic_key(query_type, query),
        }
    }

    /// Generate key for user profile queries
    fn generate_user_profile_key<Q: Serialize>(&self, query: &Q) -> String {
        if let Ok(json) = serde_json::to_value(query) {
            if let Some(user_id) = json.get("user_id").and_then(|v| v.as_str()) {
                return format!(
                    "{}:v{}:user:profile:{}",
                    self.namespace, self.version, user_id
                );
            }
        }
        self.generate_generic_key("GetUserProfileQuery", query)
    }

    /// Generate key for permission check queries
    fn generate_permissions_key<Q: Serialize>(&self, query: &Q) -> String {
        if let Ok(json) = serde_json::to_value(query) {
            if let Some(user_id) = json.get("user_id").and_then(|v| v.as_str()) {
                // Include resource_id if present for fine-grained caching
                if let Some(resource_id) = json.get("resource_id").and_then(|v| v.as_str()) {
                    let perms_hash =
                        if let Some(perms) = json.get("permissions").and_then(|v| v.as_array()) {
                            let perms_str = perms
                                .iter()
                                .filter_map(|p| p.as_str())
                                .collect::<Vec<_>>()
                                .join(",");
                            self.hash_string(&perms_str)
                        } else {
                            "none".to_string()
                        };

                    return format!(
                        "{}:v{}:user:perms:{}:{}:{}",
                        self.namespace, self.version, user_id, resource_id, perms_hash
                    );
                } else {
                    // No resource specified, hash all permissions
                    let perms_hash = self.hash_query(query);
                    return format!(
                        "{}:v{}:user:perms:{}:{}",
                        self.namespace, self.version, user_id, perms_hash
                    );
                }
            }
        }
        self.generate_generic_key("CheckUserPermissionsQuery", query)
    }

    /// Generate key for user roles queries
    fn generate_user_roles_key<Q: Serialize>(&self, query: &Q) -> String {
        if let Ok(json) = serde_json::to_value(query) {
            if let Some(user_id) = json.get("user_id").and_then(|v| v.as_str()) {
                return format!(
                    "{}:v{}:user:roles:{}",
                    self.namespace, self.version, user_id
                );
            }
        }
        self.generate_generic_key("ListUserRolesQuery", query)
    }

    /// Generate key for session queries
    fn generate_session_key<Q: Serialize>(&self, query: &Q) -> String {
        if let Ok(json) = serde_json::to_value(query) {
            if let Some(session_id) = json.get("session_id").and_then(|v| v.as_str()) {
                return format!(
                    "{}:v{}:session:current:{}",
                    self.namespace, self.version, session_id
                );
            }
        }
        self.generate_generic_key("GetCurrentSessionQuery", query)
    }

    /// Generate key for search queries (includes hash of parameters)
    fn generate_search_key<Q: Serialize>(&self, query: &Q) -> String {
        let hash = self.hash_query(query);
        format!("{}:v{}:search:users:{}", self.namespace, self.version, hash)
    }

    /// Generate key for audit log queries
    fn generate_audit_key<Q: Serialize>(&self, query: &Q) -> String {
        let hash = self.hash_query(query);
        format!("{}:v{}:audit:log:{}", self.namespace, self.version, hash)
    }

    /// Generate key for role detail queries
    fn generate_role_key<Q: Serialize>(&self, query: &Q) -> String {
        if let Ok(json) = serde_json::to_value(query) {
            if let Some(role_id) = json.get("role_id").and_then(|v| v.as_str()) {
                return format!(
                    "{}:v{}:role:details:{}",
                    self.namespace, self.version, role_id
                );
            }
        }
        self.generate_generic_key("GetRoleDetailsQuery", query)
    }

    /// Generate a generic cache key using query type and hash
    fn generate_generic_key<Q: Serialize>(&self, query_type: &str, query: &Q) -> String {
        let hash = self.hash_query(query);
        format!(
            "{}:v{}:{}:{}",
            self.namespace, self.version, query_type, hash
        )
    }

    /// Hash a query to create a unique identifier
    fn hash_query<Q: Serialize>(&self, query: &Q) -> String {
        let json = serde_json::to_string(query).unwrap_or_default();
        self.hash_string(&json)
    }

    /// Hash a string to create a unique identifier
    fn hash_string(&self, input: &str) -> String {
        let mut hasher = DefaultHasher::new();
        input.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// Generate invalidation patterns for different entity types
    pub fn invalidation_pattern_for_user(&self, user_id: &str) -> Vec<String> {
        vec![
            format!(
                "{}:v{}:user:profile:{}",
                self.namespace, self.version, user_id
            ),
            format!(
                "{}:v{}:user:perms:{}:*",
                self.namespace, self.version, user_id
            ),
            format!(
                "{}:v{}:user:roles:{}",
                self.namespace, self.version, user_id
            ),
            format!("{}:v{}:search:users:*", self.namespace, self.version),
        ]
    }

    pub fn invalidation_pattern_for_role(&self, role_id: &str) -> Vec<String> {
        vec![
            format!(
                "{}:v{}:role:details:{}",
                self.namespace, self.version, role_id
            ),
            format!("{}:v{}:user:perms:*", self.namespace, self.version), // Permissions depend on roles
            format!("{}:v{}:user:roles:*", self.namespace, self.version), // User roles might be affected
        ]
    }

    pub fn invalidation_pattern_for_session(&self, session_id: &str) -> Vec<String> {
        vec![format!(
            "{}:v{}:session:current:{}",
            self.namespace, self.version, session_id
        )]
    }

    pub fn invalidation_pattern_for_audit(&self) -> Vec<String> {
        vec![format!("{}:v{}:audit:log:*", self.namespace, self.version)]
    }
}

/// Security utilities for cache keys
pub struct CacheKeySecurity;

impl CacheKeySecurity {
    /// Validate that a cache key doesn't contain sensitive information
    pub fn validate_key(key: &str) -> bool {
        // Keys should not contain:
        // - Passwords or password hashes
        // - Full session tokens (only IDs)
        // - API keys or secrets
        // - Personal identifiable information beyond IDs

        let forbidden_patterns = [
            "password",
            "secret",
            "token",
            "api_key",
            "credit_card",
            "ssn",
            "email", // Emails should be hashed, not plain
        ];

        let lower_key = key.to_lowercase();
        !forbidden_patterns
            .iter()
            .any(|pattern| lower_key.contains(pattern))
    }

    /// Sanitize user input before including in cache keys
    pub fn sanitize_input(input: &str) -> String {
        // Remove special characters that could break key parsing
        input
            .chars()
            .filter(|c| c.is_alphanumeric() || *c == '-' || *c == '_')
            .take(128) // Limit length
            .collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_application::queries::{
        check_user_permissions::CheckUserPermissionsQuery, get_user_profile::GetUserProfileQuery,
        list_user_roles::ListUserRolesQuery, search_users::SearchUsersQuery,
    };

    #[test]
    fn test_user_profile_key_generation() {
        let generator = CacheKeyGenerator::new("test".to_string());
        let query = GetUserProfileQuery {
            user_id: "user123".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        let key = generator.generate_key(&query);
        assert_eq!(key, "test:v1:user:profile:user123");
    }

    #[test]
    fn test_permissions_key_generation() {
        let generator = CacheKeyGenerator::new("test".to_string());
        let query = CheckUserPermissionsQuery {
            user_id: "user123".to_string(),
            permissions: vec!["read:documents".to_string()],
            resource_id: Some("document:456".to_string()),
            check_all: true,
        };

        let key = generator.generate_key(&query);
        assert!(key.starts_with("test:v1:user:perms:user123:document:456:"));
    }

    #[test]
    fn test_user_roles_key_generation() {
        let generator = CacheKeyGenerator::new("test".to_string());
        let query = ListUserRolesQuery {
            user_id: "user123".to_string(),
            include_permissions: false,
            include_inherited: false,
            active_only: false,
        };

        let key = generator.generate_key(&query);
        assert_eq!(key, "test:v1:user:roles:user123");
    }

    #[test]
    fn test_search_key_generation_deterministic() {
        let generator = CacheKeyGenerator::new("test".to_string());
        let query1 = SearchUsersQuery {
            email_filter: Some("<EMAIL>".to_string()),
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: Some(true),
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(20),
            sort_by: None,
            sort_direction: None,
        };

        let query2 = SearchUsersQuery {
            email_filter: Some("<EMAIL>".to_string()),
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: Some(true),
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(20),
            sort_by: None,
            sort_direction: None,
        };

        let key1 = generator.generate_key(&query1);
        let key2 = generator.generate_key(&query2);

        // Same query parameters should generate same key
        assert_eq!(key1, key2);
        assert!(key1.starts_with("test:v1:search:users:"));
    }

    #[test]
    fn test_different_queries_different_keys() {
        let generator = CacheKeyGenerator::new("test".to_string());

        let query1 = GetUserProfileQuery {
            user_id: "user123".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        let query2 = GetUserProfileQuery {
            user_id: "user456".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        let key1 = generator.generate_key(&query1);
        let key2 = generator.generate_key(&query2);

        assert_ne!(key1, key2);
    }

    #[test]
    fn test_invalidation_patterns() {
        let generator = CacheKeyGenerator::new("prod".to_string());

        let user_patterns = generator.invalidation_pattern_for_user("user123");
        assert_eq!(user_patterns.len(), 4);
        assert!(user_patterns.contains(&"prod:v1:user:profile:user123".to_string()));
        assert!(user_patterns.contains(&"prod:v1:user:perms:user123:*".to_string()));

        let role_patterns = generator.invalidation_pattern_for_role("role456");
        assert!(role_patterns.contains(&"prod:v1:role:details:role456".to_string()));

        let session_patterns = generator.invalidation_pattern_for_session("sess789");
        assert!(session_patterns.contains(&"prod:v1:session:current:sess789".to_string()));
    }

    #[test]
    fn test_cache_key_security() {
        // Valid keys
        assert!(CacheKeySecurity::validate_key(
            "test:v1:user:profile:user123"
        ));
        assert!(CacheKeySecurity::validate_key(
            "test:v1:session:current:sess123"
        ));

        // Invalid keys containing sensitive data
        assert!(!CacheKeySecurity::validate_key(
            "test:v1:user:password:hash"
        ));
        assert!(!CacheKeySecurity::validate_key("test:v1:api_key:12345"));
        assert!(!CacheKeySecurity::validate_key(
            "test:v1:user:email:<EMAIL>"
        ));
    }

    #[test]
    fn test_input_sanitization() {
        assert_eq!(
            CacheKeySecurity::sanitize_input("user-123_test"),
            "user-123_test"
        );

        assert_eq!(CacheKeySecurity::sanitize_input("user@#$%123"), "user123");

        assert_eq!(
            CacheKeySecurity::sanitize_input(
                "very_long_input_that_exceeds_the_maximum_allowed_length_of_128_characters_and_should_be_truncated_to_prevent_issues_with_cache_key_length_limits_in_redis"
            ),
            "very_long_input_that_exceeds_the_maximum_allowed_length_of_128_characters_and_should_be_truncated_to_prevent_issues_with_cache_k"
        );
    }

    #[test]
    fn test_namespace_isolation() {
        let generator1 = CacheKeyGenerator::new("tenant1".to_string());
        let generator2 = CacheKeyGenerator::new("tenant2".to_string());

        let query = GetUserProfileQuery {
            user_id: "user123".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        let key1 = generator1.generate_key(&query);
        let key2 = generator2.generate_key(&query);

        assert_ne!(key1, key2);
        assert!(key1.starts_with("tenant1:"));
        assert!(key2.starts_with("tenant2:"));
    }
}
