// Monitored cache adapter that integrates with performance monitoring
// Wraps cache operations with metrics collection and tracing

use super::redis_cache::{CacheError, RedisCache};
use auth_application::monitoring::{
    CacheMetrics, CacheOperationType, MetricsCollector, PerformanceTracer, TraceContext,
    trace_cache_operation,
};
use serde::{Serialize, de::DeserializeOwned};
use std::sync::Arc;
use std::time::{Instant, SystemTime};
use tracing::{debug, warn};

/// Cache adapter with integrated monitoring
pub struct MonitoredCache {
    inner: RedisCache,
    metrics_collector: Arc<dyn MetricsCollector>,
    tracer: Arc<dyn PerformanceTracer>,
    enable_detailed_metrics: bool,
}

impl MonitoredCache {
    /// Create a new monitored cache
    pub fn new(
        inner: RedisCache,
        metrics_collector: Arc<dyn MetricsCollector>,
        tracer: Arc<dyn PerformanceTracer>,
    ) -> Self {
        Self {
            inner,
            metrics_collector,
            tracer,
            enable_detailed_metrics: true,
        }
    }

    /// Set whether to collect detailed metrics (size, TTL, etc.)
    pub fn with_detailed_metrics(mut self, enable: bool) -> Self {
        self.enable_detailed_metrics = enable;
        self
    }

    /// Get a value from cache with monitoring
    pub async fn get<T>(&self, key: &str) -> Result<Option<T>, CacheError>
    where
        T: DeserializeOwned + Serialize,
    {
        let start = Instant::now();
        let trace_context = self.tracer.start_trace(&format!("cache:get:{key}"));

        // Execute the cache operation
        let result = self.inner.get::<T>(key).await;
        let duration = start.elapsed();

        // Determine success and size
        let (success, size_bytes) = match &result {
            Ok(Some(value)) => {
                let size = if self.enable_detailed_metrics {
                    // Estimate size by serializing (expensive, so optional)
                    serde_json::to_vec(value).ok().map(|v| v.len())
                } else {
                    None
                };
                (true, size)
            }
            Ok(None) => (false, None), // Cache miss
            Err(_) => (false, None),   // Error
        };

        // Record metrics
        let cache_metrics = CacheMetrics {
            operation_type: CacheOperationType::Get,
            cache_key: if self.enable_detailed_metrics {
                key.to_string()
            } else {
                // Anonymize key for security
                format!("key_hash_{:x}", calculate_key_hash(key))
            },
            duration,
            success,
            size_bytes,
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_cache_operation(cache_metrics);

        // Complete trace
        self.tracer.complete_trace(trace_context);

        // Log if operation was slow
        if duration.as_millis() > 5 {
            warn!("Slow cache GET operation: {} took {:?}", key, duration);
        }

        debug!(
            "Cache GET {}: {} in {:?}",
            key,
            if success { "HIT" } else { "MISS" },
            duration
        );

        result
    }

    /// Set a value in cache with monitoring
    pub async fn set<T>(
        &self,
        key: &str,
        value: &T,
        ttl: std::time::Duration,
    ) -> Result<(), CacheError>
    where
        T: Serialize,
    {
        let start = Instant::now();
        let trace_context = self.tracer.start_trace(&format!("cache:set:{key}"));

        // Calculate size before operation
        let size_bytes = if self.enable_detailed_metrics {
            serde_json::to_vec(value).ok().map(|v| v.len())
        } else {
            None
        };

        // Execute the cache operation
        let result = self.inner.set(key, value, ttl).await;
        let duration = start.elapsed();

        // Record metrics
        let cache_metrics = CacheMetrics {
            operation_type: CacheOperationType::Set,
            cache_key: if self.enable_detailed_metrics {
                key.to_string()
            } else {
                format!("key_hash_{:x}", calculate_key_hash(key))
            },
            duration,
            success: result.is_ok(),
            size_bytes,
            ttl_seconds: Some(ttl.as_secs()),
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_cache_operation(cache_metrics);

        // Complete trace
        self.tracer.complete_trace(trace_context);

        // Log if operation was slow
        if duration.as_millis() > 5 {
            warn!("Slow cache SET operation: {} took {:?}", key, duration);
        }

        debug!(
            "Cache SET {}: {} in {:?} (TTL: {:?})",
            key,
            if result.is_ok() { "SUCCESS" } else { "FAILED" },
            duration,
            ttl
        );

        result
    }

    /// Delete a value from cache with monitoring
    pub async fn delete(&self, key: &str) -> Result<(), CacheError> {
        let start = Instant::now();
        let trace_context = self.tracer.start_trace(&format!("cache:delete:{key}"));

        // Execute the cache operation
        let result = self.inner.delete(key).await;
        let duration = start.elapsed();

        // Record metrics
        let cache_metrics = CacheMetrics {
            operation_type: CacheOperationType::Delete,
            cache_key: if self.enable_detailed_metrics {
                key.to_string()
            } else {
                format!("key_hash_{:x}", calculate_key_hash(key))
            },
            duration,
            success: result.is_ok(),
            size_bytes: None,
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_cache_operation(cache_metrics);

        // Complete trace
        self.tracer.complete_trace(trace_context);

        debug!(
            "Cache DELETE {}: {} in {:?}",
            key,
            if result.is_ok() { "SUCCESS" } else { "FAILED" },
            duration
        );

        result.map(|_| ())
    }

    /// Invalidate cache entries matching a pattern with monitoring
    pub async fn invalidate_pattern(&self, pattern: &str) -> Result<u64, CacheError> {
        let start = Instant::now();
        let trace_context = self
            .tracer
            .start_trace(&format!("cache:invalidate:{pattern}"));

        // For now, we'll track this as a series of delete operations
        // In production, this would use SCAN and DEL commands
        let result = Ok(0u64); // Placeholder
        let duration = start.elapsed();

        // Record metrics
        let cache_metrics = CacheMetrics {
            operation_type: CacheOperationType::Invalidate,
            cache_key: format!("pattern:{pattern}"),
            duration,
            success: result.is_ok(),
            size_bytes: None,
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_cache_operation(cache_metrics);

        // Complete trace
        self.tracer.complete_trace(trace_context);

        debug!(
            "Cache INVALIDATE pattern {}: {} keys in {:?}",
            pattern,
            result.as_ref().unwrap_or(&0),
            duration
        );

        result
    }

    /// Get multiple values with monitoring (batch operation)
    pub async fn get_many<T>(&self, keys: &[String]) -> Result<Vec<Option<T>>, CacheError>
    where
        T: DeserializeOwned + Serialize,
    {
        let start = Instant::now();
        let trace_context = self
            .tracer
            .start_trace(&format!("cache:get_many:{}_keys", keys.len()));

        // Execute batch operation
        let mut results = Vec::with_capacity(keys.len());
        let mut hits = 0;
        let mut total_size = 0;

        for key in keys {
            match self.inner.get::<T>(key).await {
                Ok(Some(value)) => {
                    hits += 1;
                    if self.enable_detailed_metrics {
                        if let Ok(serialized) = serde_json::to_vec(&value) {
                            total_size += serialized.len();
                        }
                    }
                    results.push(Some(value));
                }
                Ok(None) => results.push(None),
                Err(e) => return Err(e),
            }
        }

        let duration = start.elapsed();
        let hit_rate = hits as f64 / keys.len() as f64;

        // Record aggregated metrics
        let cache_metrics = CacheMetrics {
            operation_type: CacheOperationType::Get,
            cache_key: format!("batch:{}_keys", keys.len()),
            duration,
            success: true,
            size_bytes: if self.enable_detailed_metrics {
                Some(total_size)
            } else {
                None
            },
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_cache_operation(cache_metrics);

        // Complete trace
        self.tracer.complete_trace(trace_context);

        debug!(
            "Cache BATCH GET: {}/{} hits ({:.1}%) in {:?}",
            hits,
            keys.len(),
            hit_rate * 100.0,
            duration
        );

        Ok(results)
    }

    /// Execute a cache operation with tracing helper
    pub async fn with_tracing<T, F, Fut>(
        &self,
        parent_context: &TraceContext,
        operation: &str,
        f: F,
    ) -> Result<T, String>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T, CacheError>>,
    {
        trace_cache_operation(self.tracer.as_ref(), parent_context, operation, || {
            // Convert async to sync for the helper
            // In production, would use a proper async trace helper
            match tokio::runtime::Handle::current().block_on(f()) {
                Ok(result) => Ok(result),
                Err(e) => Err(e.to_string()),
            }
        })
    }
}

/// Calculate a hash of the cache key for anonymization
fn calculate_key_hash(key: &str) -> u64 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    key.hash(&mut hasher);
    hasher.finish()
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_application::monitoring::{DefaultPerformanceTracer, InMemoryMetricsCollector};

    // Mock Redis cache for testing
    #[allow(dead_code)]
    struct MockRedisCache {
        storage: std::sync::Arc<std::sync::Mutex<std::collections::HashMap<String, String>>>,
    }

    impl MockRedisCache {
        fn new() -> Self {
            Self {
                storage: std::sync::Arc::new(std::sync::Mutex::new(
                    std::collections::HashMap::new(),
                )),
            }
        }
    }

    #[tokio::test]
    async fn test_monitored_cache_get() {
        let _inner = MockRedisCache::new();
        let _metrics_collector = Arc::new(InMemoryMetricsCollector::new());
        let _tracer = Arc::new(DefaultPerformanceTracer::new());

        // This would require a full mock implementation
        // For now, we're demonstrating the structure
    }

    #[test]
    fn test_key_hash_anonymization() {
        let key1 = "user:profile:123";
        let key2 = "user:profile:456";
        let key3 = "user:profile:123"; // Same as key1

        let hash1 = calculate_key_hash(key1);
        let hash2 = calculate_key_hash(key2);
        let hash3 = calculate_key_hash(key3);

        assert_ne!(hash1, hash2); // Different keys have different hashes
        assert_eq!(hash1, hash3); // Same keys have same hash
    }
}
