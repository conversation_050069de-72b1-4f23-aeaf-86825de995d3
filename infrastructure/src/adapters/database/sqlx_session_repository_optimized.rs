// Optimized SQLx implementation of SessionRepository
// Provides database persistence for Session entities with performance optimizations

use async_trait::async_trait;
use auth_domain::{
    entities::Session,
    errors::DomainError,
    repositories::SessionRepository,
    value_objects::{SessionId, UserId},
};
use sqlx::{Pool, Sqlite, Postgres, Row, query, query_as};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tracing::{debug, error, warn, instrument};

/// Optimized SQLx implementation of SessionRepository with performance monitoring
pub struct OptimizedSqlxSessionRepository {
    sqlite_pool: Option<Pool<Sqlite>>,
    postgres_pool: Option<Pool<Postgres>>,
    performance_threshold_ms: u64,
}

// Persistence record - separate from domain model
#[derive(Debug, sqlx::FromRow)]
struct SessionRecord {
    id: String,
    user_id: String,
    created_at: i64,    // Unix timestamp
    expires_at: i64,    // Unix timestamp
    last_accessed: i64, // Unix timestamp
    ip_address: Option<String>,
    user_agent: Option<String>,
    is_active: bool,
}

impl SessionRecord {
    /// Convert from domain Session to persistence record
    fn from_domain(session: &Session) -> Self {
        SessionRecord {
            id: session.id().as_str().to_string(),
            user_id: session.user_id().as_str().to_string(),
            created_at: session.created_at().timestamp() as i64,
            expires_at: session.expires_at().timestamp() as i64,
            last_accessed: session.last_accessed().timestamp() as i64,
            ip_address: session.ip_address().map(|s| s.to_string()),
            user_agent: session.user_agent().map(|s| s.to_string()),
            is_active: session.is_active(),
        }
    }

    /// Convert from persistence record to domain Session
    #[allow(clippy::wrong_self_convention)]
    fn to_domain(self) -> Result<Session, DomainError> {
        let user_id = UserId::from_string(self.user_id)?;

        // Calculate duration from created_at to expires_at
        let duration_seconds = (self.expires_at - self.created_at) as u64;

        // Create session with the calculated duration
        let session = Session::new(user_id, duration_seconds, self.ip_address, self.user_agent);

        Ok(session)
    }
}

impl OptimizedSqlxSessionRepository {
    /// Create new repository with SQLite pool
    pub fn new_sqlite(pool: Pool<Sqlite>) -> Self {
        OptimizedSqlxSessionRepository {
            sqlite_pool: Some(pool),
            postgres_pool: None,
            performance_threshold_ms: 30, // Sessions need to be faster
        }
    }

    /// Create new repository with PostgreSQL pool
    pub fn new_postgres(pool: Pool<Postgres>) -> Self {
        OptimizedSqlxSessionRepository {
            sqlite_pool: None,
            postgres_pool: Some(pool),
            performance_threshold_ms: 30,
        }
    }

    /// Execute query with performance monitoring
    async fn execute_with_timing<F, R>(&self, operation_name: &str, query_fn: F) -> Result<R, DomainError>
    where
        F: std::future::Future<Output = Result<R, sqlx::Error>>,
    {
        let start = Instant::now();
        let result = query_fn.await;
        let duration = start.elapsed();

        // Log performance metrics
        debug!("Session query [{}] completed in {}ms", operation_name, duration.as_millis());

        if duration.as_millis() > self.performance_threshold_ms as u128 {
            warn!("Slow session query: [{}] took {}ms", operation_name, duration.as_millis());
        }

        if duration.as_millis() > 90 {
            error!("Session query approaching SLA timeout: [{}] took {}ms", operation_name, duration.as_millis());
        }

        result.map_err(|e| {
            error!("Session database query failed [{}]: {}", operation_name, e);
            DomainError::Database {
                source: e.to_string(),
                context: operation_name.to_string(),
            }
        })
    }

    /// Get current timestamp
    fn current_timestamp() -> i64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64
    }
}

#[async_trait]
impl SessionRepository for OptimizedSqlxSessionRepository {
    #[instrument(level = "debug", skip(self, session))]
    async fn save(&self, session: &Session) -> Result<(), DomainError> {
        let record = SessionRecord::from_domain(session);

        if let Some(pool) = &self.sqlite_pool {
            let sql = r#"
                INSERT OR REPLACE INTO sessions 
                (id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active)
                VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)
            "#;

            self.execute_with_timing("save_session_sqlite",
                query(sql)
                    .bind(&record.id)
                    .bind(&record.user_id)
                    .bind(record.created_at)
                    .bind(record.expires_at)
                    .bind(record.last_accessed)
                    .bind(&record.ip_address)
                    .bind(&record.user_agent)
                    .bind(record.is_active)
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                INSERT INTO sessions 
                (id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT (id) DO UPDATE SET
                    expires_at = EXCLUDED.expires_at,
                    last_accessed = EXCLUDED.last_accessed,
                    is_active = EXCLUDED.is_active
            "#;

            self.execute_with_timing("save_session_postgres",
                query(sql)
                    .bind(&record.id)
                    .bind(&record.user_id)
                    .bind(record.created_at)
                    .bind(record.expires_at)
                    .bind(record.last_accessed)
                    .bind(&record.ip_address)
                    .bind(&record.user_agent)
                    .bind(record.is_active)
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_id(&self, id: &SessionId) -> Result<Option<Session>, DomainError> {
        let current_time = Self::current_timestamp();

        if let Some(pool) = &self.sqlite_pool {
            // Critical authentication query - uses idx_sessions_token_hash index
            let sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE id = ? AND is_active = TRUE AND expires_at > ?
            "#;

            let record: Option<SessionRecord> = self.execute_with_timing("find_session_by_id_sqlite",
                query_as::<_, SessionRecord>(sql)
                    .bind(id.as_str())
                    .bind(current_time)
                    .fetch_optional(pool)
            ).await?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else if let Some(pool) = &self.postgres_pool {
            // Uses idx_sessions_active_token index
            let sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE id = $1 AND is_active = TRUE AND expires_at > $2
            "#;

            let record: Option<SessionRecord> = self.execute_with_timing("find_session_by_id_postgres",
                query_as::<_, SessionRecord>(sql)
                    .bind(id.as_str())
                    .bind(current_time)
                    .fetch_optional(pool)
            ).await?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Err(DomainError::Configuration("No database pool configured".to_string()))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_active_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
        let current_time = Self::current_timestamp();

        if let Some(pool) = &self.sqlite_pool {
            // Uses idx_sessions_user_active_expires composite index for optimal performance
            let sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE user_id = ? AND is_active = TRUE AND expires_at > ?
                ORDER BY last_accessed DESC
            "#;

            let records: Vec<SessionRecord> = self.execute_with_timing("find_active_sessions_sqlite",
                query_as::<_, SessionRecord>(sql)
                    .bind(user_id.as_str())
                    .bind(current_time)
                    .fetch_all(pool)
            ).await?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE user_id = $1 AND is_active = TRUE AND expires_at > $2
                ORDER BY last_accessed DESC
            "#;

            let records: Vec<SessionRecord> = self.execute_with_timing("find_active_sessions_postgres",
                query_as::<_, SessionRecord>(sql)
                    .bind(user_id.as_str())
                    .bind(current_time)
                    .fetch_all(pool)
            ).await?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_all_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Gets all sessions (active and inactive) for audit purposes
            let sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE user_id = ?
                ORDER BY created_at DESC
            "#;

            let records: Vec<SessionRecord> = self.execute_with_timing("find_all_sessions_sqlite",
                query_as::<_, SessionRecord>(sql)
                    .bind(user_id.as_str())
                    .fetch_all(pool)
            ).await?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn delete(&self, id: &SessionId) -> Result<(), DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Hard delete for sessions (they don't need audit trail like users)
            let sql = "DELETE FROM sessions WHERE id = ?";

            self.execute_with_timing("delete_session_sqlite",
                query(sql)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = "DELETE FROM sessions WHERE id = $1";

            self.execute_with_timing("delete_session_postgres",
                query(sql)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }

    #[instrument(level = "debug", skip(self))]
    async fn delete_all_by_user(&self, user_id: &UserId) -> Result<(), DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Delete all sessions for a user (e.g., when user logs out everywhere)
            let sql = "DELETE FROM sessions WHERE user_id = ?";

            self.execute_with_timing("delete_all_sessions_sqlite",
                query(sql)
                    .bind(user_id.as_str())
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = "DELETE FROM sessions WHERE user_id = $1";

            self.execute_with_timing("delete_all_sessions_postgres",
                query(sql)
                    .bind(user_id.as_str())
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }

    #[instrument(level = "debug", skip(self))]
    async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError> {
        let current_time = Self::current_timestamp();

        if let Some(pool) = &self.sqlite_pool {
            // Uses idx_sessions_cleanup index for efficient expired session removal
            let sql = "DELETE FROM sessions WHERE expires_at <= ? OR is_active = FALSE";

            let result = self.execute_with_timing("cleanup_expired_sessions_sqlite",
                query(sql)
                    .bind(current_time)
                    .execute(pool)
            ).await?;

            let deleted_count = result.rows_affected() as usize;
            debug!("Cleaned up {} expired sessions", deleted_count);
            Ok(deleted_count)

        } else if let Some(pool) = &self.postgres_pool {
            let sql = "DELETE FROM sessions WHERE expires_at <= $1 OR is_active = FALSE";

            let result = self.execute_with_timing("cleanup_expired_sessions_postgres",
                query(sql)
                    .bind(current_time)
                    .execute(pool)
            ).await?;

            let deleted_count = result.rows_affected() as usize;
            debug!("Cleaned up {} expired sessions", deleted_count);
            Ok(deleted_count)
        } else {
            Ok(0)
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn count_active_by_user(&self, user_id: &UserId) -> Result<usize, DomainError> {
        let current_time = Self::current_timestamp();

        if let Some(pool) = &self.sqlite_pool {
            // Efficient count using index
            let sql = r#"
                SELECT COUNT(*) 
                FROM sessions 
                WHERE user_id = ? AND is_active = TRUE AND expires_at > ?
            "#;

            let count: (i64,) = self.execute_with_timing("count_active_sessions_sqlite",
                query_as(sql)
                    .bind(user_id.as_str())
                    .bind(current_time)
                    .fetch_one(pool)
            ).await?;

            Ok(count.0 as usize)
        } else {
            Ok(0)
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_ip_address(&self, ip_address: &str) -> Result<Vec<Session>, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Uses idx_sessions_ip_address index for security analysis
            let sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE ip_address = ? AND is_active = TRUE
                ORDER BY created_at DESC
            "#;

            let records: Vec<SessionRecord> = self.execute_with_timing("find_sessions_by_ip_sqlite",
                query_as::<_, SessionRecord>(sql)
                    .bind(ip_address)
                    .fetch_all(pool)
            ).await?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    async fn find_by_created_date_range(
        &self,
        start: SystemTime,
        end: SystemTime,
    ) -> Result<Vec<Session>, DomainError> {
        let start_timestamp = start.duration_since(UNIX_EPOCH)
            .map_err(|_| DomainError::InvalidInput("Invalid start time".to_string()))?
            .as_secs() as i64;
        
        let end_timestamp = end.duration_since(UNIX_EPOCH)
            .map_err(|_| DomainError::InvalidInput("Invalid end time".to_string()))?
            .as_secs() as i64;

        if let Some(pool) = &self.sqlite_pool {
            let sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE created_at BETWEEN ? AND ?
                ORDER BY created_at DESC
            "#;

            let records: Vec<SessionRecord> = self.execute_with_timing("find_sessions_by_date_range_sqlite",
                query_as::<_, SessionRecord>(sql)
                    .bind(start_timestamp)
                    .bind(end_timestamp)
                    .fetch_all(pool)
            ).await?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn update_last_accessed(&self, id: &SessionId) -> Result<(), DomainError> {
        let current_time = Self::current_timestamp();

        if let Some(pool) = &self.sqlite_pool {
            // Update last accessed time for session tracking
            let sql = r#"
                UPDATE sessions 
                SET last_accessed = ?
                WHERE id = ? AND is_active = TRUE
            "#;

            self.execute_with_timing("update_last_accessed_sqlite",
                query(sql)
                    .bind(current_time)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                UPDATE sessions 
                SET last_accessed = $1
                WHERE id = $2 AND is_active = TRUE
            "#;

            self.execute_with_timing("update_last_accessed_postgres",
                query(sql)
                    .bind(current_time)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }

    #[instrument(level = "debug", skip(self))]
    async fn invalidate(&self, id: &SessionId) -> Result<(), DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Soft invalidation - mark as inactive
            let sql = "UPDATE sessions SET is_active = FALSE WHERE id = ?";

            self.execute_with_timing("invalidate_session_sqlite",
                query(sql)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = "UPDATE sessions SET is_active = FALSE WHERE id = $1";

            self.execute_with_timing("invalidate_session_postgres",
                query(sql)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }

    #[instrument(level = "debug", skip(self))]
    async fn invalidate_all_except(
        &self,
        user_id: &UserId,
        except_session_id: &SessionId,
    ) -> Result<(), DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Invalidate all other sessions for a user (e.g., after password change)
            let sql = r#"
                UPDATE sessions 
                SET is_active = FALSE 
                WHERE user_id = ? AND id != ?
            "#;

            self.execute_with_timing("invalidate_other_sessions_sqlite",
                query(sql)
                    .bind(user_id.as_str())
                    .bind(except_session_id.as_str())
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                UPDATE sessions 
                SET is_active = FALSE 
                WHERE user_id = $1 AND id != $2
            "#;

            self.execute_with_timing("invalidate_other_sessions_postgres",
                query(sql)
                    .bind(user_id.as_str())
                    .bind(except_session_id.as_str())
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_session_record_conversion() {
        let user_id = UserId::new();
        let session = Session::new(
            user_id.clone(),
            3600, // 1 hour
            Some("127.0.0.1".to_string()),
            Some("Test Agent".to_string()),
        );

        let record = SessionRecord::from_domain(&session);
        assert_eq!(record.user_id, user_id.as_str());
        assert_eq!(record.ip_address, Some("127.0.0.1".to_string()));
        assert_eq!(record.user_agent, Some("Test Agent".to_string()));
        assert!(record.is_active);

        // Test conversion back to domain
        let converted_session = record.to_domain().unwrap();
        assert_eq!(converted_session.user_id(), &user_id);
        assert_eq!(converted_session.ip_address(), Some("127.0.0.1"));
        assert_eq!(converted_session.user_agent(), Some("Test Agent"));
        assert!(converted_session.is_active());
    }

    #[test]
    fn test_current_timestamp() {
        let timestamp1 = OptimizedSqlxSessionRepository::current_timestamp();
        std::thread::sleep(std::time::Duration::from_millis(10));
        let timestamp2 = OptimizedSqlxSessionRepository::current_timestamp();
        
        assert!(timestamp2 > timestamp1);
    }
}