// SQLx implementation of UserRepository
// Provides database persistence for User entities with performance optimizations

use crate::database::{OptimizedConnectionPool, PreparedStatementCache, QueryOptimizer};
use async_trait::async_trait;
use auth_domain::{
    entities::User,
    errors::DomainError,
    repositories::{UserRepository, UserSearchCriteria, UserSearchResult},
    value_objects::{Email, Password, UserId},
};
use sqlx::{query, query_as};
use std::sync::{Arc, Mutex};
use std::time::Instant;
use tracing::{error, instrument};

/// Repository metrics for performance monitoring
#[derive(Debug, De<PERSON>ult)]
struct RepositoryMetrics {
    query_count: u64,
    total_execution_time_ms: u64,
    slow_query_count: u64,
    error_count: u64,
}

/// Optimized SQLx implementation of UserRepository
#[derive(Clone)]
pub struct SqlxUserRepository {
    connection_pool: OptimizedConnectionPool,
    #[allow(dead_code)]
    query_optimizer: QueryOptimizer,
    #[allow(dead_code)]
    statement_cache: PreparedStatementCache,
    performance_threshold_ms: u64,
    metrics: Arc<Mutex<RepositoryMetrics>>,
}

impl SqlxUserRepository {
    /// Create new optimized user repository
    pub fn new(connection_pool: OptimizedConnectionPool) -> Self {
        SqlxUserRepository {
            connection_pool,
            query_optimizer: QueryOptimizer::new(),
            statement_cache: PreparedStatementCache::new_optimized(),
            performance_threshold_ms: 50, // Warn on queries > 50ms
            metrics: Arc::new(Mutex::new(RepositoryMetrics::default())),
        }
    }

    /// Create repository with custom performance threshold
    pub fn with_performance_threshold(mut self, threshold_ms: u64) -> Self {
        self.performance_threshold_ms = threshold_ms;
        self
    }

    /// Record metrics for a database operation
    fn record_metrics<T>(
        &self,
        query_name: &str,
        start_time: Instant,
        result: &Result<T, sqlx::Error>,
    ) {
        let execution_time = start_time.elapsed();
        let execution_time_ms = execution_time.as_millis() as u64;

        // Update metrics using interior mutability
        if let Ok(mut metrics) = self.metrics.lock() {
            metrics.query_count += 1;
            metrics.total_execution_time_ms += execution_time_ms;

            if execution_time_ms > self.performance_threshold_ms {
                metrics.slow_query_count += 1;
            }

            if result.is_err() {
                metrics.error_count += 1;
            }
        }

        // Log performance warnings
        if execution_time_ms > self.performance_threshold_ms {
            tracing::warn!(
                "Slow query detected: {} took {}ms",
                query_name,
                execution_time_ms
            );
        }

        // Log SLA violations
        if execution_time_ms > 90 {
            tracing::error!(
                "Query approaching SLA timeout: {} took {}ms",
                query_name,
                execution_time_ms
            );
        }

        if let Err(e) = result {
            error!("Database query failed [{}]: {}", query_name, e);
        }
    }
}

// Persistence record - separate from domain model
#[derive(Debug, sqlx::FromRow)]
#[allow(dead_code)]
struct UserRecord {
    id: String,
    email: String,
    password_hash: String,
    is_verified: bool,
    is_active: bool,
    created_at: i64, // Unix timestamp
    updated_at: i64, // Unix timestamp
}

#[allow(dead_code)]
impl UserRecord {
    /// Convert from domain User to persistence record
    fn from_domain(user: &User) -> Self {
        UserRecord {
            id: user.id().as_str().to_string(),
            email: user.email().as_str().to_string(),
            password_hash: "$argon2id$v=19$m=19456,t=2,p=1$dummy_salt$dummy_hash".to_string(), // This would be properly handled in real implementation
            is_verified: user.is_verified(),
            is_active: user.is_active(),
            created_at: user.created_at().timestamp() as i64,
            updated_at: user.updated_at().timestamp() as i64,
        }
    }

    /// Convert from persistence record to domain User
    #[allow(clippy::wrong_self_convention)]
    fn to_domain(self) -> Result<User, DomainError> {
        let email = Email::new(&self.email)?;
        let password = Password::from_hash(self.password_hash);

        // In a real implementation, you would reconstruct the User with all fields
        // For now, we'll create a new user and modify its state
        let mut user = User::new(email, password)?;

        if self.is_verified {
            let _ = user.verify()?;
        }

        if !self.is_active {
            user.deactivate()?;
        }

        Ok(user)
    }
}

#[async_trait]
impl UserRepository for SqlxUserRepository {
    #[instrument(level = "debug", skip(self, user))]
    async fn save(&self, user: &User) -> Result<(), DomainError> {
        // For now, we'll implement without mutation for trait compliance
        // In a production system, you'd use Arc<Mutex<>> or similar for interior mutability
        let record = UserRecord::from_domain(user);

        let sql = r#"
            INSERT OR REPLACE INTO users 
            (id, email, password_hash, is_verified, is_active, created_at, updated_at)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql)
                .bind(&record.id)
                .bind(&record.email)
                .bind(&record.password_hash)
                .bind(record.is_verified)
                .bind(record.is_active)
                .bind(record.created_at)
                .bind(record.updated_at)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_user", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Save user failed: {e}")))
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                INSERT INTO users 
                (id, email, password_hash, is_verified, is_active, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (id) DO UPDATE SET
                    email = EXCLUDED.email,
                    password_hash = EXCLUDED.password_hash,
                    is_verified = EXCLUDED.is_verified,
                    is_active = EXCLUDED.is_active,
                    updated_at = EXCLUDED.updated_at
            "#;

            let start_time = Instant::now();
            let result = query(pg_sql)
                .bind(&record.id)
                .bind(&record.email)
                .bind(&record.password_hash)
                .bind(record.is_verified)
                .bind(record.is_active)
                .bind(record.created_at)
                .bind(record.updated_at)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_user_pg", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Save user failed: {e}")))
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
        let sql = r#"
            SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
            FROM users 
            WHERE id = ? AND is_active = TRUE
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(sql)
                .bind(id.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_user_by_id", start_time, &result);
            let record: Option<UserRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find by ID failed: {e}")))?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE id = $1 AND is_active = TRUE
            "#;

            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(pg_sql)
                .bind(id.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_user_by_id_pg", start_time, &result);
            let record: Option<UserRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find by ID failed: {e}")))?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
        // This is the most critical query for authentication - optimized with index
        let sql = r#"
            SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
            FROM users 
            WHERE email = ? AND is_active = TRUE AND is_verified = TRUE
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(sql)
                .bind(email.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_user_by_email", start_time, &result);
            let record: Option<UserRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find by email failed: {e}")))?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE email = $1 AND is_active = TRUE AND is_verified = TRUE
            "#;

            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(pg_sql)
                .bind(email.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_user_by_email_pg", start_time, &result);
            let record: Option<UserRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find by email failed: {e}")))?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    async fn find_by_username(&self, username: &str) -> Result<Option<User>, DomainError> {
        // Note: This implementation assumes username is stored in email field
        // In a full system, there would be a separate username field
        let sql = r#"
            SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
            FROM users 
            WHERE email = ? AND is_active = TRUE
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(sql)
                .bind(username)
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_user_by_username", start_time, &result);
            let record: Option<UserRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find by username failed: {e}"))
            })?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
        // Optimized existence check - no need to fetch full record
        let sql = r#"
            SELECT 1 FROM users 
            WHERE email = ? AND is_active = TRUE 
            LIMIT 1
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let query_result = query_as(sql)
                .bind(email.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("exists_by_email", start_time, &query_result);
            let result: Option<(i32,)> = query_result
                .map_err(|e| DomainError::Infrastructure(format!("Exists check failed: {e}")))?;

            Ok(result.is_some())
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                SELECT 1 FROM users 
                WHERE email = $1 AND is_active = TRUE 
                LIMIT 1
            "#;

            let start_time = Instant::now();
            let query_result = query_as(pg_sql)
                .bind(email.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("exists_by_email_pg", start_time, &query_result);
            let result: Option<(i32,)> = query_result
                .map_err(|e| DomainError::Infrastructure(format!("Exists check failed: {e}")))?;

            Ok(result.is_some())
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    async fn delete(&self, id: &UserId) -> Result<(), DomainError> {
        // Soft delete to maintain audit trail
        let sql = r#"
            UPDATE users 
            SET is_active = FALSE, updated_at = strftime('%s', 'now')
            WHERE id = ?
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql).bind(id.as_str()).execute(pool).await.map(|_| ());

            self.record_metrics("delete_user", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Delete user failed: {e}")))
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                UPDATE users 
                SET is_active = FALSE, updated_at = EXTRACT(epoch FROM NOW())
                WHERE id = $1
            "#;

            let start_time = Instant::now();
            let result = query(pg_sql)
                .bind(id.as_str())
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("delete_user_pg", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Delete user failed: {e}")))
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError> {
        let sql = format!(
            "SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
             FROM users 
             WHERE is_active = TRUE 
             ORDER BY created_at DESC 
             LIMIT {limit} OFFSET {offset}"
        );

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(&sql).fetch_all(pool).await;

            self.record_metrics("find_all_users", start_time, &result);
            let records: Vec<UserRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find all users failed: {e}")))?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![])
        }
    }

    async fn count(&self) -> Result<usize, DomainError> {
        let sql = "SELECT COUNT(*) FROM users WHERE is_active = TRUE";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, (i64,)>(sql)
                .fetch_one(pool)
                .await
                .map(|row| row.0);

            self.record_metrics("count_users", start_time, &result);
            let count: i64 = result
                .map_err(|e| DomainError::Infrastructure(format!("Count users failed: {e}")))?;
            Ok(count as usize)
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, (i64,)>(sql)
                .fetch_one(pool)
                .await
                .map(|row| row.0);

            self.record_metrics("count_users_pg", start_time, &result);
            let count: i64 = result
                .map_err(|e| DomainError::Infrastructure(format!("Count users failed: {e}")))?;
            Ok(count as usize)
        } else {
            Ok(0)
        }
    }

    async fn find_by_created_date_range(
        &self,
        start: std::time::SystemTime,
        end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError> {
        let start_timestamp = start
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;
        let end_timestamp = end
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let sql = r#"
            SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
            FROM users 
            WHERE is_active = TRUE AND created_at >= ? AND created_at <= ?
            ORDER BY created_at DESC
            LIMIT 1000
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(sql)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_users_by_date_range", start_time, &result);
            let records: Vec<UserRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find users by date range failed: {e}"))
            })?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![])
        }
    }

    async fn find_by_verification_status(
        &self,
        is_verified: bool,
    ) -> Result<Vec<User>, DomainError> {
        let sql = r#"
            SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
            FROM users 
            WHERE is_active = TRUE AND is_verified = ?
            ORDER BY created_at DESC
            LIMIT 1000
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(sql)
                .bind(is_verified)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_users_by_verification", start_time, &result);
            let records: Vec<UserRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find users by verification failed: {e}"))
            })?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![])
        }
    }

    async fn find_by_active_status(&self, is_active: bool) -> Result<Vec<User>, DomainError> {
        let sql = r#"
            SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
            FROM users 
            WHERE is_active = ?
            ORDER BY created_at DESC
            LIMIT 1000
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, UserRecord>(sql)
                .bind(is_active)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_users_by_active_status", start_time, &result);
            let records: Vec<UserRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find users by active status failed: {e}"))
            })?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![])
        }
    }

    #[instrument(level = "debug", skip(self, criteria))]
    async fn search_users(
        &self,
        criteria: &UserSearchCriteria,
    ) -> Result<UserSearchResult, DomainError> {
        // Simplified search implementation to avoid lifetime issues
        let count_sql = "SELECT COUNT(*) FROM users WHERE is_active = TRUE";
        let search_sql = format!(
            "SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
             FROM users 
             WHERE is_active = TRUE 
             ORDER BY created_at DESC 
             LIMIT {} OFFSET {}",
            criteria.page_size,
            criteria.page * criteria.page_size
        );

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            // Execute count query
            let start_time = Instant::now();
            let count_result = query_as::<_, (i64,)>(count_sql)
                .fetch_one(pool)
                .await
                .map(|row| row.0);

            self.record_metrics("search_users_count", start_time, &count_result);
            let total_count: i64 = count_result
                .map_err(|e| DomainError::Infrastructure(format!("Search count failed: {e}")))?;

            // Execute search query
            let start_time = Instant::now();
            let search_result = query_as::<_, UserRecord>(&search_sql).fetch_all(pool).await;

            self.record_metrics("search_users", start_time, &search_result);
            let records: Vec<UserRecord> = search_result
                .map_err(|e| DomainError::Infrastructure(format!("Search users failed: {e}")))?;

            // Convert to domain objects
            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }

            Ok(UserSearchResult::new(
                users,
                total_count as usize,
                criteria.page,
                criteria.page_size,
            ))
        } else {
            // Return empty result for now - PostgreSQL implementation would be similar
            Ok(UserSearchResult::new(
                Vec::new(),
                0,
                criteria.page,
                criteria.page_size,
            ))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    #[allow(clippy::bool_assert_comparison)]
    fn test_user_record_conversion() {
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPasswordSecure123!").unwrap();
        let user = User::new(email, password).unwrap();

        let record = UserRecord::from_domain(&user);
        assert_eq!(record.email, "<EMAIL>");
        assert_eq!(record.is_verified, false);
        assert_eq!(record.is_active, true);

        // Test conversion back to domain
        let converted_user = record.to_domain().unwrap();
        assert_eq!(converted_user.email().as_str(), "<EMAIL>");
        assert_eq!(converted_user.is_verified(), false);
        assert_eq!(converted_user.is_active(), true);
    }
}
