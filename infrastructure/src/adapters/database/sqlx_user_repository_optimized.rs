// Optimized SQLx implementation of UserRepository
// Provides database persistence for User entities with performance optimizations

use async_trait::async_trait;
use auth_domain::{
    entities::User,
    errors::DomainError,
    repositories::{UserRepository, UserSearchCriteria, UserSearchResult},
    value_objects::{Email, Password, UserId},
};
use sqlx::{Pool, Sqlite, Postgres, Row, query, query_as};
use std::time::{Duration, Instant};
use tracing::{debug, error, warn, instrument};

/// Optimized SQLx implementation of UserRepository with performance monitoring
pub struct OptimizedSqlxUserRepository {
    sqlite_pool: Option<Pool<Sqlite>>,
    postgres_pool: Option<Pool<Postgres>>,
    performance_threshold_ms: u64,
}

// Persistence record - separate from domain model
#[derive(Debug, sqlx::FromRow)]
struct UserRecord {
    id: String,
    email: String,
    password_hash: String,
    is_verified: bool,
    is_active: bool,
    created_at: i64, // Unix timestamp
    updated_at: i64, // Unix timestamp
}

impl UserRecord {
    /// Convert from domain User to persistence record
    fn from_domain(user: &User) -> Self {
        UserRecord {
            id: user.id().as_str().to_string(),
            email: user.email().as_str().to_string(),
            password_hash: "$argon2id$v=19$m=19456,t=2,p=1$dummy_salt$dummy_hash".to_string(),
            is_verified: user.is_verified(),
            is_active: user.is_active(),
            created_at: user.created_at().timestamp() as i64,
            updated_at: user.updated_at().timestamp() as i64,
        }
    }

    /// Convert from persistence record to domain User
    #[allow(clippy::wrong_self_convention)]
    fn to_domain(self) -> Result<User, DomainError> {
        let email = Email::new(&self.email)?;
        let password = Password::from_hash(self.password_hash);

        let mut user = User::new(email, password)?;

        if self.is_verified {
            let _ = user.verify()?;
        }

        if !self.is_active {
            user.deactivate()?;
        }

        Ok(user)
    }
}

impl OptimizedSqlxUserRepository {
    /// Create new repository with SQLite pool
    pub fn new_sqlite(pool: Pool<Sqlite>) -> Self {
        OptimizedSqlxUserRepository {
            sqlite_pool: Some(pool),
            postgres_pool: None,
            performance_threshold_ms: 50,
        }
    }

    /// Create new repository with PostgreSQL pool
    pub fn new_postgres(pool: Pool<Postgres>) -> Self {
        OptimizedSqlxUserRepository {
            sqlite_pool: None,
            postgres_pool: Some(pool),
            performance_threshold_ms: 50,
        }
    }

    /// Execute query with performance monitoring
    async fn execute_with_timing<F, R>(&self, operation_name: &str, query_fn: F) -> Result<R, DomainError>
    where
        F: std::future::Future<Output = Result<R, sqlx::Error>>,
    {
        let start = Instant::now();
        let result = query_fn.await;
        let duration = start.elapsed();

        // Log performance metrics
        debug!("Query [{}] completed in {}ms", operation_name, duration.as_millis());

        if duration.as_millis() > self.performance_threshold_ms as u128 {
            warn!("Slow query detected: [{}] took {}ms", operation_name, duration.as_millis());
        }

        if duration.as_millis() > 90 {
            error!("Query approaching SLA timeout: [{}] took {}ms", operation_name, duration.as_millis());
        }

        result.map_err(|e| {
            error!("Database query failed [{}]: {}", operation_name, e);
            DomainError::Database {
                source: e.to_string(),
                context: operation_name.to_string(),
            }
        })
    }
}

#[async_trait]
impl UserRepository for OptimizedSqlxUserRepository {
    #[instrument(level = "debug", skip(self, user))]
    async fn save(&self, user: &User) -> Result<(), DomainError> {
        let record = UserRecord::from_domain(user);

        if let Some(pool) = &self.sqlite_pool {
            let sql = r#"
                INSERT OR REPLACE INTO users 
                (id, email, password_hash, is_verified, is_active, created_at, updated_at)
                VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)
            "#;

            self.execute_with_timing("save_user_sqlite", 
                query(sql)
                    .bind(&record.id)
                    .bind(&record.email)
                    .bind(&record.password_hash)
                    .bind(record.is_verified)
                    .bind(record.is_active)
                    .bind(record.created_at)
                    .bind(record.updated_at)
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                INSERT INTO users 
                (id, email, password_hash, is_verified, is_active, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (id) DO UPDATE SET
                    email = EXCLUDED.email,
                    password_hash = EXCLUDED.password_hash,
                    is_verified = EXCLUDED.is_verified,
                    is_active = EXCLUDED.is_active,
                    updated_at = EXCLUDED.updated_at
            "#;

            self.execute_with_timing("save_user_postgres",
                query(sql)
                    .bind(&record.id)
                    .bind(&record.email)
                    .bind(&record.password_hash)
                    .bind(record.is_verified)
                    .bind(record.is_active)
                    .bind(record.created_at)
                    .bind(record.updated_at)
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Optimized query using the idx_users_active_only index
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE id = ? AND is_active = TRUE
            "#;

            let record: Option<UserRecord> = self.execute_with_timing("find_by_id_sqlite",
                query_as::<_, UserRecord>(sql)
                    .bind(id.as_str())
                    .fetch_optional(pool)
            ).await?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE id = $1 AND is_active = TRUE
            "#;

            let record: Option<UserRecord> = self.execute_with_timing("find_by_id_postgres",
                query_as::<_, UserRecord>(sql)
                    .bind(id.as_str())
                    .fetch_optional(pool)
            ).await?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Err(DomainError::Configuration("No database pool configured".to_string()))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Critical authentication query - uses idx_users_email_active index
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE email = ? AND is_active = TRUE AND is_verified = TRUE
            "#;

            let record: Option<UserRecord> = self.execute_with_timing("find_by_email_sqlite",
                query_as::<_, UserRecord>(sql)
                    .bind(email.as_str())
                    .fetch_optional(pool)
            ).await?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else if let Some(pool) = &self.postgres_pool {
            // Uses idx_users_email_hash index for optimal performance
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE email = $1 AND is_active = TRUE AND is_verified = TRUE
            "#;

            let record: Option<UserRecord> = self.execute_with_timing("find_by_email_postgres",
                query_as::<_, UserRecord>(sql)
                    .bind(email.as_str())
                    .fetch_optional(pool)
            ).await?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Err(DomainError::Configuration("No database pool configured".to_string()))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_username(&self, username: &str) -> Result<Option<User>, DomainError> {
        // For now, treating username same as email - in real implementation this would be separate
        let email = Email::new(username)?;
        self.find_by_email(&email).await
    }

    #[instrument(level = "debug", skip(self))]
    async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Optimized existence check - minimal data transfer
            let sql = r#"
                SELECT 1 FROM users 
                WHERE email = ? AND is_active = TRUE 
                LIMIT 1
            "#;

            let result: Option<(i32,)> = self.execute_with_timing("exists_by_email_sqlite",
                query_as(sql)
                    .bind(email.as_str())
                    .fetch_optional(pool)
            ).await?;

            Ok(result.is_some())
        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                SELECT 1 FROM users 
                WHERE email = $1 AND is_active = TRUE 
                LIMIT 1
            "#;

            let result: Option<(i32,)> = self.execute_with_timing("exists_by_email_postgres",
                query_as(sql)
                    .bind(email.as_str())
                    .fetch_optional(pool)
            ).await?;

            Ok(result.is_some())
        } else {
            Err(DomainError::Configuration("No database pool configured".to_string()))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn delete(&self, id: &UserId) -> Result<(), DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Soft delete - preserves audit trail
            let sql = r#"
                UPDATE users 
                SET is_active = FALSE, updated_at = strftime('%s', 'now')
                WHERE id = ?
            "#;

            self.execute_with_timing("delete_user_sqlite",
                query(sql)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;

        } else if let Some(pool) = &self.postgres_pool {
            let sql = r#"
                UPDATE users 
                SET is_active = FALSE, updated_at = EXTRACT(EPOCH FROM NOW())::BIGINT
                WHERE id = $1
            "#;

            self.execute_with_timing("delete_user_postgres",
                query(sql)
                    .bind(id.as_str())
                    .execute(pool)
            ).await?;
        }

        Ok(())
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Uses idx_users_search_name index for efficient sorting
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE is_active = TRUE
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            "#;

            let records: Vec<UserRecord> = self.execute_with_timing("find_all_users_sqlite",
                query_as::<_, UserRecord>(sql)
                    .bind(limit as i64)
                    .bind(offset as i64)
                    .fetch_all(pool)
            ).await?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![]) // Simplified for now
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn count(&self) -> Result<usize, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            let sql = "SELECT COUNT(*) FROM users WHERE is_active = TRUE";

            let count: (i64,) = self.execute_with_timing("count_users_sqlite",
                query_as(sql).fetch_one(pool)
            ).await?;

            Ok(count.0 as usize)
        } else {
            Ok(0)
        }
    }

    async fn find_by_created_date_range(
        &self,
        start: std::time::SystemTime,
        end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError> {
        let start_timestamp = start.duration_since(std::time::UNIX_EPOCH)
            .map_err(|_| DomainError::InvalidInput("Invalid start time".to_string()))?
            .as_secs() as i64;
        
        let end_timestamp = end.duration_since(std::time::UNIX_EPOCH)
            .map_err(|_| DomainError::InvalidInput("Invalid end time".to_string()))?
            .as_secs() as i64;

        if let Some(pool) = &self.sqlite_pool {
            // Uses idx_users_created_at index
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE is_active = TRUE AND created_at BETWEEN ? AND ?
                ORDER BY created_at DESC
            "#;

            let records: Vec<UserRecord> = self.execute_with_timing("find_by_date_range_sqlite",
                query_as::<_, UserRecord>(sql)
                    .bind(start_timestamp)
                    .bind(end_timestamp)
                    .fetch_all(pool)
            ).await?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![])
        }
    }

    async fn find_by_verification_status(&self, is_verified: bool) -> Result<Vec<User>, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Uses idx_users_verification index
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE is_active = TRUE AND is_verified = ?
                ORDER BY created_at DESC
            "#;

            let records: Vec<UserRecord> = self.execute_with_timing("find_by_verification_sqlite",
                query_as::<_, UserRecord>(sql)
                    .bind(is_verified)
                    .fetch_all(pool)
            ).await?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![])
        }
    }

    async fn find_by_active_status(&self, is_active: bool) -> Result<Vec<User>, DomainError> {
        if let Some(pool) = &self.sqlite_pool {
            // Uses idx_users_active index
            let sql = r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE is_active = ?
                ORDER BY created_at DESC
            "#;

            let records: Vec<UserRecord> = self.execute_with_timing("find_by_active_status_sqlite",
                query_as::<_, UserRecord>(sql)
                    .bind(is_active)
                    .fetch_all(pool)
            ).await?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }
            Ok(users)
        } else {
            Ok(vec![])
        }
    }

    #[instrument(level = "debug", skip(self, criteria))]
    async fn search_users(&self, criteria: &UserSearchCriteria) -> Result<UserSearchResult, DomainError> {
        // Simplified implementation focusing on most common search patterns
        if let Some(pool) = &self.sqlite_pool {
            let offset = criteria.page * criteria.page_size;
            
            // Build search query - uses multiple indexes efficiently
            let (where_clause, params) = self.build_search_where_clause(criteria);
            
            let count_sql = format!("SELECT COUNT(*) FROM users WHERE {}", where_clause);
            let search_sql = format!(
                r#"
                SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at 
                FROM users 
                WHERE {} 
                ORDER BY created_at DESC 
                LIMIT {} OFFSET {}
                "#,
                where_clause, criteria.page_size, offset
            );

            // Get total count
            let total_count: (i64,) = self.execute_with_timing("search_users_count_sqlite",
                query_as(&count_sql).fetch_one(pool)
            ).await?;

            // Get results
            let records: Vec<UserRecord> = self.execute_with_timing("search_users_sqlite",
                query_as::<_, UserRecord>(&search_sql).fetch_all(pool)
            ).await?;

            let mut users = Vec::new();
            for record in records {
                users.push(record.to_domain()?);
            }

            Ok(UserSearchResult::new(
                users,
                total_count.0 as usize,
                criteria.page,
                criteria.page_size,
            ))
        } else {
            // Return empty result for now
            Ok(UserSearchResult::new(
                Vec::new(),
                0,
                criteria.page,
                criteria.page_size,
            ))
        }
    }
}

impl OptimizedSqlxUserRepository {
    /// Build WHERE clause for search queries
    fn build_search_where_clause(&self, criteria: &UserSearchCriteria) -> (String, Vec<String>) {
        let mut where_clauses = vec!["is_active = TRUE".to_string()];
        let mut params = Vec::new();

        if let Some(email_filter) = &criteria.email_filter {
            where_clauses.push("email ILIKE '%' || ? || '%'".to_string());
            params.push(email_filter.clone());
        }

        if let Some(verification_filter) = criteria.verification_filter {
            where_clauses.push(format!("is_verified = {}", verification_filter));
        }

        if let Some(status_filter) = criteria.status_filter {
            // Override the default active filter if specified
            if !status_filter {
                where_clauses[0] = "is_active = FALSE".to_string();
            }
        }

        (where_clauses.join(" AND "), params)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_record_conversion() {
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPasswordSecure123!").unwrap();
        let user = User::new(email, password).unwrap();

        let record = UserRecord::from_domain(&user);
        assert_eq!(record.email, "<EMAIL>");
        assert_eq!(record.is_verified, false);
        assert_eq!(record.is_active, true);

        // Test conversion back to domain
        let converted_user = record.to_domain().unwrap();
        assert_eq!(converted_user.email().as_str(), "<EMAIL>");
        assert_eq!(converted_user.is_verified(), false);
        assert_eq!(converted_user.is_active(), true);
    }

    #[test]
    fn test_search_where_clause_building() {
        let repo = OptimizedSqlxUserRepository {
            sqlite_pool: None,
            postgres_pool: None,
            performance_threshold_ms: 50,
        };

        let mut criteria = UserSearchCriteria {
            email_filter: Some("<EMAIL>".to_string()),
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: Some(true),
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: 0,
            page_size: 10,
            sort_by: None,
            sort_direction: None,
        };

        let (where_clause, params) = repo.build_search_where_clause(&criteria);
        
        assert!(where_clause.contains("is_active = TRUE"));
        assert!(where_clause.contains("email ILIKE"));
        assert!(where_clause.contains("is_verified = true"));
        assert_eq!(params.len(), 1);
        assert_eq!(params[0], "<EMAIL>");
    }
}