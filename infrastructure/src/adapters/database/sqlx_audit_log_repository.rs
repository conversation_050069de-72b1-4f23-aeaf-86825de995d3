// SQLx implementation of AuditLogRepository
// Provides database persistence for AuditLog entities with performance optimizations

use crate::database::OptimizedConnectionPool;
use async_trait::async_trait;
use auth_domain::{
    entities::audit_log::{AuditAction, AuditLogEntry, EntityType},
    errors::DomainError,
    repositories::{
        AuditFailureStats, AuditLogRepository, AuditLogSearchCriteria, AuditLogSearchResult,
    },
    value_objects::{DateTime, UserId},
};
use sqlx::{query, query_as};
use std::sync::{Arc, Mutex};
use std::time::{Instant, SystemTime, UNIX_EPOCH};
use tracing::{error, instrument};

/// Repository metrics for performance monitoring
#[derive(Debug, Default)]
struct RepositoryMetrics {
    query_count: u64,
    total_execution_time_ms: u64,
    slow_query_count: u64,
    error_count: u64,
}

#[derive(Clone)]
pub struct SqlxAuditLogRepository {
    connection_pool: OptimizedConnectionPool,
    performance_threshold_ms: u64,
    metrics: Arc<Mutex<RepositoryMetrics>>,
}

impl SqlxAuditLogRepository {
    pub fn new(connection_pool: OptimizedConnectionPool) -> Self {
        SqlxAuditLogRepository {
            connection_pool,
            performance_threshold_ms: 100, // Audit logging can be less strict
            metrics: Arc::new(Mutex::new(RepositoryMetrics::default())),
        }
    }

    /// Record metrics for a database operation
    fn record_metrics<T>(
        &self,
        query_name: &str,
        start_time: Instant,
        result: &Result<T, sqlx::Error>,
    ) {
        let execution_time = start_time.elapsed();
        let execution_time_ms = execution_time.as_millis() as u64;

        // Update metrics using interior mutability
        if let Ok(mut metrics) = self.metrics.lock() {
            metrics.query_count += 1;
            metrics.total_execution_time_ms += execution_time_ms;

            if execution_time_ms > self.performance_threshold_ms {
                metrics.slow_query_count += 1;
            }

            if result.is_err() {
                metrics.error_count += 1;
            }
        }

        // Log performance warnings
        if execution_time_ms > self.performance_threshold_ms {
            tracing::warn!(
                "Slow audit query detected: {} took {}ms",
                query_name,
                execution_time_ms
            );
        }

        if let Err(e) = result {
            error!("Audit database query failed [{}]: {}", query_name, e);
        }
    }
}

// Persistence record - separate from domain model
#[derive(Debug, sqlx::FromRow)]
struct AuditLogRecord {
    id: String,
    user_id: Option<String>,
    session_id: Option<String>,
    action: String,
    resource: Option<String>,
    resource_id: Option<String>,
    details: Option<String>, // JSON blob
    ip_address: Option<String>,
    user_agent: Option<String>,
    success: bool,
    error_message: Option<String>,
    created_at: i64,
}

impl AuditLogRecord {
    /// Convert from domain AuditLogEntry to persistence record
    fn from_domain(entry: &AuditLogEntry) -> Self {
        AuditLogRecord {
            id: entry.id.clone(),
            user_id: entry.user_id.as_ref().map(|uid| uid.as_str().to_string()),
            session_id: None, // Would need to be added to AuditLogEntry
            action: entry.action.as_str().to_string(),
            resource: Some(entry.entity_type.as_str().to_string()),
            resource_id: Some(entry.entity_id.clone()),
            details: entry.changes.as_ref().map(|c| c.to_string()),
            ip_address: Some(entry.ip_address.clone()),
            user_agent: Some(entry.user_agent.clone()),
            success: entry.success,
            error_message: None, // Would need to be added to AuditLogEntry
            created_at: entry.timestamp.timestamp() as i64,
        }
    }

    /// Convert from persistence record to domain AuditLogEntry
    #[allow(clippy::wrong_self_convention)]
    fn to_domain(self) -> Result<AuditLogEntry, DomainError> {
        let user_id = if let Some(uid) = self.user_id {
            Some(UserId::from_string(uid)?)
        } else {
            None
        };

        let action = AuditAction::parse(&self.action).ok_or_else(|| {
            DomainError::Infrastructure(format!("Invalid action: {}", self.action))
        })?;

        let entity_type = EntityType::parse(&self.resource.unwrap_or_default())
            .ok_or_else(|| DomainError::Infrastructure("Invalid entity type".to_string()))?;

        let changes = if let Some(details_str) = self.details {
            serde_json::from_str(&details_str).ok()
        } else {
            None
        };

        let timestamp = DateTime::from_timestamp(self.created_at as u64)?;

        Ok(AuditLogEntry {
            id: self.id,
            timestamp,
            user_id,
            action,
            entity_type,
            entity_id: self.resource_id.unwrap_or_default(),
            changes,
            ip_address: self.ip_address.unwrap_or_default(),
            user_agent: self.user_agent.unwrap_or_default(),
            success: self.success,
            error_message: self.error_message,
            metadata: std::collections::HashMap::new(),
        })
    }
}

#[async_trait]
impl AuditLogRepository for SqlxAuditLogRepository {
    #[instrument(level = "debug", skip(self, entry))]
    async fn save(&self, entry: &AuditLogEntry) -> Result<(), DomainError> {
        let record = AuditLogRecord::from_domain(entry);

        let sql = r#"
            INSERT INTO audit_log 
            (id, user_id, session_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12)
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql)
                .bind(&record.id)
                .bind(&record.user_id)
                .bind(&record.session_id)
                .bind(&record.action)
                .bind(&record.resource)
                .bind(&record.resource_id)
                .bind(&record.details)
                .bind(&record.ip_address)
                .bind(&record.user_agent)
                .bind(record.success)
                .bind(&record.error_message)
                .bind(record.created_at)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_audit_log", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Save audit log failed: {e}")))
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                INSERT INTO audit_log 
                (id, user_id, session_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#;

            let start_time = Instant::now();
            let result = query(pg_sql)
                .bind(&record.id)
                .bind(&record.user_id)
                .bind(&record.session_id)
                .bind(&record.action)
                .bind(&record.resource)
                .bind(&record.resource_id)
                .bind(&record.details)
                .bind(&record.ip_address)
                .bind(&record.user_agent)
                .bind(record.success)
                .bind(&record.error_message)
                .bind(record.created_at)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_audit_log_pg", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Save audit log failed: {e}")))
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    async fn search(
        &self,
        criteria: &AuditLogSearchCriteria,
    ) -> Result<AuditLogSearchResult, DomainError> {
        // Build dynamic query based on search criteria
        let mut conditions = Vec::new();
        let mut params = Vec::new();

        if let Some(user_id) = &criteria.user_id {
            conditions.push("user_id = ?".to_string());
            params.push(user_id.as_str());
        }

        if !criteria.actions.is_empty() {
            let action_placeholders = criteria
                .actions
                .iter()
                .map(|_| "?")
                .collect::<Vec<_>>()
                .join(", ");
            conditions.push(format!("action IN ({action_placeholders})"));
            for action in &criteria.actions {
                params.push(action.as_str());
            }
        }

        if let Some(entity_type) = &criteria.entity_type {
            conditions.push("resource = ?".to_string());
            params.push(entity_type.as_str());
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        let count_sql = format!("SELECT COUNT(*) FROM audit_log {where_clause}");

        let search_sql = format!(
            "SELECT id, user_id, session_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at 
             FROM audit_log {} 
             ORDER BY created_at DESC 
             LIMIT {} OFFSET {}",
            where_clause,
            criteria.page_size,
            criteria.page * criteria.page_size
        );

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            // Execute count query
            let start_time = Instant::now();
            let count_result = query_as::<_, (i64,)>(&count_sql)
                .fetch_one(pool)
                .await
                .map(|row| row.0);

            self.record_metrics("search_audit_count", start_time, &count_result);
            let total_count: i64 = count_result.map_err(|e| {
                DomainError::Infrastructure(format!("Search audit count failed: {e}"))
            })?;

            // Execute search query
            let start_time = Instant::now();
            let search_result = query_as::<_, AuditLogRecord>(&search_sql)
                .fetch_all(pool)
                .await;

            self.record_metrics("search_audit_logs", start_time, &search_result);
            let records: Vec<AuditLogRecord> = search_result.map_err(|e| {
                DomainError::Infrastructure(format!("Search audit logs failed: {e}"))
            })?;

            // Convert to domain objects
            let mut entries = Vec::new();
            for record in records {
                entries.push(record.to_domain()?);
            }

            Ok(AuditLogSearchResult::new(
                entries,
                total_count as usize,
                criteria.page,
                criteria.page_size,
            ))
        } else {
            // Return empty result for now - PostgreSQL implementation would be similar
            Ok(AuditLogSearchResult::new(
                Vec::new(),
                0,
                criteria.page,
                criteria.page_size,
            ))
        }
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<AuditLogEntry>, DomainError> {
        let sql = r#"
            SELECT id, user_id, session_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at
            FROM audit_log 
            WHERE id = ?
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, AuditLogRecord>(sql)
                .bind(id)
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_audit_by_id", start_time, &result);
            let record: Option<AuditLogRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find audit by ID failed: {e}"))
            })?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    async fn find_recent_by_user(
        &self,
        user_id: &UserId,
        limit: usize,
    ) -> Result<Vec<AuditLogEntry>, DomainError> {
        let sql = r#"
            SELECT id, user_id, session_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at
            FROM audit_log 
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ?
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, AuditLogRecord>(sql)
                .bind(user_id.as_str())
                .bind(limit as i64)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_recent_by_user", start_time, &result);
            let records: Vec<AuditLogRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find recent by user failed: {e}"))
            })?;

            let mut entries = Vec::new();
            for record in records {
                entries.push(record.to_domain()?);
            }
            Ok(entries)
        } else {
            Ok(vec![])
        }
    }

    async fn find_recent_security_events(
        &self,
        limit: usize,
    ) -> Result<Vec<AuditLogEntry>, DomainError> {
        let sql = r#"
            SELECT id, user_id, session_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at
            FROM audit_log 
            WHERE action IN ('SecurityViolation', 'SuspiciousActivity', 'UnauthorizedAccess', 'AccountLockout', 'RateLimitExceeded')
            ORDER BY created_at DESC
            LIMIT ?
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, AuditLogRecord>(sql)
                .bind(limit as i64)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_security_events", start_time, &result);
            let records: Vec<AuditLogRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find security events failed: {e}"))
            })?;

            let mut entries = Vec::new();
            for record in records {
                entries.push(record.to_domain()?);
            }
            Ok(entries)
        } else {
            Ok(vec![])
        }
    }

    async fn find_by_entity(
        &self,
        entity_type: EntityType,
        entity_id: &str,
        start_time: SystemTime,
        end_time: SystemTime,
    ) -> Result<Vec<AuditLogEntry>, DomainError> {
        let start_timestamp = start_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;
        let end_timestamp = end_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let sql = r#"
            SELECT id, user_id, session_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at
            FROM audit_log 
            WHERE resource = ? AND resource_id = ? AND created_at >= ? AND created_at <= ?
            ORDER BY created_at DESC
            LIMIT 1000
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, AuditLogRecord>(sql)
                .bind(entity_type.as_str())
                .bind(entity_id)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_by_entity", start_time, &result);
            let records: Vec<AuditLogRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find by entity failed: {e}")))?;

            let mut entries = Vec::new();
            for record in records {
                entries.push(record.to_domain()?);
            }
            Ok(entries)
        } else {
            Ok(vec![])
        }
    }

    async fn count_by_time_range(
        &self,
        start_time: SystemTime,
        end_time: SystemTime,
    ) -> Result<usize, DomainError> {
        let start_timestamp = start_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;
        let end_timestamp = end_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let sql = "SELECT COUNT(*) FROM audit_log WHERE created_at >= ? AND created_at <= ?";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, (i64,)>(sql)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_one(pool)
                .await
                .map(|row| row.0);

            self.record_metrics("count_by_time_range", start_time, &result);
            let count: i64 = result.map_err(|e| {
                DomainError::Infrastructure(format!("Count by time range failed: {e}"))
            })?;
            Ok(count as usize)
        } else {
            Ok(0)
        }
    }

    async fn get_failure_stats(
        &self,
        start_time: SystemTime,
        end_time: SystemTime,
    ) -> Result<AuditFailureStats, DomainError> {
        let start_timestamp = start_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;
        let end_timestamp = end_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            // Get total failures
            let total_sql = "SELECT COUNT(*) FROM audit_log WHERE success = FALSE AND created_at >= ? AND created_at <= ?";
            let start_time = Instant::now();
            let total_result = query_as::<_, (i64,)>(total_sql)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_one(pool)
                .await
                .map(|row| row.0);
            self.record_metrics("failure_stats_total", start_time, &total_result);
            let total_failures = total_result.unwrap_or(0) as usize;

            // Get security failures
            let security_sql = r#"
                SELECT COUNT(*) FROM audit_log 
                WHERE success = FALSE AND created_at >= ? AND created_at <= ? 
                AND action IN ('SecurityViolation', 'SuspiciousActivity', 'UnauthorizedAccess')
            "#;
            let start_time = Instant::now();
            let security_result = query_as::<_, (i64,)>(security_sql)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_one(pool)
                .await
                .map(|row| row.0);
            self.record_metrics("failure_stats_security", start_time, &security_result);
            let security_failures = security_result.unwrap_or(0) as usize;

            // Get authentication failures
            let auth_sql = r#"
                SELECT COUNT(*) FROM audit_log 
                WHERE success = FALSE AND created_at >= ? AND created_at <= ? 
                AND action IN ('Login', 'LoginAttempt')
            "#;
            let start_time = Instant::now();
            let auth_result = query_as::<_, (i64,)>(auth_sql)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_one(pool)
                .await
                .map(|row| row.0);
            self.record_metrics("failure_stats_auth", start_time, &auth_result);
            let authentication_failures = auth_result.unwrap_or(0) as usize;

            // Get most common failure action
            let action_sql = r#"
                SELECT action, COUNT(*) as count FROM audit_log 
                WHERE success = FALSE AND created_at >= ? AND created_at <= ? 
                GROUP BY action 
                ORDER BY count DESC 
                LIMIT 1
            "#;
            let start_time = Instant::now();
            let action_result = query_as::<_, (String, i64)>(action_sql)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_optional(pool)
                .await;
            self.record_metrics("failure_stats_action", start_time, &action_result);
            let most_common_failure_action = action_result
                .unwrap_or_default()
                .and_then(|(action, _)| AuditAction::parse(&action));

            Ok(AuditFailureStats {
                total_failures,
                security_failures,
                authentication_failures,
                authorization_failures: 0, // Would need more specific tracking
                most_common_failure_action,
                most_common_failure_entity: None, // Would need additional query
            })
        } else {
            Ok(AuditFailureStats {
                total_failures: 0,
                security_failures: 0,
                authentication_failures: 0,
                authorization_failures: 0,
                most_common_failure_action: None,
                most_common_failure_entity: None,
            })
        }
    }

    async fn delete_older_than(&self, cutoff_time: SystemTime) -> Result<usize, DomainError> {
        let cutoff_timestamp = cutoff_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let sql = "DELETE FROM audit_log WHERE created_at < ?";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql).bind(cutoff_timestamp).execute(pool).await;

            self.record_metrics("delete_old_audit_logs", start_time, &result);
            let query_result = result.map_err(|e| {
                DomainError::Infrastructure(format!("Delete old audit logs failed: {e}"))
            })?;
            Ok(query_result.rows_affected() as usize)
        } else {
            Ok(0)
        }
    }
}
