// SQLx implementation of RoleRepository
// Provides database persistence for Role entities with performance optimizations

use crate::database::OptimizedConnectionPool;
use async_trait::async_trait;
use auth_domain::{
    entities::{Role, role::Permission},
    errors::DomainError,
    repositories::RoleRepository,
    value_objects::{RoleId, UserId},
};
use sqlx::{query, query_as};
use std::sync::{Arc, Mutex};
use std::time::Instant;
use tracing::{error, instrument};

/// Repository metrics for performance monitoring
#[derive(Debug, Default)]
struct RepositoryMetrics {
    query_count: u64,
    total_execution_time_ms: u64,
    slow_query_count: u64,
    error_count: u64,
}

#[derive(Clone)]
pub struct SqlxRoleRepository {
    connection_pool: OptimizedConnectionPool,
    performance_threshold_ms: u64,
    metrics: Arc<Mutex<RepositoryMetrics>>,
}

impl SqlxRoleRepository {
    pub fn new(connection_pool: OptimizedConnectionPool) -> Self {
        SqlxRoleRepository {
            connection_pool,
            performance_threshold_ms: 50, // Warn on queries > 50ms
            metrics: Arc::new(Mutex::new(RepositoryMetrics::default())),
        }
    }

    /// Record metrics for a database operation
    fn record_metrics<T>(
        &self,
        query_name: &str,
        start_time: Instant,
        result: &Result<T, sqlx::Error>,
    ) {
        let execution_time = start_time.elapsed();
        let execution_time_ms = execution_time.as_millis() as u64;

        // Update metrics using interior mutability
        if let Ok(mut metrics) = self.metrics.lock() {
            metrics.query_count += 1;
            metrics.total_execution_time_ms += execution_time_ms;

            if execution_time_ms > self.performance_threshold_ms {
                metrics.slow_query_count += 1;
            }

            if result.is_err() {
                metrics.error_count += 1;
            }
        }

        // Log performance warnings
        if execution_time_ms > self.performance_threshold_ms {
            tracing::warn!(
                "Slow query detected: {} took {}ms",
                query_name,
                execution_time_ms
            );
        }

        // Log SLA violations for auth operations
        if execution_time_ms > 90 {
            tracing::error!(
                "Query approaching SLA timeout: {} took {}ms",
                query_name,
                execution_time_ms
            );
        }

        if let Err(e) = result {
            error!("Database query failed [{}]: {}", query_name, e);
        }
    }
}

// Persistence record - separate from domain model
#[derive(Debug, sqlx::FromRow)]
struct RoleRecord {
    id: String,
    name: String,
    description: Option<String>,
    is_active: bool,
    parent_role_id: Option<String>,
    created_at: i64,
    updated_at: i64,
}

#[derive(Debug, sqlx::FromRow)]
struct PermissionRecord {
    name: String,
}

impl RoleRecord {
    /// Convert from domain Role to persistence record
    fn from_domain(role: &Role) -> Self {
        RoleRecord {
            id: role.id().as_str().to_string(),
            name: role.name().to_string(),
            description: role.description().map(|s| s.to_string()),
            is_active: role.is_active(),
            parent_role_id: role.parent_role().map(|p| p.as_str().to_string()),
            created_at: role
                .created_at()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs() as i64,
            updated_at: role
                .updated_at()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs() as i64,
        }
    }

    /// Convert from persistence record to domain Role
    #[allow(clippy::wrong_self_convention)]
    async fn to_domain(self, permissions: Vec<Permission>) -> Result<Role, DomainError> {
        let role_id = RoleId::from_string(self.id)?;
        let mut role = Role::new(role_id, self.name, self.description, permissions)?;

        // Set parent role if exists
        if let Some(parent_id) = self.parent_role_id {
            let parent_role_id = RoleId::from_string(parent_id)?;
            role.set_parent_role(parent_role_id)?;
        }

        // Set active status if different
        if !self.is_active {
            role.deactivate();
        }

        Ok(role)
    }
}

#[async_trait]
impl RoleRepository for SqlxRoleRepository {
    #[instrument(level = "debug", skip(self, role))]
    async fn save(&self, role: &Role) -> Result<(), DomainError> {
        let record = RoleRecord::from_domain(role);

        let sql = r#"
            INSERT OR REPLACE INTO roles 
            (id, name, description, is_active, parent_role_id, created_at, updated_at)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql)
                .bind(&record.id)
                .bind(&record.name)
                .bind(&record.description)
                .bind(record.is_active)
                .bind(&record.parent_role_id)
                .bind(record.created_at)
                .bind(record.updated_at)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_role", start_time, &result);

            // Save permissions
            self.save_role_permissions(role).await?;

            result.map_err(|e| DomainError::Infrastructure(format!("Save role failed: {e}")))
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                INSERT INTO roles 
                (id, name, description, is_active, parent_role_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    description = EXCLUDED.description,
                    is_active = EXCLUDED.is_active,
                    parent_role_id = EXCLUDED.parent_role_id,
                    updated_at = EXCLUDED.updated_at
            "#;

            let start_time = Instant::now();
            let result = query(pg_sql)
                .bind(&record.id)
                .bind(&record.name)
                .bind(&record.description)
                .bind(record.is_active)
                .bind(&record.parent_role_id)
                .bind(record.created_at)
                .bind(record.updated_at)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_role_pg", start_time, &result);

            // Save permissions
            self.save_role_permissions(role).await?;

            result.map_err(|e| DomainError::Infrastructure(format!("Save role failed: {e}")))
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_id(&self, id: &RoleId) -> Result<Option<Role>, DomainError> {
        let sql = r#"
            SELECT id, name, description, is_active, parent_role_id, created_at, updated_at 
            FROM roles 
            WHERE id = ? AND is_active = TRUE
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(sql)
                .bind(id.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_role_by_id", start_time, &result);
            let record: Option<RoleRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find role by ID failed: {e}")))?;

            if let Some(record) = record {
                let permissions = self.load_role_permissions(id).await?;
                Ok(Some(record.to_domain(permissions).await?))
            } else {
                Ok(None)
            }
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                SELECT id, name, description, is_active, parent_role_id, created_at, updated_at 
                FROM roles 
                WHERE id = $1 AND is_active = TRUE
            "#;

            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(pg_sql)
                .bind(id.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_role_by_id_pg", start_time, &result);
            let record: Option<RoleRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find role by ID failed: {e}")))?;

            if let Some(record) = record {
                let permissions = self.load_role_permissions(id).await?;
                Ok(Some(record.to_domain(permissions).await?))
            } else {
                Ok(None)
            }
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_user(&self, user_id: &UserId) -> Result<Vec<Role>, DomainError> {
        let sql = r#"
            SELECT r.id, r.name, r.description, r.is_active, r.parent_role_id, r.created_at, r.updated_at
            FROM roles r
            INNER JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ? AND r.is_active = TRUE
            AND (ur.expires_at IS NULL OR ur.expires_at > strftime('%s', 'now'))
            ORDER BY r.name
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(sql)
                .bind(user_id.as_str())
                .fetch_all(pool)
                .await;

            self.record_metrics("find_roles_by_user", start_time, &result);
            let records: Vec<RoleRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find roles by user failed: {e}"))
            })?;

            let mut roles = Vec::new();
            for record in records {
                let role_id = RoleId::from_string(record.id.clone())?;
                let permissions = self.load_role_permissions(&role_id).await?;
                roles.push(record.to_domain(permissions).await?);
            }
            Ok(roles)
        } else {
            // Similar PostgreSQL implementation would go here
            Ok(vec![])
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_name(&self, name: &str) -> Result<Option<Role>, DomainError> {
        let sql = r#"
            SELECT id, name, description, is_active, parent_role_id, created_at, updated_at 
            FROM roles 
            WHERE name = ? AND is_active = TRUE
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(sql)
                .bind(name)
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_role_by_name", start_time, &result);
            let record: Option<RoleRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find role by name failed: {e}"))
            })?;

            if let Some(record) = record {
                let role_id = RoleId::from_string(record.id.clone())?;
                let permissions = self.load_role_permissions(&role_id).await?;
                Ok(Some(record.to_domain(permissions).await?))
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    async fn delete(&self, id: &RoleId) -> Result<(), DomainError> {
        // Soft delete to maintain audit trail
        let sql = r#"
            UPDATE roles 
            SET is_active = FALSE, updated_at = strftime('%s', 'now')
            WHERE id = ?
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql).bind(id.as_str()).execute(pool).await.map(|_| ());

            self.record_metrics("delete_role", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Delete role failed: {e}")))
        } else {
            Ok(())
        }
    }

    async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<Role>, DomainError> {
        let sql = format!(
            "SELECT id, name, description, is_active, parent_role_id, created_at, updated_at 
             FROM roles 
             WHERE is_active = TRUE 
             ORDER BY name 
             LIMIT {limit} OFFSET {offset}"
        );

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(&sql).fetch_all(pool).await;

            self.record_metrics("find_all_roles", start_time, &result);
            let records: Vec<RoleRecord> = result
                .map_err(|e| DomainError::Infrastructure(format!("Find all roles failed: {e}")))?;

            let mut roles = Vec::new();
            for record in records {
                let role_id = RoleId::from_string(record.id.clone())?;
                let permissions = self.load_role_permissions(&role_id).await?;
                roles.push(record.to_domain(permissions).await?);
            }
            Ok(roles)
        } else {
            Ok(vec![])
        }
    }

    async fn count(&self) -> Result<usize, DomainError> {
        let sql = "SELECT COUNT(*) FROM roles WHERE is_active = TRUE";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, (i64,)>(sql)
                .fetch_one(pool)
                .await
                .map(|row| row.0);

            self.record_metrics("count_roles", start_time, &result);
            let count: i64 = result
                .map_err(|e| DomainError::Infrastructure(format!("Count roles failed: {e}")))?;
            Ok(count as usize)
        } else {
            Ok(0)
        }
    }

    async fn find_active(&self) -> Result<Vec<Role>, DomainError> {
        self.find_all(0, 1000).await // Get first 1000 active roles
    }

    async fn exists_by_name(&self, name: &str) -> Result<bool, DomainError> {
        let sql = "SELECT 1 FROM roles WHERE name = ? AND is_active = TRUE LIMIT 1";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, (i32,)>(sql)
                .bind(name)
                .fetch_optional(pool)
                .await;

            self.record_metrics("exists_role_by_name", start_time, &result);
            let exists: Option<(i32,)> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Check role exists failed: {e}"))
            })?;
            Ok(exists.is_some())
        } else {
            Ok(false)
        }
    }

    async fn find_by_permission(&self, permission: &str) -> Result<Vec<Role>, DomainError> {
        let sql = r#"
            SELECT DISTINCT r.id, r.name, r.description, r.is_active, r.parent_role_id, r.created_at, r.updated_at
            FROM roles r
            INNER JOIN role_permissions rp ON r.id = rp.role_id
            INNER JOIN permissions p ON rp.permission_id = p.id
            WHERE p.name = ? AND r.is_active = TRUE
            ORDER BY r.name
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(sql)
                .bind(permission)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_roles_by_permission", start_time, &result);
            let records: Vec<RoleRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find roles by permission failed: {e}"))
            })?;

            let mut roles = Vec::new();
            for record in records {
                let role_id = RoleId::from_string(record.id.clone())?;
                let permissions = self.load_role_permissions(&role_id).await?;
                roles.push(record.to_domain(permissions).await?);
            }
            Ok(roles)
        } else {
            Ok(vec![])
        }
    }

    async fn find_children(&self, parent_id: &RoleId) -> Result<Vec<Role>, DomainError> {
        let sql = r#"
            SELECT id, name, description, is_active, parent_role_id, created_at, updated_at 
            FROM roles 
            WHERE parent_role_id = ? AND is_active = TRUE
            ORDER BY name
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(sql)
                .bind(parent_id.as_str())
                .fetch_all(pool)
                .await;

            self.record_metrics("find_child_roles", start_time, &result);
            let records: Vec<RoleRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find child roles failed: {e}"))
            })?;

            let mut roles = Vec::new();
            for record in records {
                let role_id = RoleId::from_string(record.id.clone())?;
                let permissions = self.load_role_permissions(&role_id).await?;
                roles.push(record.to_domain(permissions).await?);
            }
            Ok(roles)
        } else {
            Ok(vec![])
        }
    }

    async fn get_hierarchy(&self) -> Result<Vec<Role>, DomainError> {
        // Get all active roles ordered by hierarchy (parent roles first)
        let sql = r#"
            WITH RECURSIVE role_hierarchy AS (
                -- Base case: root roles (no parent)
                SELECT id, name, description, is_active, parent_role_id, created_at, updated_at, 0 as level
                FROM roles 
                WHERE parent_role_id IS NULL AND is_active = TRUE
                
                UNION ALL
                
                -- Recursive case: child roles
                SELECT r.id, r.name, r.description, r.is_active, r.parent_role_id, r.created_at, r.updated_at, rh.level + 1
                FROM roles r
                INNER JOIN role_hierarchy rh ON r.parent_role_id = rh.id
                WHERE r.is_active = TRUE
            )
            SELECT id, name, description, is_active, parent_role_id, created_at, updated_at
            FROM role_hierarchy
            ORDER BY level, name
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, RoleRecord>(sql).fetch_all(pool).await;

            self.record_metrics("get_role_hierarchy", start_time, &result);
            let records: Vec<RoleRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Get role hierarchy failed: {e}"))
            })?;

            let mut roles = Vec::new();
            for record in records {
                let role_id = RoleId::from_string(record.id.clone())?;
                let permissions = self.load_role_permissions(&role_id).await?;
                roles.push(record.to_domain(permissions).await?);
            }
            Ok(roles)
        } else {
            Ok(vec![])
        }
    }
}

impl SqlxRoleRepository {
    /// Load permissions for a role
    async fn load_role_permissions(
        &self,
        role_id: &RoleId,
    ) -> Result<Vec<Permission>, DomainError> {
        let sql = r#"
            SELECT p.name
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = ?
            ORDER BY p.name
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, PermissionRecord>(sql)
                .bind(role_id.as_str())
                .fetch_all(pool)
                .await;

            self.record_metrics("load_role_permissions", start_time, &result);
            let records: Vec<PermissionRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Load role permissions failed: {e}"))
            })?;

            let mut permissions = Vec::new();
            for record in records {
                permissions.push(Permission::from_string(record.name));
            }
            Ok(permissions)
        } else {
            Ok(vec![])
        }
    }

    /// Save role permissions
    async fn save_role_permissions(&self, role: &Role) -> Result<(), DomainError> {
        if let Some(pool) = self.connection_pool.sqlite_pool() {
            // First, delete existing permissions for this role
            let delete_sql = "DELETE FROM role_permissions WHERE role_id = ?";
            let start_time = Instant::now();
            let result = query(delete_sql)
                .bind(role.id().as_str())
                .execute(pool)
                .await;
            self.record_metrics("delete_role_permissions", start_time, &result);
            result.map_err(|e| {
                DomainError::Infrastructure(format!("Delete role permissions failed: {e}"))
            })?;

            // Insert new permissions
            let insert_sql = r#"
                INSERT INTO role_permissions (role_id, permission_id, granted_at)
                VALUES (?, ?, strftime('%s', 'now'))
            "#;

            for permission in role.permissions() {
                let start_time = Instant::now();
                let result = query(insert_sql)
                    .bind(role.id().as_str())
                    .bind(permission.as_str())
                    .execute(pool)
                    .await;
                self.record_metrics("insert_role_permission", start_time, &result);
                result.map_err(|e| {
                    DomainError::Infrastructure(format!("Insert role permission failed: {e}"))
                })?;
            }
        }
        Ok(())
    }
}
