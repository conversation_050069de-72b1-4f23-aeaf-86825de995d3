// SQLx implementation of SessionRepository
// Provides database persistence for Session entities with performance optimizations

use crate::database::OptimizedConnectionPool;
use async_trait::async_trait;
use auth_domain::{
    entities::Session,
    errors::DomainError,
    repositories::SessionRepository,
    value_objects::{SessionId, UserId},
};
use sqlx::{query, query_as};
use std::sync::{Arc, Mutex};
use std::time::{Instant, SystemTime, UNIX_EPOCH};
use tracing::{error, instrument};

/// Repository metrics for performance monitoring
#[derive(Debug, Default)]
struct RepositoryMetrics {
    query_count: u64,
    total_execution_time_ms: u64,
    slow_query_count: u64,
    error_count: u64,
}

#[derive(Clone)]
pub struct SqlxSessionRepository {
    connection_pool: OptimizedConnectionPool,
    performance_threshold_ms: u64,
    metrics: Arc<Mutex<RepositoryMetrics>>,
}

impl SqlxSessionRepository {
    pub fn new(connection_pool: OptimizedConnectionPool) -> Self {
        SqlxSessionRepository {
            connection_pool,
            performance_threshold_ms: 25, // Sessions are critical for auth - stricter threshold
            metrics: Arc::new(Mutex::new(RepositoryMetrics::default())),
        }
    }

    /// Record metrics for a database operation
    fn record_metrics<T>(
        &self,
        query_name: &str,
        start_time: Instant,
        result: &Result<T, sqlx::Error>,
    ) {
        let execution_time = start_time.elapsed();
        let execution_time_ms = execution_time.as_millis() as u64;

        // Update metrics using interior mutability
        if let Ok(mut metrics) = self.metrics.lock() {
            metrics.query_count += 1;
            metrics.total_execution_time_ms += execution_time_ms;

            if execution_time_ms > self.performance_threshold_ms {
                metrics.slow_query_count += 1;
            }

            if result.is_err() {
                metrics.error_count += 1;
            }
        }

        // Log performance warnings
        if execution_time_ms > self.performance_threshold_ms {
            tracing::warn!(
                "Slow session query detected: {} took {}ms (threshold: {}ms)",
                query_name,
                execution_time_ms,
                self.performance_threshold_ms
            );
        }

        // Log critical SLA violations for auth operations
        if execution_time_ms > 90 {
            tracing::error!(
                "Session query approaching SLA timeout: {} took {}ms",
                query_name,
                execution_time_ms
            );
        }

        if let Err(e) = result {
            error!("Session database query failed [{}]: {}", query_name, e);
        }
    }
}

// Persistence record - separate from domain model
#[derive(Debug, sqlx::FromRow)]
#[allow(dead_code)]
struct SessionRecord {
    id: String,
    user_id: String,
    created_at: i64,    // Unix timestamp
    expires_at: i64,    // Unix timestamp
    last_accessed: i64, // Unix timestamp
    ip_address: Option<String>,
    user_agent: Option<String>,
    is_active: bool,
}

#[allow(dead_code)]
impl SessionRecord {
    /// Convert from domain Session to persistence record
    fn from_domain(session: &Session) -> Self {
        SessionRecord {
            id: session.id().as_str().to_string(),
            user_id: session.user_id().as_str().to_string(),
            created_at: session.created_at().timestamp() as i64,
            expires_at: session.expires_at().timestamp() as i64,
            last_accessed: session.last_accessed().timestamp() as i64,
            ip_address: session.ip_address().map(|s| s.to_string()),
            user_agent: session.user_agent().map(|s| s.to_string()),
            is_active: session.is_active(),
        }
    }

    /// Convert from persistence record to domain Session
    #[allow(clippy::wrong_self_convention)]
    fn to_domain(self) -> Result<Session, DomainError> {
        use auth_domain::value_objects::DateTime;

        let user_id = UserId::from_string(self.user_id)?;
        let _session_id = SessionId::from_string(self.id)?;

        // Convert timestamps back to DateTime
        let _created_at = DateTime::from_timestamp(self.created_at as u64)?;
        let _expires_at = DateTime::from_timestamp(self.expires_at as u64)?;
        let _last_accessed = DateTime::from_timestamp(self.last_accessed as u64)?;

        // Calculate duration for session creation
        let duration_seconds = (self.expires_at - self.created_at) as u64;

        // Create a new session and then update its state
        let session = Session::new(user_id, duration_seconds, self.ip_address, self.user_agent);

        // We would need additional methods on Session to fully restore state
        // For now, this creates a functionally equivalent session
        // In a full implementation, Session would need methods like:
        // - set_id(SessionId)
        // - set_created_at(DateTime)
        // - set_last_accessed(DateTime)
        // - set_active_state(bool)

        if !self.is_active {
            // Session would need an invalidate method that preserves other data
            // For now, the new session will be active
        }

        Ok(session)
    }
}

#[async_trait]
impl SessionRepository for SqlxSessionRepository {
    #[instrument(level = "debug", skip(self, session))]
    async fn save(&self, session: &Session) -> Result<(), DomainError> {
        let record = SessionRecord::from_domain(session);

        let sql = r#"
            INSERT OR REPLACE INTO sessions 
            (id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql)
                .bind(&record.id)
                .bind(&record.user_id)
                .bind(record.created_at)
                .bind(record.expires_at)
                .bind(record.last_accessed)
                .bind(&record.ip_address)
                .bind(&record.user_agent)
                .bind(record.is_active)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_session", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Save session failed: {e}")))
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                INSERT INTO sessions 
                (id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT (id) DO UPDATE SET
                    last_accessed = EXCLUDED.last_accessed,
                    ip_address = EXCLUDED.ip_address,
                    user_agent = EXCLUDED.user_agent,
                    is_active = EXCLUDED.is_active,
                    expires_at = EXCLUDED.expires_at
            "#;

            let start_time = Instant::now();
            let result = query(pg_sql)
                .bind(&record.id)
                .bind(&record.user_id)
                .bind(record.created_at)
                .bind(record.expires_at)
                .bind(record.last_accessed)
                .bind(&record.ip_address)
                .bind(&record.user_agent)
                .bind(record.is_active)
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("save_session_pg", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Save session failed: {e}")))
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_by_id(&self, id: &SessionId) -> Result<Option<Session>, DomainError> {
        let sql = r#"
            SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
            FROM sessions 
            WHERE id = ? AND is_active = TRUE AND expires_at > strftime('%s', 'now')
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, SessionRecord>(sql)
                .bind(id.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_session_by_id", start_time, &result);
            let record: Option<SessionRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find session by ID failed: {e}"))
            })?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else if let Some(pool) = self.connection_pool.postgres_pool() {
            let pg_sql = r#"
                SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
                FROM sessions 
                WHERE id = $1 AND is_active = TRUE AND expires_at > EXTRACT(epoch FROM NOW())
            "#;

            let start_time = Instant::now();
            let result = query_as::<_, SessionRecord>(pg_sql)
                .bind(id.as_str())
                .fetch_optional(pool)
                .await;

            self.record_metrics("find_session_by_id_pg", start_time, &result);
            let record: Option<SessionRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find session by ID failed: {e}"))
            })?;

            if let Some(record) = record {
                Ok(Some(record.to_domain()?))
            } else {
                Ok(None)
            }
        } else {
            Err(DomainError::Configuration(
                "No database pool available".to_string(),
            ))
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn find_active_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
        let sql = r#"
            SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
            FROM sessions 
            WHERE user_id = ? AND is_active = TRUE AND expires_at > strftime('%s', 'now')
            ORDER BY last_accessed DESC
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, SessionRecord>(sql)
                .bind(user_id.as_str())
                .fetch_all(pool)
                .await;

            self.record_metrics("find_active_sessions_by_user", start_time, &result);
            let records: Vec<SessionRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find active sessions failed: {e}"))
            })?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    async fn find_all_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
        let sql = r#"
            SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
            FROM sessions 
            WHERE user_id = ?
            ORDER BY created_at DESC
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, SessionRecord>(sql)
                .bind(user_id.as_str())
                .fetch_all(pool)
                .await;

            self.record_metrics("find_all_sessions_by_user", start_time, &result);
            let records: Vec<SessionRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find all sessions failed: {e}"))
            })?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    async fn delete(&self, id: &SessionId) -> Result<(), DomainError> {
        let sql = "DELETE FROM sessions WHERE id = ?";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql).bind(id.as_str()).execute(pool).await.map(|_| ());

            self.record_metrics("delete_session", start_time, &result);
            result.map_err(|e| DomainError::Infrastructure(format!("Delete session failed: {e}")))
        } else {
            Ok(())
        }
    }

    async fn delete_all_by_user(&self, user_id: &UserId) -> Result<(), DomainError> {
        let sql = "DELETE FROM sessions WHERE user_id = ?";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql)
                .bind(user_id.as_str())
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("delete_all_user_sessions", start_time, &result);
            result.map_err(|e| {
                DomainError::Infrastructure(format!("Delete user sessions failed: {e}"))
            })
        } else {
            Ok(())
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError> {
        let sql = "DELETE FROM sessions WHERE expires_at < strftime('%s', 'now')";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql).execute(pool).await;

            self.record_metrics("cleanup_expired_sessions", start_time, &result);
            let query_result = result.map_err(|e| {
                DomainError::Infrastructure(format!("Cleanup sessions failed: {e}"))
            })?;
            Ok(query_result.rows_affected() as usize)
        } else {
            Ok(0)
        }
    }

    async fn count_active_by_user(&self, user_id: &UserId) -> Result<usize, DomainError> {
        let sql = r#"
            SELECT COUNT(*) FROM sessions 
            WHERE user_id = ? AND is_active = TRUE AND expires_at > strftime('%s', 'now')
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, (i64,)>(sql)
                .bind(user_id.as_str())
                .fetch_one(pool)
                .await
                .map(|row| row.0);

            self.record_metrics("count_active_sessions", start_time, &result);
            let count: i64 = result.map_err(|e| {
                DomainError::Infrastructure(format!("Count active sessions failed: {e}"))
            })?;
            Ok(count as usize)
        } else {
            Ok(0)
        }
    }

    async fn find_by_ip_address(&self, ip_address: &str) -> Result<Vec<Session>, DomainError> {
        let sql = r#"
            SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
            FROM sessions 
            WHERE ip_address = ? AND is_active = TRUE AND expires_at > strftime('%s', 'now')
            ORDER BY last_accessed DESC
            LIMIT 100
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, SessionRecord>(sql)
                .bind(ip_address)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_sessions_by_ip", start_time, &result);
            let records: Vec<SessionRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find sessions by IP failed: {e}"))
            })?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    async fn find_by_created_date_range(
        &self,
        start: SystemTime,
        end: SystemTime,
    ) -> Result<Vec<Session>, DomainError> {
        let start_timestamp = start
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;
        let end_timestamp = end.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs() as i64;

        let sql = r#"
            SELECT id, user_id, created_at, expires_at, last_accessed, ip_address, user_agent, is_active
            FROM sessions 
            WHERE created_at >= ? AND created_at <= ?
            ORDER BY created_at DESC
            LIMIT 1000
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query_as::<_, SessionRecord>(sql)
                .bind(start_timestamp)
                .bind(end_timestamp)
                .fetch_all(pool)
                .await;

            self.record_metrics("find_sessions_by_date_range", start_time, &result);
            let records: Vec<SessionRecord> = result.map_err(|e| {
                DomainError::Infrastructure(format!("Find sessions by date failed: {e}"))
            })?;

            let mut sessions = Vec::new();
            for record in records {
                sessions.push(record.to_domain()?);
            }
            Ok(sessions)
        } else {
            Ok(vec![])
        }
    }

    #[instrument(level = "debug", skip(self))]
    async fn update_last_accessed(&self, id: &SessionId) -> Result<(), DomainError> {
        let sql = r#"
            UPDATE sessions 
            SET last_accessed = strftime('%s', 'now')
            WHERE id = ? AND is_active = TRUE AND expires_at > strftime('%s', 'now')
        "#;

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql).bind(id.as_str()).execute(pool).await.map(|_| ());

            self.record_metrics("update_session_accessed", start_time, &result);
            result.map_err(|e| {
                DomainError::Infrastructure(format!("Update session access failed: {e}"))
            })
        } else {
            Ok(())
        }
    }

    async fn invalidate(&self, id: &SessionId) -> Result<(), DomainError> {
        let sql = "UPDATE sessions SET is_active = FALSE WHERE id = ?";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql).bind(id.as_str()).execute(pool).await.map(|_| ());

            self.record_metrics("invalidate_session", start_time, &result);
            result
                .map_err(|e| DomainError::Infrastructure(format!("Invalidate session failed: {e}")))
        } else {
            Ok(())
        }
    }

    async fn invalidate_all_except(
        &self,
        user_id: &UserId,
        except_session_id: &SessionId,
    ) -> Result<(), DomainError> {
        let sql = "UPDATE sessions SET is_active = FALSE WHERE user_id = ? AND id != ?";

        if let Some(pool) = self.connection_pool.sqlite_pool() {
            let start_time = Instant::now();
            let result = query(sql)
                .bind(user_id.as_str())
                .bind(except_session_id.as_str())
                .execute(pool)
                .await
                .map(|_| ());

            self.record_metrics("invalidate_other_sessions", start_time, &result);
            result.map_err(|e| {
                DomainError::Infrastructure(format!("Invalidate other sessions failed: {e}"))
            })
        } else {
            Ok(())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_session_record_conversion() {
        let user_id = UserId::new();
        let session = Session::new(
            user_id.clone(),
            3600, // 1 hour
            Some("127.0.0.1".to_string()),
            Some("Test Agent".to_string()),
        );

        let record = SessionRecord::from_domain(&session);
        assert_eq!(record.user_id, user_id.as_str());
        assert_eq!(record.ip_address, Some("127.0.0.1".to_string()));
        assert_eq!(record.user_agent, Some("Test Agent".to_string()));
        assert!(record.is_active);

        // Test conversion back to domain
        let converted_session = record.to_domain().unwrap();
        assert_eq!(converted_session.user_id(), &user_id);
        assert_eq!(converted_session.ip_address(), Some("127.0.0.1"));
        assert_eq!(converted_session.user_agent(), Some("Test Agent"));
        assert!(converted_session.is_active());
    }
}
