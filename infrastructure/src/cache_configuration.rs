// Cache configuration and wiring
// Sets up the caching infrastructure for query handlers

use crate::adapters::cache::{
    CacheInvalidator, CacheKeyGenerator, RedisCache, RedisCacheInvalidator, RedisCacheQueryAdapter,
};
use anyhow::Result;
use auth_application::{
    queries::{
        check_user_permissions::CheckUserPermissionsQuery, get_audit_log::GetAuditLogQuery,
        get_current_session::GetCurrentSessionQuery, get_role_details::GetRoleDetailsQuery,
        get_user_profile::GetUserProfileQuery, list_user_roles::ListUserRolesQuery,
        list_user_sessions::ListUserSessionsQuery, search_users::SearchUsersQuery,
    },
    security::rate_limiter::{RateLimiter, RateLimiterConfig},
    services::{CacheTTLConfig, QueryHandlerFactory},
};
use auth_domain::repositories::{
    AuditLogRepository, RoleRepository, SessionRepository, UserRepository,
};
use std::sync::Arc;

/// Main cache configuration
pub struct CacheConfiguration {
    redis_cache: Arc<RedisCache>,
    key_generator: CacheKeyGenerator,
    ttl_config: CacheTTLConfig,
    invalidator: Arc<RedisCacheInvalidator>,
}

impl CacheConfiguration {
    /// Create new cache configuration with Redis connection
    pub async fn new(redis_url: &str, namespace: String) -> Result<Self> {
        let redis_cache = Arc::new(RedisCache::new(redis_url)?);
        let key_generator = CacheKeyGenerator::new(namespace.clone());
        let ttl_config = CacheTTLConfig::default();
        let invalidator = Arc::new(RedisCacheInvalidator::new(
            redis_cache.clone(),
            key_generator.clone(),
        ));

        // Test Redis connection
        redis_cache
            .ping()
            .await
            .map_err(|e| anyhow::anyhow!("Failed to ping Redis: {}", e))?;

        Ok(Self {
            redis_cache,
            key_generator,
            ttl_config,
            invalidator,
        })
    }

    /// Get the cache invalidator for event handling
    pub fn invalidator(&self) -> Arc<dyn CacheInvalidator> {
        self.invalidator.clone()
    }

    /// Create cache adapter for GetUserProfileQuery
    pub fn create_user_profile_cache(&self) -> RedisCacheQueryAdapter<GetUserProfileQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.user_profile_ttl,
        )
    }

    /// Create cache adapter for CheckUserPermissionsQuery
    pub fn create_permissions_cache(&self) -> RedisCacheQueryAdapter<CheckUserPermissionsQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.permissions_ttl,
        )
    }

    /// Create cache adapter for ListUserRolesQuery
    pub fn create_user_roles_cache(&self) -> RedisCacheQueryAdapter<ListUserRolesQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.user_roles_ttl,
        )
    }

    /// Create cache adapter for GetCurrentSessionQuery
    pub fn create_session_cache(&self) -> RedisCacheQueryAdapter<GetCurrentSessionQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.session_ttl,
        )
    }

    /// Create cache adapter for SearchUsersQuery
    pub fn create_search_cache(&self) -> RedisCacheQueryAdapter<SearchUsersQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.search_ttl,
        )
    }

    /// Create cache adapter for GetAuditLogQuery
    pub fn create_audit_log_cache(&self) -> RedisCacheQueryAdapter<GetAuditLogQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.audit_log_ttl,
        )
    }

    /// Create cache adapter for GetRoleDetailsQuery
    pub fn create_role_details_cache(&self) -> RedisCacheQueryAdapter<GetRoleDetailsQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.role_details_ttl,
        )
    }

    /// Create cache adapter for ListUserSessionsQuery
    pub fn create_user_sessions_cache(&self) -> RedisCacheQueryAdapter<ListUserSessionsQuery> {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            self.ttl_config.user_sessions_ttl,
        )
    }
}

/// Wire up all query handlers with caching
pub struct CachedQueryHandlers<UR, RR, SR, AR>
where
    UR: UserRepository + Clone,
    RR: RoleRepository + Clone,
    SR: SessionRepository + Clone,
    AR: AuditLogRepository + Clone,
{
    factory: QueryHandlerFactory<UR, RR, SR, AR>,
    cache_config: Arc<CacheConfiguration>,
    rate_limiter: Arc<RateLimiter>,
}

impl<UR, RR, SR, AR> CachedQueryHandlers<UR, RR, SR, AR>
where
    UR: UserRepository + Clone,
    RR: RoleRepository + Clone,
    SR: SessionRepository + Clone,
    AR: AuditLogRepository + Clone,
{
    pub fn new(
        user_repository: Arc<UR>,
        role_repository: Arc<RR>,
        session_repository: Arc<SR>,
        audit_repository: Arc<AR>,
        cache_config: Arc<CacheConfiguration>,
    ) -> Self {
        let factory = QueryHandlerFactory::new(
            user_repository,
            role_repository,
            session_repository,
            audit_repository,
        );

        // Create rate limiter with custom config for query operations
        let rate_limiter_config = RateLimiterConfig {
            max_attempts_per_ip: 100, // Higher limit for read operations
            max_attempts_per_account: 50,
            window_duration: std::time::Duration::from_secs(60), // 1 minute window
            ..Default::default()
        };
        let rate_limiter = Arc::new(RateLimiter::with_config(rate_limiter_config));

        Self {
            factory,
            cache_config,
            rate_limiter,
        }
    }

    /// Get user profile handler with caching
    pub fn get_user_profile_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<GetUserProfileQuery> {
        self.factory
            .create_get_user_profile_handler(self.cache_config.create_user_profile_cache())
    }

    /// Get permissions check handler with caching
    pub fn check_permissions_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<CheckUserPermissionsQuery> {
        self.factory
            .create_check_permissions_handler(self.cache_config.create_permissions_cache())
    }

    /// Get user roles handler with caching
    pub fn list_user_roles_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<ListUserRolesQuery> {
        self.factory
            .create_list_user_roles_handler(self.cache_config.create_user_roles_cache())
    }

    /// Get current session handler with caching
    pub fn get_current_session_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<GetCurrentSessionQuery> {
        self.factory
            .create_get_current_session_handler(self.cache_config.create_session_cache())
    }

    /// Get search users handler with rate limiting
    pub fn search_users_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<SearchUsersQuery> {
        self.factory
            .create_search_users_handler(self.rate_limiter.clone())
    }

    /// Get audit log handler with rate limiting
    pub fn get_audit_log_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<GetAuditLogQuery> {
        self.factory
            .create_get_audit_log_handler(self.rate_limiter.clone())
    }

    /// Get role details handler with rate limiting
    pub fn get_role_details_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<GetRoleDetailsQuery> {
        self.factory
            .create_get_role_details_handler(self.rate_limiter.clone())
    }

    /// Get user sessions handler with caching
    pub fn list_user_sessions_handler(
        &self,
    ) -> impl auth_application::queries::base::AsyncQueryHandler<ListUserSessionsQuery> {
        self.factory
            .create_list_user_sessions_handler(self.cache_config.create_user_sessions_cache())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_configuration_creation() {
        // Test with mock Redis URL
        let result = CacheConfiguration::new("redis://localhost:6379/15", "test".to_string()).await;

        // This will fail if Redis is not running, which is expected in unit tests
        // In integration tests with testcontainers, this would succeed
        assert!(result.is_err() || result.is_ok());
    }

    #[test]
    fn test_ttl_configuration() {
        let config = CacheTTLConfig::default();

        // Verify TTLs match requirements
        assert_eq!(config.user_profile_ttl.as_secs(), 300); // 5 minutes
        assert_eq!(config.permissions_ttl.as_secs(), 300); // 5 minutes
        assert_eq!(config.session_ttl.as_secs(), 60); // 1 minute
        assert_eq!(config.search_ttl.as_secs(), 30); // 30 seconds
        assert_eq!(config.audit_log_ttl.as_secs(), 120); // 2 minutes
        assert_eq!(config.role_details_ttl.as_secs(), 600); // 10 minutes
    }
}
