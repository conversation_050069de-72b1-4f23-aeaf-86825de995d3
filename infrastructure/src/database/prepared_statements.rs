// Prepared statement cache for improved query performance
// Provides caching and reuse of prepared statements to reduce parsing overhead

use auth_domain::errors::DomainError;
use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use tracing::{debug, instrument, warn};

/// Cache for prepared statements
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct PreparedStatementCache {
    statements: HashMap<StatementKey, PreparedStatementEntry>,
    max_cache_size: usize,
    default_ttl: Duration,
    access_order: Vec<StatementKey>, // For LRU eviction
}

/// Key for identifying prepared statements
#[derive(Debug, <PERSON><PERSON>, Hash, PartialEq, Eq)]
pub struct StatementKey {
    pub sql: String,
    pub database_type: String,
}

/// Cache entry for a prepared statement
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct PreparedStatementEntry {
    #[allow(dead_code)]
    key: StatementKey,
    created_at: SystemTime,
    last_accessed: SystemTime,
    access_count: u64,
    ttl: Duration,
    sql: String,
    #[allow(dead_code)]
    parameter_count: usize,
}

/// Statistics for prepared statement cache
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct CacheStatistics {
    pub total_requests: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub evictions: u64,
    pub current_size: usize,
    pub max_size: usize,
    pub hit_rate: f64,
}

impl PreparedStatementCache {
    /// Create a new prepared statement cache
    pub fn new(max_cache_size: usize, default_ttl: Duration) -> Self {
        PreparedStatementCache {
            statements: HashMap::new(),
            max_cache_size,
            default_ttl,
            access_order: Vec::new(),
        }
    }

    /// Create cache with default settings optimized for auth workload
    pub fn new_optimized() -> Self {
        Self::new(
            1000,                      // Cache up to 1000 prepared statements
            Duration::from_secs(3600), // 1 hour TTL
        )
    }

    /// Get or create a prepared statement entry
    #[instrument(level = "debug", skip(self))]
    pub fn get_or_insert(
        &mut self,
        key: StatementKey,
    ) -> Result<PreparedStatementEntry, DomainError> {
        let now = SystemTime::now();

        // Check if statement exists and handle expiration separately
        let should_remove = if let Some(entry) = self.statements.get(&key) {
            now.duration_since(entry.created_at)
                .unwrap_or(Duration::MAX)
                > entry.ttl
        } else {
            false
        };

        if should_remove {
            debug!("Prepared statement expired, removing: {}", key.sql);
            self.statements.remove(&key);
            self.remove_from_access_order(&key);
        }

        // Try to get existing non-expired entry
        if self.statements.contains_key(&key) {
            // Update access information
            let entry = self.statements.get_mut(&key).unwrap();
            entry.last_accessed = now;
            entry.access_count += 1;
            let result = entry.clone();

            // Update access order after borrowing is done
            self.update_access_order(&key);

            debug!("Cache hit for prepared statement: {}", key.sql);
            return Ok(result);
        }

        debug!("Cache miss for prepared statement: {}", key.sql);

        // Ensure we have space in cache
        if self.statements.len() >= self.max_cache_size {
            self.evict_lru()?;
        }

        // Create new entry
        let parameter_count = self.count_parameters(&key.sql);
        let entry = PreparedStatementEntry {
            key: key.clone(),
            created_at: now,
            last_accessed: now,
            access_count: 1,
            ttl: self.default_ttl,
            sql: key.sql.clone(),
            parameter_count,
        };

        // Insert into cache
        self.statements.insert(key.clone(), entry.clone());
        self.access_order.push(key);

        Ok(entry)
    }

    /// Check if a statement is cached
    pub fn contains(&self, key: &StatementKey) -> bool {
        if let Some(entry) = self.statements.get(key) {
            let now = SystemTime::now();
            now.duration_since(entry.created_at)
                .unwrap_or(Duration::MAX)
                <= entry.ttl
        } else {
            false
        }
    }

    /// Remove a statement from cache
    pub fn remove(&mut self, key: &StatementKey) -> bool {
        let removed = self.statements.remove(key).is_some();
        if removed {
            self.remove_from_access_order(key);
            debug!("Removed prepared statement from cache: {}", key.sql);
        }
        removed
    }

    /// Clear expired entries from cache
    #[instrument(level = "debug")]
    pub fn cleanup_expired(&mut self) -> usize {
        let now = SystemTime::now();
        let mut expired_keys = Vec::new();

        for (key, entry) in &self.statements {
            if now
                .duration_since(entry.created_at)
                .unwrap_or(Duration::MAX)
                > entry.ttl
            {
                expired_keys.push(key.clone());
            }
        }

        let expired_count = expired_keys.len();
        for key in expired_keys {
            self.statements.remove(&key);
            self.remove_from_access_order(&key);
        }

        if expired_count > 0 {
            debug!("Cleaned up {} expired prepared statements", expired_count);
        }

        expired_count
    }

    /// Clear all cached statements
    pub fn clear(&mut self) {
        let count = self.statements.len();
        self.statements.clear();
        self.access_order.clear();
        debug!("Cleared all {} prepared statements from cache", count);
    }

    /// Get cache statistics
    pub fn statistics(&self) -> CacheStatistics {
        let total_accesses: u64 = self.statements.values().map(|e| e.access_count).sum();
        let total_statements = self.statements.len() as u64;

        // Simple estimation of hits vs misses based on access patterns
        let estimated_hits = total_accesses.saturating_sub(total_statements);

        let hit_rate = if total_accesses > 0 {
            estimated_hits as f64 / total_accesses as f64 * 100.0
        } else {
            0.0
        };

        CacheStatistics {
            total_requests: total_accesses,
            cache_hits: estimated_hits,
            cache_misses: total_statements,
            evictions: 0, // Would track this in a real implementation
            current_size: self.statements.len(),
            max_size: self.max_cache_size,
            hit_rate,
        }
    }

    /// Get most frequently used statements
    pub fn get_top_statements(&self, limit: usize) -> Vec<(String, u64)> {
        let mut statements: Vec<_> = self
            .statements
            .values()
            .map(|entry| (entry.sql.clone(), entry.access_count))
            .collect();

        statements.sort_by(|a, b| b.1.cmp(&a.1));
        statements.truncate(limit);
        statements
    }

    /// Evict least recently used entry
    fn evict_lru(&mut self) -> Result<(), DomainError> {
        if let Some(lru_key) = self.access_order.first().cloned() {
            self.statements.remove(&lru_key);
            self.access_order.remove(0);
            debug!("Evicted LRU prepared statement: {}", lru_key.sql);
            Ok(())
        } else {
            Err(DomainError::Configuration(
                "Cache is empty, cannot evict".to_string(),
            ))
        }
    }

    /// Update access order for LRU tracking
    fn update_access_order(&mut self, key: &StatementKey) {
        // Remove from current position
        self.remove_from_access_order(key);
        // Add to end (most recently used)
        self.access_order.push(key.clone());
    }

    /// Remove key from access order tracking  
    fn remove_from_access_order(&mut self, key: &StatementKey) {
        if let Some(pos) = self.access_order.iter().position(|k| k == key) {
            self.access_order.remove(pos);
        }
    }

    /// Count SQL parameters in a query
    fn count_parameters(&self, sql: &str) -> usize {
        // Count ? placeholders for SQLite/libSQL
        let question_marks = sql.matches('?').count();

        // Count $1, $2, etc. placeholders for PostgreSQL
        let mut postgres_params = 0;
        let mut i = 1;
        while sql.contains(&format!("${i}")) {
            postgres_params += 1;
            i += 1;
        }

        // Return the higher count (assume only one style is used per query)
        question_marks.max(postgres_params)
    }

    /// Validate cache state (for debugging)
    pub fn validate(&self) -> Result<(), String> {
        // Check that access_order length matches statements length
        if self.access_order.len() != self.statements.len() {
            return Err(format!(
                "Access order length ({}) doesn't match statements length ({})",
                self.access_order.len(),
                self.statements.len()
            ));
        }

        // Check that all keys in access_order exist in statements
        for key in &self.access_order {
            if !self.statements.contains_key(key) {
                return Err(format!(
                    "Access order contains key not in statements: {key:?}"
                ));
            }
        }

        // Check that all keys in statements exist in access_order
        for key in self.statements.keys() {
            if !self.access_order.contains(key) {
                return Err(format!(
                    "Statements contains key not in access order: {key:?}"
                ));
            }
        }

        Ok(())
    }
}

impl StatementKey {
    /// Create a new statement key
    pub fn new(sql: String, database_type: String) -> Self {
        StatementKey { sql, database_type }
    }

    /// Create key for SQLite/libSQL
    pub fn sqlite(sql: String) -> Self {
        Self::new(sql, "sqlite".to_string())
    }

    /// Create key for PostgreSQL
    pub fn postgres(sql: String) -> Self {
        Self::new(sql, "postgres".to_string())
    }

    /// Normalize SQL for consistent caching (remove extra whitespace, etc.)
    pub fn normalized(mut self) -> Self {
        self.sql = self
            .sql
            .split_whitespace()
            .collect::<Vec<_>>()
            .join(" ")
            .to_uppercase();
        self
    }
}

impl Default for PreparedStatementCache {
    fn default() -> Self {
        Self::new_optimized()
    }
}

/// Helper functions for common auth service queries
impl PreparedStatementCache {
    /// Get statement key for user lookup by email
    pub fn user_by_email_key(database_type: &str) -> StatementKey {
        StatementKey::new(
            "SELECT id, email, password_hash, is_verified, is_active, created_at, updated_at FROM users WHERE email = ? AND is_active = TRUE".to_string(),
            database_type.to_string(),
        )
    }

    /// Get statement key for session validation
    pub fn session_validation_key(database_type: &str) -> StatementKey {
        StatementKey::new(
            "SELECT id, user_id, expires_at, is_active FROM sessions WHERE id = ? AND is_active = TRUE AND expires_at > ?".to_string(),
            database_type.to_string(),
        )
    }

    /// Get statement key for permission check
    pub fn permission_check_key(database_type: &str) -> StatementKey {
        StatementKey::new(
            "SELECT COUNT(*) FROM user_roles ur JOIN role_permissions rp ON ur.role_id = rp.role_id WHERE ur.user_id = ? AND rp.permission_id = ?".to_string(),
            database_type.to_string(),
        )
    }

    /// Get statement key for user roles
    pub fn user_roles_key(database_type: &str) -> StatementKey {
        StatementKey::new(
            "SELECT r.id, r.name, r.description FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?".to_string(),
            database_type.to_string(),
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cache_creation() {
        let cache = PreparedStatementCache::new(100, Duration::from_secs(300));
        assert_eq!(cache.max_cache_size, 100);
        assert_eq!(cache.default_ttl, Duration::from_secs(300));
        assert!(cache.statements.is_empty());
    }

    #[test]
    fn test_statement_key_creation() {
        let key1 = StatementKey::sqlite("SELECT * FROM users".to_string());
        let key2 = StatementKey::postgres("SELECT * FROM users".to_string());

        assert_eq!(key1.database_type, "sqlite");
        assert_eq!(key2.database_type, "postgres");
        assert_ne!(key1, key2);
    }

    #[test]
    fn test_sql_normalization() {
        let key = StatementKey::sqlite("SELECT   *   FROM   users   WHERE   id = ?".to_string())
            .normalized();

        assert_eq!(key.sql, "SELECT * FROM USERS WHERE ID = ?");
    }

    #[test]
    fn test_parameter_counting() {
        let cache = PreparedStatementCache::new_optimized();

        // SQLite style
        let sqlite_sql = "SELECT * FROM users WHERE email = ? AND active = ?";
        assert_eq!(cache.count_parameters(sqlite_sql), 2);

        // PostgreSQL style
        let postgres_sql = "SELECT * FROM users WHERE email = $1 AND active = $2";
        assert_eq!(cache.count_parameters(postgres_sql), 2);

        // No parameters
        let no_params = "SELECT COUNT(*) FROM users";
        assert_eq!(cache.count_parameters(no_params), 0);
    }

    #[test]
    fn test_cache_insert_and_retrieve() {
        let mut cache = PreparedStatementCache::new(10, Duration::from_secs(300));
        let key = StatementKey::sqlite("SELECT * FROM users".to_string());

        // First access should create entry
        let entry1 = cache.get_or_insert(key.clone()).unwrap();
        assert_eq!(entry1.access_count, 1);

        // Second access should increment count
        let entry2 = cache.get_or_insert(key.clone()).unwrap();
        assert_eq!(entry2.access_count, 2);

        assert!(cache.contains(&key));
        assert_eq!(cache.statements.len(), 1);
    }

    #[test]
    fn test_cache_validation() {
        let mut cache = PreparedStatementCache::new(10, Duration::from_secs(300));
        let key = StatementKey::sqlite("SELECT * FROM users".to_string());

        cache.get_or_insert(key).unwrap();
        assert!(cache.validate().is_ok());
    }

    #[test]
    fn test_cache_statistics() {
        let mut cache = PreparedStatementCache::new(10, Duration::from_secs(300));
        let key = StatementKey::sqlite("SELECT * FROM users".to_string());

        // Initial stats
        let initial_stats = cache.statistics();
        assert_eq!(initial_stats.current_size, 0);

        // After insertion
        cache.get_or_insert(key.clone()).unwrap();
        cache.get_or_insert(key).unwrap(); // Second access

        let stats = cache.statistics();
        assert_eq!(stats.current_size, 1);
        assert!(stats.hit_rate >= 0.0);
    }

    #[test]
    fn test_common_query_keys() {
        let user_key = PreparedStatementCache::user_by_email_key("sqlite");
        assert!(user_key.sql.contains("email"));
        assert!(user_key.sql.contains("users"));

        let session_key = PreparedStatementCache::session_validation_key("postgres");
        assert!(session_key.sql.contains("sessions"));
        assert!(session_key.sql.contains("expires_at"));
    }
}
