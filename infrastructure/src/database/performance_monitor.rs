// Database performance monitoring and alerting system
// Provides comprehensive performance tracking, alerting, and reporting

use auth_domain::errors::DomainError;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant, SystemTime};
use tracing::{error, info, warn};

/// Performance monitoring system for database operations
#[derive(Clone)]
pub struct DatabasePerformanceMonitor {
    metrics: Arc<Mutex<PerformanceMetrics>>,
    config: MonitoringConfig,
}

/// Configuration for performance monitoring
#[derive(Debug, Clone)]
pub struct MonitoringConfig {
    pub slow_query_threshold_ms: u64,
    pub sla_warning_threshold_ms: u64,
    pub sla_critical_threshold_ms: u64,
    pub enable_query_logging: bool,
    pub enable_alerting: bool,
    pub metrics_retention_hours: u64,
    pub sample_rate: f64, // 0.0 to 1.0
}

/// Performance metrics collection
#[derive(Debug, Default)]
struct PerformanceMetrics {
    query_stats: HashMap<String, QueryStats>,
    connection_stats: ConnectionStats,
    alert_history: Vec<PerformanceAlert>,
    monitoring_start_time: Option<SystemTime>,
}

/// Statistics for individual query types
#[derive(Debug, Default, Clone)]
pub struct QueryStats {
    pub total_executions: u64,
    pub total_duration_ms: u64,
    pub min_duration_ms: u64,
    pub max_duration_ms: u64,
    pub p50_duration_ms: u64,
    pub p95_duration_ms: u64,
    pub p99_duration_ms: u64,
    pub success_count: u64,
    pub error_count: u64,
    pub slow_query_count: u64,
    pub sla_violations: u64,
    pub last_execution: Option<SystemTime>,
    pub durations: Vec<u64>, // For percentile calculations
}

/// Connection pool statistics
#[derive(Debug, Default, Clone)]
pub struct ConnectionStats {
    pub total_connections_created: u64,
    pub total_connections_closed: u64,
    pub current_active_connections: u32,
    pub current_idle_connections: u32,
    pub max_connections_used: u32,
    pub connection_acquisition_timeouts: u64,
    pub connection_acquisition_avg_ms: f64,
}

/// Performance alert types
#[derive(Debug, Clone)]
pub struct PerformanceAlert {
    pub alert_type: AlertType,
    pub message: String,
    pub query_name: Option<String>,
    pub duration_ms: Option<u64>,
    pub timestamp: SystemTime,
    pub severity: AlertSeverity,
}

#[derive(Debug, Clone, PartialEq)]
pub enum AlertType {
    SlowQuery,
    SlaWarning,
    SlaCritical,
    ConnectionPoolExhaustion,
    HighErrorRate,
    QueryFrequencySpike,
}

#[derive(Debug, Clone, PartialEq)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
}

/// Query execution context for monitoring
pub struct QueryExecution {
    query_name: String,
    start_time: Instant,
    monitor: DatabasePerformanceMonitor,
}

impl DatabasePerformanceMonitor {
    /// Create a new performance monitor with default configuration
    pub fn new() -> Self {
        Self::with_config(MonitoringConfig::default())
    }

    /// Create a new performance monitor with custom configuration
    pub fn with_config(config: MonitoringConfig) -> Self {
        let metrics = PerformanceMetrics {
            monitoring_start_time: Some(SystemTime::now()),
            ..Default::default()
        };

        DatabasePerformanceMonitor {
            metrics: Arc::new(Mutex::new(metrics)),
            config,
        }
    }

    /// Start monitoring a query execution
    pub fn start_query(&self, query_name: String) -> QueryExecution {
        QueryExecution {
            query_name,
            start_time: Instant::now(),
            monitor: self.clone(),
        }
    }

    /// Record query execution completion
    pub fn record_query_completion(
        &self,
        query_name: &str,
        duration: Duration,
        success: bool,
    ) -> Result<(), DomainError> {
        // Skip sampling if configured
        if self.config.sample_rate < 1.0 {
            use std::collections::hash_map::DefaultHasher;
            use std::hash::{Hash, Hasher};

            let mut hasher = DefaultHasher::new();
            query_name.hash(&mut hasher);
            let hash = hasher.finish();
            let sample_threshold = (self.config.sample_rate * u64::MAX as f64) as u64;

            if hash > sample_threshold {
                return Ok(()); // Skip this sample
            }
        }

        let duration_ms = duration.as_millis() as u64;
        let mut metrics = self.metrics.lock().map_err(|_| {
            DomainError::Infrastructure("Failed to acquire metrics lock".to_string())
        })?;

        // Collect alerts to create after updating stats
        let mut alerts_to_create = Vec::new();

        // Update query statistics
        {
            let stats = metrics
                .query_stats
                .entry(query_name.to_string())
                .or_default();

            stats.total_executions += 1;
            stats.total_duration_ms += duration_ms;
            stats.last_execution = Some(SystemTime::now());

            if success {
                stats.success_count += 1;
            } else {
                stats.error_count += 1;
            }

            // Update min/max
            if stats.min_duration_ms == 0 || duration_ms < stats.min_duration_ms {
                stats.min_duration_ms = duration_ms;
            }
            if duration_ms > stats.max_duration_ms {
                stats.max_duration_ms = duration_ms;
            }

            // Store duration for percentile calculations (with size limit)
            stats.durations.push(duration_ms);
            if stats.durations.len() > 1000 {
                stats.durations.remove(0); // Remove oldest to maintain size
            }

            // Calculate percentiles
            self.update_percentiles(stats);

            // Check for performance issues
            if duration_ms >= self.config.slow_query_threshold_ms {
                stats.slow_query_count += 1;

                if self.config.enable_query_logging {
                    warn!("Slow query detected: {} took {}ms", query_name, duration_ms);
                }

                if self.config.enable_alerting {
                    alerts_to_create.push((
                        AlertType::SlowQuery,
                        format!("Slow query: {query_name} took {duration_ms}ms"),
                        Some(query_name.to_string()),
                        Some(duration_ms),
                        AlertSeverity::Warning,
                    ));
                }
            }

            if duration_ms >= self.config.sla_critical_threshold_ms {
                stats.sla_violations += 1;
            }

            // Log performance metrics periodically
            if stats.total_executions % 1000 == 0 {
                info!(
                    "Query stats for {}: {} executions, avg: {}ms, p95: {}ms",
                    query_name,
                    stats.total_executions,
                    stats.total_duration_ms / stats.total_executions,
                    stats.p95_duration_ms
                );
            }
        }

        // Check for SLA warnings outside the stats block
        if duration_ms >= self.config.sla_warning_threshold_ms && self.config.enable_alerting {
            alerts_to_create.push((
                AlertType::SlaWarning,
                format!("Query approaching SLA threshold: {query_name} took {duration_ms}ms"),
                Some(query_name.to_string()),
                Some(duration_ms),
                AlertSeverity::Warning,
            ));
        }

        if duration_ms >= self.config.sla_critical_threshold_ms && self.config.enable_alerting {
            alerts_to_create.push((
                AlertType::SlaCritical,
                format!("SLA violation: {query_name} took {duration_ms}ms"),
                Some(query_name.to_string()),
                Some(duration_ms),
                AlertSeverity::Critical,
            ));
        }

        // Create all collected alerts
        for (alert_type, message, query, duration, severity) in alerts_to_create {
            self.create_alert(&mut metrics, alert_type, message, query, duration, severity);
        }

        Ok(())
    }

    /// Update connection pool statistics
    pub fn update_connection_stats(&self, stats: ConnectionStats) -> Result<(), DomainError> {
        let mut metrics = self.metrics.lock().map_err(|_| {
            DomainError::Infrastructure("Failed to acquire metrics lock".to_string())
        })?;

        metrics.connection_stats = stats;

        // Check for connection pool issues
        if self.config.enable_alerting {
            let stats = metrics.connection_stats.clone();
            let utilization =
                if stats.current_active_connections + stats.current_idle_connections > 0 {
                    stats.current_active_connections as f64
                        / (stats.current_active_connections + stats.current_idle_connections) as f64
                } else {
                    0.0
                };

            if utilization > 0.9 {
                self.create_alert(
                    &mut metrics,
                    AlertType::ConnectionPoolExhaustion,
                    format!(
                        "High connection pool utilization: {:.1}%",
                        utilization * 100.0
                    ),
                    None,
                    None,
                    AlertSeverity::Warning,
                );
            }

            if stats.connection_acquisition_timeouts > 0 {
                self.create_alert(
                    &mut metrics,
                    AlertType::ConnectionPoolExhaustion,
                    format!(
                        "Connection acquisition timeouts detected: {}",
                        stats.connection_acquisition_timeouts
                    ),
                    None,
                    None,
                    AlertSeverity::Critical,
                );
            }
        }

        Ok(())
    }

    /// Get comprehensive performance report
    pub fn get_performance_report(&self) -> Result<PerformanceReport, DomainError> {
        let metrics = self.metrics.lock().map_err(|_| {
            DomainError::Infrastructure("Failed to acquire metrics lock".to_string())
        })?;

        let uptime = metrics
            .monitoring_start_time
            .and_then(|start| SystemTime::now().duration_since(start).ok())
            .unwrap_or_default();

        let total_queries: u64 = metrics
            .query_stats
            .values()
            .map(|s| s.total_executions)
            .sum();
        let total_errors: u64 = metrics.query_stats.values().map(|s| s.error_count).sum();
        let total_slow_queries: u64 = metrics
            .query_stats
            .values()
            .map(|s| s.slow_query_count)
            .sum();
        let total_sla_violations: u64 =
            metrics.query_stats.values().map(|s| s.sla_violations).sum();

        let error_rate = if total_queries > 0 {
            (total_errors as f64 / total_queries as f64) * 100.0
        } else {
            0.0
        };

        let slow_query_rate = if total_queries > 0 {
            (total_slow_queries as f64 / total_queries as f64) * 100.0
        } else {
            0.0
        };

        // Find top slow queries
        let mut top_slow_queries: Vec<_> = metrics
            .query_stats
            .iter()
            .map(|(name, stats)| (name.clone(), stats.clone()))
            .collect();
        top_slow_queries.sort_by(|a, b| b.1.slow_query_count.cmp(&a.1.slow_query_count));
        top_slow_queries.truncate(10);

        // Recent alerts
        let recent_alerts: Vec<_> = metrics
            .alert_history
            .iter()
            .rev()
            .take(50)
            .cloned()
            .collect();

        Ok(PerformanceReport {
            uptime_seconds: uptime.as_secs(),
            total_queries,
            total_errors,
            error_rate,
            total_slow_queries,
            slow_query_rate,
            total_sla_violations,
            query_stats: metrics.query_stats.clone(),
            connection_stats: metrics.connection_stats.clone(),
            top_slow_queries,
            recent_alerts,
            monitoring_config: self.config.clone(),
        })
    }

    /// Get query statistics for a specific query
    pub fn get_query_stats(&self, query_name: &str) -> Result<Option<QueryStats>, DomainError> {
        let metrics = self.metrics.lock().map_err(|_| {
            DomainError::Infrastructure("Failed to acquire metrics lock".to_string())
        })?;

        Ok(metrics.query_stats.get(query_name).cloned())
    }

    /// Clear all metrics (useful for testing)
    pub fn clear_metrics(&self) -> Result<(), DomainError> {
        let mut metrics = self.metrics.lock().map_err(|_| {
            DomainError::Infrastructure("Failed to acquire metrics lock".to_string())
        })?;

        metrics.query_stats.clear();
        metrics.alert_history.clear();
        metrics.monitoring_start_time = Some(SystemTime::now());

        Ok(())
    }

    /// Update percentile calculations for query stats
    fn update_percentiles(&self, stats: &mut QueryStats) {
        if stats.durations.is_empty() {
            return;
        }

        let mut sorted_durations = stats.durations.clone();
        sorted_durations.sort_unstable();

        let len = sorted_durations.len();
        stats.p50_duration_ms = sorted_durations[len * 50 / 100];
        stats.p95_duration_ms = sorted_durations[len * 95 / 100];
        stats.p99_duration_ms = sorted_durations[len * 99 / 100];
    }

    /// Create a performance alert
    fn create_alert(
        &self,
        metrics: &mut PerformanceMetrics,
        alert_type: AlertType,
        message: String,
        query_name: Option<String>,
        duration_ms: Option<u64>,
        severity: AlertSeverity,
    ) {
        let alert = PerformanceAlert {
            alert_type,
            message: message.clone(),
            query_name,
            duration_ms,
            timestamp: SystemTime::now(),
            severity: severity.clone(),
        };

        metrics.alert_history.push(alert);

        // Limit alert history size
        if metrics.alert_history.len() > 1000 {
            metrics.alert_history.remove(0);
        }

        // Log the alert
        match severity {
            AlertSeverity::Info => info!("Performance alert: {}", message),
            AlertSeverity::Warning => warn!("Performance alert: {}", message),
            AlertSeverity::Critical => error!("Performance alert: {}", message),
        }
    }
}

impl QueryExecution {
    /// Complete the query execution and record metrics
    pub fn complete(self, success: bool) -> Result<Duration, DomainError> {
        let duration = self.start_time.elapsed();
        self.monitor
            .record_query_completion(&self.query_name, duration, success)?;
        Ok(duration)
    }
}

/// Comprehensive performance report
#[derive(Debug, Clone)]
pub struct PerformanceReport {
    pub uptime_seconds: u64,
    pub total_queries: u64,
    pub total_errors: u64,
    pub error_rate: f64,
    pub total_slow_queries: u64,
    pub slow_query_rate: f64,
    pub total_sla_violations: u64,
    pub query_stats: HashMap<String, QueryStats>,
    pub connection_stats: ConnectionStats,
    pub top_slow_queries: Vec<(String, QueryStats)>,
    pub recent_alerts: Vec<PerformanceAlert>,
    pub monitoring_config: MonitoringConfig,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        MonitoringConfig {
            slow_query_threshold_ms: 50,
            sla_warning_threshold_ms: 75,
            sla_critical_threshold_ms: 90,
            enable_query_logging: true,
            enable_alerting: true,
            metrics_retention_hours: 24,
            sample_rate: 1.0, // Monitor all queries by default
        }
    }
}

impl Default for DatabasePerformanceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;

    #[test]
    fn test_monitor_creation() {
        let monitor = DatabasePerformanceMonitor::new();
        let report = monitor.get_performance_report().unwrap();

        assert_eq!(report.total_queries, 0);
        assert_eq!(report.total_errors, 0);
        assert_eq!(report.error_rate, 0.0);
    }

    #[test]
    fn test_query_execution_monitoring() {
        let monitor = DatabasePerformanceMonitor::new();

        // Record a successful query
        let execution = monitor.start_query("test_query".to_string());
        thread::sleep(Duration::from_millis(10));
        let duration = execution.complete(true).unwrap();

        assert!(duration.as_millis() >= 10);

        let stats = monitor.get_query_stats("test_query").unwrap().unwrap();
        assert_eq!(stats.total_executions, 1);
        assert_eq!(stats.success_count, 1);
        assert_eq!(stats.error_count, 0);
        assert!(stats.min_duration_ms >= 10);
    }

    #[test]
    fn test_slow_query_detection() {
        let config = MonitoringConfig {
            slow_query_threshold_ms: 5,
            enable_alerting: true,
            ..Default::default()
        };
        let monitor = DatabasePerformanceMonitor::with_config(config);

        // Record a slow query
        monitor
            .record_query_completion("slow_query", Duration::from_millis(10), true)
            .unwrap();

        let stats = monitor.get_query_stats("slow_query").unwrap().unwrap();
        assert_eq!(stats.slow_query_count, 1);

        let report = monitor.get_performance_report().unwrap();
        assert!(!report.recent_alerts.is_empty());
        assert_eq!(report.recent_alerts[0].alert_type, AlertType::SlowQuery);
    }

    #[test]
    fn test_percentile_calculations() {
        let monitor = DatabasePerformanceMonitor::new();

        // Record multiple queries with different durations
        for duration_ms in [10, 20, 30, 40, 50, 100, 200, 500, 1000] {
            monitor
                .record_query_completion("test_query", Duration::from_millis(duration_ms), true)
                .unwrap();
        }

        let stats = monitor.get_query_stats("test_query").unwrap().unwrap();
        assert!(stats.p50_duration_ms > 0);
        assert!(stats.p95_duration_ms > stats.p50_duration_ms);
        assert!(stats.p99_duration_ms >= stats.p95_duration_ms);
    }

    #[test]
    fn test_connection_stats_monitoring() {
        let monitor = DatabasePerformanceMonitor::new();

        let conn_stats = ConnectionStats {
            current_active_connections: 45,
            current_idle_connections: 5,
            ..Default::default()
        };

        monitor.update_connection_stats(conn_stats.clone()).unwrap();

        let report = monitor.get_performance_report().unwrap();
        assert_eq!(report.connection_stats.current_active_connections, 45);
        assert_eq!(report.connection_stats.current_idle_connections, 5);
    }

    #[test]
    fn test_sample_rate() {
        let config = MonitoringConfig {
            sample_rate: 0.0, // Don't sample any queries
            ..Default::default()
        };
        let monitor = DatabasePerformanceMonitor::with_config(config);

        monitor
            .record_query_completion("test_query", Duration::from_millis(10), true)
            .unwrap();

        let stats = monitor.get_query_stats("test_query").unwrap();
        assert!(stats.is_none()); // Query should not have been recorded due to sampling
    }
}
