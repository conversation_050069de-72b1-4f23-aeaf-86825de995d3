// Database module for optimized connections and query management
// Provides high-performance database access with connection pooling and monitoring

pub mod connection_pool;
pub mod monitoring_integration;
pub mod performance_monitor;
pub mod prepared_statements;
pub mod query_optimizer;

pub use connection_pool::{OptimizedConnectionPool, PoolHealthStatus, PoolMetrics, QueryMetrics};
pub use monitoring_integration::{
    MonitoredDatabase, MonitoredDatabaseExt, trace_database_operation,
};
pub use performance_monitor::{
    DatabasePerformanceMonitor, MonitoringConfig, PerformanceReport, QueryStats,
};
pub use prepared_statements::{PreparedStatementCache, StatementKey};
pub use query_optimizer::{QueryAnalysis, QueryOptimizer, QueryPlan};
