// Query optimization and analysis module
// Provides query planning, analysis, and performance monitoring

use auth_domain::errors::DomainError;
use sqlx::{Pool, Postgres, Row, Sqlite, query};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tracing::{debug, instrument};

/// Query optimizer for analyzing and improving query performance
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct QueryOptimizer {
    query_cache: HashMap<String, QueryPlan>,
    performance_stats: HashMap<String, QueryPerformanceStats>,
}

/// Query execution plan information
#[derive(Debug, Clone)]
pub struct QueryPlan {
    pub query_hash: String,
    pub estimated_cost: f64,
    pub uses_index: bool,
    pub table_scans: Vec<String>,
    pub index_usage: Vec<IndexUsage>,
    pub optimization_suggestions: Vec<String>,
}

/// Index usage information
#[derive(Debug, <PERSON>lone)]
pub struct IndexUsage {
    pub table_name: String,
    pub index_name: String,
    pub usage_type: IndexUsageType,
}

#[derive(Debug, <PERSON>lone)]
pub enum IndexUsageType {
    Primary,
    Index,
    FullTableScan,
}

/// Query performance statistics
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct QueryPerformanceStats {
    pub total_executions: u64,
    pub total_time_ms: u64,
    pub avg_time_ms: f64,
    pub min_time_ms: u64,
    pub max_time_ms: u64,
    pub success_rate: f64,
    pub last_execution: Option<std::time::SystemTime>,
}

/// Query analysis results
#[derive(Debug)]
pub struct QueryAnalysis {
    pub execution_time: Duration,
    pub plan: QueryPlan,
    pub performance_warning: Option<String>,
    pub optimization_score: u8, // 0-100, higher is better
}

impl QueryOptimizer {
    /// Create a new query optimizer
    pub fn new() -> Self {
        QueryOptimizer {
            query_cache: HashMap::new(),
            performance_stats: HashMap::new(),
        }
    }

    /// Analyze a query and return optimization information
    #[instrument(level = "debug", skip(self, pool))]
    pub async fn analyze_query_sqlite(
        &mut self,
        pool: &Pool<Sqlite>,
        sql: &str,
    ) -> Result<QueryAnalysis, DomainError> {
        let start_time = Instant::now();
        let query_hash = self.hash_query(sql);

        // Check if we have cached plan
        if let Some(cached_plan) = self.query_cache.get(&query_hash) {
            return Ok(QueryAnalysis {
                execution_time: Duration::from_millis(0), // Cached, no execution
                plan: cached_plan.clone(),
                performance_warning: None,
                optimization_score: self.calculate_optimization_score(cached_plan),
            });
        }

        // Analyze query execution plan
        let plan = self.get_sqlite_query_plan(pool, sql).await?;

        // Cache the plan
        self.query_cache.insert(query_hash.clone(), plan.clone());

        let execution_time = start_time.elapsed();

        // Generate performance warning if needed
        let performance_warning = self.generate_performance_warning(&plan, execution_time);

        // Update performance statistics
        self.update_performance_stats(&query_hash, execution_time, true);

        Ok(QueryAnalysis {
            execution_time,
            plan: plan.clone(),
            performance_warning,
            optimization_score: self.calculate_optimization_score(&plan),
        })
    }

    /// Analyze PostgreSQL query
    #[instrument(level = "debug", skip(self, pool))]
    pub async fn analyze_query_postgres(
        &mut self,
        pool: &Pool<Postgres>,
        sql: &str,
    ) -> Result<QueryAnalysis, DomainError> {
        let start_time = Instant::now();
        let query_hash = self.hash_query(sql);

        // Check cached plan
        if let Some(cached_plan) = self.query_cache.get(&query_hash) {
            return Ok(QueryAnalysis {
                execution_time: Duration::from_millis(0),
                plan: cached_plan.clone(),
                performance_warning: None,
                optimization_score: self.calculate_optimization_score(cached_plan),
            });
        }

        // Analyze PostgreSQL execution plan
        let plan = self.get_postgres_query_plan(pool, sql).await?;

        // Cache the plan
        self.query_cache.insert(query_hash.clone(), plan.clone());

        let execution_time = start_time.elapsed();
        let performance_warning = self.generate_performance_warning(&plan, execution_time);

        self.update_performance_stats(&query_hash, execution_time, true);

        Ok(QueryAnalysis {
            execution_time,
            plan: plan.clone(),
            performance_warning,
            optimization_score: self.calculate_optimization_score(&plan),
        })
    }

    /// Get SQLite query execution plan
    async fn get_sqlite_query_plan(
        &self,
        pool: &Pool<Sqlite>,
        sql: &str,
    ) -> Result<QueryPlan, DomainError> {
        let explain_sql = format!("EXPLAIN QUERY PLAN {sql}");

        let rows =
            query(&explain_sql)
                .fetch_all(pool)
                .await
                .map_err(|e| DomainError::Database {
                    source: e.to_string(),
                    context: "get_query_plan".to_string(),
                })?;

        let mut uses_index = false;
        let mut table_scans = Vec::new();
        let mut index_usage = Vec::new();
        let mut optimization_suggestions = Vec::new();

        for row in rows {
            let detail: String = row.try_get("detail").map_err(|e| DomainError::Database {
                source: e.to_string(),
                context: "read_plan_detail".to_string(),
            })?;

            debug!("Query plan detail: {}", detail);

            // Analyze plan details
            if detail.contains("USING INDEX") {
                uses_index = true;
                // Extract index information
                if let Some(index_info) = self.extract_sqlite_index_info(&detail) {
                    index_usage.push(index_info);
                }
            } else if detail.contains("SCAN TABLE") {
                // Extract table name from scan
                if let Some(table_name) = self.extract_table_name_from_scan(&detail) {
                    table_scans.push(table_name);
                    optimization_suggestions
                        .push(format!("Consider adding index for table scan: {detail}"));
                }
            }
        }

        // Generate additional optimization suggestions
        if !uses_index && !table_scans.is_empty() {
            optimization_suggestions.push(
                "Query performs full table scan - consider adding appropriate indexes".to_string(),
            );
        }

        Ok(QueryPlan {
            query_hash: self.hash_query(sql),
            estimated_cost: if uses_index { 1.0 } else { 10.0 }, // Simple cost estimation
            uses_index,
            table_scans,
            index_usage,
            optimization_suggestions,
        })
    }

    /// Get PostgreSQL query execution plan
    async fn get_postgres_query_plan(
        &self,
        pool: &Pool<Postgres>,
        sql: &str,
    ) -> Result<QueryPlan, DomainError> {
        let explain_sql = format!("EXPLAIN (FORMAT JSON, ANALYZE, BUFFERS) {sql}");

        let row = query(&explain_sql)
            .fetch_one(pool)
            .await
            .map_err(|e| DomainError::Database {
                source: e.to_string(),
                context: "get_query_plan".to_string(),
            })?;

        // PostgreSQL returns JSON plan - this is simplified
        let plan_json: String = row.try_get(0).map_err(|e| DomainError::Database {
            source: e.to_string(),
            context: "read_postgres_plan".to_string(),
        })?;

        debug!("PostgreSQL query plan: {}", plan_json);

        // Simple analysis - in production you'd parse the JSON properly
        let uses_index = plan_json.contains("Index") && !plan_json.contains("Seq Scan");
        let has_seq_scan = plan_json.contains("Seq Scan");

        let mut optimization_suggestions = Vec::new();
        if has_seq_scan {
            optimization_suggestions
                .push("Query contains sequential scan - consider adding indexes".to_string());
        }

        Ok(QueryPlan {
            query_hash: self.hash_query(sql),
            estimated_cost: if uses_index { 1.0 } else { 10.0 },
            uses_index,
            table_scans: if has_seq_scan {
                vec!["table".to_string()]
            } else {
                vec![]
            },
            index_usage: vec![], // Would be populated from JSON analysis
            optimization_suggestions,
        })
    }

    /// Extract SQLite index information from plan detail
    fn extract_sqlite_index_info(&self, detail: &str) -> Option<IndexUsage> {
        // Simple pattern matching - in production this would be more sophisticated
        if detail.contains("USING INDEX") {
            // Extract table and index names
            Some(IndexUsage {
                table_name: "unknown".to_string(), // Would extract from detail
                index_name: "unknown".to_string(), // Would extract from detail
                usage_type: IndexUsageType::Index,
            })
        } else {
            None
        }
    }

    /// Extract table name from scan detail
    fn extract_table_name_from_scan(&self, detail: &str) -> Option<String> {
        // Simple extraction - would be more sophisticated in production
        if detail.contains("SCAN TABLE") {
            detail.split_whitespace().nth(2).map(|s| s.to_string())
        } else {
            None
        }
    }

    /// Generate performance warning based on plan and execution time
    fn generate_performance_warning(
        &self,
        plan: &QueryPlan,
        execution_time: Duration,
    ) -> Option<String> {
        let mut warnings = Vec::new();

        // Check execution time
        if execution_time > Duration::from_millis(50) {
            warnings.push(format!(
                "Slow query: {}ms execution time",
                execution_time.as_millis()
            ));
        }

        // Check for table scans
        if !plan.table_scans.is_empty() {
            warnings.push("Query performs full table scan".to_string());
        }

        // Check estimated cost
        if plan.estimated_cost > 5.0 {
            warnings.push(format!("High query cost: {:.2}", plan.estimated_cost));
        }

        if warnings.is_empty() {
            None
        } else {
            Some(warnings.join("; "))
        }
    }

    /// Calculate optimization score (0-100, higher is better)
    fn calculate_optimization_score(&self, plan: &QueryPlan) -> u8 {
        let mut score = 100u8;

        // Deduct points for table scans
        score = score.saturating_sub(plan.table_scans.len() as u8 * 30);

        // Deduct points for high estimated cost
        if plan.estimated_cost > 5.0 {
            score = score.saturating_sub(20);
        } else if plan.estimated_cost > 2.0 {
            score = score.saturating_sub(10);
        }

        // Add points for index usage
        if plan.uses_index {
            score = score.saturating_add(10).min(100);
        }

        score
    }

    /// Update performance statistics for a query
    fn update_performance_stats(
        &mut self,
        query_hash: &str,
        execution_time: Duration,
        success: bool,
    ) {
        let stats = self
            .performance_stats
            .entry(query_hash.to_string())
            .or_default();

        stats.total_executions += 1;
        let execution_time_ms = execution_time.as_millis() as u64;

        if success {
            stats.total_time_ms += execution_time_ms;
            stats.avg_time_ms = stats.total_time_ms as f64 / stats.total_executions as f64;

            if stats.min_time_ms == 0 || execution_time_ms < stats.min_time_ms {
                stats.min_time_ms = execution_time_ms;
            }

            if execution_time_ms > stats.max_time_ms {
                stats.max_time_ms = execution_time_ms;
            }
        }

        // Update success rate
        let successful_executions = (stats.total_executions as f64 * stats.success_rate
            + if success { 1.0 } else { 0.0 })
            / stats.total_executions as f64;
        stats.success_rate = successful_executions;
        stats.last_execution = Some(std::time::SystemTime::now());
    }

    /// Generate simple query hash
    fn hash_query(&self, sql: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        sql.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// Get performance statistics for a query
    pub fn get_query_stats(&self, sql: &str) -> Option<&QueryPerformanceStats> {
        let query_hash = self.hash_query(sql);
        self.performance_stats.get(&query_hash)
    }

    /// Get all performance statistics
    pub fn get_all_stats(&self) -> &HashMap<String, QueryPerformanceStats> {
        &self.performance_stats
    }

    /// Clear optimization cache
    pub fn clear_cache(&mut self) {
        self.query_cache.clear();
        debug!("Query optimization cache cleared");
    }

    /// Get optimization suggestions for slow queries
    pub fn get_slow_query_suggestions(&self, threshold_ms: u64) -> Vec<(String, String)> {
        let mut suggestions = Vec::new();

        for (query_hash, stats) in &self.performance_stats {
            if stats.avg_time_ms > threshold_ms as f64 {
                if let Some(plan) = self.query_cache.get(query_hash) {
                    for suggestion in &plan.optimization_suggestions {
                        suggestions.push((query_hash.clone(), suggestion.clone()));
                    }
                }
            }
        }

        suggestions
    }
}

impl Default for QueryOptimizer {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_query_optimizer_creation() {
        let optimizer = QueryOptimizer::new();
        assert!(optimizer.query_cache.is_empty());
        assert!(optimizer.performance_stats.is_empty());
    }

    #[test]
    fn test_query_hash_generation() {
        let optimizer = QueryOptimizer::new();
        let sql1 = "SELECT * FROM users WHERE email = ?";
        let sql2 = "SELECT * FROM users WHERE email = ?";
        let sql3 = "SELECT * FROM sessions WHERE user_id = ?";

        let hash1 = optimizer.hash_query(sql1);
        let hash2 = optimizer.hash_query(sql2);
        let hash3 = optimizer.hash_query(sql3);

        assert_eq!(hash1, hash2); // Same query should have same hash
        assert_ne!(hash1, hash3); // Different queries should have different hashes
    }

    #[test]
    fn test_optimization_score_calculation() {
        let optimizer = QueryOptimizer::new();

        // Good plan with index usage
        let good_plan = QueryPlan {
            query_hash: "test".to_string(),
            estimated_cost: 1.0,
            uses_index: true,
            table_scans: vec![],
            index_usage: vec![],
            optimization_suggestions: vec![],
        };

        let good_score = optimizer.calculate_optimization_score(&good_plan);
        assert!(good_score > 90);

        // Bad plan with table scan
        let bad_plan = QueryPlan {
            query_hash: "test".to_string(),
            estimated_cost: 10.0,
            uses_index: false,
            table_scans: vec!["users".to_string()],
            index_usage: vec![],
            optimization_suggestions: vec![],
        };

        let bad_score = optimizer.calculate_optimization_score(&bad_plan);
        assert!(bad_score < 60);
    }

    #[test]
    fn test_performance_stats_update() {
        let mut optimizer = QueryOptimizer::new();
        let query_hash = "test_query";

        // First execution
        optimizer.update_performance_stats(query_hash, Duration::from_millis(50), true);

        let stats = optimizer.performance_stats.get(query_hash).unwrap();
        assert_eq!(stats.total_executions, 1);
        assert_eq!(stats.min_time_ms, 50);
        assert_eq!(stats.max_time_ms, 50);
        assert_eq!(stats.avg_time_ms, 50.0);

        // Second execution
        optimizer.update_performance_stats(query_hash, Duration::from_millis(30), true);

        let stats = optimizer.performance_stats.get(query_hash).unwrap();
        assert_eq!(stats.total_executions, 2);
        assert_eq!(stats.min_time_ms, 30);
        assert_eq!(stats.max_time_ms, 50);
        assert_eq!(stats.avg_time_ms, 40.0); // (50 + 30) / 2
    }
}
