// Database monitoring integration
// Bridges database performance monitoring with application-level monitoring system

use super::performance_monitor::{ConnectionStats, DatabasePerformanceMonitor, QueryStats};
use auth_application::monitoring::{
    DatabaseMetrics, MetricsCollector, PerformanceTracer, TraceContext,
};
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tracing::{debug, warn};

/// Database wrapper with integrated monitoring
pub struct MonitoredDatabase<DB> {
    inner: DB,
    db_monitor: DatabasePerformanceMonitor,
    metrics_collector: Arc<dyn MetricsCollector>,
    tracer: Arc<dyn PerformanceTracer>,
}

impl<DB> MonitoredDatabase<DB> {
    /// Create a new monitored database
    pub fn new(
        inner: DB,
        metrics_collector: Arc<dyn MetricsCollector>,
        tracer: Arc<dyn PerformanceTracer>,
    ) -> Self {
        Self {
            inner,
            db_monitor: DatabasePerformanceMonitor::new(),
            metrics_collector,
            tracer,
        }
    }

    /// Get reference to inner database
    pub fn inner(&self) -> &DB {
        &self.inner
    }

    /// Get mutable reference to inner database
    pub fn inner_mut(&mut self) -> &mut DB {
        &mut self.inner
    }

    /// Execute a query with monitoring
    pub async fn execute_query<T, F, Fut>(
        &self,
        query_name: &str,
        operation_type: &str,
        table_name: Option<&str>,
        f: F,
    ) -> Result<T, sqlx::Error>
    where
        F: FnOnce(&DB) -> Fut,
        Fut: std::future::Future<Output = Result<T, sqlx::Error>>,
    {
        // Start monitoring
        let query_execution = self.db_monitor.start_query(query_name.to_string());
        let trace_context = self
            .tracer
            .start_trace(&format!("db:{operation_type}:{query_name}"));
        let start = std::time::Instant::now();

        // Execute the query
        let result = f(&self.inner).await;
        let duration = start.elapsed();

        // Complete database monitoring
        let db_monitor_success = result.is_ok();
        query_execution.complete(db_monitor_success).ok();

        // Record metrics to application monitoring
        let db_metrics = DatabaseMetrics {
            operation_type: operation_type.to_string(),
            table_name: table_name.map(String::from),
            duration,
            rows_affected: 0, // Would need to extract from result in real implementation
            connection_acquisition_time: None, // Would get from pool metrics
            prepared_statement_used: true, // Assuming prepared statements are used
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_database_operation(db_metrics);

        // Complete trace
        self.tracer.complete_trace(trace_context);

        // Log slow queries
        if duration > Duration::from_millis(50) {
            warn!(
                "Slow database query '{}' took {:?} (budget: 50ms)",
                query_name, duration
            );
        }

        result
    }

    /// Sync metrics from database monitor to application metrics
    pub fn sync_metrics(&self) {
        // Get performance report from database monitor
        if let Ok(report) = self.db_monitor.get_performance_report() {
            // Sync connection stats
            self.sync_connection_stats(&report.connection_stats);

            // Sync query stats
            for (query_name, stats) in report.query_stats {
                self.sync_query_stats(&query_name, &stats);
            }
        }
    }

    /// Sync connection pool statistics
    fn sync_connection_stats(&self, stats: &ConnectionStats) {
        debug!(
            "Connection pool stats - Active: {}, Idle: {}, Timeouts: {}",
            stats.current_active_connections,
            stats.current_idle_connections,
            stats.connection_acquisition_timeouts
        );

        // In a real implementation, these would be exported as metrics
        // For now, we'll log warnings for concerning stats
        let total_connections = stats.current_active_connections + stats.current_idle_connections;
        if total_connections > 0 {
            let utilization = stats.current_active_connections as f64 / total_connections as f64;
            if utilization > 0.8 {
                warn!(
                    "High connection pool utilization: {:.1}% ({}/{})",
                    utilization * 100.0,
                    stats.current_active_connections,
                    total_connections
                );
            }
        }

        if stats.connection_acquisition_timeouts > 0 {
            warn!(
                "Connection acquisition timeouts detected: {}",
                stats.connection_acquisition_timeouts
            );
        }
    }

    /// Sync query-specific statistics
    fn sync_query_stats(&self, query_name: &str, stats: &QueryStats) {
        // Log performance issues
        if stats.sla_violations > 0 {
            warn!(
                "Query '{}' has {} SLA violations (p95: {}ms, p99: {}ms)",
                query_name, stats.sla_violations, stats.p95_duration_ms, stats.p99_duration_ms
            );
        }

        if stats.error_count > 0 {
            let error_rate = stats.error_count as f64 / stats.total_executions as f64;
            if error_rate > 0.01 {
                warn!(
                    "Query '{}' has high error rate: {:.2}% ({}/{})",
                    query_name,
                    error_rate * 100.0,
                    stats.error_count,
                    stats.total_executions
                );
            }
        }
    }
}

/// Extension trait for database connections with monitoring
pub trait MonitoredDatabaseExt {
    /// Execute a monitored transaction
    #[allow(async_fn_in_trait)]
    async fn monitored_transaction<T, F, Fut>(
        &self,
        operation_name: &str,
        metrics_collector: Arc<dyn MetricsCollector>,
        tracer: Arc<dyn PerformanceTracer>,
        f: F,
    ) -> Result<T, sqlx::Error>
    where
        F: FnOnce(&mut sqlx::Transaction<'_, sqlx::Postgres>) -> Fut,
        Fut: std::future::Future<Output = Result<T, sqlx::Error>>;
}

impl MonitoredDatabaseExt for sqlx::PgPool {
    async fn monitored_transaction<T, F, Fut>(
        &self,
        operation_name: &str,
        metrics_collector: Arc<dyn MetricsCollector>,
        tracer: Arc<dyn PerformanceTracer>,
        f: F,
    ) -> Result<T, sqlx::Error>
    where
        F: FnOnce(&mut sqlx::Transaction<'_, sqlx::Postgres>) -> Fut,
        Fut: std::future::Future<Output = Result<T, sqlx::Error>>,
    {
        let trace_context = tracer.start_trace(&format!("db:transaction:{operation_name}"));
        let start = std::time::Instant::now();

        // Start transaction
        let mut tx = self.begin().await?;

        // Execute transaction body
        let result = f(&mut tx).await;

        // Commit or rollback based on result
        match result {
            Ok(value) => {
                tx.commit().await?;
                let duration = start.elapsed();

                // Record successful transaction
                let db_metrics = DatabaseMetrics {
                    operation_type: "TRANSACTION".to_string(),
                    table_name: None,
                    duration,
                    rows_affected: 0,
                    connection_acquisition_time: None,
                    prepared_statement_used: true,
                    timestamp: SystemTime::now(),
                };

                metrics_collector.record_database_operation(db_metrics);
                tracer.complete_trace(trace_context);

                debug!(
                    "Transaction '{}' completed successfully in {:?}",
                    operation_name, duration
                );

                Ok(value)
            }
            Err(e) => {
                // Transaction will be rolled back automatically on drop
                let duration = start.elapsed();

                // Record failed transaction
                let db_metrics = DatabaseMetrics {
                    operation_type: "TRANSACTION_ROLLBACK".to_string(),
                    table_name: None,
                    duration,
                    rows_affected: 0,
                    connection_acquisition_time: None,
                    prepared_statement_used: true,
                    timestamp: SystemTime::now(),
                };

                metrics_collector.record_database_operation(db_metrics);

                warn!(
                    "Transaction '{}' failed and was rolled back after {:?}: {}",
                    operation_name, duration, e
                );

                Err(e)
            }
        }
    }
}

/// Helper to trace database operations
pub async fn trace_database_operation<T, F, Fut>(
    tracer: &dyn PerformanceTracer,
    parent: &TraceContext,
    operation: &str,
    f: F,
) -> Result<T, sqlx::Error>
where
    F: FnOnce() -> Fut,
    Fut: std::future::Future<Output = Result<T, sqlx::Error>>,
{
    let context = parent.child(&format!("db:{operation}"));
    let result = f().await;

    let status = match &result {
        Ok(_) => auth_application::monitoring::SpanStatus::Ok,
        Err(e) => auth_application::monitoring::SpanStatus::Error(e.to_string()),
    };

    let mut span =
        auth_application::monitoring::DefaultPerformanceTracer::span_from_context(&context, status);
    span.span_type = auth_application::monitoring::SpanType::Database;

    tracer.record_span(span);
    result
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_application::monitoring::{DefaultPerformanceTracer, InMemoryMetricsCollector};

    // Mock database for testing
    struct MockDatabase;

    #[tokio::test]
    async fn test_monitored_database_creation() {
        let db = MockDatabase;
        let metrics_collector = Arc::new(InMemoryMetricsCollector::new());
        let tracer = Arc::new(DefaultPerformanceTracer::new());

        let monitored = MonitoredDatabase::new(db, metrics_collector, tracer);

        // Verify we can access the inner database
        let _inner = monitored.inner();
    }

    #[tokio::test]
    async fn test_metrics_sync() {
        let db = MockDatabase;
        let metrics_collector = Arc::new(InMemoryMetricsCollector::new());
        let tracer = Arc::new(DefaultPerformanceTracer::new());

        let monitored = MonitoredDatabase::new(db, metrics_collector, tracer);

        // Sync metrics (should not panic even with empty stats)
        monitored.sync_metrics();
    }
}
