// Error sanitizer for removing sensitive information from error messages
// Used to ensure no internal details leak through to API responses

use auth_domain::errors::DomainError;

pub struct ErrorSanitizer;

impl ErrorSanitizer {
    /// Sanitize a DomainError by removing sensitive information from messages
    pub fn sanitize(error: &DomainError) -> DomainError {
        match error {
            DomainError::Database { source, context } => DomainError::Database {
                source: Self::clean_message(source),
                context: Self::clean_message(context),
            },
            DomainError::Cache { operation, reason } => DomainError::Cache {
                operation: operation.clone(), // Operations are typically safe
                reason: Self::clean_message(reason),
            },
            DomainError::Infrastructure(msg) => {
                DomainError::Infrastructure(Self::clean_message(msg))
            }
            DomainError::Configuration(msg) => DomainError::Configuration(Self::clean_message(msg)),
            DomainError::External(msg) => DomainError::External(Self::clean_message(msg)),
            DomainError::InvalidEmail(msg) => DomainError::InvalidEmail(Self::clean_message(msg)),
            DomainError::InvalidPassword(_) => {
                // Never expose password validation details
                DomainError::InvalidPassword("Invalid password".to_string())
            }
            DomainError::InvalidUserId(msg) => DomainError::InvalidUserId(Self::clean_message(msg)),
            DomainError::InvalidRole(msg) => DomainError::InvalidRole(Self::clean_message(msg)),
            DomainError::InvalidInput(msg) => DomainError::InvalidInput(Self::clean_message(msg)),
            DomainError::InvalidId(msg) => DomainError::InvalidId(Self::clean_message(msg)),
            DomainError::CryptoError(_) => {
                // Never expose crypto error details
                DomainError::CryptoError("Cryptographic operation failed".to_string())
            }
            // These errors are already safe
            DomainError::UserAlreadyExists
            | DomainError::UserNotFound
            | DomainError::InvalidSession
            | DomainError::SessionExpired
            | DomainError::AuthenticationFailed
            | DomainError::AuthorizationFailed => error.clone(),
        }
    }

    /// Clean a message by removing sensitive patterns using simple string operations
    fn clean_message(message: &str) -> String {
        let mut cleaned = message.to_string();

        // Check for sensitive keywords and return generic message if found
        let sensitive_keywords = [
            "password",
            "secret",
            "token",
            "key",
            "credential",
            "auth",
            "private",
            "api_key",
            "private_key",
        ];

        let lower_message = cleaned.to_lowercase();
        for keyword in &sensitive_keywords {
            if lower_message.contains(keyword) {
                return "Operation failed due to security constraints".to_string();
            }
        }

        // Remove common sensitive patterns
        cleaned = Self::remove_ip_addresses(&cleaned);
        cleaned = Self::remove_emails(&cleaned);
        cleaned = Self::remove_file_paths(&cleaned);
        cleaned = Self::remove_urls(&cleaned);
        cleaned = Self::remove_uuids(&cleaned);

        // Limit message length to prevent verbose error disclosure
        if cleaned.len() > 200 {
            cleaned.truncate(197);
            cleaned.push_str("...");
        }

        cleaned
    }

    /// Remove IP addresses from the message
    fn remove_ip_addresses(message: &str) -> String {
        // Simple pattern to detect potential IP addresses
        let parts: Vec<&str> = message.split_whitespace().collect();
        let cleaned_parts: Vec<String> = parts
            .iter()
            .map(|part| {
                // Check if it looks like an IP address (4 groups of digits separated by dots)
                let segments: Vec<&str> = part.split('.').collect();
                if segments.len() == 4
                    && segments.iter().all(|s| {
                        s.parse::<u8>().is_ok()
                            || (s.contains(':')
                                && s.split(':').next().unwrap().parse::<u8>().is_ok())
                    })
                {
                    "[REDACTED]".to_string()
                } else {
                    part.to_string()
                }
            })
            .collect();
        cleaned_parts.join(" ")
    }

    /// Remove email addresses from the message
    fn remove_emails(message: &str) -> String {
        let parts: Vec<&str> = message.split_whitespace().collect();
        let cleaned_parts: Vec<String> = parts
            .iter()
            .map(|part| {
                if part.contains('@') && part.contains('.') {
                    "[REDACTED]".to_string()
                } else {
                    part.to_string()
                }
            })
            .collect();
        cleaned_parts.join(" ")
    }

    /// Remove file paths from the message
    fn remove_file_paths(message: &str) -> String {
        let parts: Vec<&str> = message.split_whitespace().collect();
        let cleaned_parts: Vec<String> = parts
            .iter()
            .map(|part| {
                if (part.starts_with('/') || part.starts_with("./") || part.starts_with("../"))
                    && part.contains('/')
                {
                    "[REDACTED]".to_string()
                } else {
                    part.to_string()
                }
            })
            .collect();
        cleaned_parts.join(" ")
    }

    /// Remove URLs from the message
    fn remove_urls(message: &str) -> String {
        let parts: Vec<&str> = message.split_whitespace().collect();
        let cleaned_parts: Vec<String> = parts
            .iter()
            .map(|part| {
                if part.starts_with("http://")
                    || part.starts_with("https://")
                    || part.starts_with("ftp://")
                    || part.starts_with("postgresql://")
                    || part.starts_with("mysql://")
                    || part.starts_with("redis://")
                {
                    "[REDACTED]".to_string()
                } else {
                    part.to_string()
                }
            })
            .collect();
        cleaned_parts.join(" ")
    }

    /// Remove UUIDs from the message
    fn remove_uuids(message: &str) -> String {
        let parts: Vec<&str> = message.split_whitespace().collect();
        let cleaned_parts: Vec<String> = parts
            .iter()
            .map(|part| {
                // Simple UUID detection: 8-4-4-4-12 pattern with hyphens
                let segments: Vec<&str> = part.split('-').collect();
                if segments.len() == 5
                    && segments[0].len() == 8
                    && segments[1].len() == 4
                    && segments[2].len() == 4
                    && segments[3].len() == 4
                    && segments[4].len() == 12
                    && segments
                        .iter()
                        .all(|s| s.chars().all(|c| c.is_ascii_hexdigit()))
                {
                    "[REDACTED]".to_string()
                } else {
                    part.to_string()
                }
            })
            .collect();
        cleaned_parts.join(" ")
    }

    /// Check if an error should be logged vs returned to user
    pub fn is_internal_error(error: &DomainError) -> bool {
        matches!(
            error,
            DomainError::Infrastructure(_)
                | DomainError::Configuration(_)
                | DomainError::Database { .. }
                | DomainError::Cache { .. }
                | DomainError::CryptoError(_)
        )
    }

    /// Get user-friendly message for internal errors
    pub fn get_user_message(error: &DomainError) -> String {
        if Self::is_internal_error(error) {
            "An internal error occurred. Please try again later.".to_string()
        } else {
            error.to_string()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_ip_addresses() {
        let msg = "Connection failed to ***********:5432";
        let cleaned = ErrorSanitizer::clean_message(msg);
        assert_eq!(cleaned, "Connection failed to [REDACTED]");
    }

    #[test]
    fn test_sanitize_email_addresses() {
        let msg = "User <EMAIL> not found";
        let cleaned = ErrorSanitizer::clean_message(msg);
        assert_eq!(cleaned, "User [REDACTED] not found");
    }

    #[test]
    fn test_sanitize_uuids() {
        let msg = "Record 550e8400-e29b-41d4-a716-************ not found";
        let cleaned = ErrorSanitizer::clean_message(msg);
        assert_eq!(cleaned, "Record [REDACTED] not found");
    }

    #[test]
    fn test_sanitize_file_paths() {
        let msg = "Cannot read file /etc/app/config.toml";
        let cleaned = ErrorSanitizer::clean_message(msg);
        assert_eq!(cleaned, "Cannot read file [REDACTED]");
    }

    #[test]
    fn test_sanitize_credentials_in_urls() {
        let msg = "Failed to connect to postgresql://user:pass@localhost/db";
        let cleaned = ErrorSanitizer::clean_message(msg);
        assert_eq!(cleaned, "Failed to connect to [REDACTED]");
    }

    #[test]
    fn test_sanitize_password_errors() {
        let error =
            DomainError::InvalidPassword("Password must be at least 8 characters".to_string());
        let sanitized = ErrorSanitizer::sanitize(&error);
        match sanitized {
            DomainError::InvalidPassword(msg) => assert_eq!(msg, "Invalid password"),
            _ => panic!("Wrong error type"),
        }
    }

    #[test]
    fn test_sanitize_crypto_errors() {
        let error = DomainError::CryptoError("AES decryption failed: invalid padding".to_string());
        let sanitized = ErrorSanitizer::sanitize(&error);
        match sanitized {
            DomainError::CryptoError(msg) => {
                assert_eq!(msg, "Cryptographic operation failed")
            }
            _ => panic!("Wrong error type"),
        }
    }

    #[test]
    fn test_sensitive_keyword_detection() {
        let messages = vec![
            "Invalid password provided",
            "Secret key not found",
            "Authentication token expired",
            "Private key validation failed",
        ];

        for msg in messages {
            let cleaned = ErrorSanitizer::clean_message(msg);
            assert_eq!(cleaned, "Operation failed due to security constraints");
        }
    }

    #[test]
    fn test_message_length_truncation() {
        let long_msg = "A".repeat(250);
        let cleaned = ErrorSanitizer::clean_message(&long_msg);
        assert_eq!(cleaned.len(), 200);
        assert!(cleaned.ends_with("..."));
    }

    #[test]
    fn test_is_internal_error() {
        assert!(ErrorSanitizer::is_internal_error(
            &DomainError::Infrastructure("test".to_string())
        ));
        assert!(ErrorSanitizer::is_internal_error(
            &DomainError::Configuration("test".to_string())
        ));
        assert!(ErrorSanitizer::is_internal_error(&DomainError::Database {
            source: "test".to_string(),
            context: "test".to_string()
        }));
        assert!(!ErrorSanitizer::is_internal_error(
            &DomainError::UserNotFound
        ));
        assert!(!ErrorSanitizer::is_internal_error(
            &DomainError::AuthenticationFailed
        ));
    }

    #[test]
    fn test_user_friendly_messages() {
        let internal_error = DomainError::Infrastructure("Connection pool exhausted".to_string());
        assert_eq!(
            ErrorSanitizer::get_user_message(&internal_error),
            "An internal error occurred. Please try again later."
        );

        let user_error = DomainError::UserNotFound;
        assert_eq!(
            ErrorSanitizer::get_user_message(&user_error),
            "User not found"
        );
    }
}
