// Example usage of error mapping utilities
// This module demonstrates how to use the error mapping in infrastructure code

#[cfg(test)]
mod tests {
    use crate::errors::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ToDomainError};
    use auth_domain::errors::DomainError;

    // Example infrastructure function that uses error mapping
    async fn example_database_operation() -> Result<String, DomainError> {
        // Simulate a database operation that might fail
        let result: Result<String, sqlx::Error> = Err(sqlx::Error::RowNotFound);

        // Convert sqlx error to domain error using the trait
        result.map_err(|e| e.to_domain_error())
    }

    // Example of sanitizing errors before returning to users
    async fn example_api_handler() -> Result<String, String> {
        match example_database_operation().await {
            Ok(data) => Ok(data),
            Err(domain_error) => {
                // Sanitize the error before returning to user
                let sanitized = ErrorSanitizer::sanitize(&domain_error);

                // For internal errors, return generic message
                if ErrorSanitizer::is_internal_error(&sanitized) {
                    Err(ErrorSanitizer::get_user_message(&sanitized))
                } else {
                    Err(sanitized.to_string())
                }
            }
        }
    }

    #[tokio::test]
    async fn test_error_mapping_usage() {
        let result = example_api_handler().await;
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), "User not found");
    }

    // Example: Mapping Redis errors
    async fn example_cache_operation() -> Result<String, DomainError> {
        use redis::RedisError;

        // Simulate a Redis error
        let redis_err = RedisError::from((redis::ErrorKind::IoError, "Connection refused"));

        // Convert to domain error
        Err(redis_err.to_domain_error())
    }

    #[tokio::test]
    async fn test_redis_error_mapping() {
        let result = example_cache_operation().await;
        assert!(result.is_err());

        match result.unwrap_err() {
            DomainError::Cache { operation, reason } => {
                assert_eq!(operation, "cache_access");
                assert_eq!(reason, "Cache service temporarily unavailable");
            }
            _ => panic!("Wrong error type"),
        }
    }

    // Example: Handling sensitive information
    #[test]
    fn test_sensitive_info_removal() {
        let error = DomainError::Database {
            source: "duplicate key value violates unique constraint users_email_key".to_string(),
            context: "Failed to insert user <NAME_EMAIL>".to_string(),
        };

        let sanitized = ErrorSanitizer::sanitize(&error);

        match sanitized {
            DomainError::Database { source, context } => {
                assert!(!source.contains("users_email_key"));
                assert!(!context.contains("<EMAIL>"));
            }
            _ => panic!("Wrong error type"),
        }
    }
}
