// Error mapping implementations from infrastructure to domain errors
// Ensures no sensitive information leaks through error messages

use auth_domain::errors::DomainError;
use tracing::{error, warn};

// Helper trait for mapping infrastructure errors to domain errors
pub trait ToDomainError {
    fn to_domain_error(self) -> DomainError;
}

// sqlx::Error -> DomainError
impl ToDomainError for sqlx::Error {
    fn to_domain_error(self) -> DomainError {
        // Log full error details securely
        let err = &self;
        error!(
            error = %err,
            error_type = ?err,
            "Database error occurred"
        );

        // Map to domain error with sanitized message
        match err {
            sqlx::Error::RowNotFound => DomainError::UserNotFound,
            sqlx::Error::Database(db_err) => {
                // Check for specific constraint violations
                if let Some(constraint) = db_err.constraint() {
                    match constraint {
                        "users_email_key" | "users_email_unique" => DomainError::UserAlreadyExists,
                        "users_username_key" | "users_username_unique" => {
                            DomainError::UserAlreadyExists
                        }
                        _ => DomainError::Database {
                            source: "Database constraint violation".to_string(),
                            context: "Operation failed due to data integrity rules".to_string(),
                        },
                    }
                } else {
                    DomainError::Database {
                        source: "Database operation failed".to_string(),
                        context: sanitize_database_context(&db_err.to_string()),
                    }
                }
            }
            sqlx::Error::PoolTimedOut => DomainError::Infrastructure(
                "Service temporarily unavailable. Please try again.".to_string(),
            ),
            sqlx::Error::PoolClosed => DomainError::Infrastructure(
                "Service is shutting down. Please try again later.".to_string(),
            ),
            _ => DomainError::Database {
                source: "Database operation failed".to_string(),
                context: "An unexpected error occurred".to_string(),
            },
        }
    }
}

// redis::RedisError -> DomainError
impl ToDomainError for redis::RedisError {
    fn to_domain_error(self) -> DomainError {
        // Log full error details securely
        let err = &self;
        error!(
            error = %err,
            error_kind = ?err.kind(),
            "Redis cache error occurred"
        );

        // Map to domain error with sanitized message
        match err.kind() {
            redis::ErrorKind::IoError => DomainError::Cache {
                operation: "cache_access".to_string(),
                reason: "Cache service temporarily unavailable".to_string(),
            },
            redis::ErrorKind::TypeError => DomainError::Cache {
                operation: "cache_operation".to_string(),
                reason: "Invalid cache operation".to_string(),
            },
            redis::ErrorKind::AuthenticationFailed => {
                warn!("Redis authentication failed - check configuration");
                DomainError::Configuration("Cache service configuration error".to_string())
            }
            _ => DomainError::Cache {
                operation: "cache_operation".to_string(),
                reason: "Cache operation failed".to_string(),
            },
        }
    }
}

// std::io::Error -> DomainError
impl ToDomainError for std::io::Error {
    fn to_domain_error(self) -> DomainError {
        // Log full error details securely
        let err = &self;
        error!(
            error = %err,
            error_kind = ?err.kind(),
            "IO error occurred"
        );

        // Map to domain error with sanitized message
        match err.kind() {
            std::io::ErrorKind::NotFound => {
                DomainError::Configuration("Required resource not found".to_string())
            }
            std::io::ErrorKind::PermissionDenied => {
                DomainError::Configuration("Insufficient permissions".to_string())
            }
            std::io::ErrorKind::ConnectionRefused => {
                DomainError::External("External service unavailable".to_string())
            }
            std::io::ErrorKind::TimedOut => {
                DomainError::External("External service timeout".to_string())
            }
            _ => DomainError::Infrastructure("System error occurred".to_string()),
        }
    }
}

// serde_json::Error -> DomainError
impl ToDomainError for serde_json::Error {
    fn to_domain_error(self) -> DomainError {
        // Log error details
        let err = &self;
        warn!(
            error = %err,
            "Serialization/deserialization error"
        );

        DomainError::Configuration("Data format error".to_string())
    }
}

// reqwest::Error -> DomainError (for HTTP client errors)
impl ToDomainError for reqwest::Error {
    fn to_domain_error(self) -> DomainError {
        // Log full error details securely
        let err = &self;
        error!(
            error = %err,
            is_timeout = err.is_timeout(),
            is_connect = err.is_connect(),
            "HTTP client error occurred"
        );

        // Map to domain error with sanitized message
        if err.is_timeout() {
            DomainError::External("External service request timeout".to_string())
        } else if err.is_connect() {
            DomainError::External("Unable to connect to external service".to_string())
        } else if err.is_status() {
            if let Some(status) = err.status() {
                match status.as_u16() {
                    401 | 403 => DomainError::AuthorizationFailed,
                    429 => DomainError::External("Rate limit exceeded".to_string()),
                    500..=599 => DomainError::External("External service error".to_string()),
                    _ => DomainError::External("External service request failed".to_string()),
                }
            } else {
                DomainError::External("External service request failed".to_string())
            }
        } else {
            DomainError::External("External service communication error".to_string())
        }
    }
}

// Note: Email service error mapping would be implemented when lettre is added

// Helper function to sanitize database context messages
fn sanitize_database_context(raw_message: &str) -> String {
    // Remove potentially sensitive information from database errors
    if raw_message.contains("duplicate key") {
        "Duplicate entry detected".to_string()
    } else if raw_message.contains("foreign key") {
        "Related data constraint violation".to_string()
    } else if raw_message.contains("null value") {
        "Required field missing".to_string()
    } else if raw_message.contains("check constraint") {
        "Data validation failed".to_string()
    } else {
        "Database operation failed".to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sqlx_error_mapping_row_not_found() {
        // This would require creating actual sqlx errors in tests
        // For now, we'll test the sanitization function
        let context = sanitize_database_context("duplicate key value violates unique constraint");
        assert_eq!(context, "Duplicate entry detected");
    }

    #[test]
    fn test_database_context_sanitization() {
        assert_eq!(
            sanitize_database_context("duplicate key violation"),
            "Duplicate entry detected"
        );
        assert_eq!(
            sanitize_database_context("foreign key constraint failed"),
            "Related data constraint violation"
        );
        assert_eq!(
            sanitize_database_context("null value in column"),
            "Required field missing"
        );
        assert_eq!(
            sanitize_database_context("check constraint violation"),
            "Data validation failed"
        );
        assert_eq!(
            sanitize_database_context("some other error"),
            "Database operation failed"
        );
    }

    #[test]
    fn test_no_sensitive_info_leak() {
        // Ensure sensitive patterns are not exposed
        let sensitive_contexts = vec![
            "password column at row 5",
            "user_id=12345 not found",
            "connection string ***************************",
            "table 'secret_data' does not exist",
        ];

        for context in sensitive_contexts {
            let sanitized = sanitize_database_context(context);
            assert_eq!(sanitized, "Database operation failed");
            assert!(!sanitized.contains("password"));
            assert!(!sanitized.contains("user_id"));
            assert!(!sanitized.contains("postgresql"));
            assert!(!sanitized.contains("secret"));
        }
    }
}
