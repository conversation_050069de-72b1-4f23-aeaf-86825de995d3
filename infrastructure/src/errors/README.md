# Infrastructure Error Mapping Module

This module provides secure error mapping from infrastructure errors to domain errors, following the Infrastructure Recovery Plan requirements.

## Key Components

### 1. Error Mapping (`error_mapping.rs`)
- Implements `ToDomainError` trait for mapping infrastructure errors to domain errors
- Maps common infrastructure errors:
  - `sqlx::Error` → `DomainError` variants
  - `redis::RedisError` → `DomainError::Cache`
  - `std::io::Error` → `DomainError::Infrastructure/Configuration/External`
  - `serde_json::Error` → `DomainError::Configuration`
  - `reqwest::Error` → `DomainError::External/AuthorizationFailed`

### 2. Error Sanitizer (`sanitizer.rs`)
- Removes sensitive information from error messages
- Detects and redacts:
  - IP addresses
  - Email addresses
  - UUIDs
  - File paths
  - URLs with credentials
  - Sensitive keywords (password, secret, token, etc.)
- Provides user-friendly messages for internal errors
- Implements message length limiting (200 chars max)

## Security Features

1. **No Information Disclosure**
   - All infrastructure errors are logged with full details using `tracing`
   - User-facing errors are sanitized to remove sensitive data
   - Internal errors return generic messages

2. **Constant-Time Considerations**
   - Error messages don't reveal timing information
   - Authentication failures return consistent messages
   - No distinguishing between "user not found" vs "wrong password"

3. **Pattern Detection**
   - Simple string-based pattern matching (no regex dependency)
   - Efficient whitespace-based tokenization
   - Conservative approach: when in doubt, redact

## Usage Example

```rust
use infrastructure::errors::{ToDomainError, ErrorSanitizer};

// In repository implementation
async fn find_user(id: UserId) -> Result<User, DomainError> {
    sqlx::query_as("SELECT * FROM users WHERE id = $1")
        .bind(&id)
        .fetch_one(&pool)
        .await
        .map_err(|e| e.to_domain_error())  // Converts sqlx::Error to DomainError
}

// In API handler
async fn handle_request() -> Result<Response, ApiError> {
    match service.perform_operation().await {
        Ok(result) => Ok(Response::success(result)),
        Err(domain_error) => {
            // Sanitize error before returning to user
            let sanitized = ErrorSanitizer::sanitize(&domain_error);
            
            // Log the original error for debugging
            tracing::error!(?domain_error, "Operation failed");
            
            // Return appropriate user message
            if ErrorSanitizer::is_internal_error(&sanitized) {
                Err(ApiError::internal(ErrorSanitizer::get_user_message(&sanitized)))
            } else {
                Err(ApiError::from(sanitized))
            }
        }
    }
}
```

## Testing

The module includes comprehensive tests for:
- Error mapping correctness
- Sensitive pattern detection
- Message sanitization
- User-friendly message generation

Run tests with:
```bash
cargo test -p auth-infrastructure errors::
```

## Performance Considerations

- No regex dependency for better performance
- Simple string operations for pattern matching
- Minimal allocations during error conversion
- Efficient for high-throughput error handling

## Future Enhancements

1. Add support for more infrastructure error types as needed
2. Implement rate-limited error logging to prevent log flooding
3. Add metrics for error frequency tracking
4. Consider adding error categorization for better monitoring