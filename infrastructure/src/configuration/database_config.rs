// Database configuration
// Configuration for database connections and settings

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct DatabaseConfig {
    pub database_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout_seconds: u64,
    pub idle_timeout_seconds: u64,
    pub max_lifetime_seconds: u64,
    pub run_migrations: bool,
    pub database_type: DatabaseType,
}

#[derive(Debu<PERSON>, <PERSON>lone, PartialEq, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum DatabaseType {
    LibSql,
    PostgreSql,
    Sqlite,
}

impl DatabaseConfig {
    /// Create configuration for testing
    pub fn test_config() -> Self {
        DatabaseConfig {
            database_url: ":memory:".to_string(), // In-memory SQLite for tests
            max_connections: 5,
            min_connections: 1,
            connection_timeout_seconds: 10,
            idle_timeout_seconds: 60,
            max_lifetime_seconds: 3600,
            run_migrations: true,
            database_type: DatabaseType::Sqlite,
        }
    }

    /// Create configuration for local development
    pub fn development_config() -> Self {
        DatabaseConfig {
            database_url: "libsql://localhost:8080".to_string(),
            max_connections: 10,
            min_connections: 2,
            connection_timeout_seconds: 30,
            idle_timeout_seconds: 300,
            max_lifetime_seconds: 1800,
            run_migrations: true,
            database_type: DatabaseType::LibSql,
        }
    }

    /// Create configuration for production
    pub fn production_config(database_url: String) -> Self {
        DatabaseConfig {
            database_url,
            max_connections: 100,
            min_connections: 10,
            connection_timeout_seconds: 30,
            idle_timeout_seconds: 600,
            max_lifetime_seconds: 3600,
            run_migrations: false, // Migrations should be run separately in production
            database_type: DatabaseType::PostgreSql,
        }
    }

    /// Validate database configuration
    pub fn validate(&self) -> Result<(), String> {
        if self.database_url.is_empty() {
            return Err("Database URL cannot be empty".to_string());
        }

        if self.max_connections == 0 {
            return Err("Max connections must be greater than 0".to_string());
        }

        if self.min_connections > self.max_connections {
            return Err("Min connections cannot be greater than max connections".to_string());
        }

        if self.connection_timeout_seconds == 0 {
            return Err("Connection timeout must be greater than 0".to_string());
        }

        // Validate URL format based on database type
        match self.database_type {
            DatabaseType::LibSql => {
                if !self.database_url.starts_with("libsql://")
                    && !self.database_url.starts_with("file:")
                    && self.database_url != ":memory:"
                {
                    return Err("Invalid libSQL URL format".to_string());
                }
            }
            DatabaseType::PostgreSql => {
                if !self.database_url.starts_with("postgres://")
                    && !self.database_url.starts_with("postgresql://")
                {
                    return Err("Invalid PostgreSQL URL format".to_string());
                }
            }
            DatabaseType::Sqlite => {
                if !self.database_url.starts_with("sqlite://")
                    && !self.database_url.starts_with("file:")
                    && self.database_url != ":memory:"
                {
                    return Err("Invalid SQLite URL format".to_string());
                }
            }
        }

        Ok(())
    }

    /// Get the connection pool options
    pub fn pool_options(&self) -> DatabasePoolOptions {
        DatabasePoolOptions {
            max_connections: self.max_connections,
            min_connections: self.min_connections,
            connection_timeout: std::time::Duration::from_secs(self.connection_timeout_seconds),
            idle_timeout: std::time::Duration::from_secs(self.idle_timeout_seconds),
            max_lifetime: std::time::Duration::from_secs(self.max_lifetime_seconds),
        }
    }

    /// Check if this is an in-memory database
    pub fn is_in_memory(&self) -> bool {
        self.database_url == ":memory:"
    }

    /// Check if this is a file-based database
    pub fn is_file_based(&self) -> bool {
        self.database_url.starts_with("file:")
            || self.database_url.starts_with("sqlite://")
            || (!self.database_url.contains("://") && self.database_url.ends_with(".db"))
    }

    /// Get database name from URL
    pub fn database_name(&self) -> Option<String> {
        if self.is_in_memory() {
            return Some("memory".to_string());
        }

        // Extract database name from URL
        if let Some(url) = self.database_url.split('/').next_back() {
            if let Some(name) = url.split('?').next() {
                if !name.is_empty() {
                    return Some(name.to_string());
                }
            }
        }

        None
    }
}

#[derive(Debug, Clone)]
pub struct DatabasePoolOptions {
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: std::time::Duration,
    pub idle_timeout: std::time::Duration,
    pub max_lifetime: std::time::Duration,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        DatabaseConfig {
            database_url: "libsql://localhost:8080".to_string(),
            max_connections: 20,
            min_connections: 5,
            connection_timeout_seconds: 30,
            idle_timeout_seconds: 300,
            max_lifetime_seconds: 1800,
            run_migrations: true,
            database_type: DatabaseType::LibSql,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = DatabaseConfig::default();
        assert!(config.validate().is_ok());
        assert_eq!(config.database_type, DatabaseType::LibSql);
        assert!(config.run_migrations);
    }

    #[test]
    fn test_test_config() {
        let config = DatabaseConfig::test_config();
        assert!(config.validate().is_ok());
        assert!(config.is_in_memory());
        assert_eq!(config.database_name(), Some("memory".to_string()));
    }

    #[test]
    fn test_production_config() {
        let config = DatabaseConfig::production_config(
            "postgresql://user:pass@localhost/authdb".to_string(),
        );
        assert!(config.validate().is_ok());
        assert_eq!(config.database_type, DatabaseType::PostgreSql);
        assert!(!config.run_migrations);
        assert_eq!(config.database_name(), Some("authdb".to_string()));
    }

    #[test]
    #[allow(clippy::field_reassign_with_default)]
    fn test_validation_empty_url() {
        let mut config = DatabaseConfig::default();
        config.database_url = "".to_string();
        assert!(config.validate().is_err());
    }

    #[test]
    #[allow(clippy::field_reassign_with_default)]
    fn test_validation_zero_max_connections() {
        let mut config = DatabaseConfig::default();
        config.max_connections = 0;
        assert!(config.validate().is_err());
    }

    #[test]
    #[allow(clippy::field_reassign_with_default)]
    fn test_validation_min_greater_than_max() {
        let mut config = DatabaseConfig::default();
        config.min_connections = 10;
        config.max_connections = 5;
        assert!(config.validate().is_err());
    }

    #[test]
    #[allow(clippy::field_reassign_with_default)]
    fn test_validation_invalid_postgres_url() {
        let mut config = DatabaseConfig::default();
        config.database_type = DatabaseType::PostgreSql;
        config.database_url = "invalid://url".to_string();
        assert!(config.validate().is_err());
    }

    #[test]
    #[allow(clippy::field_reassign_with_default)]
    fn test_is_in_memory() {
        let config = DatabaseConfig::test_config();
        assert!(config.is_in_memory());

        let mut config = DatabaseConfig::default();
        config.database_url = "file:test.db".to_string();
        assert!(!config.is_in_memory());
    }

    #[test]
    #[allow(clippy::field_reassign_with_default)]
    fn test_is_file_based() {
        let mut config = DatabaseConfig::default();
        config.database_url = "file:test.db".to_string();
        assert!(config.is_file_based());

        config.database_url = "sqlite:///path/to/db.db".to_string();
        assert!(config.is_file_based());

        config.database_url = "postgresql://localhost/db".to_string();
        assert!(!config.is_file_based());
    }

    #[test]
    fn test_pool_options() {
        let config = DatabaseConfig::default();
        let options = config.pool_options();

        assert_eq!(options.max_connections, config.max_connections);
        assert_eq!(options.min_connections, config.min_connections);
        assert_eq!(
            options.connection_timeout.as_secs(),
            config.connection_timeout_seconds
        );
    }
}
