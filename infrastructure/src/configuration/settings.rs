// Main application settings
// Strongly-typed configuration with figment

use super::{DatabaseConfig, SecurityConfig};
use figment::{
    Figment,
    providers::{Env, Format, Toml},
};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Settings {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub security: SecurityConfig,
    pub cache: CacheConfig,
    pub email: EmailConfig,
    pub observability: ObservabilityConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub workers: Option<usize>,
    pub max_connections: Option<usize>,
    pub timeout_seconds: Option<u64>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CacheConfig {
    pub redis_url: String,
    pub max_connections: u32,
    pub timeout_seconds: u64,
    pub default_ttl_seconds: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct EmailConfig {
    pub smtp_host: String,
    pub smtp_port: u16,
    pub smtp_username: String,
    pub smtp_password: String,
    pub from_email: String,
    pub from_name: String,
    pub use_tls: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ObservabilityConfig {
    pub tracing_level: String,
    pub jaeger_endpoint: Option<String>,
    pub metrics_enabled: bool,
    pub log_format: LogFormat,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum LogFormat {
    Json,
    Pretty,
    Compact,
}

impl Settings {
    /// Load configuration from files and environment variables
    pub fn load() -> Result<Self, Box<figment::Error>> {
        Figment::new()
            .merge(Toml::file("configuration.toml"))
            .merge(Toml::file("local.toml")) // Optional local overrides
            .merge(Env::prefixed("AUTH_SERVICE_"))
            .extract()
            .map_err(Box::new)
    }

    /// Load configuration for testing with sensible defaults
    pub fn load_test() -> Self {
        Settings {
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 0, // Random port for tests
                workers: Some(1),
                max_connections: Some(10),
                timeout_seconds: Some(30),
            },
            database: DatabaseConfig::test_config(),
            security: SecurityConfig::test_config(),
            cache: CacheConfig {
                redis_url: "redis://127.0.0.1:6379".to_string(),
                max_connections: 10,
                timeout_seconds: 5,
                default_ttl_seconds: 300,
            },
            email: EmailConfig {
                smtp_host: "localhost".to_string(),
                smtp_port: 1025, // MailHog default
                smtp_username: "test".to_string(),
                smtp_password: "test".to_string(),
                from_email: "<EMAIL>".to_string(),
                from_name: "Test Service".to_string(),
                use_tls: false,
            },
            observability: ObservabilityConfig {
                tracing_level: "debug".to_string(),
                jaeger_endpoint: None,
                metrics_enabled: false,
                log_format: LogFormat::Pretty,
            },
        }
    }

    /// Validate configuration settings
    pub fn validate(&self) -> Result<(), String> {
        // Validate server config
        if self.server.port == 0 && self.server.host != "127.0.0.1" {
            return Err("Port 0 is only allowed for localhost".to_string());
        }

        // Validate database config
        self.database.validate()?;

        // Validate security config
        self.security.validate()?;

        // Validate cache config
        if self.cache.max_connections == 0 {
            return Err("Cache max_connections must be greater than 0".to_string());
        }

        // Validate email config
        if self.email.smtp_host.is_empty() {
            return Err("SMTP host cannot be empty".to_string());
        }

        if !self.email.from_email.contains('@') {
            return Err("Invalid from_email format".to_string());
        }

        Ok(())
    }

    /// Get the server bind address
    pub fn server_address(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }

    /// Check if running in development mode
    pub fn is_development(&self) -> bool {
        self.server.host == "127.0.0.1" || self.server.host == "localhost"
    }

    /// Check if TLS is required
    pub fn requires_tls(&self) -> bool {
        !self.is_development() && self.server.port == 443
    }
}

impl Default for Settings {
    fn default() -> Self {
        Settings {
            server: ServerConfig {
                host: "0.0.0.0".to_string(),
                port: 8080,
                workers: None, // Auto-detect
                max_connections: Some(1000),
                timeout_seconds: Some(60),
            },
            database: DatabaseConfig::default(),
            security: SecurityConfig::default(),
            cache: CacheConfig {
                redis_url: "redis://localhost:6379".to_string(),
                max_connections: 100,
                timeout_seconds: 5,
                default_ttl_seconds: 3600,
            },
            email: EmailConfig {
                smtp_host: "localhost".to_string(),
                smtp_port: 587,
                smtp_username: "".to_string(),
                smtp_password: "".to_string(),
                from_email: "<EMAIL>".to_string(),
                from_name: "AuthService".to_string(),
                use_tls: true,
            },
            observability: ObservabilityConfig {
                tracing_level: "info".to_string(),
                jaeger_endpoint: None,
                metrics_enabled: true,
                log_format: LogFormat::Json,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_settings() {
        let settings = Settings::default();
        assert_eq!(settings.server.host, "0.0.0.0");
        assert_eq!(settings.server.port, 8080);
        assert!(settings.validate().is_ok());
    }

    #[test]
    fn test_test_settings() {
        let settings = Settings::load_test();
        assert_eq!(settings.server.host, "127.0.0.1");
        assert_eq!(settings.server.port, 0);
        assert!(settings.is_development());
        assert!(settings.validate().is_ok());
    }

    #[test]
    fn test_server_address() {
        let settings = Settings::default();
        assert_eq!(settings.server_address(), "0.0.0.0:8080");
    }

    #[test]
    fn test_validation_invalid_email() {
        let mut settings = Settings::default();
        settings.email.from_email = "invalid-email".to_string();
        assert!(settings.validate().is_err());
    }

    #[test]
    fn test_validation_zero_cache_connections() {
        let mut settings = Settings::default();
        settings.cache.max_connections = 0;
        assert!(settings.validate().is_err());
    }

    #[test]
    fn test_requires_tls() {
        let mut settings = Settings::default();
        settings.server.port = 443;
        settings.server.host = "example.com".to_string();
        assert!(settings.requires_tls());

        settings.server.host = "127.0.0.1".to_string();
        assert!(!settings.requires_tls()); // Development mode
    }
}
