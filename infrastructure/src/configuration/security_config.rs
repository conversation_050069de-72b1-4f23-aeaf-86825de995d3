// Security configuration
// Configuration for authentication, authorization, and security features

use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct SecurityConfig {
    pub jwt: JwtConfig,
    pub password: PasswordConfig,
    pub session: SessionConfig,
    pub rate_limiting: RateLimitingConfig,
    pub oauth: OAuthConfig,
    pub mfa: MfaConfig,
    pub webauthn: WebAuthnConfig,
}

#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct JwtConfig {
    pub secret_key: String,
    pub algorithm: JwtAlgorithm,
    pub access_token_ttl_seconds: u64,
    pub refresh_token_ttl_seconds: u64,
    pub issuer: String,
    pub audience: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "UPPERCASE")]
pub enum JwtAlgorithm {
    HS256,
    HS384,
    HS512,
    RS256,
    RS384,
    <PERSON>512,
    <PERSON><PERSON>256,
    <PERSON><PERSON>384,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct PasswordConfig {
    pub min_length: usize,
    pub max_length: usize,
    pub require_uppercase: bool,
    pub require_lowercase: bool,
    pub require_digits: bool,
    pub require_special_chars: bool,
    pub min_strength_score: u8,
    pub max_age_days: Option<u64>,
    pub history_size: usize,
    pub argon2_memory_cost: u32,
    pub argon2_time_cost: u32,
    pub argon2_parallelism: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SessionConfig {
    pub default_ttl_seconds: u64,
    pub remember_me_ttl_seconds: u64,
    pub max_concurrent_sessions: usize,
    pub cleanup_interval_seconds: u64,
    pub secure_cookies: bool,
    pub same_site_policy: SameSitePolicy,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "PascalCase")]
pub enum SameSitePolicy {
    Strict,
    Lax,
    None,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RateLimitingConfig {
    pub enabled: bool,
    pub login_attempts: RateLimit,
    pub registration_attempts: RateLimit,
    pub password_reset_attempts: RateLimit,
    pub api_requests: RateLimit,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RateLimit {
    pub max_attempts: u32,
    pub window_seconds: u64,
    pub lockout_duration_seconds: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct OAuthConfig {
    pub enabled: bool,
    pub providers: Vec<OAuthProvider>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct OAuthProvider {
    pub name: String,
    pub client_id: String,
    pub client_secret: String,
    pub authorization_url: String,
    pub token_url: String,
    pub user_info_url: String,
    pub scopes: Vec<String>,
    pub enabled: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct MfaConfig {
    pub enabled: bool,
    pub required_for_admin: bool,
    pub totp: TotpConfig,
    pub sms: SmsConfig,
    pub email: EmailMfaConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct TotpConfig {
    pub enabled: bool,
    pub issuer: String,
    pub algorithm: TotpAlgorithm,
    pub digits: u32,
    pub period_seconds: u64,
    pub skew_tolerance: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "UPPERCASE")]
pub enum TotpAlgorithm {
    SHA1,
    SHA256,
    SHA512,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SmsConfig {
    pub enabled: bool,
    pub provider: String,
    pub api_key: String,
    pub from_number: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct EmailMfaConfig {
    pub enabled: bool,
    pub code_length: usize,
    pub code_ttl_seconds: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct WebAuthnConfig {
    pub enabled: bool,
    pub rp_id: String,
    pub rp_name: String,
    pub rp_origin: String,
    pub timeout_seconds: u64,
    pub user_verification: UserVerificationRequirement,
    pub authenticator_attachment: Option<AuthenticatorAttachment>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum UserVerificationRequirement {
    Required,
    Preferred,
    Discouraged,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "kebab-case")]
pub enum AuthenticatorAttachment {
    Platform,
    CrossPlatform,
}

impl SecurityConfig {
    /// Create configuration for testing
    pub fn test_config() -> Self {
        SecurityConfig {
            jwt: JwtConfig {
                secret_key: "test-secret-key-for-testing-only".to_string(),
                algorithm: JwtAlgorithm::HS256,
                access_token_ttl_seconds: 900,    // 15 minutes
                refresh_token_ttl_seconds: 86400, // 24 hours
                issuer: "auth-service-test".to_string(),
                audience: "auth-service-test".to_string(),
            },
            password: PasswordConfig {
                min_length: 8,
                max_length: 128,
                require_uppercase: true,
                require_lowercase: true,
                require_digits: true,
                require_special_chars: false,
                min_strength_score: 60,
                max_age_days: None,
                history_size: 5,
                argon2_memory_cost: 4096, // 4 MB
                argon2_time_cost: 3,
                argon2_parallelism: 1,
            },
            session: SessionConfig {
                default_ttl_seconds: 3600,           // 1 hour
                remember_me_ttl_seconds: 86400 * 30, // 30 days
                max_concurrent_sessions: 5,
                cleanup_interval_seconds: 3600,
                secure_cookies: false, // For testing
                same_site_policy: SameSitePolicy::Lax,
            },
            rate_limiting: RateLimitingConfig {
                enabled: false, // Disabled for tests
                login_attempts: RateLimit {
                    max_attempts: 5,
                    window_seconds: 300,
                    lockout_duration_seconds: 900,
                },
                registration_attempts: RateLimit {
                    max_attempts: 3,
                    window_seconds: 3600,
                    lockout_duration_seconds: 3600,
                },
                password_reset_attempts: RateLimit {
                    max_attempts: 3,
                    window_seconds: 3600,
                    lockout_duration_seconds: 3600,
                },
                api_requests: RateLimit {
                    max_attempts: 100,
                    window_seconds: 60,
                    lockout_duration_seconds: 60,
                },
            },
            oauth: OAuthConfig {
                enabled: false,
                providers: vec![],
            },
            mfa: MfaConfig {
                enabled: false,
                required_for_admin: false,
                totp: TotpConfig {
                    enabled: false,
                    issuer: "AuthService Test".to_string(),
                    algorithm: TotpAlgorithm::SHA1,
                    digits: 6,
                    period_seconds: 30,
                    skew_tolerance: 1,
                },
                sms: SmsConfig {
                    enabled: false,
                    provider: "test".to_string(),
                    api_key: "test-key".to_string(),
                    from_number: "+**********".to_string(),
                },
                email: EmailMfaConfig {
                    enabled: false,
                    code_length: 6,
                    code_ttl_seconds: 300,
                },
            },
            webauthn: WebAuthnConfig {
                enabled: false,
                rp_id: "localhost".to_string(),
                rp_name: "AuthService Test".to_string(),
                rp_origin: "http://localhost:3000".to_string(),
                timeout_seconds: 60,
                user_verification: UserVerificationRequirement::Preferred,
                authenticator_attachment: None,
            },
        }
    }

    /// Validate security configuration
    pub fn validate(&self) -> Result<(), String> {
        // Validate JWT config
        if self.jwt.secret_key.is_empty() {
            return Err("JWT secret key cannot be empty".to_string());
        }

        if self.jwt.secret_key.len() < 32 {
            return Err("JWT secret key must be at least 32 characters".to_string());
        }

        if self.jwt.access_token_ttl_seconds == 0 {
            return Err("JWT access token TTL must be greater than 0".to_string());
        }

        // Validate password config
        if self.password.min_length == 0 {
            return Err("Password minimum length must be greater than 0".to_string());
        }

        if self.password.min_length > self.password.max_length {
            return Err(
                "Password minimum length cannot be greater than maximum length".to_string(),
            );
        }

        if self.password.min_strength_score > 100 {
            return Err("Password minimum strength score cannot exceed 100".to_string());
        }

        // Validate session config
        if self.session.default_ttl_seconds == 0 {
            return Err("Session default TTL must be greater than 0".to_string());
        }

        if self.session.max_concurrent_sessions == 0 {
            return Err("Maximum concurrent sessions must be greater than 0".to_string());
        }

        // Validate rate limiting config
        if self.rate_limiting.enabled && self.rate_limiting.login_attempts.max_attempts == 0 {
            return Err("Login rate limit max attempts must be greater than 0".to_string());
        }

        // Validate WebAuthn config if enabled
        if self.webauthn.enabled {
            if self.webauthn.rp_id.is_empty() {
                return Err("WebAuthn RP ID cannot be empty when WebAuthn is enabled".to_string());
            }

            if self.webauthn.rp_name.is_empty() {
                return Err("WebAuthn RP name cannot be empty when WebAuthn is enabled".to_string());
            }

            if self.webauthn.rp_origin.is_empty() {
                return Err(
                    "WebAuthn RP origin cannot be empty when WebAuthn is enabled".to_string(),
                );
            }
        }

        Ok(())
    }

    /// Check if MFA is required for the given role
    pub fn is_mfa_required(&self, is_admin: bool) -> bool {
        self.mfa.enabled && (self.mfa.required_for_admin && is_admin)
    }

    /// Get Argon2 configuration parameters
    pub fn argon2_params(&self) -> Argon2Params {
        Argon2Params {
            memory_cost: self.password.argon2_memory_cost,
            time_cost: self.password.argon2_time_cost,
            parallelism: self.password.argon2_parallelism,
        }
    }
}

#[derive(Debug, Clone)]
pub struct Argon2Params {
    pub memory_cost: u32,
    pub time_cost: u32,
    pub parallelism: u32,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        SecurityConfig {
            jwt: JwtConfig {
                secret_key: "change-this-secret-key-in-production".to_string(),
                algorithm: JwtAlgorithm::HS256,
                access_token_ttl_seconds: 900,        // 15 minutes
                refresh_token_ttl_seconds: 86400 * 7, // 7 days
                issuer: "auth-service".to_string(),
                audience: "auth-service".to_string(),
            },
            password: PasswordConfig {
                min_length: 8,
                max_length: 128,
                require_uppercase: true,
                require_lowercase: true,
                require_digits: true,
                require_special_chars: true,
                min_strength_score: 70,
                max_age_days: Some(90),
                history_size: 10,
                argon2_memory_cost: 65536, // 64 MB
                argon2_time_cost: 3,
                argon2_parallelism: 4,
            },
            session: SessionConfig {
                default_ttl_seconds: 3600 * 8,       // 8 hours
                remember_me_ttl_seconds: 86400 * 30, // 30 days
                max_concurrent_sessions: 10,
                cleanup_interval_seconds: 3600,
                secure_cookies: true,
                same_site_policy: SameSitePolicy::Strict,
            },
            rate_limiting: RateLimitingConfig {
                enabled: true,
                login_attempts: RateLimit {
                    max_attempts: 5,
                    window_seconds: 300,
                    lockout_duration_seconds: 900,
                },
                registration_attempts: RateLimit {
                    max_attempts: 3,
                    window_seconds: 3600,
                    lockout_duration_seconds: 3600,
                },
                password_reset_attempts: RateLimit {
                    max_attempts: 3,
                    window_seconds: 3600,
                    lockout_duration_seconds: 3600,
                },
                api_requests: RateLimit {
                    max_attempts: 1000,
                    window_seconds: 60,
                    lockout_duration_seconds: 60,
                },
            },
            oauth: OAuthConfig {
                enabled: false,
                providers: vec![],
            },
            mfa: MfaConfig {
                enabled: true,
                required_for_admin: true,
                totp: TotpConfig {
                    enabled: true,
                    issuer: "AuthService".to_string(),
                    algorithm: TotpAlgorithm::SHA1,
                    digits: 6,
                    period_seconds: 30,
                    skew_tolerance: 1,
                },
                sms: SmsConfig {
                    enabled: false,
                    provider: "".to_string(),
                    api_key: "".to_string(),
                    from_number: "".to_string(),
                },
                email: EmailMfaConfig {
                    enabled: true,
                    code_length: 6,
                    code_ttl_seconds: 300,
                },
            },
            webauthn: WebAuthnConfig {
                enabled: true,
                rp_id: "localhost".to_string(),
                rp_name: "AuthService".to_string(),
                rp_origin: "https://localhost:3000".to_string(),
                timeout_seconds: 60,
                user_verification: UserVerificationRequirement::Preferred,
                authenticator_attachment: None,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = SecurityConfig::default();
        assert!(config.validate().is_ok());
        assert!(config.mfa.enabled);
        assert!(config.rate_limiting.enabled);
    }

    #[test]
    fn test_test_config() {
        let config = SecurityConfig::test_config();
        assert!(config.validate().is_ok());
        assert!(!config.mfa.enabled);
        assert!(!config.rate_limiting.enabled);
        assert!(!config.session.secure_cookies);
    }

    #[test]
    fn test_validation_empty_jwt_secret() {
        let mut config = SecurityConfig::default();
        config.jwt.secret_key = "".to_string();
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_validation_short_jwt_secret() {
        let mut config = SecurityConfig::default();
        config.jwt.secret_key = "short".to_string();
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_validation_zero_password_length() {
        let mut config = SecurityConfig::default();
        config.password.min_length = 0;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_validation_invalid_password_length_range() {
        let mut config = SecurityConfig::default();
        config.password.min_length = 20;
        config.password.max_length = 10;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_is_mfa_required() {
        let config = SecurityConfig::default();
        assert!(config.is_mfa_required(true)); // Admin with MFA required
        assert!(!config.is_mfa_required(false)); // Regular user

        let mut config = SecurityConfig::test_config();
        config.mfa.enabled = false;
        assert!(!config.is_mfa_required(true)); // MFA disabled
    }

    #[test]
    fn test_argon2_params() {
        let config = SecurityConfig::default();
        let params = config.argon2_params();

        assert_eq!(params.memory_cost, config.password.argon2_memory_cost);
        assert_eq!(params.time_cost, config.password.argon2_time_cost);
        assert_eq!(params.parallelism, config.password.argon2_parallelism);
    }

    #[test]
    fn test_webauthn_validation() {
        let mut config = SecurityConfig::default();
        config.webauthn.enabled = true;
        config.webauthn.rp_id = "".to_string();
        assert!(config.validate().is_err());

        config.webauthn.rp_id = "example.com".to_string();
        config.webauthn.rp_name = "".to_string();
        assert!(config.validate().is_err());

        config.webauthn.rp_name = "Test Service".to_string();
        config.webauthn.rp_origin = "".to_string();
        assert!(config.validate().is_err());

        config.webauthn.rp_origin = "https://example.com".to_string();
        assert!(config.validate().is_ok());
    }
}
