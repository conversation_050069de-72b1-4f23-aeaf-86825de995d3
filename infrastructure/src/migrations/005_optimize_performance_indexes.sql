-- Performance optimization indexes for high-frequency queries
-- These indexes are designed to meet the <100ms SLA requirement

-- ============================================================================
-- USERS TABLE OPTIMIZATIONS
-- ============================================================================

-- Optimize user lookup by email with active status (most critical auth query)
DROP INDEX IF EXISTS idx_users_email;
CREATE INDEX idx_users_email_active ON users(email) WHERE is_active = TRUE AND is_verified = TRUE;

-- Optimize for soft-delete pattern (add deleted_at column concept via is_active)
CREATE INDEX IF NOT EXISTS idx_users_active_only ON users(id) WHERE is_active = TRUE;

-- Optimize user search queries (admin functions)
CREATE INDEX IF NOT EXISTS idx_users_search_name ON users(email, created_at DESC) WHERE is_active = TRUE;

-- ============================================================================
-- SESSIONS TABLE OPTIMIZATIONS  
-- ============================================================================

-- Drop existing less optimal indexes
DROP INDEX IF EXISTS idx_sessions_user_id;
DROP INDEX IF EXISTS idx_sessions_active;

-- Optimized composite index for active session lookups by user
CREATE INDEX idx_sessions_user_active_expires ON sessions(user_id, is_active, expires_at) WHERE is_active = TRUE;

-- Optimized session token lookup (critical for authentication)
CREATE INDEX IF NOT EXISTS idx_sessions_token_hash ON sessions(id) WHERE is_active = TRUE AND expires_at > strftime('%s', 'now');

-- Optimized for session cleanup operations
CREATE INDEX IF NOT EXISTS idx_sessions_cleanup ON sessions(expires_at, is_active) WHERE expires_at <= strftime('%s', 'now');

-- ============================================================================
-- USER ROLES OPTIMIZATIONS (High-frequency permission checks)
-- ============================================================================

-- Critical index for permission checks - most important for performance
CREATE INDEX IF NOT EXISTS idx_user_roles_permission_check ON user_roles(user_id, role_id) 
    WHERE (expires_at IS NULL OR expires_at > strftime('%s', 'now'));

-- Optimized for role enumeration queries
CREATE INDEX IF NOT EXISTS idx_user_roles_active_assignments ON user_roles(user_id, assigned_at DESC) 
    WHERE (expires_at IS NULL OR expires_at > strftime('%s', 'now'));

-- Optimized for role management queries
CREATE INDEX IF NOT EXISTS idx_user_roles_role_users ON user_roles(role_id, assigned_at DESC)
    WHERE (expires_at IS NULL OR expires_at > strftime('%s', 'now'));

-- ============================================================================
-- ROLE PERMISSIONS OPTIMIZATIONS
-- ============================================================================

-- Critical index for permission resolution
CREATE INDEX IF NOT EXISTS idx_role_permissions_lookup ON role_permissions(role_id, permission_id);

-- Optimized for permission enumeration
CREATE INDEX IF NOT EXISTS idx_role_permissions_by_role ON role_permissions(role_id, granted_at DESC);

-- ============================================================================
-- ROLES TABLE OPTIMIZATIONS
-- ============================================================================

-- Optimized for role hierarchy queries
CREATE INDEX IF NOT EXISTS idx_roles_hierarchy_active ON roles(parent_role_id, id, name) 
    WHERE is_active = TRUE AND parent_role_id IS NOT NULL;

-- Optimized for role lookup by name
CREATE INDEX IF NOT EXISTS idx_roles_name_active ON roles(name) WHERE is_active = TRUE;

-- ============================================================================
-- QUERY PERFORMANCE HINTS (SQLite-specific optimizations)
-- ============================================================================

-- Enable WAL mode for better concurrency
PRAGMA journal_mode = WAL;

-- Optimize for read performance 
PRAGMA synchronous = NORMAL;

-- Increase cache size (10MB)
PRAGMA cache_size = 10000;

-- Enable memory-mapped I/O for better performance
PRAGMA mmap_size = 67108864; -- 64MB

-- Optimize query planner for our access patterns
PRAGMA optimize;

-- Update table statistics for better query planning
ANALYZE;