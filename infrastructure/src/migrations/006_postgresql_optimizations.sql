-- PostgreSQL-specific optimizations
-- This migration only runs when using PostgreSQL as the database

-- Note: This file uses PostgreSQL-specific features and will be skipped for SQLite/libSQL

-- ============================================================================
-- POSTGRESQL SPECIFIC INDEXES
-- ============================================================================

-- Use hash indexes for exact equality lookups (PostgreSQL-specific)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_hash ON users USING hash(email) 
    WHERE is_active = TRUE AND is_verified = TRUE;

-- Use partial indexes with better predicate pushdown
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_active_token ON sessions(id) 
    WHERE is_active = TRUE AND expires_at > NOW();

-- Use BRIN indexes for timestamp columns (efficient for large tables)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_log_created_at_brin ON audit_log 
    USING brin(created_at) WITH (pages_per_range = 128);

-- Use GIN indexes for JSONB data (if details column is JSONB)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_log_details_gin ON audit_log 
--     USING gin(details) WHERE details IS NOT NULL;

-- ============================================================================
-- POSTGRESQL CONNECTION OPTIMIZATIONS
-- ============================================================================

-- Optimize connection pooling settings
-- These would typically be set in postgresql.conf, but included here for reference

-- Connection Settings (adjust based on connection pool size)
-- max_connections = 100-200 (depends on load)
-- shared_buffers = 25% of RAM
-- effective_cache_size = 75% of RAM
-- work_mem = RAM / max_connections / 4

-- Performance Settings
-- random_page_cost = 1.1 (for SSD storage)
-- seq_page_cost = 1.0
-- cpu_tuple_cost = 0.01
-- cpu_index_tuple_cost = 0.005
-- cpu_operator_cost = 0.0025

-- WAL Settings for performance
-- wal_buffers = 16MB
-- checkpoint_completion_target = 0.9
-- wal_writer_delay = 200ms

-- Query Planning
-- default_statistics_target = 100
-- constraint_exclusion = partition
-- enable_partitionwise_join = on
-- enable_partitionwise_aggregate = on

-- ============================================================================
-- POSTGRESQL SPECIFIC CONSTRAINTS AND CHECKS
-- ============================================================================

-- Add check constraints for data validation (more efficient than application-level checks)
ALTER TABLE users ADD CONSTRAINT check_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE sessions ADD CONSTRAINT check_expires_after_created 
    CHECK (expires_at > created_at);

ALTER TABLE user_roles ADD CONSTRAINT check_expires_after_assigned 
    CHECK (expires_at > assigned_at OR expires_at IS NULL);

-- ============================================================================
-- POSTGRESQL ROW LEVEL SECURITY (for multi-tenant scenarios)
-- ============================================================================

-- Enable RLS for enhanced security (commented out for single-tenant)
-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Example policy for user data isolation
-- CREATE POLICY user_isolation ON users 
--     FOR ALL TO auth_service_role 
--     USING (id = current_setting('app.current_user_id')::TEXT);

-- ============================================================================
-- POSTGRESQL PERFORMANCE MONITORING
-- ============================================================================

-- Create extension for monitoring (if not exists)
-- CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Update statistics for better query planning
ANALYZE users;
ANALYZE sessions;
ANALYZE roles;
ANALYZE user_roles;
ANALYZE role_permissions;
ANALYZE audit_log;