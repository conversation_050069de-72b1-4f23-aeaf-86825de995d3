-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Create index on email for fast lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create index on verification status
CREATE INDEX IF NOT EXISTS idx_users_verification ON users(is_verified);

-- Create index on active status
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);

-- Create index on created_at for date range queries
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);