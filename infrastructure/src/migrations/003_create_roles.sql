-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    parent_role_id TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (parent_role_id) REFERENCES roles(id) ON DELETE SET NULL
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at INTEGER NOT NULL
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id TEXT NOT NULL,
    permission_id TEXT NOT NULL,
    granted_at INTEGER NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    <PERSON>OR<PERSON><PERSON><PERSON>E<PERSON> (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS user_roles (
    user_id TEXT NOT NULL,
    role_id TEXT NOT NULL,
    assigned_at INTEGER NOT NULL,
    assigned_by TEXT,
    expires_at INTEGER,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for roles
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(is_active);
CREATE INDEX IF NOT EXISTS idx_roles_parent ON roles(parent_role_id);

-- Create indexes for permissions
CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name);

-- Create indexes for user_roles
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_expires_at ON user_roles(expires_at);

-- Insert default permissions
INSERT OR IGNORE INTO permissions (id, name, description, created_at) VALUES
('read', 'read', 'Read access to resources', strftime('%s', 'now')),
('write', 'write', 'Write access to resources', strftime('%s', 'now')),
('delete', 'delete', 'Delete access to resources', strftime('%s', 'now')),
('admin', 'admin', 'Administrative access to all resources', strftime('%s', 'now'));

-- Insert default roles
INSERT OR IGNORE INTO roles (id, name, description, is_active, parent_role_id, created_at, updated_at) VALUES
('user', 'User', 'Standard user role', TRUE, NULL, strftime('%s', 'now'), strftime('%s', 'now')),
('admin', 'Administrator', 'Administrative role with full access', TRUE, NULL, strftime('%s', 'now'), strftime('%s', 'now'));

-- Assign permissions to default roles
INSERT OR IGNORE INTO role_permissions (role_id, permission_id, granted_at) VALUES
('user', 'read', strftime('%s', 'now')),
('admin', 'read', strftime('%s', 'now')),
('admin', 'write', strftime('%s', 'now')),
('admin', 'delete', strftime('%s', 'now')),
('admin', 'admin', strftime('%s', 'now'));