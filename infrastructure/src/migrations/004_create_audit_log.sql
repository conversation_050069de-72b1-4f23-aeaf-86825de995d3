-- Create audit_log table for security and action tracking
CREATE TABLE IF NOT EXISTS audit_log (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT,
    session_id TEXT,
    action TEXT NOT NULL,
    resource TEXT,
    resource_id TEXT,
    details TEXT, -- <PERSON><PERSON><PERSON> blob for additional context
    ip_address TEXT,
    user_agent TEXT,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    created_at INTEGER NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL
);

-- High-priority indexes for audit log queries
-- Optimized for time-based queries (most common pattern)
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log(created_at DESC);

-- Optimized for user activity queries
CREATE INDEX IF NOT EXISTS idx_audit_log_user_action ON audit_log(user_id, action, created_at DESC);

-- Optimized for security analysis (IP-based queries)
CREATE INDEX IF NOT EXISTS idx_audit_log_ip_address ON audit_log(ip_address, created_at DESC) WHERE ip_address IS NOT NULL;

-- Optimized for resource-specific queries
CREATE INDEX IF NOT EXISTS idx_audit_log_resource ON audit_log(resource, resource_id, created_at DESC) WHERE resource IS NOT NULL;

-- Optimized for error analysis
CREATE INDEX IF NOT EXISTS idx_audit_log_errors ON audit_log(success, created_at DESC) WHERE success = FALSE;

-- Optimized for session tracking
CREATE INDEX IF NOT EXISTS idx_audit_log_session ON audit_log(session_id, created_at DESC) WHERE session_id IS NOT NULL;