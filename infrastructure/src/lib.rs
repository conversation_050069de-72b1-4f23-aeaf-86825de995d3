// Infrastructure crate - External adapters and implementations
// This crate contains adapters that implement domain ports for external systems

pub mod adapters;
pub mod cache_configuration;
pub mod configuration;
pub mod database;
pub mod errors;
pub mod migrations;

#[cfg(test)]
mod cache_performance_test;

// Re-export commonly used adapters
pub use adapters::cache::RedisCache;
pub use adapters::database::{SqlxSessionRepository, SqlxUserRepository};
pub use adapters::email::SmtpEmailService;
pub use adapters::security::{JwtTokenService, TotpService};
pub use cache_configuration::{CacheConfiguration, CachedQueryHandlers};
pub use configuration::Settings;
pub use database::{OptimizedConnectionPool, PoolMetrics, PreparedStatementCache, QueryOptimizer};

// Re-export error handling utilities
pub use errors::{DomainError, ErrorSanitizer};
