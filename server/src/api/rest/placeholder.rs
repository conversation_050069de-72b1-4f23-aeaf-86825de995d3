// Placeholder REST API implementations
// These will be replaced with real Axum handlers in Phase 4

#[allow(dead_code)]
pub async fn placeholder_handler() -> &'static str {
    "AuthService REST API - Coming Soon!"
}

// Future implementations will include:
// - POST /auth/login
// - POST /auth/register
// - POST /auth/logout
// - POST /auth/refresh
// - GET /auth/profile
// - PUT /auth/profile
// - GET /health
// - And more...
