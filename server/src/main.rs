// Main entry point for AuthService
// Handles CLI arguments and starts the appropriate service

use clap::{Parser, Subcommand};
use std::process;

mod api;
mod cli;
mod startup;

#[derive(Parser)]
#[command(name = "auth-service")]
#[command(about = "Aethelus Authentication Service")]
#[command(version = env!("CARGO_PKG_VERSION"))]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Start the authentication server
    Serve {
        /// Configuration file path
        #[arg(short, long, default_value = "configuration.toml")]
        config: String,

        /// Server port (overrides config)
        #[arg(short, long)]
        port: Option<u16>,

        /// Enable development mode
        #[arg(long)]
        dev: bool,
    },

    /// Database operations
    Database {
        #[command(subcommand)]
        action: DatabaseCommands,
    },

    /// User management operations
    User {
        #[command(subcommand)]
        action: UserCommands,
    },

    /// Configuration management
    Config {
        #[command(subcommand)]
        action: ConfigCommands,
    },
}

#[derive(Subcommand)]
enum DatabaseCommands {
    /// Run database migrations
    Migrate,

    /// Reset database (dangerous!)
    Reset {
        /// Skip confirmation prompt
        #[arg(long)]
        force: bool,
    },

    /// Show database status
    Status,
}

#[derive(Subcommand)]
enum UserCommands {
    /// Create a new user
    Create {
        /// User email
        #[arg(short, long)]
        email: String,

        /// User password (will prompt if not provided)
        #[arg(short, long)]
        password: Option<String>,

        /// Make user an admin
        #[arg(long)]
        admin: bool,
    },

    /// List users
    List {
        /// Number of users to show
        #[arg(short, long, default_value = "10")]
        limit: usize,

        /// Offset for pagination
        #[arg(short, long, default_value = "0")]
        offset: usize,
    },

    /// Delete a user
    Delete {
        /// User email or ID
        user: String,

        /// Skip confirmation prompt
        #[arg(long)]
        force: bool,
    },
}

#[derive(Subcommand)]
enum ConfigCommands {
    /// Show current configuration
    Show,

    /// Validate configuration
    Validate,

    /// Generate default configuration file
    Init {
        /// Output file path
        #[arg(short, long, default_value = "configuration.toml")]
        output: String,
    },
}

#[tokio::main]
async fn main() {
    let cli = Cli::parse();

    // Initialize tracing
    init_tracing();

    let result = match cli.command {
        Commands::Serve { config, port, dev } => serve_command(config, port, dev).await,
        Commands::Database { action } => database_command(action).await,
        Commands::User { action } => user_command(action).await,
        Commands::Config { action } => config_command(action).await,
    };

    if let Err(e) = result {
        eprintln!("Error: {e}");
        process::exit(1);
    }
}

async fn serve_command(
    config_path: String,
    port: Option<u16>,
    dev: bool,
) -> Result<(), anyhow::Error> {
    println!("Starting AuthService server...");
    println!("Config: {config_path}");
    if let Some(p) = port {
        println!("Port override: {p}");
    }
    if dev {
        println!("Development mode enabled");
    }

    // Load configuration
    let mut settings = auth_infrastructure::Settings::load()
        .map_err(|e| anyhow::anyhow!("Failed to load configuration: {}", e))?;

    // Apply CLI overrides
    if let Some(p) = port {
        settings.server.port = p;
    }

    // Validate configuration
    settings
        .validate()
        .map_err(|e| anyhow::anyhow!("Configuration validation failed: {}", e))?;

    // Start the server
    startup::run_server(settings).await
}

async fn database_command(action: DatabaseCommands) -> Result<(), anyhow::Error> {
    match action {
        DatabaseCommands::Migrate => {
            println!("Running database migrations...");
            cli::database::run_migrations().await
        }
        DatabaseCommands::Reset { force } => {
            println!("Resetting database...");
            cli::database::reset_database(force).await
        }
        DatabaseCommands::Status => {
            println!("Checking database status...");
            cli::database::show_status().await
        }
    }
}

async fn user_command(action: UserCommands) -> Result<(), anyhow::Error> {
    match action {
        UserCommands::Create {
            email,
            password,
            admin,
        } => cli::user::create_user(&email, password.as_deref(), admin).await,
        UserCommands::List { limit, offset } => cli::user::list_users(limit, offset).await,
        UserCommands::Delete { user, force } => cli::user::delete_user(&user, force).await,
    }
}

async fn config_command(action: ConfigCommands) -> Result<(), anyhow::Error> {
    match action {
        ConfigCommands::Show => cli::config::show_config().await,
        ConfigCommands::Validate => cli::config::validate_config().await,
        ConfigCommands::Init { output } => cli::config::init_config(&output).await,
    }
}

fn init_tracing() {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "auth_service=debug,auth_domain=debug,auth_application=debug,auth_infrastructure=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
}
