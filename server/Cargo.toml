[package]
name = "auth-server"
version = "0.1.0"
edition.workspace = true

[[bin]]
name = "auth-service"
path = "src/main.rs"

[dependencies]
auth-domain = { path = "../domain" }
auth-application = { path = "../application" }
auth-infrastructure = { path = "../infrastructure" }

# Server dependencies
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
anyhow = { workspace = true }
uuid = { workspace = true }
chrono = { workspace = true }

# Configuration
figment = { workspace = true }

# CLI
clap = { workspace = true }
crossterm = { workspace = true }
indicatif = { workspace = true }
dialoguer = { workspace = true }
console = { workspace = true }

# HTTP server (we'll add axum when implementing REST API)
# axum = "0.7"
# tower = "0.4"
# tower-http = "0.5"

# gRPC server
tonic = { workspace = true }
prost = { workspace = true }

# Database
sqlx = { workspace = true }

# Observability
opentelemetry = { workspace = true }
opentelemetry-jaeger = { workspace = true }