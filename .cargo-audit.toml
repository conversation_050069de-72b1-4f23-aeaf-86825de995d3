# Cargo audit configuration for AuthService
# This file configures security vulnerability scanning
#
# Note: The ignore functionality in this file may not work with all versions
# of cargo-audit. Use the command line flag instead:
# cargo audit --ignore RUSTSEC-2023-0071

[advisories]
# Documentation of ignored advisories (see SECURITY-ADVISORIES.md for details)
# 
# RUSTSEC-2023-0071: RSA Marvin Attack timing sidechannel
# - Transitive dependency through sqlx-mysql -> rsa 0.9.8  
# - We don't use MySQL or RSA operations
# - Risk: LOW - documented in SECURITY-ADVISORIES.md

[yanked]
# Check for yanked packages (removed from crates.io)
enabled = true

[licenses]
# License policy - ensure all dependencies have acceptable licenses
# This section can be expanded as needed for organizational compliance
allow = [
    "MIT",
    "Apache-2.0",
    "BSD-2-Clause", 
    "BSD-3-Clause",
    "ISC",
    "Unicode-DFS-2016",
]

# Warn about dependencies with licenses that need review
warn = [
    "MPL-2.0",
    "LGPL-2.1",
    "LGPL-3.0",
]

# Deny dependencies with incompatible licenses
deny = [
    "GPL-2.0",
    "GPL-3.0",
    "AGPL-1.0",
    "AGPL-3.0",
]