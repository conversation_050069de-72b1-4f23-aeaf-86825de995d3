# Dependency Policy Clarification for AuthService

## Updated Policy: Domain Layer Dependencies

**Previous Policy (Corrected)**: "Domain crate has NO external dependencies"
**New Policy (Clarified)**: "Domain crate cannot depend on OTHER PROJECT LAYERS but may use external crates from crates.io"

## Rationale for Change

The original "zero external dependencies" policy for the domain layer was overly restrictive and counterproductive. The real architectural concern is preventing **circular dependencies within our project layers**, not avoiding well-maintained external crates.

### What We Actually Want to Prevent

❌ **Project Layer Dependencies** (Bad):
```toml
# domain/Cargo.toml - This is what we want to prevent
[dependencies]
auth-application = { path = "../application" }  # FORBIDDEN
auth-infrastructure = { path = "../infrastructure" }  # FORBIDDEN
auth-server = { path = "../server" }  # FORBIDDEN
```

✅ **External Crate Dependencies** (Good):
```toml
# domain/Cargo.toml - This is perfectly acceptable
[dependencies]
argon2 = "0.6.0-rc.0"  # Security-critical crypto
serde = "1.0"          # Data serialization
uuid = "1.0"           # Unique identifiers
chrono = "0.4"         # Date/time handling
```

## Why External Dependencies Are Beneficial

### 1. **Security Critical Operations**
- Cryptographic operations MUST use audited libraries (CRYPTOGRAPHIC-POLICY.md)
- Custom crypto implementations create CRITICAL security vulnerabilities
- Examples: `argon2`, `rand`, `subtle`, `aes-gcm`

### 2. **Standard Data Types**
- Common data structures shouldn't be reinvented
- External crates provide battle-tested implementations
- Examples: `uuid` for IDs, `serde` for serialization, `url` for URL validation

### 3. **Domain-Specific Logic**
- Business rules may require specialized libraries
- Mathematical operations, parsing, validation
- Examples: `regex` for pattern matching, `email-address` for validation

### 4. **Performance & Reliability**
- External crates are often more optimized than custom implementations
- Regular security updates and bug fixes
- Community testing and validation

## Hexagonal Architecture Compliance

The **hexagonal architecture principle** is about keeping the domain **independent from infrastructure concerns**, not from all external code:

### ✅ **Allowed External Dependencies**
- **Pure libraries**: No I/O, no framework coupling
- **Data types**: UUID, DateTime, BigDecimal, etc.
- **Algorithms**: Cryptography, parsing, validation
- **Utilities**: Serialization, encoding, mathematical operations

### ❌ **Forbidden External Dependencies**
- **Databases**: PostgreSQL, Redis clients
- **HTTP frameworks**: Axum, Warp, Rocket
- **Message queues**: RabbitMQ, Kafka clients
- **External services**: AWS SDK, payment processors

## Implementation Guidelines

### Domain Layer Dependencies Should Be:

1. **Pure Functions**: No side effects, no I/O operations
2. **Deterministic**: Same input always produces same output
3. **Well-Maintained**: Active development, security updates
4. **Battle-Tested**: Wide adoption, proven reliability
5. **Minimal API Surface**: Simple, focused functionality

### Examples of Good Domain Dependencies:

```toml
[dependencies]
# Cryptography (security-critical)
argon2 = "0.6.0-rc.0"
rand = "0.8"
subtle = "2.6"

# Data types and validation
uuid = { version = "1.0", features = ["v4"] }
email-address = "0.2"
url = "2.4"

# Serialization (for events/DTOs)
serde = { version = "1.0", features = ["derive"] }

# Mathematical operations
rust_decimal = "1.0"
```

### Examples of Bad Domain Dependencies:

```toml
[dependencies]
# These violate hexagonal architecture
sqlx = "0.8"           # Database driver
reqwest = "0.12"       # HTTP client
tokio = "1.0"          # Async runtime
axum = "0.7"           # Web framework
redis = "0.25"         # Cache client
```

## Updated Architecture Principle

**New Principle**: The domain layer must remain **infrastructure-agnostic** and **framework-independent**, but may use **pure external libraries** that support business logic implementation.

**Focus**: Prevent coupling to infrastructure and other project layers, not to well-designed external crates.

## Security Exception: Cryptographic Dependencies

Per CRYPTOGRAPHIC-POLICY.md, the domain layer **MUST** use approved cryptographic crates:

- This is a **mandatory security requirement**
- Custom cryptographic implementations pose CRITICAL vulnerabilities (CVSS 10.0)
- Approved cryptographic crates are explicitly allowed in the domain layer
- Security takes precedence over architectural purity

## Conclusion

The clarified dependency policy better aligns with:
- **Domain-Driven Design** principles (business logic focus)
- **Hexagonal Architecture** goals (infrastructure independence)
- **Security requirements** (vetted cryptographic libraries)
- **Practical development** needs (don't reinvent the wheel)

The domain layer should be **logically pure** and **infrastructure-independent**, not artificially constrained from using well-designed external libraries that support business logic.

---

*Document Version: 1.0*  
*Updated: 2025-08-04*  
*Authority: Architecture Team*