# Cryptographic Policy and Standards for AuthService

## Executive Summary

This document establishes mandatory cryptographic policies, standards, and procedures for the AuthService project to prevent the implementation of custom cryptographic algorithms and ensure the use of vetted, audited cryptographic libraries.

## Policy Statement

**MANDATORY REQUIREMENT**: All cryptographic operations within the AuthService project MUST use established, audited, community-trusted cryptographic libraries. Custom implementation of cryptographic algorithms is **STRICTLY PROHIBITED**.

**ARCHITECTURAL EXCEPTION**: The domain layer is permitted to use approved cryptographic crates as external dependencies. This is an exception to the general "domain layer independence" rule due to the critical security requirements of cryptographic operations.

### Scope
This policy applies to:
- All password hashing and verification
- All symmetric and asymmetric encryption
- All cryptographic hash functions
- All random number generation for security purposes
- All digital signatures and MACs
- All key derivation functions
- All encoding/decoding of cryptographic data

## Prohibited Practices

### 1. Custom Cryptographic Algorithm Implementation
**STRICTLY FORBIDDEN:**
- Implementing any cryptographic primitive from scratch
- Creating "simplified" versions of standard algorithms
- Modifying existing cryptographic algorithms
- Rolling custom entropy collection methods
- Implementing custom constant-time operations

### 2. Unapproved Libraries
**FORBIDDEN:**
- Using cryptographic libraries without security audits
- Using unmaintained or deprecated cryptographic crates
- Using libraries with known security vulnerabilities
- Using libraries not listed in the approved list

### 3. Weak Cryptographic Practices
**PROHIBITED:**
- Using MD5, SHA-1, or other cryptographically broken hash functions
- Using DES, 3DES, or other weak encryption algorithms
- Using weak random number generators (PRNG, LCG, etc.)
- Storing passwords in plaintext or using reversible encryption
- Using custom or weak key derivation functions

## Approved Cryptographic Libraries

### Tier 1: Mandatory Use (Pre-Approved)

#### Password Hashing
- **`argon2`** (v0.6.0-rc.0+): Argon2id implementation
  - **Justification**: RFC 9106 compliant, multiple security audits, OWASP recommended
  - **Use Case**: All password hashing operations
  - **Configuration**: Must use OWASP 2025 parameters (19MiB, 2 iterations, 1 parallelism)

#### Symmetric Encryption
- **`chacha20poly1305`** (RustCrypto): ChaCha20-Poly1305 AEAD
  - **Justification**: RFC 8439 compliant, constant-time, widely audited
  - **Use Case**: Symmetric encryption with authentication

#### Cryptographic Hashing
- **`sha2`** (RustCrypto): SHA-2 family (SHA-256, SHA-512)
  - **Justification**: NIST standard, extensive validation
  - **Use Case**: General cryptographic hashing, HMAC

- **`blake2`** (RustCrypto): BLAKE2b/BLAKE2s
  - **Justification**: RFC 7693 compliant, high performance
  - **Use Case**: Fast cryptographic hashing, key derivation

#### Random Number Generation
- **`rand`** + **`getrandom`**: Secure random number generation
  - **Justification**: OS entropy integration, extensive testing
  - **Use Case**: All cryptographically secure random number needs

#### Message Authentication
- **`hmac`** (RustCrypto): HMAC implementation
  - **Justification**: RFC 2104 compliant, constant-time
  - **Use Case**: Message authentication codes

#### Constant-Time Operations
- **`subtle`**: Constant-time cryptographic operations
  - **Justification**: Timing attack prevention, peer reviewed
  - **Use Case**: Constant-time comparisons, conditional operations

#### Encoding
- **`base64`**: Base64 encoding/decoding
  - **Justification**: Standard implementation, performance optimized
  - **Use Case**: Cryptographic data encoding

### Tier 2: Approved with Review (Case-by-Case)

#### Advanced Cryptography
- **`ring`**: Comprehensive cryptographic library
  - **Justification**: BoringSSL-based, Google security team maintained
  - **Review Required**: For specific use cases requiring additional primitives

- **`ed25519-dalek`**: Ed25519 digital signatures
  - **Justification**: RFC 8032 compliant, high performance
  - **Review Required**: For digital signature requirements

#### Specialized Libraries
- **`x25519-dalek`**: X25519 key agreement
  - **Justification**: RFC 7748 compliant, constant-time
  - **Review Required**: For key exchange requirements

### Tier 3: Prohibited Libraries

#### Cryptographically Broken
- Any MD5 implementations
- Any SHA-1 implementations for security purposes
- Any DES/3DES implementations
- Any RC4 implementations

#### Unmaintained/Deprecated
- Libraries with no commits in the last year
- Libraries with known unpatched vulnerabilities
- Libraries not following semantic versioning

## Dependency Selection Criteria

### Security Requirements
1. **Active Maintenance**: Regular updates and vulnerability responses
2. **Security Audits**: Independent security audits or peer review
3. **Community Trust**: Wide adoption in security-critical applications
4. **Standards Compliance**: Implementation of recognized standards (RFCs, NIST, etc.)
5. **Constant-Time Operations**: Timing attack resistance where applicable

### Technical Requirements
1. **Pure Rust**: Prefer pure Rust implementations (no C dependencies unless justified)
2. **no_std Compatible**: Support for embedded/constrained environments where possible
3. **Performance**: Meet AuthService performance requirements (<100ms authentication)
4. **Memory Safety**: Rust memory safety guarantees maintained

### Evaluation Process
1. **Security Analysis**: Review security audit reports and vulnerability history
2. **Code Review**: Examine implementation quality and testing coverage
3. **Performance Testing**: Benchmark against requirements
4. **Integration Testing**: Validate compatibility with existing systems

## Implementation Requirements

### Code Structure
```rust
// ✅ CORRECT: Using approved libraries
use argon2::{Argon2, Algorithm, Version, Params};
use chacha20poly1305::{ChaCha20Poly1305, Key, Nonce};
use rand::rngs::OsRng;

// ❌ FORBIDDEN: Custom implementations
mod custom_crypto {
    // Any custom cryptographic implementation
}
```

### Configuration Management
```rust
// ✅ CORRECT: Using standard parameters
const ARGON2_MEMORY: u32 = 19456;    // 19 MiB (OWASP 2025)
const ARGON2_ITERATIONS: u32 = 2;    // 2 iterations
const ARGON2_PARALLELISM: u32 = 1;   // 1 thread

// ❌ FORBIDDEN: Custom or weak parameters
const WEAK_ITERATIONS: u32 = 1;      // Too weak
const CUSTOM_MEMORY: u32 = 1024;     // Custom parameter
```

### Error Handling
```rust
// ✅ CORRECT: Proper error handling
pub enum CryptoError {
    HashingError(argon2::Error),
    EncryptionError(chacha20poly1305::Error),
    RandomGenerationError(getrandom::Error),
}

// ❌ FORBIDDEN: Ignoring crypto errors
let _ = hash_password("password").unwrap(); // Never panic on crypto operations
```

## Compliance and Audit Requirements

### Dependency Auditing
- **Automated Scanning**: `cargo audit` in CI/CD pipeline
- **Regular Reviews**: Monthly dependency vulnerability scans
- **Update Policy**: Security updates applied within 48 hours
- **Version Pinning**: Explicit version constraints in Cargo.toml

### Code Review Requirements
- **Security Focus**: All cryptographic code requires security-focused review
- **Checklist**: Mandatory cryptographic review checklist
- **Expertise**: Reviewer must have cryptographic security expertise
- **Documentation**: Security decisions must be documented

### Testing Requirements
- **Unit Tests**: 100% coverage of cryptographic functions
- **Security Tests**: Timing attack resistance, entropy quality
- **Integration Tests**: End-to-end security validation
- **Property Tests**: Cryptographic property verification

## Violation Response

### Detection
- **Static Analysis**: Automated detection of prohibited patterns
- **Code Review**: Manual review for policy compliance
- **Dependency Scanning**: Automated library compliance checking

### Enforcement
1. **Build Failure**: CI/CD pipeline blocks non-compliant code
2. **Review Rejection**: Code reviews must verify policy compliance
3. **Security Review**: Escalation to security team for violations

### Remediation
1. **Immediate**: Replace custom implementations with approved libraries
2. **Testing**: Comprehensive security testing of remediated code
3. **Audit**: Security audit of changed cryptographic components

## Exception Process

### Request Process
1. **Business Justification**: Clear business need for exception
2. **Security Analysis**: Comprehensive security impact assessment
3. **Alternative Evaluation**: Analysis of approved alternatives
4. **Risk Assessment**: Quantified risk analysis with mitigation plans

### Approval Requirements
- **Security Team Approval**: Mandatory security team sign-off
- **Architecture Review**: Technical architecture team approval
- **Time Limits**: All exceptions have expiration dates
- **Regular Review**: Monthly review of active exceptions

### Documentation Requirements
- **Risk Documentation**: Comprehensive risk documentation
- **Mitigation Plans**: Detailed risk mitigation strategies
- **Review Schedule**: Regular exception review schedule
- **Sunset Plan**: Plan for eliminating exception

## Training and Awareness

### Developer Training
- **Cryptographic Security**: Basic cryptographic security principles
- **Library Usage**: Proper usage of approved libraries
- **Common Pitfalls**: Common cryptographic implementation mistakes
- **Policy Understanding**: Comprehensive policy training

### Regular Updates
- **Security Bulletins**: Regular security update communications
- **New Threats**: Emerging cryptographic threat awareness
- **Library Updates**: Updates on approved library changes
- **Best Practices**: Evolving cryptographic best practices

## Policy Maintenance

### Review Schedule
- **Quarterly**: Review of approved library list
- **Semi-Annual**: Policy update review
- **Annual**: Comprehensive policy audit
- **Ad-Hoc**: Emergency updates for critical vulnerabilities

### Update Process
1. **Threat Analysis**: Emerging threat and vulnerability analysis
2. **Library Evaluation**: New library evaluation and approval
3. **Policy Revision**: Policy updates based on analysis
4. **Communication**: Policy change communication to team

### Version Control
- **Document Versioning**: All policy changes tracked in version control
- **Change Documentation**: Comprehensive change documentation
- **Approval Process**: Formal approval process for policy changes

## Enforcement and Governance

### Automated Enforcement
```toml
# Cargo.toml - Dependency restrictions
[dependencies]
# ✅ Approved cryptographic libraries only
argon2 = "0.6.0-rc.0"
chacha20poly1305 = "0.10.1"
blake2 = "0.10.6"

# ❌ Forbidden - would be caught by CI
# md5 = "*"  # Cryptographically broken
# custom-crypto = "*"  # Not approved
```

### CI/CD Integration
```yaml
# Security checks in CI pipeline
- name: Cryptographic Policy Check
  run: |
    cargo audit
    ./scripts/check-crypto-policy.sh
    cargo deny check
```

### Monitoring
- **Dependency Tracking**: Automated tracking of cryptographic dependencies
- **Vulnerability Monitoring**: Real-time vulnerability monitoring
- **Usage Analytics**: Analysis of cryptographic library usage patterns

## Conclusion

This cryptographic policy establishes a comprehensive framework for ensuring the security and compliance of all cryptographic operations within the AuthService project. Adherence to this policy is mandatory and will be enforced through automated checks, code reviews, and regular audits.

**Key Principles:**
1. Never implement custom cryptographic algorithms
2. Use only approved, audited libraries
3. Follow established standards and best practices
4. Maintain comprehensive testing and monitoring
5. Regular review and updates of cryptographic components

---

*Policy Version: 1.0*  
*Effective Date: 2025-08-04*  
*Next Review: 2025-11-04*  
*Authority: AuthService Security Team*