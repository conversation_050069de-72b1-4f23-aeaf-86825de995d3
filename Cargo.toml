[workspace]
resolver = "3"  # MANDATORY - DO NOT CHANGE
members = [
    "domain",
    "application", 
    "infrastructure",
    "server"
]

[workspace.package]
edition = "2024"  # MANDATORY - DO NOT CHANGE
authors = ["AuthService Team"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/aethelus/auth-service"

[workspace.dependencies]
# Core dependencies (from Pinned-Versions.md)
tokio = { version = "1.47.0", features = ["full"] }
serde = { version = "1.0.204", features = ["derive"] }
serde_json = "1.0.141"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
anyhow = "1.0.98"
uuid = { version = "1.17.0", features = ["v4", "serde"] }
chrono = { version = "0.4.41", features = ["serde"] }
rand = "0.9.2"

# Configuration
figment = { version = "0.10.19", features = ["toml", "env"] }

# Security
argon2 = "0.6.0-rc.0"
jsonwebtoken = "9.3.1"
otpauth = "0.5.1"
webauthn-rs = "0.5.0"
ring = "0.17.14"
thiserror = "2.0"
base32 = "0.5"

# Database
sqlx = { version = "0.8.6", features = ["postgres", "sqlite", "runtime-tokio", "migrate", "chrono", "uuid", "derive"], default-features = false }
libsql = "0.9.19"

# Cache (using redis crate for Valkey/Redis connectivity)
redis = { version = "0.32.4", features = ["tokio-comp", "connection-manager"] }

# HTTP/gRPC
reqwest = { version = "0.12.22", features = ["json"] }
tonic = "0.14.0"
prost = "0.14.1"

# CLI
clap = { version = "4.5.42", features = ["derive"] }
crossterm = "0.29.0"
indicatif = "0.18.0"
dialoguer = "0.11.0"
console = "0.16.0"

# Observability
opentelemetry = "0.30.0"
opentelemetry-jaeger = "0.22.0"

# Statistics
statrs = "0.18.0"

# File watching
notify = "8.1.0"

# Async traits
async-trait = "0.1.85"

# Load testing dependencies
warp = "0.3.8"
testcontainers = "0.23.0"
base64 = "0.22.1"

# Tauri (for potential desktop client)
# tauri = "2.7.0"  # Commented out for now - can be added later when needed

