# Application Layer Implementation Guide

## 📋 **Domain Layer Handoff to Application Layer**

This document provides comprehensive guidance for implementing the application layer to work with the AuthService domain layer, with special focus on handling blocking operations and async requirements.

---

## 🚨 **CRITICAL: Threading Requirements**

### **Domain Layer Blocking Operations**

The domain layer intentionally implements **synchronous, blocking** operations for security and architectural purity. The following operations are CPU-intensive and will block the calling thread:

| Operation                         | Duration  | Memory | Threading Impact |
|-----------------------------------|-----------|--------|------------------|
| `Password::into_hash()`           | 150-200ms | 64MB   | 🚫 BLOCKS        |
| `Password::verify_against_hash()` | 150-200ms | 64MB   | 🚫 BLOCKS        |
| `User::change_password()`         | 150-200ms | 64MB   | 🚫 BLOCKS        |
| `User::verify_password()`         | 150-200ms | 64MB   | 🚫 BLOCKS        |

### **Application Layer Responsibility**

**The application layer MUST implement async wrappers for all password-related operations.**

---

## 🔧 **Required Async Implementations**

### **1. Authentication Command Handler**

```rust
// Application layer: commands/authenticate_user.rs
use tokio::task;
use auth_domain::{entities::User, value_objects::Password};

pub struct AuthenticateUserCommand {
    pub email: String,
    pub password: String,
    pub ip_address: String,
}

#[async_trait]
impl CommandHandler<AuthenticateUserCommand> for AuthenticateUserHandler {
    type Result = AuthenticationResult;
    
    async fn handle(&self, command: AuthenticateUserCommand) -> Result<Self::Result> {
        // Step 1: Fast validation (non-blocking)
        let email = Email::new(&command.email)
            .map_err(|_| AuthError::InvalidCredentials)?;
        
        // Step 2: Database lookup (async)
        let user = self.user_repository
            .find_by_email(&email)
            .await?
            .ok_or(AuthError::UserNotFound)?;
        
        // Step 3: CRITICAL - Move password verification to thread pool
        let password = command.password;
        let stored_hash = user.password_hash().to_string();
        
        let is_valid = task::spawn_blocking(move || {
            let provided_password = Password::new(&password)?;
            provided_password.verify_against_hash(&stored_hash)
        }).await
        .map_err(|_| AuthError::InternalError)?;
        
        if is_valid {
            // Step 4: Create session (fast)
            let session = Session::new(user.id().clone(), &command.ip_address)?;
            
            // Step 5: Async database save
            self.session_repository.save(&session).await?;
            
            Ok(AuthenticationResult::Success { session })
        } else {
            // Step 6: Record failed attempt (move to thread pool if needed)
            self.record_failed_attempt(&user, &command.ip_address).await?;
            Err(AuthError::InvalidCredentials)
        }
    }
}
```

### **2. User Registration Command Handler**

```rust
// Application layer: commands/register_user.rs
pub struct RegisterUserCommand {
    pub email: String,
    pub password: String,
}

#[async_trait]
impl CommandHandler<RegisterUserCommand> for RegisterUserHandler {
    type Result = RegistrationResult;
    
    async fn handle(&self, command: RegisterUserCommand) -> Result<Self::Result> {
        // Step 1: Fast validation
        let email = Email::new(&command.email)?;
        
        // Step 2: Check if user exists (async)
        if self.user_repository.exists_by_email(&email).await? {
            return Err(RegistrationError::EmailAlreadyExists);
        }
        
        // Step 3: CRITICAL - Move password hashing to thread pool
        let password_str = command.password;
        let password_hash = task::spawn_blocking(move || {
            let password = Password::new(&password_str)?;
            Ok::<String, DomainError>(password.into_hash())
        }).await
        .map_err(|_| RegistrationError::InternalError)??;
        
        // Step 4: Create user with pre-hashed password
        let user = User::new_with_hash(email, password_hash)?;
        
        // Step 5: Async database save
        self.user_repository.save(&user).await?;
        
        // Step 6: Async event publishing
        self.event_publisher.publish(UserRegistered {
            user_id: user.id().clone(),
            email: user.email().clone(),
        }).await?;
        
        Ok(RegistrationResult::Success { user_id: user.id().clone() })
    }
}
```

### **3. Change Password Command Handler**

```rust
// Application layer: commands/change_password.rs
pub struct ChangePasswordCommand {
    pub user_id: UserId,
    pub current_password: String,
    pub new_password: String,
}

#[async_trait]
impl CommandHandler<ChangePasswordCommand> for ChangePasswordHandler {
    type Result = PasswordChangeResult;
    
    async fn handle(&self, command: ChangePasswordCommand) -> Result<Self::Result> {
        // Step 1: Load user (async)
        let mut user = self.user_repository
            .find_by_id(&command.user_id)
            .await?
            .ok_or(PasswordChangeError::UserNotFound)?;
        
        // Step 2: CRITICAL - Verify current password in thread pool
        let current_password = command.current_password;
        let stored_hash = user.password_hash().to_string();
        
        let is_current_valid = task::spawn_blocking(move || {
            let password = Password::new(&current_password)?;
            Ok::<bool, DomainError>(password.verify_against_hash(&stored_hash))
        }).await
        .map_err(|_| PasswordChangeError::InternalError)??;
        
        if !is_current_valid {
            return Err(PasswordChangeError::InvalidCurrentPassword);
        }
        
        // Step 3: CRITICAL - Hash new password in thread pool
        let new_password_str = command.new_password;
        let new_password_hash = task::spawn_blocking(move || {
            let password = Password::new(&new_password_str)?;
            Ok::<String, DomainError>(password.into_hash())
        }).await
        .map_err(|_| PasswordChangeError::InternalError)??;
        
        // Step 4: Domain operation (fast - no password hashing)
        user.change_password_with_hash(new_password_hash)?;
        
        // Step 5: Async save
        self.user_repository.save(&user).await?;
        
        Ok(PasswordChangeResult::Success)
    }
}
```

---

## 📊 **Performance Monitoring Implementation**

### **Required Metrics Collection**

```rust
// Application layer: metrics/auth_metrics.rs
use prometheus::{Histogram, Counter, Gauge};

pub struct AuthMetrics {
    // Password operation timing
    pub password_hash_duration: Histogram,
    pub password_verify_duration: Histogram,
    pub authentication_total_duration: Histogram,
    
    // Concurrency tracking
    pub concurrent_authentications: Gauge,
    pub concurrent_password_operations: Gauge,
    
    // Error tracking
    pub authentication_failures: Counter,
    pub password_hash_errors: Counter,
    
    // Resource utilization
    pub thread_pool_queue_size: Gauge,
    pub memory_usage_during_auth: Histogram,
}

impl AuthMetrics {
    pub fn record_password_operation<F, T>(&self, operation_name: &str, f: F) -> T 
    where 
        F: FnOnce() -> T,
    {
        let _timer = match operation_name {
            "hash" => self.password_hash_duration.start_timer(),
            "verify" => self.password_verify_duration.start_timer(),
            _ => panic!("Unknown operation"),
        };
        
        self.concurrent_password_operations.inc();
        let result = f();
        self.concurrent_password_operations.dec();
        
        result
    }
}
```

### **Monitoring Integration**

```rust
// Application layer: Enhanced command handler with monitoring
#[async_trait]
impl CommandHandler<AuthenticateUserCommand> for AuthenticateUserHandler {
    async fn handle(&self, command: AuthenticateUserCommand) -> Result<Self::Result> {
        let _auth_timer = self.metrics.authentication_total_duration.start_timer();
        self.metrics.concurrent_authentications.inc();
        
        // ... validation and database lookup ...
        
        // Monitor password verification
        let is_valid = self.metrics.record_password_operation("verify", || {
            task::spawn_blocking(move || {
                let provided_password = Password::new(&password)?;
                provided_password.verify_against_hash(&stored_hash)
            })
        }).await?;
        
        self.metrics.concurrent_authentications.dec();
        
        if !is_valid {
            self.metrics.authentication_failures.inc();
        }
        
        // ... rest of implementation ...
    }
}
```

---

## 🚫 **Critical Anti-Patterns to Avoid**

### **1. DON'T Block Async Runtime**

```rust
// ❌ NEVER do this - blocks the async runtime
pub async fn authenticate_user_wrong(email: &str, password: &str) -> Result<Session> {
    let user = find_user(email).await?;
    
    // This blocks the entire async runtime!
    let password = Password::new(password)?;
    let is_valid = password.verify_against_hash(user.password_hash())?;
    
    // ... rest
}
```

```rust
// ✅ Correct approach - use spawn_blocking
pub async fn authenticate_user_correct(email: &str, password: &str) -> Result<Session> {
    let user = find_user(email).await?;
    
    let password_str = password.to_string();
    let hash = user.password_hash().to_string();
    
    let is_valid = tokio::task::spawn_blocking(move || {
        let password = Password::new(&password_str)?;
        password.verify_against_hash(&hash)
    }).await??;
    
    // ... rest
}
```

### **2. DON'T Reduce Security Parameters**

```rust
// ❌ NEVER modify domain layer security settings for performance
// Don't create "fast" password verification methods
pub fn verify_password_fast(password: &str, hash: &str) -> bool {
    // This bypasses security - DON'T DO THIS
}
```

### **3. DON'T Skip Error Handling**

```rust
// ❌ Poor error handling
let is_valid = task::spawn_blocking(move || {
    password.verify_against_hash(&hash)  // Missing error handling
}).await.unwrap();  // Panics on thread pool issues

// ✅ Proper error handling
let is_valid = task::spawn_blocking(move || {
    let password = Password::new(&password_str)?;
    Ok::<bool, DomainError>(password.verify_against_hash(&hash))
}).await
.map_err(|_| AuthError::InternalError)??;
```

---

## 🧪 **Testing Strategy**

### **Unit Tests for Async Wrappers**

```rust
// tests/commands/test_authenticate_user.rs
#[tokio::test]
async fn test_authenticate_user_success() {
    let handler = setup_handler().await;
    
    let command = AuthenticateUserCommand {
        email: "<EMAIL>".to_string(),
        password: "ValidPassword123!".to_string(),
        ip_address: "127.0.0.1".to_string(),
    };
    
    let result = handler.handle(command).await;
    
    assert!(result.is_ok());
    // Verify the operation completed in reasonable time
    // (should be fast due to async implementation)
}

#[tokio::test]
async fn test_concurrent_authentication() {
    let handler = setup_handler().await;
    
    // Test 10 concurrent authentications
    let tasks: Vec<_> = (0..10).map(|i| {
        let handler = handler.clone();
        tokio::spawn(async move {
            let command = AuthenticateUserCommand {
                email: format!("user{}@example.com", i),
                password: "ValidPassword123!".to_string(),
                ip_address: "127.0.0.1".to_string(),
            };
            handler.handle(command).await
        })
    }).collect();
    
    let results = futures::future::join_all(tasks).await;
    
    // All should complete successfully and in parallel
    for result in results {
        assert!(result.unwrap().is_ok());
    }
}
```

### **Load Testing Requirements**

```rust
// tests/load/authentication_load_test.rs
#[tokio::test]
#[ignore] // Run separately as load test
async fn load_test_authentication() {
    let handler = setup_handler().await;
    
    // Test 100 concurrent authentications
    let start = std::time::Instant::now();
    
    let tasks: Vec<_> = (0..100).map(|_| {
        let handler = handler.clone();
        tokio::spawn(async move {
            // Each authentication should complete quickly
            let result = handler.handle(auth_command()).await;
            result.is_ok()
        })
    }).collect();
    
    let results = futures::future::join_all(tasks).await;
    let duration = start.elapsed();
    
    // Verify performance requirements
    assert!(duration < Duration::from_secs(5)); // All 100 auths in <5s
    assert_eq!(results.len(), 100);
    
    // Verify success rate
    let success_count = results.iter().filter(|r| r.is_ok()).count();
    assert!(success_count > 95); // >95% success rate
}
```

---

## 📁 **Application Layer Structure**

### **Recommended Organization**

```
application/
├── src/
│   ├── commands/
│   │   ├── authenticate_user.rs     # Async auth command
│   │   ├── register_user.rs         # Async registration  
│   │   ├── change_password.rs       # Async password change
│   │   └── mod.rs
│   │
│   ├── queries/
│   │   ├── get_user_profile.rs      # Fast read operations
│   │   ├── list_user_sessions.rs    # Session queries
│   │   └── mod.rs
│   │
│   ├── services/
│   │   ├── auth_application_service.rs  # Orchestrates auth flows
│   │   ├── password_service.rs          # Async password operations
│   │   └── mod.rs
│   │
│   ├── metrics/
│   │   ├── auth_metrics.rs          # Performance monitoring
│   │   └── mod.rs
│   │
│   ├── errors/
│   │   ├── application_errors.rs    # Application-specific errors
│   │   └── mod.rs
│   │
│   └── lib.rs
├── tests/
│   ├── unit/
│   ├── integration/
│   └── load/
└── Cargo.toml
```

---

## ⚙️ **Configuration Requirements**

### **Thread Pool Configuration**

```rust
// Application layer: config/thread_pool.rs
#[derive(Debug, Clone)]
pub struct ThreadPoolConfig {
    /// Number of threads for blocking password operations
    pub password_worker_threads: usize,
    
    /// Queue size for password operations
    pub password_queue_size: usize,
    
    /// Timeout for password operations
    pub password_operation_timeout: Duration,
}

impl Default for ThreadPoolConfig {
    fn default() -> Self {
        Self {
            // Size based on CPU cores, but at least 4
            password_worker_threads: std::cmp::max(4, num_cpus::get()),
            password_queue_size: 1000,
            password_operation_timeout: Duration::from_secs(30),
        }
    }
}
```

### **Performance Thresholds**

```rust
// Application layer: config/performance.rs
#[derive(Debug, Clone)]
pub struct PerformanceConfig {
    /// Maximum acceptable authentication latency (p95)
    pub max_auth_latency_ms: u64,
    
    /// Maximum concurrent password operations
    pub max_concurrent_password_ops: usize,
    
    /// Memory alert threshold (MB)
    pub memory_alert_threshold_mb: usize,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            max_auth_latency_ms: 500,     // 500ms p95
            max_concurrent_password_ops: 50,  // Limit concurrent ops
            memory_alert_threshold_mb: 2048,  // 2GB alert
        }
    }
}
```

---

## 🚀 **Deployment Checklist**

### **Pre-Production Requirements**

- [ ] ✅ All password operations use `tokio::task::spawn_blocking()`
- [ ] ✅ Load testing completed with 100+ concurrent authentications
- [ ] ✅ Memory usage monitoring implemented
- [ ] ✅ Thread pool health monitoring implemented  
- [ ] ✅ Error handling covers thread pool exhaustion
- [ ] ✅ Timeout handling for password operations
- [ ] ✅ Metrics collection and alerting configured
- [ ] ✅ Performance thresholds defined and monitored

### **Production Monitoring**

```rust
// Required production alerts
struct ProductionAlerts {
    // Alert if authentication p95 > 500ms
    authentication_latency_alert: Alert,
    
    // Alert if memory usage > 2GB
    memory_usage_alert: Alert,
    
    // Alert if thread pool utilization > 80%
    thread_pool_alert: Alert,
    
    // Alert if error rate > 5%
    error_rate_alert: Alert,
}
```

---

## 📋 **Summary for Application Layer Team**

### **Key Takeaways**

1. **Domain Layer is Blocking by Design** - This is correct and should not be changed
2. **Application Layer Must Be Async** - Use `tokio::task::spawn_blocking()` for all password operations
3. **Performance is Critical** - Implement comprehensive monitoring and load testing
4. **Security is Non-Negotiable** - Never modify domain layer security parameters

### **Success Criteria**

- Authentication latency: <500ms p95 under load
- Memory usage: <2GB under 100 concurrent users  
- Error rate: <1% under normal load
- Thread pool: Healthy utilization (<80%)

This document ensures the application layer will properly handle the domain layer's blocking operations while maintaining excellent performance and security standards.

---

**Document Version**: 1.0  
**Last Updated**: 2025-02-02  
**Next Review**: Before Application Layer Implementation