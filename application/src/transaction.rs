// Transaction management for ensuring data consistency
// Provides atomic operations across multiple repositories and event publishing

use crate::errors::{ApplicationError, ApplicationResult};
use auth_domain::events::DomainEvent;
use std::sync::Arc;

/// Transaction context that manages database transactions and event publishing
/// Ensures consistency between data changes and event publishing
#[async_trait::async_trait]
pub trait TransactionManager: Send + Sync + Clone {
    type Transaction: Transaction;

    /// Begin a new transaction
    async fn begin(&self) -> ApplicationResult<Self::Transaction>;
}

/// Active transaction that coordinates database operations and event publishing
/// All operations within a transaction are atomic - either all succeed or all rollback
#[async_trait::async_trait]
pub trait Transaction: Send + Sync {
    /// Commit all changes and publish events atomically
    /// If event publishing fails, the transaction is still committed but error is returned
    async fn commit(self) -> ApplicationResult<()>;

    /// Rollback all changes - events are not published
    async fn rollback(self) -> ApplicationResult<()>;

    /// Add event to be published when transaction commits
    /// Events are published in order they were added
    fn add_event(&mut self, event: DomainEvent);

    /// Add multiple events to be published when transaction commits
    fn add_events(&mut self, events: Vec<DomainEvent>);

    /// Get reference to accumulated events (for testing/debugging)
    fn pending_events(&self) -> &[DomainEvent];
}

/// Unit of work pattern for managing transactions and event publishing
/// Ensures that all operations within a unit of work are atomic
pub struct UnitOfWork<TM: TransactionManager> {
    transaction_manager: TM,
    event_publisher: Arc<dyn EventPublisher>,
}

#[async_trait::async_trait]
pub trait EventPublisher: Send + Sync {
    /// Publish a single event
    async fn publish(&self, event: DomainEvent) -> ApplicationResult<()>;

    /// Publish multiple events in order
    async fn publish_batch(&self, events: Vec<DomainEvent>) -> ApplicationResult<()>;
}

impl<TM: TransactionManager> UnitOfWork<TM> {
    pub fn new(transaction_manager: TM, event_publisher: Arc<dyn EventPublisher>) -> Self {
        Self {
            transaction_manager,
            event_publisher,
        }
    }

    /// Execute a closure within a transaction context
    /// If the closure succeeds, the transaction is committed and events are published
    /// If the closure fails or event publishing fails, the transaction is rolled back
    pub async fn execute<F, R>(&self, operation: F) -> ApplicationResult<R>
    where
        F: for<'a> FnOnce(
            &'a mut TM::Transaction,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = ApplicationResult<R>> + Send + 'a>,
        >,
    {
        let mut transaction = self.transaction_manager.begin().await?;

        match operation(&mut transaction).await {
            Ok(result) => {
                // Get events before consuming transaction
                let events = transaction.pending_events().to_vec();

                // Commit transaction first - this ensures data consistency
                match transaction.commit().await {
                    Ok(()) => {
                        // Publish events after successful commit
                        // If event publishing fails, data is still committed but we return error
                        if !events.is_empty() {
                            if let Err(e) = self.event_publisher.publish_batch(events).await {
                                tracing::warn!(
                                    "Transaction committed but event publishing failed: {}",
                                    e
                                );
                                return Err(ApplicationError::EventPublishingFailed(e.to_string()));
                            }
                        }
                        Ok(result)
                    }
                    Err(e) => {
                        tracing::error!("Transaction commit failed: {}", e);
                        Err(e)
                    }
                }
            }
            Err(e) => {
                // Operation failed - rollback transaction
                if let Err(rollback_err) = transaction.rollback().await {
                    tracing::error!("Transaction rollback failed: {}", rollback_err);
                    // Return original error, but log rollback failure
                }
                Err(e)
            }
        }
    }
}

/// In-memory transaction implementation for testing and simple scenarios
/// Not suitable for production use with persistent storage
pub struct InMemoryTransaction {
    committed: bool,
    events: Vec<DomainEvent>,
}

impl InMemoryTransaction {
    pub fn new() -> Self {
        Self {
            committed: false,
            events: Vec::new(),
        }
    }
}

impl Default for InMemoryTransaction {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait::async_trait]
impl Transaction for InMemoryTransaction {
    async fn commit(mut self) -> ApplicationResult<()> {
        self.committed = true;
        Ok(())
    }

    async fn rollback(self) -> ApplicationResult<()> {
        // Events are dropped when transaction is dropped
        Ok(())
    }

    fn add_event(&mut self, event: DomainEvent) {
        self.events.push(event);
    }

    fn add_events(&mut self, mut events: Vec<DomainEvent>) {
        self.events.append(&mut events);
    }

    fn pending_events(&self) -> &[DomainEvent] {
        &self.events
    }
}

/// In-memory transaction manager for testing
#[derive(Clone)]
pub struct InMemoryTransactionManager;

impl InMemoryTransactionManager {
    pub fn new() -> Self {
        Self
    }
}

impl Default for InMemoryTransactionManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait::async_trait]
impl TransactionManager for InMemoryTransactionManager {
    type Transaction = InMemoryTransaction;

    async fn begin(&self) -> ApplicationResult<Self::Transaction> {
        Ok(InMemoryTransaction::new())
    }
}

/// Mock event publisher for testing
pub struct MockEventPublisher {
    should_fail: bool,
    published_events: std::sync::Mutex<Vec<DomainEvent>>,
}

impl MockEventPublisher {
    pub fn new() -> Self {
        Self {
            should_fail: false,
            published_events: std::sync::Mutex::new(Vec::new()),
        }
    }

    pub fn with_failure() -> Self {
        Self {
            should_fail: true,
            published_events: std::sync::Mutex::new(Vec::new()),
        }
    }

    pub fn published_events(&self) -> Vec<DomainEvent> {
        self.published_events.lock().unwrap().clone()
    }

    pub fn event_count(&self) -> usize {
        self.published_events.lock().unwrap().len()
    }
}

impl Default for MockEventPublisher {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait::async_trait]
impl EventPublisher for MockEventPublisher {
    async fn publish(&self, event: DomainEvent) -> ApplicationResult<()> {
        if self.should_fail {
            return Err(ApplicationError::EventPublishingFailed(
                "Mock failure".to_string(),
            ));
        }

        self.published_events.lock().unwrap().push(event);
        Ok(())
    }

    async fn publish_batch(&self, events: Vec<DomainEvent>) -> ApplicationResult<()> {
        if self.should_fail {
            return Err(ApplicationError::EventPublishingFailed(
                "Mock batch failure".to_string(),
            ));
        }

        self.published_events.lock().unwrap().extend(events);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::value_objects::UserId;

    #[tokio::test]
    async fn should_commit_transaction_and_publish_events() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let event_publisher = Arc::new(MockEventPublisher::new());
        let uow = UnitOfWork::new(transaction_manager, event_publisher.clone());

        let test_event = DomainEvent::UserActivated {
            user_id: UserId::new(),
        };

        // Act
        let result = uow
            .execute(|tx| {
                Box::pin(async move {
                    tx.add_event(test_event.clone());
                    Ok("success".to_string())
                })
            })
            .await;

        // Assert
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
        assert_eq!(event_publisher.event_count(), 1);
    }

    #[tokio::test]
    async fn should_rollback_on_operation_failure() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let event_publisher = Arc::new(MockEventPublisher::new());
        let uow = UnitOfWork::new(transaction_manager, event_publisher.clone());

        let test_event = DomainEvent::UserActivated {
            user_id: UserId::new(),
        };

        // Act
        let result = uow
            .execute(|tx| {
                Box::pin(async move {
                    tx.add_event(test_event);
                    Err::<String, ApplicationError>(ApplicationError::InvalidInput(
                        "test error".to_string(),
                    ))
                })
            })
            .await;

        // Assert
        assert!(result.is_err());
        assert_eq!(event_publisher.event_count(), 0); // No events should be published
    }

    #[tokio::test]
    async fn should_return_error_on_event_publishing_failure() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let event_publisher = Arc::new(MockEventPublisher::with_failure());
        let uow = UnitOfWork::new(transaction_manager, event_publisher.clone());

        let test_event = DomainEvent::UserActivated {
            user_id: UserId::new(),
        };

        // Act
        let result = uow
            .execute(|tx| {
                Box::pin(async move {
                    tx.add_event(test_event);
                    Ok("success".to_string())
                })
            })
            .await;

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::EventPublishingFailed(_) => {} // Expected
            _ => panic!("Expected EventPublishingFailed error"),
        }
    }

    #[tokio::test]
    async fn should_handle_multiple_events() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let event_publisher = Arc::new(MockEventPublisher::new());
        let uow = UnitOfWork::new(transaction_manager, event_publisher.clone());

        let events = vec![
            DomainEvent::UserActivated {
                user_id: UserId::new(),
            },
            DomainEvent::UserDeactivated {
                user_id: UserId::new(),
            },
        ];

        // Act
        let result = uow
            .execute(|tx| {
                let events_clone = events.clone();
                Box::pin(async move {
                    tx.add_events(events_clone);
                    Ok("success".to_string())
                })
            })
            .await;

        // Assert
        assert!(result.is_ok());
        assert_eq!(event_publisher.event_count(), 2);
    }

    #[test]
    fn should_add_and_track_events_in_transaction() {
        // Arrange
        let mut transaction = InMemoryTransaction::new();
        let event1 = DomainEvent::UserActivated {
            user_id: UserId::new(),
        };
        let event2 = DomainEvent::UserDeactivated {
            user_id: UserId::new(),
        };

        // Act
        transaction.add_event(event1);
        transaction.add_event(event2);

        // Assert
        assert_eq!(transaction.pending_events().len(), 2);
    }
}
