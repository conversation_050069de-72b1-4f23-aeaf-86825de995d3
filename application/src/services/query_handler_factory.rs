// Query handler factory
// Creates query handlers with caching infrastructure wired up

use crate::queries::{
    base::QueryCache,
    check_user_permissions::{CheckUserPermissionsHandler, CheckUserPermissionsQuery},
    get_audit_log::Get<PERSON>uditLogHand<PERSON>,
    get_current_session::{GetCurrentSessionHandler, GetCurrentSessionQuery},
    get_role_details::GetRoleDetailsHandler,
    get_user_profile::{GetUserProfileHandler, GetUserProfileQuery},
    list_user_roles::{ListUserRolesHandler, ListUserRolesQuery},
    list_user_sessions::{ListUserSessionsHandler, ListUserSessionsQuery},
    search_users::SearchUsersHandler,
};
use auth_domain::repositories::{
    AuditLogRepository, RoleRepository, SessionRepository, UserRepository,
};
use std::sync::Arc;

/// Factory for creating query handlers with caching
pub struct QueryHandlerFactory<UR, RR, SR, AR>
where
    UR: UserRepository + <PERSON>lone,
    RR: RoleRepository + Clone,
    SR: SessionRepository + Clone,
    AR: AuditLogRepository + Clone,
{
    user_repository: Arc<UR>,
    role_repository: Arc<RR>,
    session_repository: Arc<SR>,
    audit_repository: Arc<AR>,
}

impl<UR, RR, SR, AR> QueryHandlerFactory<UR, RR, SR, AR>
where
    UR: UserRepository + Clone,
    RR: RoleRepository + Clone,
    SR: SessionRepository + Clone,
    AR: AuditLogRepository + Clone,
{
    pub fn new(
        user_repository: Arc<UR>,
        role_repository: Arc<RR>,
        session_repository: Arc<SR>,
        audit_repository: Arc<AR>,
    ) -> Self {
        Self {
            user_repository,
            role_repository,
            session_repository,
            audit_repository,
        }
    }

    /// Create GetUserProfileHandler with caching
    pub fn create_get_user_profile_handler<C>(&self, cache: C) -> GetUserProfileHandler<UR, C>
    where
        C: QueryCache<GetUserProfileQuery>,
    {
        GetUserProfileHandler::new((*self.user_repository).clone(), cache)
    }

    /// Create CheckUserPermissionsHandler with caching
    pub fn create_check_permissions_handler<C>(
        &self,
        cache: C,
    ) -> CheckUserPermissionsHandler<RR, C>
    where
        C: QueryCache<CheckUserPermissionsQuery>,
    {
        CheckUserPermissionsHandler::new((*self.role_repository).clone(), cache)
    }

    /// Create ListUserRolesHandler with caching
    pub fn create_list_user_roles_handler<C>(&self, cache: C) -> ListUserRolesHandler<RR, C>
    where
        C: QueryCache<ListUserRolesQuery>,
    {
        ListUserRolesHandler::new((*self.role_repository).clone(), cache)
    }

    /// Create GetCurrentSessionHandler with caching
    pub fn create_get_current_session_handler<C>(&self, cache: C) -> GetCurrentSessionHandler<SR, C>
    where
        C: QueryCache<GetCurrentSessionQuery>,
    {
        GetCurrentSessionHandler::new((*self.session_repository).clone(), cache)
    }

    /// Create SearchUsersHandler with rate limiting
    pub fn create_search_users_handler<RL>(&self, rate_limiter: RL) -> SearchUsersHandler<UR, RL>
    where
        RL: AsRef<crate::security::rate_limiter::RateLimiter> + Send + Sync,
    {
        SearchUsersHandler::new((*self.user_repository).clone(), rate_limiter)
    }

    /// Create GetAuditLogHandler with rate limiting
    pub fn create_get_audit_log_handler<RL>(&self, rate_limiter: RL) -> GetAuditLogHandler<AR, RL>
    where
        RL: AsRef<crate::security::rate_limiter::RateLimiter> + Send + Sync,
    {
        GetAuditLogHandler::new((*self.audit_repository).clone(), rate_limiter)
    }

    /// Create GetRoleDetailsHandler with rate limiting
    pub fn create_get_role_details_handler<RL>(
        &self,
        rate_limiter: RL,
    ) -> GetRoleDetailsHandler<RR, RL>
    where
        RL: AsRef<crate::security::rate_limiter::RateLimiter> + Send + Sync,
    {
        GetRoleDetailsHandler::new((*self.role_repository).clone(), rate_limiter)
    }

    /// Create ListUserSessionsHandler with caching
    pub fn create_list_user_sessions_handler<C>(&self, cache: C) -> ListUserSessionsHandler<SR, C>
    where
        C: QueryCache<ListUserSessionsQuery>,
    {
        ListUserSessionsHandler::new((*self.session_repository).clone(), cache)
    }
}

/// Configuration for cache TTLs per query type
pub struct CacheTTLConfig {
    pub user_profile_ttl: std::time::Duration,
    pub permissions_ttl: std::time::Duration,
    pub user_roles_ttl: std::time::Duration,
    pub session_ttl: std::time::Duration,
    pub search_ttl: std::time::Duration,
    pub audit_log_ttl: std::time::Duration,
    pub role_details_ttl: std::time::Duration,
    pub user_sessions_ttl: std::time::Duration,
}

impl Default for CacheTTLConfig {
    fn default() -> Self {
        Self {
            user_profile_ttl: std::time::Duration::from_secs(300), // 5 minutes
            permissions_ttl: std::time::Duration::from_secs(300),  // 5 minutes
            user_roles_ttl: std::time::Duration::from_secs(300),   // 5 minutes
            session_ttl: std::time::Duration::from_secs(60),       // 1 minute
            search_ttl: std::time::Duration::from_secs(30),        // 30 seconds
            audit_log_ttl: std::time::Duration::from_secs(120),    // 2 minutes
            role_details_ttl: std::time::Duration::from_secs(600), // 10 minutes
            user_sessions_ttl: std::time::Duration::from_secs(60), // 1 minute
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::queries::base::{Query, QueryCache as QC};
    use async_trait::async_trait;
    use std::time::Duration;

    // Mock cache for testing
    #[allow(dead_code)]
    struct MockCache<Q: Query> {
        _phantom: std::marker::PhantomData<Q>,
    }

    #[allow(dead_code)]
    impl<Q: Query> MockCache<Q> {
        fn new() -> Self {
            Self {
                _phantom: std::marker::PhantomData,
            }
        }
    }

    #[async_trait]
    impl<Q> QC<Q> for MockCache<Q>
    where
        Q: Query + Send + Sync,
        Q::Result: Send + Sync,
    {
        async fn get(&self, _query: &Q) -> Option<Q::Result> {
            None
        }

        async fn set(&self, _query: &Q, _result: &Q::Result, _ttl: Duration) {
            // No-op
        }

        async fn invalidate(&self, _pattern: &str) {
            // No-op
        }
    }

    #[test]
    fn test_cache_ttl_config_defaults() {
        let config = CacheTTLConfig::default();

        assert_eq!(config.user_profile_ttl, Duration::from_secs(300));
        assert_eq!(config.permissions_ttl, Duration::from_secs(300));
        assert_eq!(config.user_roles_ttl, Duration::from_secs(300));
        assert_eq!(config.session_ttl, Duration::from_secs(60));
        assert_eq!(config.search_ttl, Duration::from_secs(30));
        assert_eq!(config.audit_log_ttl, Duration::from_secs(120));
        assert_eq!(config.role_details_ttl, Duration::from_secs(600));
        assert_eq!(config.user_sessions_ttl, Duration::from_secs(60));
    }

    #[tokio::test]
    async fn test_factory_creation() {
        // This test verifies that the factory can be created
        // In a real implementation, you would test handler creation
        // with mock repositories and caches

        let config = CacheTTLConfig::default();
        assert!(config.user_profile_ttl > Duration::ZERO);
    }
}
