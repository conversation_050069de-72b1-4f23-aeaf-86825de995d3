// Application crate - Use cases and command/query handlers
// This crate orchestrates domain logic and coordinates between different parts

pub mod commands;
pub mod errors;
// pub mod handlers; // Temporarily disabled due to compilation issues
pub mod monitoring;
pub mod queries;
pub mod security;
pub mod services;
pub mod transaction;

#[cfg(test)]
pub mod testing;

// Test macros are automatically exported at crate root via #[macro_export]

// Re-export commonly used types
pub use commands::{authenticate_user::*, base::*, register_user::*};
pub use errors::{ApplicationError, ApplicationResult};
pub use queries::{AsyncQueryHandler, PaginatedResult, Query, QueryContext, QueryHandler};
pub use services::ApplicationService;
pub use transaction::{
    EventPublisher as TransactionEventPublisher, InMemoryTransaction, InMemoryTransactionManager,
    MockEventPublisher, Transaction, TransactionManager, UnitOfWork,
};
