// Query handlers module
// Contains handlers for read operations (queries)

pub mod base;
pub mod check_user_permissions;
pub mod get_audit_log;
pub mod get_current_session;
pub mod get_role_details;
pub mod get_user_profile;
pub mod list_user_roles;
pub mod list_user_sessions;
pub mod monitored_handler;
pub mod search_users;

pub use base::{
    AsyncQueryHandler, PaginatedResult, Query, QueryAuthorizer, QueryCache, QueryContext,
    QueryHandler,
};
pub use check_user_permissions::{CheckUserPermissionsHandler, CheckUserPermissionsQuery};
pub use get_audit_log::{AuditLogItem, GetAuditLogHandler, GetAuditLogQuery, GetAuditLogResult};
pub use get_current_session::{GetCurrentSessionHandler, GetCurrentSessionQuery};
pub use get_role_details::{
    ChildRoleInfo, GetRoleDetailsHandler, GetRoleDetailsQuery, GetRoleDetailsResult,
    PermissionInfo, RoleHierarchyPath,
};
pub use get_user_profile::{GetU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GetUserProfileQuery};
pub use list_user_roles::{ListUserRolesHandler, ListUserRolesQuery};
pub use list_user_sessions::{ListUserSessionsHandler, ListUserSessionsQuery};
pub use monitored_handler::{MonitoredQueryHandler, MonitoredQueryHandlerExt};
pub use search_users::{SearchUsersHandler, SearchUsersQuery, SearchUsersResult, UserSearchItem};
