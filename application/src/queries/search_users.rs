// Search users query and handler
// Implements secure, performant user search with admin authorization and rate limiting

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{AsyncQuery<PERSON><PERSON><PERSON>, Query, QueryContext};
use crate::security::rate_limiter::{RateLimitKey, RateLimiter};
use auth_domain::{
    entities::User,
    repositories::{SortDirection, UserRepository, UserSearchCriteria, UserSortField},
    value_objects::UserId,
};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Query to search users with comprehensive filtering and pagination
/// Requires admin:users:read permission
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct SearchUsersQuery {
    /// Email filter (partial match)
    pub email_filter: Option<String>,
    /// Name filter (partial match on first or last name)
    pub name_filter: Option<String>,
    /// Username filter (partial match)
    pub username_filter: Option<String>,
    /// Role filter (exact match)
    pub role_filter: Option<String>,
    /// Active status filter
    pub status_filter: Option<bool>,
    /// Verification status filter
    pub verification_filter: Option<bool>,
    /// Created date range filter (ISO 8601 strings)
    pub created_after: Option<String>,
    pub created_before: Option<String>,
    /// Last login date range filter (ISO 8601 strings)
    pub last_login_after: Option<String>,
    pub last_login_before: Option<String>,
    /// Pagination parameters
    pub page: Option<usize>,
    pub page_size: Option<usize>,
    /// Sort field and direction
    pub sort_by: Option<String>,
    pub sort_direction: Option<String>,
}

impl Query for SearchUsersQuery {
    type Result = SearchUsersResult;
}

/// User information for search results (safe for API responses)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSearchItem {
    pub user_id: String,
    pub email: String,
    pub username: Option<String>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub is_active: bool,
    pub is_verified: bool,
    pub created_at: std::time::SystemTime,
    pub last_login_at: Option<std::time::SystemTime>,
    pub roles: Vec<String>,
}

/// Search result with pagination metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchUsersResult {
    pub users: Vec<UserSearchItem>,
    pub total: usize,
    pub page: usize,
    pub page_size: usize,
    pub has_next: bool,
    pub has_previous: bool,
    pub query_time_ms: u64,
}

/// Handler for searching users with security and performance optimization
pub struct SearchUsersHandler<UR, RL>
where
    UR: UserRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    user_repository: UR,
    rate_limiter: RL,
}

impl<UR, RL> SearchUsersHandler<UR, RL>
where
    UR: UserRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    pub fn new(user_repository: UR, rate_limiter: RL) -> Self {
        Self {
            user_repository,
            rate_limiter,
        }
    }

    /// Handle the search users query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: SearchUsersQuery,
        context: QueryContext,
    ) -> ApplicationResult<SearchUsersResult> {
        // Start timing for SLA monitoring
        let start = Instant::now();

        // Authorization check - admin only
        self.authorize(&context).await?;

        // Rate limiting check - 10 searches per 60 seconds per admin user
        if let Some(ref user_id) = context.user_id {
            self.check_rate_limit(user_id).await?;
        }

        // Validate and sanitize input
        let validated_query = self.validate_and_sanitize_query(query)?;

        // Build search criteria
        let criteria = self.build_search_criteria(&validated_query)?;

        // Execute search
        let search_result = self
            .user_repository
            .search_users(&criteria)
            .await
            .map_err(|e| ApplicationError::Repository(format!("Search failed: {e}")))?;

        // Transform to API response format
        let user_items: Vec<UserSearchItem> = search_result
            .users
            .into_iter()
            .map(|user| self.user_to_search_item(user))
            .collect();

        // Check SLA compliance
        let elapsed = start.elapsed();
        let query_time_ms = elapsed.as_millis() as u64;

        if elapsed > Duration::from_millis(100) {
            tracing::warn!(
                "SearchUsersQuery exceeded SLA: {:?} - page: {}, filters: {:?}",
                elapsed,
                criteria.page,
                self.summarize_filters(&criteria)
            );
        }

        if elapsed > Duration::from_millis(50) {
            tracing::info!(
                "SearchUsersQuery approaching SLA limit: {:?} - page: {}, filters: {:?}",
                elapsed,
                criteria.page,
                self.summarize_filters(&criteria)
            );
        }

        // Log audit information
        tracing::info!(
            "User search executed - user: {:?}, filters: {:?}, results: {}, time: {}ms",
            context.user_id,
            self.summarize_filters(&criteria),
            search_result.total_count,
            query_time_ms
        );

        Ok(SearchUsersResult {
            users: user_items,
            total: search_result.total_count,
            page: search_result.page,
            page_size: search_result.page_size,
            has_next: search_result.has_next,
            has_previous: search_result.has_previous,
            query_time_ms,
        })
    }

    async fn authorize(&self, context: &QueryContext) -> ApplicationResult<()> {
        // Must be authenticated
        if !context.is_authenticated() {
            return Err(ApplicationError::Forbidden);
        }

        // Must have admin users read permission
        if !context.has_permission("admin:users:read") {
            return Err(ApplicationError::Forbidden);
        }

        Ok(())
    }

    async fn check_rate_limit(&self, user_id: &UserId) -> ApplicationResult<()> {
        let key = RateLimitKey::Account(format!("user_search:{}", user_id.as_str()));

        if self
            .rate_limiter
            .as_ref()
            .check_rate_limit(&key)
            .await
            .is_err()
        {
            tracing::warn!(
                "Rate limit exceeded for user search - user: {}",
                user_id.as_str()
            );
            return Err(ApplicationError::RateLimitExceeded);
        }

        Ok(())
    }

    fn validate_and_sanitize_query(
        &self,
        query: SearchUsersQuery,
    ) -> ApplicationResult<SearchUsersQuery> {
        let mut sanitized = query.clone();

        // Validate and sanitize string filters
        if let Some(ref email) = sanitized.email_filter {
            if email.len() > 100 {
                return Err(ApplicationError::Validation(
                    "Email filter too long".to_string(),
                ));
            }
            sanitized.email_filter = Some(self.sanitize_search_string(email));
        }

        if let Some(ref name) = sanitized.name_filter {
            if name.len() > 100 {
                return Err(ApplicationError::Validation(
                    "Name filter too long".to_string(),
                ));
            }
            sanitized.name_filter = Some(self.sanitize_search_string(name));
        }

        if let Some(ref username) = sanitized.username_filter {
            if username.len() > 50 {
                return Err(ApplicationError::Validation(
                    "Username filter too long".to_string(),
                ));
            }
            sanitized.username_filter = Some(self.sanitize_search_string(username));
        }

        if let Some(ref role) = sanitized.role_filter {
            if role.len() > 50 {
                return Err(ApplicationError::Validation(
                    "Role filter too long".to_string(),
                ));
            }
            sanitized.role_filter = Some(self.sanitize_search_string(role));
        }

        // Validate pagination
        let page = sanitized.page.unwrap_or(0);
        let page_size = sanitized.page_size.unwrap_or(20);

        if page_size == 0 || page_size > 100 {
            return Err(ApplicationError::Validation(
                "Page size must be between 1 and 100".to_string(),
            ));
        }

        sanitized.page = Some(page);
        sanitized.page_size = Some(page_size);

        // Validate sort parameters
        if let Some(ref sort_by) = sanitized.sort_by {
            match sort_by.as_str() {
                "email" | "username" | "first_name" | "last_name" | "created_at"
                | "last_login_at" => {}
                _ => {
                    return Err(ApplicationError::Validation(
                        "Invalid sort field".to_string(),
                    ));
                }
            }
        }

        if let Some(ref sort_dir) = sanitized.sort_direction {
            match sort_dir.as_str() {
                "asc" | "desc" => {}
                _ => {
                    return Err(ApplicationError::Validation(
                        "Sort direction must be 'asc' or 'desc'".to_string(),
                    ));
                }
            }
        }

        Ok(sanitized)
    }

    fn sanitize_search_string(&self, input: &str) -> String {
        // Remove potentially dangerous characters and normalize
        input
            .chars()
            .filter(|c| c.is_alphanumeric() || c.is_whitespace() || "@.-_".contains(*c))
            .collect::<String>()
            .trim()
            .to_lowercase()
    }

    fn build_search_criteria(
        &self,
        query: &SearchUsersQuery,
    ) -> ApplicationResult<UserSearchCriteria> {
        let page = query.page.unwrap_or(0);
        let page_size = query.page_size.unwrap_or(20);

        // Parse date filters
        let created_after = if let Some(ref date_str) = query.created_after {
            Some(self.parse_date_string(date_str)?)
        } else {
            None
        };

        let created_before = if let Some(ref date_str) = query.created_before {
            Some(self.parse_date_string(date_str)?)
        } else {
            None
        };

        let last_login_after = if let Some(ref date_str) = query.last_login_after {
            Some(self.parse_date_string(date_str)?)
        } else {
            None
        };

        let last_login_before = if let Some(ref date_str) = query.last_login_before {
            Some(self.parse_date_string(date_str)?)
        } else {
            None
        };

        // Parse sort parameters
        let sort_by = query.sort_by.as_ref().map(|s| match s.as_str() {
            "email" => UserSortField::Email,
            "username" => UserSortField::Username,
            "first_name" => UserSortField::FirstName,
            "last_name" => UserSortField::LastName,
            "created_at" => UserSortField::CreatedAt,
            "last_login_at" => UserSortField::LastLoginAt,
            _ => UserSortField::CreatedAt, // Default fallback
        });

        let sort_direction = query.sort_direction.as_ref().map(|s| match s.as_str() {
            "desc" => SortDirection::Descending,
            _ => SortDirection::Ascending, // Default to ascending
        });

        Ok(UserSearchCriteria {
            email_filter: query.email_filter.clone(),
            name_filter: query.name_filter.clone(),
            username_filter: query.username_filter.clone(),
            role_filter: query.role_filter.clone(),
            status_filter: query.status_filter,
            verification_filter: query.verification_filter,
            created_after,
            created_before,
            last_login_after,
            last_login_before,
            page,
            page_size,
            sort_by,
            sort_direction,
        })
    }

    fn parse_date_string(&self, date_str: &str) -> ApplicationResult<std::time::SystemTime> {
        // For now, expect Unix timestamp strings
        // In production, you might want to support ISO 8601 format
        let timestamp: u64 = date_str
            .parse()
            .map_err(|_| ApplicationError::Validation("Invalid date format".to_string()))?;

        Ok(std::time::UNIX_EPOCH + Duration::from_secs(timestamp))
    }

    fn user_to_search_item(&self, user: User) -> UserSearchItem {
        UserSearchItem {
            user_id: user.id().as_str().to_string(),
            email: user.email().as_str().to_string(),
            username: Some(user.username().to_string()),
            first_name: None, // User entity doesn't have first_name in domain
            last_name: None,  // User entity doesn't have last_name in domain
            is_active: user.is_active(),
            is_verified: user.is_verified(),
            created_at: user.created_at().into(),
            last_login_at: None, // User entity doesn't have last_login_at in domain
            roles: Vec::new(),   // Would be populated from role repository in real implementation
        }
    }

    fn summarize_filters(&self, criteria: &UserSearchCriteria) -> String {
        let mut filters = Vec::new();

        if criteria.email_filter.is_some() {
            filters.push("email");
        }
        if criteria.name_filter.is_some() {
            filters.push("name");
        }
        if criteria.username_filter.is_some() {
            filters.push("username");
        }
        if criteria.role_filter.is_some() {
            filters.push("role");
        }
        if criteria.status_filter.is_some() {
            filters.push("status");
        }
        if criteria.verification_filter.is_some() {
            filters.push("verification");
        }
        if criteria.created_after.is_some() || criteria.created_before.is_some() {
            filters.push("created_date");
        }
        if criteria.last_login_after.is_some() || criteria.last_login_before.is_some() {
            filters.push("last_login");
        }

        if filters.is_empty() {
            "none".to_string()
        } else {
            filters.join(",")
        }
    }
}

#[async_trait::async_trait]
impl<UR, RL> AsyncQueryHandler<SearchUsersQuery> for SearchUsersHandler<UR, RL>
where
    UR: UserRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    async fn handle(&self, query: SearchUsersQuery) -> ApplicationResult<SearchUsersResult> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::security::rate_limiter::RateLimiter;
    use async_trait::async_trait;
    use auth_domain::{
        entities::User,
        errors::DomainError,
        repositories::{UserRepository, UserSearchResult},
        value_objects::{Email, Password, SessionId, UserId},
    };
    use std::collections::HashMap;

    // Mock user repository for testing
    struct MockUserRepository {
        users: HashMap<String, User>,
        search_delay: Option<Duration>, // For SLA testing
        should_fail: bool,
    }

    impl MockUserRepository {
        fn new() -> Self {
            Self {
                users: HashMap::new(),
                search_delay: None,
                should_fail: false,
            }
        }

        fn with_delay(mut self, delay: Duration) -> Self {
            self.search_delay = Some(delay);
            self
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }

        fn add_user(&mut self, user: User) {
            self.users.insert(user.id().as_str().to_string(), user);
        }
    }

    #[async_trait]
    impl UserRepository for MockUserRepository {
        async fn save(&self, _user: &User) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
            Ok(self.users.get(id.as_str()).cloned())
        }

        async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
            Ok(self.users.values().find(|u| u.email() == email).cloned())
        }

        async fn find_by_username(&self, username: &str) -> Result<Option<User>, DomainError> {
            Ok(self
                .users
                .values()
                .find(|u| u.username() == username)
                .cloned())
        }

        async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
            Ok(self.users.values().any(|u| u.email() == email))
        }

        async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            Ok(self.users.values().cloned().collect())
        }

        async fn count(&self) -> Result<usize, DomainError> {
            Ok(self.users.len())
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            Ok(Vec::new())
        }

        async fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            Ok(Vec::new())
        }

        async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            Ok(Vec::new())
        }

        async fn search_users(
            &self,
            criteria: &UserSearchCriteria,
        ) -> Result<UserSearchResult, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Search failed".to_string()));
            }

            // Simulate network delay if configured
            if let Some(delay) = self.search_delay {
                tokio::time::sleep(delay).await;
            }

            // Simple mock implementation - filter by email if provided
            let mut filtered_users: Vec<User> = self.users.values().cloned().collect();

            if let Some(ref email_filter) = criteria.email_filter {
                filtered_users.retain(|u| {
                    u.email()
                        .as_str()
                        .to_lowercase()
                        .contains(&email_filter.to_lowercase())
                });
            }

            if let Some(ref name_filter) = criteria.name_filter {
                filtered_users.retain(|u| {
                    // Since User entity doesn't have first_name/last_name, we'll match against username
                    let first_name_match = false; // Placeholder
                    let last_name_match = u
                        .username()
                        .to_lowercase()
                        .contains(&name_filter.to_lowercase());
                    first_name_match || last_name_match
                });
            }

            if let Some(status_filter) = criteria.status_filter {
                filtered_users.retain(|u| u.is_active() == status_filter);
            }

            let total_count = filtered_users.len();

            // Apply pagination
            let start = criteria.page * criteria.page_size;
            let end = (start + criteria.page_size).min(total_count);

            let paginated_users = if start < total_count {
                filtered_users[start..end].to_vec()
            } else {
                Vec::new()
            };

            Ok(UserSearchResult::new(
                paginated_users,
                total_count,
                criteria.page,
                criteria.page_size,
            ))
        }
    }

    // Mock rate limiter wrapper
    struct MockRateLimiterWrapper {
        limiter: RateLimiter,
    }

    impl MockRateLimiterWrapper {
        fn new() -> Self {
            Self {
                limiter: RateLimiter::new(),
            }
        }
    }

    impl AsRef<RateLimiter> for MockRateLimiterWrapper {
        fn as_ref(&self) -> &RateLimiter {
            &self.limiter
        }
    }

    // Helper function to create test user
    fn create_test_user(_id: &str, email: &str, _first_name: Option<&str>) -> User {
        let user_email = Email::new(email).unwrap();
        let password = Password::new("SecurePassword123!").unwrap();

        User::new(user_email, password).unwrap()
    }

    #[tokio::test]
    async fn test_search_users_authorization_required() {
        let repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let query = SearchUsersQuery {
            email_filter: Some("<EMAIL>".to_string()),
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Test with unauthenticated context
        let context = QueryContext::new("req-123".to_string());
        let result = handler.handle_with_context(query.clone(), context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));

        // Test with authenticated user but no admin permission
        let user_id = UserId::from_string("user-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-456".to_string(),
            user_id,
            session_id,
            vec!["user:read".to_string()], // No admin permission
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_search_users_with_admin_permission() {
        let mut repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add test users
        let user1 = create_test_user("user-1", "<EMAIL>", Some("Alice"));
        let user2 = create_test_user("user-2", "<EMAIL>", Some("Bob"));
        repository.add_user(user1);
        repository.add_user(user2);

        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let query = SearchUsersQuery {
            email_filter: Some("alice".to_string()),
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        // Create admin context
        let admin_id = UserId::from_string("admin-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 1);
        assert_eq!(result.users.len(), 1);
        assert_eq!(result.users[0].email, "<EMAIL>");
        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 10);
        assert!(!result.has_next);
        assert!(!result.has_previous);
    }

    #[tokio::test]
    async fn test_search_users_rate_limiting() {
        let repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let query = SearchUsersQuery {
            email_filter: Some("test".to_string()),
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-rate-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-rate-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        // Make requests up to the limit (default is 5 per account per window)
        for i in 0..5 {
            let result = handler
                .handle_with_context(query.clone(), context.clone())
                .await;
            assert!(result.is_ok(), "Request {} should succeed", i + 1);
        }

        // The 6th request should be rate limited
        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::RateLimitExceeded)));
    }

    #[tokio::test]
    async fn test_search_users_input_validation() {
        let repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let admin_id = UserId::from_string("admin-validation".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-validation".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        // Test invalid page size
        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(150), // Over the limit
            sort_by: None,
            sort_direction: None,
        };

        let result = handler.handle_with_context(query, context.clone()).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));

        // Test invalid sort field
        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(20),
            sort_by: Some("invalid_field".to_string()),
            sort_direction: None,
        };

        let result = handler.handle_with_context(query, context.clone()).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));

        // Test too long email filter
        let query = SearchUsersQuery {
            email_filter: Some("a".repeat(150)), // Too long
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(20),
            sort_by: None,
            sort_direction: None,
        };

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));
    }

    #[tokio::test]
    async fn test_search_users_pagination() {
        let mut repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add multiple test users
        for i in 1..=15 {
            let user = create_test_user(
                &format!("user-{i}"),
                &format!("user{i}@example.com"),
                Some(&format!("User{i}")),
            );
            repository.add_user(user);
        }

        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let admin_id = UserId::from_string("admin-pagination".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-pagination".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        // Test first page
        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let result = handler
            .handle_with_context(query, context.clone())
            .await
            .unwrap();

        assert_eq!(result.total, 15);
        assert_eq!(result.users.len(), 10);
        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 10);
        assert!(result.has_next);
        assert!(!result.has_previous);

        // Test second page
        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(1),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 15);
        assert_eq!(result.users.len(), 5); // Remaining users
        assert_eq!(result.page, 1);
        assert_eq!(result.page_size, 10);
        assert!(!result.has_next);
        assert!(result.has_previous);
    }

    #[tokio::test]
    async fn test_search_users_name_filter() {
        let mut repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add test users with different usernames (since User entity uses username from email)
        let user1 = create_test_user("user-1", "<EMAIL>", None);
        let user2 = create_test_user("user-2", "<EMAIL>", None);
        let user3 = create_test_user("user-3", "<EMAIL>", None); // Username contains "alice"
        repository.add_user(user1);
        repository.add_user(user2);
        repository.add_user(user3);

        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: Some("alice".to_string()),
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-name-filter".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-name-filter".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 2); // Two users with usernames containing "alice"
        assert_eq!(result.users.len(), 2);
        // Check that both results have usernames containing "alice"
        assert!(
            result
                .users
                .iter()
                .all(|u| u.username.as_ref().unwrap().contains("alice"))
        );
    }

    #[tokio::test]
    async fn test_search_users_status_filter() {
        let mut repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add test users with different active status
        let user1 = create_test_user("user-1", "<EMAIL>", Some("Active"));
        let mut user2 = create_test_user("user-2", "<EMAIL>", Some("Inactive"));
        let _ = user2.deactivate(); // Make user2 inactive

        repository.add_user(user1);
        repository.add_user(user2);

        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: Some(true), // Only active users
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-status-filter".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-status-filter".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 1); // Only active user
        assert_eq!(result.users.len(), 1);
        assert!(result.users[0].is_active);
        assert_eq!(result.users[0].email, "<EMAIL>");
    }

    #[tokio::test]
    async fn test_search_users_performance_sla() {
        // Create repository with artificial delay
        let repository = MockUserRepository::new().with_delay(Duration::from_millis(120));
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-sla-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-sla-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        // Should still complete successfully but exceed SLA
        assert!(result.query_time_ms > 100);
        assert_eq!(result.total, 0); // No users in mock
    }

    #[tokio::test]
    async fn test_search_users_repository_error() {
        let repository = MockUserRepository::new().with_failure();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = SearchUsersHandler::new(repository, rate_limiter);

        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-error-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-error-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Repository(_))));
    }

    #[tokio::test]
    async fn test_input_sanitization() {
        let repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = SearchUsersHandler::new(repository, rate_limiter);

        // Test with potentially dangerous input
        let query = SearchUsersQuery {
            email_filter: Some("<EMAIL>'; DROP TABLE users; --".to_string()),
            name_filter: Some("<script>alert('xss')</script>".to_string()),
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-sanitization".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-sanitization".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        // Should not panic or cause errors - input should be sanitized
        let result = handler.handle_with_context(query, context).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_search_query_defaults() {
        let repository = MockUserRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = SearchUsersHandler::new(repository, rate_limiter);

        // Query with no pagination specified
        let query = SearchUsersQuery {
            email_filter: None,
            name_filter: None,
            username_filter: None,
            role_filter: None,
            status_filter: None,
            verification_filter: None,
            created_after: None,
            created_before: None,
            last_login_after: None,
            last_login_before: None,
            page: None,      // Should default to 0
            page_size: None, // Should default to 20
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-defaults".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-defaults".to_string(),
            admin_id,
            session_id,
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 20);
    }
}
