// List user sessions query and handler
// Implements secure, paginated user session listing with proper authorization

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{AsyncQueryHandler, PaginatedResult, Query, QueryCache, QueryContext};
use auth_domain::{repositories::SessionRepository, value_objects::UserId};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Query to list sessions for a specific user
#[derive(Debug, Clone, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub struct ListUserSessionsQuery {
    pub user_id: String,
    pub active_only: bool,
    pub page: usize,
    pub page_size: usize,
}

impl Query for ListUserSessionsQuery {
    type Result = PaginatedResult<SessionInfo>;
}

/// Session information for API responses
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SessionInfo {
    pub session_id: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub is_active: bool,
    pub is_valid: bool,
    pub created_at: std::time::SystemTime,
    pub last_accessed: std::time::SystemTime,
    pub expires_at: std::time::SystemTime,
}

/// Handler for listing user sessions with security and pagination
pub struct ListUserSessionsHandler<SR, C>
where
    SR: SessionRepository,
    C: QueryCache<ListUserSessionsQuery>,
{
    session_repository: SR,
    cache: C,
}

impl<SR, C> ListUserSessionsHandler<SR, C>
where
    SR: SessionRepository,
    C: QueryCache<ListUserSessionsQuery>,
{
    pub fn new(session_repository: SR, cache: C) -> Self {
        Self {
            session_repository,
            cache,
        }
    }

    /// Handle the list user sessions query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: ListUserSessionsQuery,
        context: QueryContext,
    ) -> ApplicationResult<PaginatedResult<SessionInfo>> {
        // Start timing for SLA monitoring
        let start = Instant::now();

        // Check cache first for better performance
        if let Some(cached_result) = self.cache.get(&query).await {
            // Still check authorization for cached results
            self.authorize(&query, &context).await?;
            return Ok(cached_result);
        }

        // Authorization check
        self.authorize(&query, &context).await?;

        // Validate and parse user ID
        let user_id = UserId::from_string(query.user_id.clone())
            .map_err(|e| ApplicationError::Validation(format!("Invalid user ID: {e}")))?;

        // Fetch sessions from repository
        let sessions = if query.active_only {
            self.session_repository
                .find_active_by_user(&user_id)
                .await
                .map_err(|e| ApplicationError::Repository(format!("Database error: {e}")))?
        } else {
            self.session_repository
                .find_all_by_user(&user_id)
                .await
                .map_err(|e| ApplicationError::Repository(format!("Database error: {e}")))?
        };

        // Transform sessions to SessionInfo
        let session_infos: Vec<SessionInfo> = sessions
            .into_iter()
            .map(|session| SessionInfo {
                session_id: session.id().as_str().to_string(),
                ip_address: session.ip_address().map(|s| s.to_string()),
                user_agent: session.user_agent().map(|s| s.to_string()),
                is_active: session.is_active(),
                is_valid: session.is_valid(),
                created_at: session.created_at().into(),
                last_accessed: session.last_accessed().into(),
                expires_at: session.expires_at().into(),
            })
            .collect();

        // Apply pagination
        let total = session_infos.len();
        let start_index = query.page * query.page_size;
        let end_index = std::cmp::min(start_index + query.page_size, total);

        let paginated_items = if start_index < total {
            session_infos[start_index..end_index].to_vec()
        } else {
            Vec::new()
        };

        let result = PaginatedResult::new(paginated_items, total, query.page, query.page_size);

        // Check SLA compliance
        let elapsed = start.elapsed();
        if elapsed > Duration::from_millis(100) {
            tracing::warn!(
                "ListUserSessionsQuery exceeded SLA: {:?} for user {}",
                elapsed,
                query.user_id
            );
        }

        // Cache the result for future requests (TTL: 2 minutes - sessions change frequently)
        self.cache
            .set(&query, &result, Duration::from_secs(120))
            .await;

        Ok(result)
    }

    async fn authorize(
        &self,
        query: &ListUserSessionsQuery,
        context: &QueryContext,
    ) -> ApplicationResult<()> {
        // Allow users to view their own sessions
        if let Some(ref user_id) = context.user_id {
            if user_id.as_str() == query.user_id {
                return Ok(());
            }
        }

        // Allow admins to view any user's sessions
        if context.has_permission("admin:sessions:read") {
            return Ok(());
        }

        // Deny access for unauthenticated users or insufficient permissions
        Err(ApplicationError::Forbidden)
    }
}

#[async_trait::async_trait]
impl<SR, C> AsyncQueryHandler<ListUserSessionsQuery> for ListUserSessionsHandler<SR, C>
where
    SR: SessionRepository,
    C: QueryCache<ListUserSessionsQuery>,
{
    async fn handle(
        &self,
        query: ListUserSessionsQuery,
    ) -> ApplicationResult<PaginatedResult<SessionInfo>> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use auth_domain::{
        entities::Session,
        errors::DomainError,
        value_objects::{SessionId, UserId},
    };
    use std::collections::HashMap;

    // Mock session repository for testing
    struct MockSessionRepository {
        sessions: HashMap<String, Vec<Session>>,
    }

    impl MockSessionRepository {
        fn new() -> Self {
            Self {
                sessions: HashMap::new(),
            }
        }

        fn add_sessions_for_user(&mut self, user_id: &str, sessions: Vec<Session>) {
            self.sessions.insert(user_id.to_string(), sessions);
        }
    }

    #[async_trait]
    impl SessionRepository for MockSessionRepository {
        async fn save(&self, _session: &Session) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_by_id(&self, _id: &SessionId) -> Result<Option<Session>, DomainError> {
            Ok(None)
        }

        async fn find_active_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
            let sessions = self
                .sessions
                .get(user_id.as_str())
                .cloned()
                .unwrap_or_default();
            Ok(sessions.into_iter().filter(|s| s.is_active()).collect())
        }

        async fn find_all_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
            Ok(self
                .sessions
                .get(user_id.as_str())
                .cloned()
                .unwrap_or_default())
        }

        async fn delete(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn delete_all_by_user(&self, _user_id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn count_active_by_user(&self, _user_id: &UserId) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn find_by_ip_address(&self, _ip_address: &str) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn update_last_accessed(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn invalidate(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn invalidate_all_except(
            &self,
            _user_id: &UserId,
            _except_session_id: &SessionId,
        ) -> Result<(), DomainError> {
            unimplemented!()
        }
    }

    // Mock cache for testing
    struct MockQueryCache;

    #[async_trait]
    impl QueryCache<ListUserSessionsQuery> for MockQueryCache {
        async fn get(
            &self,
            _query: &ListUserSessionsQuery,
        ) -> Option<PaginatedResult<SessionInfo>> {
            None // Always cache miss for simplicity
        }

        async fn set(
            &self,
            _query: &ListUserSessionsQuery,
            _result: &PaginatedResult<SessionInfo>,
            _ttl: Duration,
        ) {
            // No-op for testing
        }

        async fn invalidate(&self, _pattern: &str) {
            // No-op for testing
        }
    }

    #[tokio::test]
    async fn test_list_user_sessions_success() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-123";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create test sessions
        let session1 = Session::new(
            parsed_user_id.clone(),
            3600,
            Some("127.0.0.1".to_string()),
            Some("Test Browser".to_string()),
        );
        let session2 = Session::new(
            parsed_user_id.clone(),
            3600,
            Some("192.168.1.1".to_string()),
            Some("Mobile App".to_string()),
        );

        repository.add_sessions_for_user(user_id, vec![session1, session2]);

        let handler = ListUserSessionsHandler::new(repository, cache);

        let query = ListUserSessionsQuery {
            user_id: user_id.to_string(),
            active_only: false,
            page: 0,
            page_size: 10,
        };

        // Create context for the same user (self-access)
        let context = QueryContext::for_user(
            "req-123".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 2);
        assert_eq!(result.items.len(), 2);
        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 10);
        assert!(!result.has_next);
        assert!(!result.has_previous);

        // Verify session details
        let first_session = &result.items[0];
        assert!(first_session.is_active);
        assert!(first_session.is_valid);
        assert_eq!(first_session.ip_address, Some("127.0.0.1".to_string()));
    }

    #[tokio::test]
    async fn test_authorization_denied() {
        let repository = MockSessionRepository::new();
        let cache = MockQueryCache;
        let handler = ListUserSessionsHandler::new(repository, cache);

        let query = ListUserSessionsQuery {
            user_id: "target-user".to_string(),
            active_only: false,
            page: 0,
            page_size: 10,
        };

        // Create context for different user without admin permissions
        let context = QueryContext::for_user(
            "req-unauthorized".to_string(),
            UserId::from_string("other-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()], // No admin permissions
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_admin_can_view_any_user_sessions() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let target_user_id = "target-user";
        let parsed_target_user_id = UserId::from_string(target_user_id.to_string()).unwrap();

        // Create test session for target user
        let session = Session::new(
            parsed_target_user_id,
            3600,
            Some("********".to_string()),
            Some("Admin Browser".to_string()),
        );

        repository.add_sessions_for_user(target_user_id, vec![session]);

        let handler = ListUserSessionsHandler::new(repository, cache);

        let query = ListUserSessionsQuery {
            user_id: target_user_id.to_string(),
            active_only: false,
            page: 0,
            page_size: 10,
        };

        // Create admin context
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:sessions:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 1);
        assert_eq!(result.items.len(), 1);
        assert_eq!(result.items[0].ip_address, Some("********".to_string()));
    }

    #[tokio::test]
    async fn test_pagination() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let user_id = "paginated-user";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create 5 test sessions
        let sessions: Vec<Session> = (0..5)
            .map(|i| {
                Session::new(
                    parsed_user_id.clone(),
                    3600,
                    Some(format!("192.168.1.{i}")),
                    Some(format!("Browser {i}")),
                )
            })
            .collect();

        repository.add_sessions_for_user(user_id, sessions);

        let handler = ListUserSessionsHandler::new(repository, cache);

        // Test first page
        let query = ListUserSessionsQuery {
            user_id: user_id.to_string(),
            active_only: false,
            page: 0,
            page_size: 2,
        };

        let context = QueryContext::for_user(
            "req-page1".to_string(),
            parsed_user_id.clone(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 5);
        assert_eq!(result.items.len(), 2);
        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 2);
        assert!(result.has_next);
        assert!(!result.has_previous);

        // Test second page
        let query = ListUserSessionsQuery {
            user_id: user_id.to_string(),
            active_only: false,
            page: 1,
            page_size: 2,
        };

        let context = QueryContext::for_user(
            "req-page2".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.total, 5);
        assert_eq!(result.items.len(), 2);
        assert_eq!(result.page, 1);
        assert_eq!(result.page_size, 2);
        assert!(result.has_next);
        assert!(result.has_previous);
    }
}
