// Check user permissions query and handler
// Implements secure permission checking with caching for high performance

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{AsyncQueryHandler, Query, QueryCache, QueryContext};
use auth_domain::{repositories::RoleRepository, value_objects::UserId};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::time::{Duration, Instant};

/// Query to check if a user has specific permissions
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct CheckUserPermissionsQuery {
    pub user_id: String,
    pub permissions: Vec<String>,
    pub resource_id: Option<String>,
    pub check_all: bool, // true = AND (all permissions required), false = OR (any permission sufficient)
}

impl Query for CheckUserPermissionsQuery {
    type Result = PermissionCheckResult;
}

/// Result of permission check with detailed information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PermissionCheckResult {
    pub has_permission: bool,
    pub granted_permissions: Vec<String>,
    pub denied_permissions: Vec<String>,
    pub effective_permissions: Vec<String>, // All permissions user has through roles
}

/// Handler for checking user permissions with caching and security
pub struct CheckUserPermissionsHandler<RR, C>
where
    RR: RoleRepository,
    C: QueryCache<CheckUserPermissionsQuery>,
{
    role_repository: RR,
    cache: C,
}

impl<RR, C> CheckUserPermissionsHandler<RR, C>
where
    RR: RoleRepository,
    C: QueryCache<CheckUserPermissionsQuery>,
{
    pub fn new(role_repository: RR, cache: C) -> Self {
        Self {
            role_repository,
            cache,
        }
    }

    /// Handle the permission check query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: CheckUserPermissionsQuery,
        context: QueryContext,
    ) -> ApplicationResult<PermissionCheckResult> {
        // Start timing for SLA monitoring
        let start = Instant::now();

        // Check cache first for better performance (permissions change infrequently)
        if let Some(cached_result) = self.cache.get(&query).await {
            // Still check authorization for cached results
            self.authorize(&query, &context).await?;
            return Ok(cached_result);
        }

        // Authorization check
        self.authorize(&query, &context).await?;

        // Validate and parse user ID
        let user_id = UserId::from_string(query.user_id.clone())
            .map_err(|e| ApplicationError::Validation(format!("Invalid user ID: {e}")))?;

        // Fetch user's roles to determine effective permissions
        let roles = self
            .role_repository
            .find_by_user(&user_id)
            .await
            .map_err(|e| ApplicationError::Repository(format!("Database error: {e}")))?;

        // Build effective permission set from all roles
        let effective_permissions = self.build_effective_permissions(&roles).await?;

        // Check which requested permissions are granted/denied
        let mut granted_permissions = Vec::new();
        let mut denied_permissions = Vec::new();

        for permission in &query.permissions {
            if effective_permissions.contains(permission) {
                granted_permissions.push(permission.clone());
            } else {
                denied_permissions.push(permission.clone());
            }
        }

        // Determine overall permission result based on check_all flag
        let has_permission = if query.check_all {
            // AND logic: all permissions must be granted
            denied_permissions.is_empty()
        } else {
            // OR logic: at least one permission must be granted
            !granted_permissions.is_empty()
        };

        let result = PermissionCheckResult {
            has_permission,
            granted_permissions,
            denied_permissions,
            effective_permissions: effective_permissions.into_iter().collect(),
        };

        // Check SLA compliance
        let elapsed = start.elapsed();
        if elapsed > Duration::from_millis(100) {
            tracing::warn!(
                "CheckUserPermissionsQuery exceeded SLA: {:?} for user {}",
                elapsed,
                query.user_id
            );
        }

        // Cache the result for future requests (TTL: 5 minutes - permissions are relatively stable)
        self.cache
            .set(&query, &result, Duration::from_secs(300))
            .await;

        Ok(result)
    }

    async fn authorize(
        &self,
        query: &CheckUserPermissionsQuery,
        context: &QueryContext,
    ) -> ApplicationResult<()> {
        // Allow users to check their own permissions
        if let Some(ref user_id) = context.user_id {
            if user_id.as_str() == query.user_id {
                return Ok(());
            }
        }

        // Allow admins to check any user's permissions
        if context.has_permission("admin:permissions:read") {
            return Ok(());
        }

        // Allow service accounts to check permissions (for authorization middleware)
        if context.has_permission("service:permissions:check") {
            return Ok(());
        }

        // Deny access for unauthenticated users or insufficient permissions
        Err(ApplicationError::Forbidden)
    }

    async fn build_effective_permissions(
        &self,
        roles: &[auth_domain::entities::Role],
    ) -> ApplicationResult<HashSet<String>> {
        let mut permissions = HashSet::new();

        for role in roles {
            // Add direct permissions from the role
            for permission in role.permissions() {
                permissions.insert(permission.as_str().to_string());
            }

            // Handle role hierarchy if role has a parent
            if let Some(parent_id) = role.parent_role() {
                if let Some(parent_role) = self
                    .role_repository
                    .find_by_id(parent_id)
                    .await
                    .map_err(|e| {
                        ApplicationError::Repository(format!("Error fetching parent role: {e}"))
                    })?
                {
                    // Add inherited permissions from parent role
                    for permission in parent_role.permissions() {
                        permissions.insert(permission.as_str().to_string());
                    }
                }
            }
        }

        Ok(permissions)
    }
}

#[async_trait::async_trait]
impl<RR, C> AsyncQueryHandler<CheckUserPermissionsQuery> for CheckUserPermissionsHandler<RR, C>
where
    RR: RoleRepository,
    C: QueryCache<CheckUserPermissionsQuery>,
{
    async fn handle(
        &self,
        query: CheckUserPermissionsQuery,
    ) -> ApplicationResult<PermissionCheckResult> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use auth_domain::{
        entities::{Permission, Role},
        errors::DomainError,
        value_objects::{RoleId, SessionId, UserId},
    };
    use std::collections::HashMap;

    // Mock role repository for testing
    struct MockRoleRepository {
        user_roles: HashMap<String, Vec<Role>>,
        roles_by_id: HashMap<String, Role>,
    }

    impl MockRoleRepository {
        fn new() -> Self {
            Self {
                user_roles: HashMap::new(),
                roles_by_id: HashMap::new(),
            }
        }

        fn add_user_roles(&mut self, user_id: &str, roles: Vec<Role>) {
            for role in &roles {
                self.roles_by_id
                    .insert(role.id().as_str().to_string(), role.clone());
            }
            self.user_roles.insert(user_id.to_string(), roles);
        }

        #[allow(dead_code)]
        fn add_role(&mut self, role: Role) {
            self.roles_by_id
                .insert(role.id().as_str().to_string(), role);
        }
    }

    #[async_trait]
    impl RoleRepository for MockRoleRepository {
        async fn save(&self, _role: &Role) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_by_id(&self, id: &RoleId) -> Result<Option<Role>, DomainError> {
            Ok(self.roles_by_id.get(id.as_str()).cloned())
        }

        async fn find_by_name(&self, _name: &str) -> Result<Option<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_by_user(&self, user_id: &UserId) -> Result<Vec<Role>, DomainError> {
            Ok(self
                .user_roles
                .get(user_id.as_str())
                .cloned()
                .unwrap_or_default())
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &RoleId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn exists_by_name(&self, _name: &str) -> Result<bool, DomainError> {
            unimplemented!()
        }

        async fn count(&self) -> Result<usize, DomainError> {
            Ok(self.roles_by_id.len())
        }

        async fn find_active(&self) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_by_permission(&self, _permission: &str) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_children(&self, _parent_id: &RoleId) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn get_hierarchy(&self) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }
    }

    // Mock cache for testing
    struct MockQueryCache;

    #[async_trait]
    impl QueryCache<CheckUserPermissionsQuery> for MockQueryCache {
        async fn get(&self, _query: &CheckUserPermissionsQuery) -> Option<PermissionCheckResult> {
            None // Always cache miss for simplicity
        }

        async fn set(
            &self,
            _query: &CheckUserPermissionsQuery,
            _result: &PermissionCheckResult,
            _ttl: Duration,
        ) {
            // No-op for testing
        }

        async fn invalidate(&self, _pattern: &str) {
            // No-op for testing
        }
    }

    #[tokio::test]
    async fn test_check_permissions_success_all_granted() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-123";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create a role with admin permission
        let admin_role_id = RoleId::builtin("sysadmin").unwrap();
        let admin_role = Role::new(
            admin_role_id,
            "SystemAdmin".to_string(),
            Some("Full system access".to_string()),
            vec![Permission::Admin],
        )
        .unwrap();

        repository.add_user_roles(user_id, vec![admin_role]);

        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: user_id.to_string(),
            permissions: vec!["admin".to_string()],
            resource_id: None,
            check_all: true,
        };

        // Create context for the same user (self-check)
        let context = QueryContext::for_user(
            "req-123".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert!(result.has_permission);
        assert_eq!(result.granted_permissions, vec!["admin"]);
        assert!(result.denied_permissions.is_empty());
        assert!(result.effective_permissions.contains(&"admin".to_string()));
    }

    #[tokio::test]
    async fn test_check_permissions_some_denied_check_all() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-456";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create a role with only read permission
        let read_role_id = RoleId::builtin("reader").unwrap();
        let read_role = Role::new(
            read_role_id,
            "Reader".to_string(),
            Some("Basic read access".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        repository.add_user_roles(user_id, vec![read_role]);

        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: user_id.to_string(),
            permissions: vec!["read".to_string(), "admin".to_string()],
            resource_id: None,
            check_all: true, // AND logic - both permissions required
        };

        let context = QueryContext::for_user(
            "req-456".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert!(!result.has_permission); // Should fail because admin permission is missing
        assert_eq!(result.granted_permissions, vec!["read"]);
        assert_eq!(result.denied_permissions, vec!["admin"]);
        assert!(result.effective_permissions.contains(&"read".to_string()));
        assert!(!result.effective_permissions.contains(&"admin".to_string()));
    }

    #[tokio::test]
    async fn test_check_permissions_some_granted_check_any() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-789";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create a role with only read permission
        let read_role_id = RoleId::builtin("reader").unwrap();
        let read_role = Role::new(
            read_role_id,
            "Reader".to_string(),
            Some("Basic read access".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        repository.add_user_roles(user_id, vec![read_role]);

        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: user_id.to_string(),
            permissions: vec!["read".to_string(), "admin".to_string()],
            resource_id: None,
            check_all: false, // OR logic - any permission is sufficient
        };

        let context = QueryContext::for_user(
            "req-789".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert!(result.has_permission); // Should succeed because read permission is present
        assert_eq!(result.granted_permissions, vec!["read"]);
        assert_eq!(result.denied_permissions, vec!["admin"]);
    }

    #[tokio::test]
    async fn test_authorization_denied() {
        let repository = MockRoleRepository::new();
        let cache = MockQueryCache;
        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: "target-user".to_string(),
            permissions: vec!["admin".to_string()],
            resource_id: None,
            check_all: true,
        };

        // Create context for different user without admin permissions
        let context = QueryContext::for_user(
            "req-unauthorized".to_string(),
            UserId::from_string("other-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()], // No admin or service permissions
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_admin_can_check_any_user_permissions() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let target_user_id = "target-user";
        let _parsed_target_user_id = UserId::from_string(target_user_id.to_string()).unwrap();

        // Create a role for target user
        let read_role_id = RoleId::builtin("reader").unwrap();
        let read_role = Role::new(
            read_role_id,
            "Reader".to_string(),
            Some("Basic read access".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        repository.add_user_roles(target_user_id, vec![read_role]);

        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: target_user_id.to_string(),
            permissions: vec!["read".to_string()],
            resource_id: None,
            check_all: true,
        };

        // Create admin context
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:permissions:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert!(result.has_permission);
        assert_eq!(result.granted_permissions, vec!["read"]);
        assert!(result.denied_permissions.is_empty());
    }

    #[tokio::test]
    async fn test_service_account_can_check_permissions() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let target_user_id = "target-user";
        let _parsed_target_user_id = UserId::from_string(target_user_id.to_string()).unwrap();

        // Create a role for target user
        let read_role_id = RoleId::builtin("reader").unwrap();
        let read_role = Role::new(
            read_role_id,
            "Reader".to_string(),
            Some("Basic read access".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        repository.add_user_roles(target_user_id, vec![read_role]);

        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: target_user_id.to_string(),
            permissions: vec!["read".to_string()],
            resource_id: None,
            check_all: true,
        };

        // Create service account context
        let context = QueryContext::for_user(
            "req-service".to_string(),
            UserId::from_string("service-account".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["service:permissions:check".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert!(result.has_permission);
        assert_eq!(result.granted_permissions, vec!["read"]);
    }

    #[tokio::test]
    async fn test_invalid_user_id() {
        let repository = MockRoleRepository::new();
        let cache = MockQueryCache;
        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: "".to_string(),
            permissions: vec!["read".to_string()],
            resource_id: None,
            check_all: true,
        };

        // Create admin context to bypass authorization for this test
        let context = QueryContext::for_user(
            "req-invalid".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:permissions:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));
    }

    #[tokio::test]
    async fn test_no_roles_no_permissions() {
        let repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-no-roles";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Don't add any roles for this user

        let handler = CheckUserPermissionsHandler::new(repository, cache);

        let query = CheckUserPermissionsQuery {
            user_id: user_id.to_string(),
            permissions: vec!["read".to_string(), "admin".to_string()],
            resource_id: None,
            check_all: false, // OR logic - any permission would be sufficient
        };

        let context = QueryContext::for_user(
            "req-no-roles".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert!(!result.has_permission);
        assert!(result.granted_permissions.is_empty());
        assert_eq!(result.denied_permissions.len(), 2);
        assert!(result.effective_permissions.is_empty());
    }
}
