// Role details query handler for comprehensive role information
// Implements secure, performant role lookups with hierarchy traversal and visibility filtering

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{Async<PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query, QueryContext};
use crate::security::permission_sanitizer::{PermissionSanitizer, PermissionSource};
use crate::security::rate_limiter::{RateLimitKey, RateLimiter};
use auth_domain::{
    entities::{Role, role::Permission},
    repositories::RoleRepository,
    value_objects::{RoleId, UserId},
};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Query to get comprehensive role details with hierarchy information
/// Requires appropriate role visibility permissions
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct GetRoleDetailsQuery {
    /// Role ID to fetch details for
    pub role_id: String,

    /// Include inherited permissions from parent roles
    pub include_inherited: Option<bool>,

    /// Include user count for this role
    pub include_user_count: Option<bool>,

    /// Include child role information
    pub include_children: Option<bool>,

    /// Maximum hierarchy depth to traverse (prevent infinite loops)
    pub max_hierarchy_depth: Option<usize>,
}

impl Query for GetRoleDetailsQuery {
    type Result = GetRoleDetailsResult;
}

/// Permission information for API responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionInfo {
    pub name: String,
    pub source: String, // "direct", "inherited", or specific parent role name
}

/// Child role information (summary only)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChildRoleInfo {
    pub role_id: String,
    pub name: String,
    pub description: Option<String>,
    pub is_active: bool,
    pub user_count: Option<usize>,
}

/// Role hierarchy path information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleHierarchyPath {
    pub role_id: String,
    pub name: String,
    pub level: usize, // 0 = requested role, 1 = parent, 2 = grandparent, etc.
}

/// Comprehensive role details result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetRoleDetailsResult {
    /// Basic role information
    pub role_id: String,
    pub name: String,
    pub description: Option<String>,
    pub is_active: bool,
    pub created_at: std::time::SystemTime,
    pub updated_at: std::time::SystemTime,

    /// Permission information
    pub direct_permissions: Vec<PermissionInfo>,
    pub inherited_permissions: Vec<PermissionInfo>,
    pub effective_permissions: Vec<String>, // All unique permissions

    /// Hierarchy information
    pub parent_role: Option<ChildRoleInfo>,
    pub child_roles: Vec<ChildRoleInfo>,
    pub hierarchy_path: Vec<RoleHierarchyPath>, // From root to this role

    /// Usage statistics
    pub user_count: Option<usize>,
    pub active_user_count: Option<usize>,

    /// Performance metadata
    pub query_time_ms: u64,
    pub hierarchy_depth: usize,
    pub circular_references_detected: bool,
}

/// Handler for role details queries with security and performance optimization
pub struct GetRoleDetailsHandler<RR, RL>
where
    RR: RoleRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    role_repository: RR,
    rate_limiter: RL,
    permission_sanitizer: PermissionSanitizer,
}

impl<RR, RL> GetRoleDetailsHandler<RR, RL>
where
    RR: RoleRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    pub fn new(role_repository: RR, rate_limiter: RL) -> Self {
        Self {
            role_repository,
            rate_limiter,
            permission_sanitizer: PermissionSanitizer::new(),
        }
    }

    /// Handle the role details query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: GetRoleDetailsQuery,
        context: QueryContext,
    ) -> ApplicationResult<GetRoleDetailsResult> {
        // Start timing for SLA monitoring
        let start_time = Instant::now();

        // Authorization check - must have role visibility permissions
        self.authorize(&context, &query).await?;

        // Rate limiting check - 30 queries per 60 seconds per user
        if let Some(ref user_id) = context.user_id {
            self.check_rate_limit(user_id).await?;
        }

        // Validate and sanitize input
        let validated_query = self.validate_and_sanitize_query(query)?;

        // Parse role ID
        let role_id = RoleId::from_string(validated_query.role_id.clone())
            .map_err(|_| ApplicationError::Validation("Invalid role ID format".to_string()))?;

        // Get the primary role
        let primary_role = self
            .role_repository
            .find_by_id(&role_id)
            .await
            .map_err(|e| ApplicationError::Repository(format!("Role lookup failed: {e}")))?
            .ok_or_else(|| ApplicationError::NotFound("Role not found".to_string()))?;

        // Check role visibility for the requesting user
        self.check_role_visibility(&context, &primary_role).await?;

        // Get all roles for hierarchy calculation (cached in real implementation)
        let all_roles =
            self.role_repository.get_hierarchy().await.map_err(|e| {
                ApplicationError::Repository(format!("Hierarchy lookup failed: {e}"))
            })?;

        // Build comprehensive role details
        let result = self
            .build_role_details(
                primary_role,
                &all_roles,
                &validated_query,
                &context,
                start_time,
            )
            .await?;

        // Check SLA compliance (20ms target, 100ms max)
        let elapsed = start_time.elapsed();
        if elapsed > Duration::from_millis(100) {
            tracing::error!(
                "GetRoleDetailsQuery exceeded SLA: {:?} - role: {}",
                elapsed,
                validated_query.role_id
            );
            return Err(ApplicationError::PerformanceViolation {
                operation: "GetRoleDetailsQuery".to_string(),
                duration: elapsed.as_millis() as u64,
            });
        }

        if elapsed > Duration::from_millis(20) {
            tracing::warn!(
                "GetRoleDetailsQuery approaching SLA limit: {:?} - role: {}",
                elapsed,
                validated_query.role_id
            );
        }

        // Log access for audit trail
        tracing::info!(
            "Role details accessed - user: {:?}, role: {}, time: {}ms",
            context.user_id,
            validated_query.role_id,
            result.query_time_ms
        );

        Ok(result)
    }

    async fn authorize(
        &self,
        context: &QueryContext,
        _query: &GetRoleDetailsQuery,
    ) -> ApplicationResult<()> {
        // Must be authenticated
        if !context.is_authenticated() {
            return Err(ApplicationError::Forbidden);
        }

        // Role visibility rules:
        // 1. Users can see their own roles
        // 2. Admins can see all roles (admin:roles:read)
        // 3. Role managers can see roles in their scope (roles:read)
        // 4. HR can see user-assigned roles (hr:roles:read)

        let has_admin_access = context.has_permission("admin:roles:read");
        let has_role_read = context.has_permission("roles:read");
        let has_hr_access = context.has_permission("hr:roles:read");

        if !has_admin_access && !has_role_read && !has_hr_access {
            // Check if user is querying their own roles (additional check needed at role level)
            return Err(ApplicationError::Forbidden);
        }

        Ok(())
    }

    async fn check_role_visibility(
        &self,
        context: &QueryContext,
        role: &Role,
    ) -> ApplicationResult<()> {
        // Admin can see all roles
        if context.has_permission("admin:roles:read") {
            return Ok(());
        }

        // Role managers can see non-admin roles
        if context.has_permission("roles:read") {
            if !role.has_permission(&Permission::Admin) {
                return Ok(());
            } else {
                return Err(ApplicationError::Forbidden);
            }
        }

        // HR can see user-level roles (no admin permissions)
        if context.has_permission("hr:roles:read") {
            if !role.has_permission(&Permission::Admin) && !role.has_permission(&Permission::Write)
            {
                return Ok(());
            } else {
                return Err(ApplicationError::Forbidden);
            }
        }

        // Default deny
        Err(ApplicationError::Forbidden)
    }

    async fn check_rate_limit(&self, user_id: &UserId) -> ApplicationResult<()> {
        let key = RateLimitKey::Account(format!("role_details:{}", user_id.as_str()));

        if self
            .rate_limiter
            .as_ref()
            .check_rate_limit(&key)
            .await
            .is_err()
        {
            tracing::warn!(
                "Rate limit exceeded for role details query - user: {}",
                user_id.as_str()
            );
            return Err(ApplicationError::RateLimitExceeded);
        }

        Ok(())
    }

    fn validate_and_sanitize_query(
        &self,
        query: GetRoleDetailsQuery,
    ) -> ApplicationResult<GetRoleDetailsQuery> {
        let mut sanitized = query.clone();

        // Validate role ID format
        if sanitized.role_id.is_empty() {
            return Err(ApplicationError::Validation(
                "Role ID is required".to_string(),
            ));
        }

        if sanitized.role_id.len() > 100 {
            return Err(ApplicationError::Validation("Role ID too long".to_string()));
        }

        // Sanitize role ID
        sanitized.role_id = sanitized
            .role_id
            .chars()
            .filter(|c| c.is_alphanumeric() || "_-".contains(*c))
            .collect::<String>()
            .trim()
            .to_string();

        if sanitized.role_id.is_empty() {
            return Err(ApplicationError::Validation(
                "Invalid role ID format".to_string(),
            ));
        }

        // Validate hierarchy depth limit
        if let Some(depth) = sanitized.max_hierarchy_depth {
            if depth > 20 {
                sanitized.max_hierarchy_depth = Some(20);
            }
        } else {
            sanitized.max_hierarchy_depth = Some(10); // Default safe limit
        }

        Ok(sanitized)
    }

    async fn build_role_details(
        &self,
        primary_role: Role,
        all_roles: &[Role],
        query: &GetRoleDetailsQuery,
        context: &QueryContext,
        start_time: Instant,
    ) -> ApplicationResult<GetRoleDetailsResult> {
        let circular_references_detected = false;
        let max_depth = query.max_hierarchy_depth.unwrap_or(10);

        // Get direct permissions
        let direct_permissions: Vec<PermissionInfo> = primary_role
            .permissions()
            .iter()
            .map(|p| PermissionInfo {
                name: p.as_str().to_string(),
                source: "direct".to_string(),
            })
            .collect();

        // Get inherited permissions if requested
        let (raw_inherited_permissions, hierarchy_depth) =
            if query.include_inherited.unwrap_or(true) {
                self.calculate_inherited_permissions(&primary_role, all_roles, max_depth)
            } else {
                (Vec::new(), 0)
            };

        // Sanitize permissions to prevent information disclosure
        let is_admin_viewer = context.has_permission("admin:roles:read");
        let viewer_role = context
            .user_id
            .as_ref()
            .map(|_| "user") // Simplified - in production would get actual role
            .unwrap_or("anonymous");

        // Prepare permissions for sanitization
        let mut raw_permissions = Vec::new();
        for perm in &direct_permissions {
            raw_permissions.push((perm.name.clone(), perm.source.clone()));
        }
        for perm in &raw_inherited_permissions {
            raw_permissions.push((perm.name.clone(), perm.source.clone()));
        }

        // Apply sanitization
        let sanitized_permissions = self.permission_sanitizer.sanitize_permissions(
            raw_permissions,
            viewer_role,
            is_admin_viewer,
        );

        // Reconstruct inherited permissions with sanitized sources
        let inherited_permissions: Vec<PermissionInfo> = sanitized_permissions
            .iter()
            .filter(|sp| sp.source != PermissionSource::Direct)
            .map(|sp| PermissionInfo {
                name: sp.name.clone(),
                source: match &sp.source {
                    PermissionSource::Inherited => "inherited".to_string(),
                    PermissionSource::InheritedFrom(role) => role.clone(),
                    PermissionSource::Multiple => "multiple".to_string(),
                    _ => "inherited".to_string(),
                },
            })
            .collect();

        // Calculate effective permissions (deduplicated)
        let mut effective_perms = std::collections::HashSet::new();
        for perm in &direct_permissions {
            effective_perms.insert(perm.name.clone());
        }
        for perm in &inherited_permissions {
            effective_perms.insert(perm.name.clone());
        }
        let effective_permissions: Vec<String> = effective_perms.into_iter().collect();

        // Get parent role info
        let parent_role = if let Some(parent_id) = primary_role.parent_role() {
            if let Some(parent) = all_roles.iter().find(|r| r.id() == parent_id) {
                // Check if user can see parent role
                if self.check_role_visibility(context, parent).await.is_ok() {
                    let user_count = if query.include_user_count.unwrap_or(false) {
                        Some(self.get_role_user_count(parent.id()).await.unwrap_or(0))
                    } else {
                        None
                    };

                    Some(ChildRoleInfo {
                        role_id: parent.id().as_str().to_string(),
                        name: parent.name().to_string(),
                        description: parent.description().map(|s| s.to_string()),
                        is_active: parent.is_active(),
                        user_count,
                    })
                } else {
                    None // User can't see parent role
                }
            } else {
                None
            }
        } else {
            None
        };

        // Get child roles if requested
        let child_roles = if query.include_children.unwrap_or(false) {
            let children = self
                .role_repository
                .find_children(primary_role.id())
                .await
                .map_err(|e| {
                    ApplicationError::Repository(format!("Child roles lookup failed: {e}"))
                })?;

            let mut visible_children = Vec::new();
            for child in children {
                // Check visibility for each child
                if self.check_role_visibility(context, &child).await.is_ok() {
                    let user_count = if query.include_user_count.unwrap_or(false) {
                        Some(self.get_role_user_count(child.id()).await.unwrap_or(0))
                    } else {
                        None
                    };

                    visible_children.push(ChildRoleInfo {
                        role_id: child.id().as_str().to_string(),
                        name: child.name().to_string(),
                        description: child.description().map(|s| s.to_string()),
                        is_active: child.is_active(),
                        user_count,
                    });
                }
            }
            visible_children
        } else {
            Vec::new()
        };

        // Build hierarchy path
        let hierarchy_path = self.build_hierarchy_path(&primary_role, all_roles, max_depth);

        // Get user counts if requested
        let (user_count, active_user_count) = if query.include_user_count.unwrap_or(false) {
            let total = self
                .get_role_user_count(primary_role.id())
                .await
                .unwrap_or(0);
            let active = self
                .get_role_active_user_count(primary_role.id())
                .await
                .unwrap_or(0);
            (Some(total), Some(active))
        } else {
            (None, None)
        };

        let query_time_ms = start_time.elapsed().as_millis() as u64;

        Ok(GetRoleDetailsResult {
            role_id: primary_role.id().as_str().to_string(),
            name: primary_role.name().to_string(),
            description: primary_role.description().map(|s| s.to_string()),
            is_active: primary_role.is_active(),
            created_at: primary_role.created_at(),
            updated_at: primary_role.updated_at(),
            direct_permissions,
            inherited_permissions,
            effective_permissions,
            parent_role,
            child_roles,
            hierarchy_path,
            user_count,
            active_user_count,
            query_time_ms,
            hierarchy_depth,
            circular_references_detected,
        })
    }

    fn calculate_inherited_permissions(
        &self,
        role: &Role,
        all_roles: &[Role],
        max_depth: usize,
    ) -> (Vec<PermissionInfo>, usize) {
        let mut inherited_permissions = Vec::new();
        let mut visited = std::collections::HashSet::new();
        let mut current_depth = 0;

        self.collect_inherited_permissions_recursive(
            role,
            all_roles,
            &mut inherited_permissions,
            &mut visited,
            &mut current_depth,
            max_depth,
        );

        (inherited_permissions, current_depth)
    }

    #[allow(clippy::only_used_in_recursion)]
    fn collect_inherited_permissions_recursive(
        &self,
        role: &Role,
        all_roles: &[Role],
        inherited_permissions: &mut Vec<PermissionInfo>,
        visited: &mut std::collections::HashSet<RoleId>,
        current_depth: &mut usize,
        max_depth: usize,
    ) {
        // Prevent infinite loops and excessive depth
        if !visited.insert(role.id().clone()) || *current_depth >= max_depth {
            return;
        }

        *current_depth += 1;

        // Find parent role
        if let Some(parent_id) = role.parent_role() {
            if let Some(parent) = all_roles.iter().find(|r| r.id() == parent_id) {
                // Add parent's permissions (source will be sanitized later)
                for permission in parent.permissions() {
                    inherited_permissions.push(PermissionInfo {
                        name: permission.as_str().to_string(),
                        source: parent.name().to_string(), // Will be sanitized in main handler
                    });
                }

                // Recursively collect from parent's ancestors
                self.collect_inherited_permissions_recursive(
                    parent,
                    all_roles,
                    inherited_permissions,
                    visited,
                    current_depth,
                    max_depth,
                );
            }
        }
    }

    fn build_hierarchy_path(
        &self,
        role: &Role,
        all_roles: &[Role],
        max_depth: usize,
    ) -> Vec<RoleHierarchyPath> {
        let mut path = Vec::new();
        let mut visited = std::collections::HashSet::new();
        let mut current_role = Some(role);
        let mut level = 0;

        // Build path from current role to root
        while let Some(r) = current_role {
            if !visited.insert(r.id().clone()) || level >= max_depth {
                break; // Circular reference or max depth reached
            }

            path.push(RoleHierarchyPath {
                role_id: r.id().as_str().to_string(),
                name: r.name().to_string(),
                level,
            });

            // Find parent
            current_role = if let Some(parent_id) = r.parent_role() {
                all_roles.iter().find(|parent| parent.id() == parent_id)
            } else {
                None
            };

            level += 1;
        }

        // Reverse to show root first
        path.reverse();

        // Adjust levels so current role is 0
        let max_level = path.len().saturating_sub(1);
        for item in &mut path {
            item.level = max_level - item.level;
        }

        path
    }

    async fn get_role_user_count(&self, _role_id: &RoleId) -> Result<usize, ApplicationError> {
        // In a real implementation, this would query the user-role association table
        // For now, return a placeholder
        // TODO: Implement actual user count query
        Ok(0)
    }

    async fn get_role_active_user_count(
        &self,
        _role_id: &RoleId,
    ) -> Result<usize, ApplicationError> {
        // In a real implementation, this would query active users with this role
        // For now, return a placeholder
        // TODO: Implement actual active user count query
        Ok(0)
    }
}

#[async_trait::async_trait]
impl<RR, RL> AsyncQueryHandler<GetRoleDetailsQuery> for GetRoleDetailsHandler<RR, RL>
where
    RR: RoleRepository,
    RL: AsRef<RateLimiter> + Send + Sync,
{
    async fn handle(&self, query: GetRoleDetailsQuery) -> ApplicationResult<GetRoleDetailsResult> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::security::rate_limiter::RateLimiter;
    use async_trait::async_trait;
    use auth_domain::{
        entities::Role,
        entities::role::Permission,
        errors::DomainError,
        repositories::RoleRepository,
        value_objects::{RoleId, SessionId, UserId},
    };
    use std::collections::HashMap;

    // Mock role repository for testing
    struct MockRoleRepository {
        roles: HashMap<String, Role>,
        role_users: HashMap<String, usize>, // role_id -> user_count
        active_role_users: HashMap<String, usize>, // role_id -> active_user_count
        should_fail: bool,
        query_delay: Option<Duration>,
    }

    impl MockRoleRepository {
        fn new() -> Self {
            Self {
                roles: HashMap::new(),
                role_users: HashMap::new(),
                active_role_users: HashMap::new(),
                should_fail: false,
                query_delay: None,
            }
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }

        fn with_delay(mut self, delay: Duration) -> Self {
            self.query_delay = Some(delay);
            self
        }

        fn add_role(&mut self, role: Role) {
            self.roles.insert(role.id().as_str().to_string(), role);
        }

        fn set_role_user_count(&mut self, role_id: &str, count: usize) {
            self.role_users.insert(role_id.to_string(), count);
        }

        fn set_role_active_user_count(&mut self, role_id: &str, count: usize) {
            self.active_role_users.insert(role_id.to_string(), count);
        }
    }

    #[async_trait]
    impl RoleRepository for MockRoleRepository {
        async fn save(&self, _role: &Role) -> Result<(), DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Save failed".to_string()));
            }
            Ok(())
        }

        async fn find_by_id(&self, id: &RoleId) -> Result<Option<Role>, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Find failed".to_string()));
            }

            // Simulate query delay
            if let Some(delay) = self.query_delay {
                tokio::time::sleep(delay).await;
            }

            Ok(self.roles.get(id.as_str()).cloned())
        }

        async fn find_by_name(&self, name: &str) -> Result<Option<Role>, DomainError> {
            Ok(self.roles.values().find(|r| r.name() == name).cloned())
        }

        async fn exists_by_name(&self, name: &str) -> Result<bool, DomainError> {
            Ok(self.roles.values().any(|r| r.name() == name))
        }

        async fn delete(&self, _id: &RoleId) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<Role>, DomainError> {
            Ok(self.roles.values().cloned().collect())
        }

        async fn count(&self) -> Result<usize, DomainError> {
            Ok(self.roles.len())
        }

        async fn find_active(&self) -> Result<Vec<Role>, DomainError> {
            Ok(self
                .roles
                .values()
                .filter(|r| r.is_active())
                .cloned()
                .collect())
        }

        async fn find_by_user(&self, _user_id: &UserId) -> Result<Vec<Role>, DomainError> {
            Ok(Vec::new())
        }

        async fn find_by_permission(&self, permission: &str) -> Result<Vec<Role>, DomainError> {
            let perm = Permission::from_string(permission.to_string());
            Ok(self
                .roles
                .values()
                .filter(|r| r.has_permission(&perm))
                .cloned()
                .collect())
        }

        async fn find_children(&self, parent_id: &RoleId) -> Result<Vec<Role>, DomainError> {
            Ok(self
                .roles
                .values()
                .filter(|r| r.parent_role() == Some(parent_id))
                .cloned()
                .collect())
        }

        async fn get_hierarchy(&self) -> Result<Vec<Role>, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Hierarchy failed".to_string()));
            }

            // Simulate hierarchy delay
            if let Some(delay) = self.query_delay {
                tokio::time::sleep(delay).await;
            }

            Ok(self.roles.values().cloned().collect())
        }
    }

    // Mock rate limiter
    struct MockRateLimiterWrapper {
        limiter: RateLimiter,
    }

    impl MockRateLimiterWrapper {
        fn new() -> Self {
            Self {
                limiter: RateLimiter::new(),
            }
        }
    }

    impl AsRef<RateLimiter> for MockRateLimiterWrapper {
        fn as_ref(&self) -> &RateLimiter {
            &self.limiter
        }
    }

    // Helper function to create test roles
    fn create_test_role(name: &str, permissions: Vec<Permission>) -> Role {
        Role::new(
            RoleId::builtin(name).unwrap_or_else(|_| RoleId::generate()),
            name.to_string(),
            Some(format!("{name} role description")),
            permissions,
        )
        .unwrap()
    }

    #[tokio::test]
    async fn test_get_role_details_authorization_required() {
        let repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_user".to_string(),
            include_inherited: Some(true),
            include_user_count: Some(true),
            include_children: Some(true),
            max_hierarchy_depth: Some(5),
        };

        // Test with unauthenticated context
        let context = QueryContext::new("req-123".to_string());
        let result = handler.handle_with_context(query.clone(), context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));

        // Test with authenticated user but no role permissions
        let user_id = UserId::from_string("user-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-456".to_string(),
            user_id,
            session_id,
            vec!["user:read".to_string()], // No role permission
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_get_role_details_with_admin_permission() {
        let mut repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add test role
        let test_role = create_test_role("user", vec![Permission::Read]);
        repository.add_role(test_role);
        repository.set_role_user_count("role_builtin_user", 25);
        repository.set_role_active_user_count("role_builtin_user", 20);

        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_user".to_string(),
            include_inherited: Some(true),
            include_user_count: Some(true),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        // Create admin context
        let admin_id = UserId::from_string("admin-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.role_id, "role_builtin_user");
        assert_eq!(result.name, "user");
        assert_eq!(result.direct_permissions.len(), 1);
        assert_eq!(result.direct_permissions[0].name, "read");
        assert_eq!(result.direct_permissions[0].source, "direct");
        assert!(result.is_active);
        assert!(result.query_time_ms < 100); // Should be fast
    }

    #[tokio::test]
    async fn test_get_role_details_role_not_found() {
        let repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "nonexistent_role".to_string(),
            include_inherited: Some(false),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let admin_id = UserId::from_string("admin-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-not-found".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        match result {
            Err(ApplicationError::NotFound(_)) => {
                // This is expected
            }
            Err(ApplicationError::Validation(_)) => {
                // Invalid role ID format - also acceptable
            }
            other => {
                panic!("Expected NotFound or Validation error, got: {other:?}");
            }
        }
    }

    #[tokio::test]
    async fn test_get_role_details_with_hierarchy() {
        let mut repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Create role hierarchy: admin -> moderator -> user
        let admin_role = create_test_role("admin", vec![Permission::Admin]);
        let mut moderator_role =
            create_test_role("moderator", vec![Permission::Write, Permission::Delete]);
        let mut user_role = create_test_role("user", vec![Permission::Read]);

        // Set up hierarchy
        moderator_role
            .set_parent_role(admin_role.id().clone())
            .unwrap();
        user_role
            .set_parent_role(moderator_role.id().clone())
            .unwrap();

        repository.add_role(admin_role);
        repository.add_role(moderator_role);
        repository.add_role(user_role);

        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_user".to_string(),
            include_inherited: Some(true),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(10),
        };

        let admin_id = UserId::from_string("admin-hierarchy".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-hierarchy".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        // Should have direct permissions
        assert_eq!(result.direct_permissions.len(), 1);
        assert_eq!(result.direct_permissions[0].name, "read");

        // Should have inherited permissions from parent chain
        assert!(!result.inherited_permissions.is_empty());

        // Should have hierarchy path
        assert!(!result.hierarchy_path.is_empty());
        assert!(result.hierarchy_depth > 0);

        // Should include all effective permissions
        assert!(result.effective_permissions.contains(&"read".to_string()));
    }

    #[tokio::test]
    async fn test_get_role_details_circular_reference_protection() {
        let mut repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Create circular reference (this shouldn't happen in practice)
        let role_a_id = RoleId::generate();
        let role_b_id = RoleId::generate();

        let mut role_a = Role::new(
            role_a_id.clone(),
            "Role A".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        let mut role_b = Role::new(
            role_b_id.clone(),
            "Role B".to_string(),
            None,
            vec![Permission::Write],
        )
        .unwrap();

        // Create circular reference
        role_a.set_parent_role(role_b_id.clone()).unwrap();
        role_b.set_parent_role(role_a_id.clone()).unwrap();

        repository.add_role(role_a);
        repository.add_role(role_b);

        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: role_a_id.as_str().to_string(),
            include_inherited: Some(true),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let admin_id = UserId::from_string("admin-circular".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-circular".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        // Should complete without infinite loop
        assert_eq!(result.role_id, role_a_id.as_str());
        assert!(result.hierarchy_depth <= 5); // Should respect max depth
    }

    #[tokio::test]
    async fn test_get_role_details_rate_limiting() {
        let repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_user".to_string(),
            include_inherited: Some(false),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let admin_id = UserId::from_string("admin-rate-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-rate-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        // Make requests up to the limit (default is 5 per account per window)
        for i in 0..5 {
            let result = handler
                .handle_with_context(query.clone(), context.clone())
                .await;
            // Expect NotFound since role doesn't exist, but should not be rate limited
            assert!(
                matches!(result, Err(ApplicationError::NotFound(_))),
                "Request {} should fail with NotFound, not rate limit",
                i + 1
            );
        }

        // The 6th request should be rate limited
        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::RateLimitExceeded)));
    }

    #[tokio::test]
    async fn test_get_role_details_input_validation() {
        let repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let admin_id = UserId::from_string("admin-validation".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-validation".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        // Test empty role ID
        let query = GetRoleDetailsQuery {
            role_id: "".to_string(),
            include_inherited: Some(false),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let result = handler.handle_with_context(query, context.clone()).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));

        // Test role ID too long
        let query = GetRoleDetailsQuery {
            role_id: "a".repeat(150),
            include_inherited: Some(false),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let result = handler.handle_with_context(query, context.clone()).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));

        // Test malicious role ID
        let query = GetRoleDetailsQuery {
            role_id: "role'; DROP TABLE roles; --".to_string(),
            include_inherited: Some(false),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let result = handler.handle_with_context(query, context).await;
        // Should either sanitize or fail validation
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_get_role_details_performance_sla() {
        // Create repository with artificial delay exceeding SLA
        let mut repository = MockRoleRepository::new().with_delay(Duration::from_millis(120));
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add a role so the query doesn't fail early with NotFound
        let test_role = create_test_role("user", vec![Permission::Read]);
        repository.add_role(test_role);

        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_user".to_string(),
            include_inherited: Some(true),
            include_user_count: Some(true),
            include_children: Some(true),
            max_hierarchy_depth: Some(5),
        };

        let admin_id = UserId::from_string("admin-sla-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-sla-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;

        // Should fail due to SLA violation
        assert!(matches!(
            result,
            Err(ApplicationError::PerformanceViolation { .. })
        ));
    }

    #[tokio::test]
    async fn test_get_role_details_repository_error() {
        let repository = MockRoleRepository::new().with_failure();
        let rate_limiter = MockRateLimiterWrapper::new();
        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_user".to_string(),
            include_inherited: Some(true),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let admin_id = UserId::from_string("admin-error-test".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-error-test".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Repository(_))));
    }

    #[tokio::test]
    async fn test_role_visibility_enforcement() {
        let mut repository = MockRoleRepository::new();
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add admin role (should be restricted for non-admin users)
        let admin_role = create_test_role("admin", vec![Permission::Admin]);
        repository.add_role(admin_role);

        let handler = GetRoleDetailsHandler::new(repository, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_admin".to_string(),
            include_inherited: Some(false),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        // Test with role manager (should be forbidden for admin role)
        let manager_id = UserId::from_string("manager-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-visibility".to_string(),
            manager_id,
            session_id.clone(),
            vec!["roles:read".to_string()], // Role manager, not admin
        );

        let result = handler.handle_with_context(query.clone(), context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));

        // Test with admin (should succeed)
        let admin_id = UserId::from_string("admin-visibility".to_string()).unwrap();
        let context = QueryContext::for_user(
            "req-admin-visibility".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_performance_target_compliance() {
        let repository = MockRoleRepository::new(); // No artificial delay - should be fast
        let rate_limiter = MockRateLimiterWrapper::new();

        // Add test role
        let mut repo = repository;
        let test_role = create_test_role("user", vec![Permission::Read]);
        repo.add_role(test_role);

        let handler = GetRoleDetailsHandler::new(repo, rate_limiter);

        let query = GetRoleDetailsQuery {
            role_id: "role_builtin_user".to_string(),
            include_inherited: Some(true),
            include_user_count: Some(false),
            include_children: Some(false),
            max_hierarchy_depth: Some(5),
        };

        let admin_id = UserId::from_string("admin-performance".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let context = QueryContext::for_user(
            "req-performance".to_string(),
            admin_id,
            session_id,
            vec!["admin:roles:read".to_string()],
        );

        let start = Instant::now();
        let result = handler.handle_with_context(query, context).await.unwrap();
        let elapsed = start.elapsed();

        // Should meet 20ms target
        assert!(
            elapsed.as_millis() < 20,
            "Query took {}ms, should be under 20ms target",
            elapsed.as_millis()
        );

        // Verify performance is reported correctly
        assert!(
            result.query_time_ms < 20,
            "Reported query time {}ms should be under 20ms",
            result.query_time_ms
        );
    }
}
