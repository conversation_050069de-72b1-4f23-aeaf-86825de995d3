// Base query infrastructure for CQRS read-side operations
// Contains core traits, types and common functionality for query handlers

use crate::errors::ApplicationResult;
use auth_domain::value_objects::{SessionId, UserId};
use std::time::{Duration, SystemTime};

/// Base trait for all queries
/// Queries are read-only operations that never modify state
pub trait Query: Send + Sync + std::fmt::Debug {
    type Result: Send;
}

/// Synchronous query handler trait
pub trait QueryHandler<Q: Query>: Send + Sync {
    fn handle(&self, query: Q) -> ApplicationResult<Q::Result>;
}

/// Asynchronous query handler trait
#[async_trait::async_trait]
pub trait AsyncQueryHandler<Q: Query>: Send + Sync {
    async fn handle(&self, query: Q) -> ApplicationResult<Q::Result>;
}

/// Query context containing security and request information
/// All queries must be executed within a security context
#[derive(Debug, Clone)]
pub struct QueryContext {
    pub request_id: String,
    pub user_id: Option<UserId>,
    pub session_id: Option<SessionId>,
    pub permissions: Vec<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub timestamp: SystemTime,
}

impl QueryContext {
    /// Create a new query context
    pub fn new(request_id: String) -> Self {
        Self {
            request_id,
            user_id: None,
            session_id: None,
            permissions: Vec::new(),
            ip_address: None,
            user_agent: None,
            timestamp: SystemTime::now(),
        }
    }

    /// Create a context for an authenticated user
    pub fn for_user(
        request_id: String,
        user_id: UserId,
        session_id: SessionId,
        permissions: Vec<String>,
    ) -> Self {
        Self {
            request_id,
            user_id: Some(user_id),
            session_id: Some(session_id),
            permissions,
            ip_address: None,
            user_agent: None,
            timestamp: SystemTime::now(),
        }
    }

    /// Check if context has a specific permission
    pub fn has_permission(&self, permission: &str) -> bool {
        self.permissions.iter().any(|p| p == permission)
    }

    /// Check if context represents an authenticated user
    pub fn is_authenticated(&self) -> bool {
        self.user_id.is_some() && self.session_id.is_some()
    }

    /// Check if the context user matches the given user ID
    pub fn is_user(&self, user_id: &UserId) -> bool {
        self.user_id.as_ref() == Some(user_id)
    }
}

/// Paginated query results with metadata
#[derive(Debug, Clone, PartialEq, serde::Serialize, serde::Deserialize)]
pub struct PaginatedResult<T> {
    pub items: Vec<T>,
    pub total: usize,
    pub page: usize,
    pub page_size: usize,
    pub has_next: bool,
    pub has_previous: bool,
}

impl<T> PaginatedResult<T> {
    /// Create a new paginated result
    pub fn new(items: Vec<T>, total: usize, page: usize, page_size: usize) -> Self {
        let has_next = (page + 1) * page_size < total;
        let has_previous = page > 0;

        Self {
            items,
            total,
            page,
            page_size,
            has_next,
            has_previous,
        }
    }

    /// Create an empty paginated result
    pub fn empty(page: usize, page_size: usize) -> Self {
        Self::new(Vec::new(), 0, page, page_size)
    }

    /// Get the number of items in the current page
    pub fn count(&self) -> usize {
        self.items.len()
    }

    /// Check if this is the first page
    pub fn is_first_page(&self) -> bool {
        !self.has_previous
    }

    /// Check if this is the last page
    pub fn is_last_page(&self) -> bool {
        !self.has_next
    }

    /// Transform items using a mapping function
    pub fn map<U, F>(self, f: F) -> PaginatedResult<U>
    where
        F: FnMut(T) -> U,
    {
        PaginatedResult {
            items: self.items.into_iter().map(f).collect(),
            total: self.total,
            page: self.page,
            page_size: self.page_size,
            has_next: self.has_next,
            has_previous: self.has_previous,
        }
    }
}

/// Query authorization trait for implementing access control
#[async_trait::async_trait]
pub trait QueryAuthorizer<Q: Query>: Send + Sync {
    async fn authorize(&self, query: &Q, context: &QueryContext) -> ApplicationResult<()>;
}

/// Query caching trait for performance optimization
#[async_trait::async_trait]
pub trait QueryCache<Q: Query>: Send + Sync {
    async fn get(&self, query: &Q) -> Option<Q::Result>;
    async fn set(&self, query: &Q, result: &Q::Result, ttl: Duration);
    async fn invalidate(&self, pattern: &str);
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::value_objects::{SessionId, UserId};

    #[test]
    fn test_query_context_creation() {
        let context = QueryContext::new("req-123".to_string());

        assert_eq!(context.request_id, "req-123");
        assert!(context.user_id.is_none());
        assert!(context.session_id.is_none());
        assert!(context.permissions.is_empty());
        assert!(!context.is_authenticated());
    }

    #[test]
    fn test_query_context_for_user() {
        let user_id = UserId::from_string("user-123".to_string()).unwrap();
        let session_id =
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap();
        let permissions = vec!["read:profile".to_string(), "write:settings".to_string()];

        let context = QueryContext::for_user(
            "req-789".to_string(),
            user_id.clone(),
            session_id.clone(),
            permissions.clone(),
        );

        assert_eq!(context.request_id, "req-789");
        assert_eq!(context.user_id, Some(user_id.clone()));
        assert_eq!(context.session_id, Some(session_id));
        assert_eq!(context.permissions, permissions);
        assert!(context.is_authenticated());
        assert!(context.is_user(&user_id));
    }

    #[test]
    fn test_query_context_permissions() {
        let mut context = QueryContext::new("req-001".to_string());
        context.permissions = vec!["admin".to_string(), "user:read".to_string()];

        assert!(context.has_permission("admin"));
        assert!(context.has_permission("user:read"));
        assert!(!context.has_permission("user:write"));
    }

    #[test]
    fn test_paginated_result_creation() {
        let items = vec![1, 2, 3, 4, 5];
        let result = PaginatedResult::new(items.clone(), 20, 1, 5);

        assert_eq!(result.items, items);
        assert_eq!(result.total, 20);
        assert_eq!(result.page, 1);
        assert_eq!(result.page_size, 5);
        assert_eq!(result.count(), 5);
        assert!(result.has_next);
        assert!(result.has_previous);
        assert!(!result.is_first_page());
        assert!(!result.is_last_page());
    }

    #[test]
    fn test_paginated_result_first_page() {
        let items = vec![1, 2, 3];
        let result = PaginatedResult::new(items, 10, 0, 3);

        assert!(!result.has_previous);
        assert!(result.has_next);
        assert!(result.is_first_page());
        assert!(!result.is_last_page());
    }

    #[test]
    fn test_paginated_result_last_page() {
        let items = vec![8, 9, 10];
        let result = PaginatedResult::new(items, 10, 3, 3);

        assert!(result.has_previous);
        assert!(!result.has_next);
        assert!(!result.is_first_page());
        assert!(result.is_last_page());
    }

    #[test]
    fn test_paginated_result_empty() {
        let result: PaginatedResult<i32> = PaginatedResult::empty(0, 10);

        assert!(result.items.is_empty());
        assert_eq!(result.total, 0);
        assert_eq!(result.count(), 0);
        assert!(!result.has_next);
        assert!(!result.has_previous);
        assert!(result.is_first_page());
        assert!(result.is_last_page());
    }

    #[test]
    fn test_paginated_result_map() {
        let items = vec![1, 2, 3];
        let result = PaginatedResult::new(items, 5, 0, 3);

        let mapped: PaginatedResult<String> = result.map(|x| x.to_string());

        assert_eq!(mapped.items, vec!["1", "2", "3"]);
        assert_eq!(mapped.total, 5);
        assert_eq!(mapped.page, 0);
        assert_eq!(mapped.page_size, 3);
    }

    // Test helper types for trait testing
    #[derive(Debug)]
    struct TestQuery {
        pub data: String,
    }

    impl Query for TestQuery {
        type Result = String;
    }

    struct MockQueryHandler;

    impl QueryHandler<TestQuery> for MockQueryHandler {
        fn handle(&self, query: TestQuery) -> ApplicationResult<String> {
            Ok(format!("Processed: {}", query.data))
        }
    }

    #[test]
    fn test_sync_query_handler() {
        let handler = MockQueryHandler;
        let query = TestQuery {
            data: "test".to_string(),
        };

        let result = handler.handle(query).unwrap();
        assert_eq!(result, "Processed: test");
    }

    // Async handler test
    struct MockAsyncQueryHandler;

    #[async_trait::async_trait]
    impl AsyncQueryHandler<TestQuery> for MockAsyncQueryHandler {
        async fn handle(&self, query: TestQuery) -> ApplicationResult<String> {
            Ok(format!("Async processed: {}", query.data))
        }
    }

    #[tokio::test]
    async fn test_async_query_handler() {
        let handler = MockAsyncQueryHandler;
        let query = TestQuery {
            data: "async test".to_string(),
        };

        let result = handler.handle(query).await.unwrap();
        assert_eq!(result, "Async processed: async test");
    }
}
