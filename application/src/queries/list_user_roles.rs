// List user roles query and handler
// Implements secure, optimized user role listing with proper authorization

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{AsyncQueryHandler, Query, QueryCache, QueryContext};
use auth_domain::{entities::Role, repositories::RoleRepository, value_objects::UserId};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Query to list roles assigned to a specific user
#[derive(Debug, Clone, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub struct ListUserRolesQuery {
    pub user_id: String,
    pub include_permissions: bool,
    pub include_inherited: bool,
    pub active_only: bool,
}

impl Query for ListUserRolesQuery {
    type Result = ListUserRolesResult;
}

/// Individual role information for API responses
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserRoleResult {
    pub role_id: String,
    pub role_name: String,
    pub description: Option<String>,
    pub permissions: Option<Vec<String>>,
    pub is_active: bool,
    pub is_inherited: bool,
    pub parent_role_id: Option<String>,
    pub created_at: std::time::SystemTime,
}

/// Result containing user's roles with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ListUserRolesResult {
    pub user_id: String,
    pub roles: Vec<UserRoleResult>,
    pub total_roles: usize,
    pub active_roles: usize,
    pub inherited_roles: usize,
}

/// Handler for listing user roles with security and performance optimization
pub struct ListUserRolesHandler<RR, C>
where
    RR: RoleRepository,
    C: QueryCache<ListUserRolesQuery>,
{
    role_repository: RR,
    cache: C,
}

impl<RR, C> ListUserRolesHandler<RR, C>
where
    RR: RoleRepository,
    C: QueryCache<ListUserRolesQuery>,
{
    pub fn new(role_repository: RR, cache: C) -> Self {
        Self {
            role_repository,
            cache,
        }
    }

    /// Handle the list user roles query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: ListUserRolesQuery,
        context: QueryContext,
    ) -> ApplicationResult<ListUserRolesResult> {
        // Start timing for SLA monitoring
        let start = Instant::now();

        // Check cache first for better performance
        if let Some(cached_result) = self.cache.get(&query).await {
            // Still check authorization for cached results
            self.authorize(&query, &context).await?;
            return Ok(cached_result);
        }

        // Authorization check
        self.authorize(&query, &context).await?;

        // Validate and parse user ID
        let user_id = UserId::from_string(query.user_id.clone())
            .map_err(|e| ApplicationError::Validation(format!("Invalid user ID: {e}")))?;

        // Fetch user's roles from repository
        let roles = self
            .role_repository
            .find_by_user(&user_id)
            .await
            .map_err(|e| ApplicationError::Repository(format!("Database error: {e}")))?;

        // Process roles and build result
        let mut role_results = Vec::new();

        // Process direct roles
        for role in &roles {
            let role_result = self.role_to_result(role, false, &query).await?;
            role_results.push(role_result);
        }

        // Process inherited roles if requested
        if query.include_inherited {
            let inherited_roles = self.get_inherited_roles(&roles).await?;
            for role in &inherited_roles {
                let role_result = self.role_to_result(role, true, &query).await?;
                role_results.push(role_result);
            }
        }

        // Apply filtering
        if query.active_only {
            role_results.retain(|r| r.is_active);
        }

        // Calculate statistics
        let total_roles = role_results.len();
        let active_roles = role_results.iter().filter(|r| r.is_active).count();
        let inherited_count = role_results.iter().filter(|r| r.is_inherited).count();

        let user_id_str = query.user_id.clone();
        let result = ListUserRolesResult {
            user_id: user_id_str.clone(),
            roles: role_results,
            total_roles,
            active_roles,
            inherited_roles: inherited_count,
        };

        // Check SLA compliance
        let elapsed = start.elapsed();
        if elapsed > Duration::from_millis(100) {
            tracing::warn!(
                "ListUserRolesQuery exceeded SLA: {:?} for user {}",
                elapsed,
                user_id_str
            );
        }

        // Cache the result for future requests (TTL: 10 minutes - roles change infrequently)
        self.cache
            .set(&query, &result, Duration::from_secs(600))
            .await;

        Ok(result)
    }

    async fn authorize(
        &self,
        query: &ListUserRolesQuery,
        context: &QueryContext,
    ) -> ApplicationResult<()> {
        // Allow users to view their own roles
        if let Some(ref user_id) = context.user_id {
            if user_id.as_str() == query.user_id {
                return Ok(());
            }
        }

        // Allow admins to view any user's roles
        if context.has_permission("admin:roles:read") {
            return Ok(());
        }

        // Allow HR to view user roles
        if context.has_permission("hr:roles:read") {
            return Ok(());
        }

        // Deny access for unauthenticated users or insufficient permissions
        Err(ApplicationError::Forbidden)
    }

    async fn role_to_result(
        &self,
        role: &Role,
        is_inherited: bool,
        query: &ListUserRolesQuery,
    ) -> ApplicationResult<UserRoleResult> {
        let permissions = if query.include_permissions {
            Some(
                role.permissions()
                    .iter()
                    .map(|p| p.as_str().to_string())
                    .collect(),
            )
        } else {
            None
        };

        Ok(UserRoleResult {
            role_id: role.id().as_str().to_string(),
            role_name: role.name().to_string(),
            description: role.description().map(|s| s.to_string()),
            permissions,
            is_active: role.is_active(),
            is_inherited,
            parent_role_id: role.parent_role().map(|id| id.as_str().to_string()),
            created_at: role.created_at(),
        })
    }

    async fn get_inherited_roles(&self, direct_roles: &[Role]) -> ApplicationResult<Vec<Role>> {
        let mut inherited_roles = Vec::new();
        let mut processed_role_ids = std::collections::HashSet::new();

        for role in direct_roles {
            self.collect_parent_roles(role, &mut inherited_roles, &mut processed_role_ids)
                .await?;
        }

        Ok(inherited_roles)
    }

    fn collect_parent_roles<'a>(
        &'a self,
        role: &'a Role,
        inherited_roles: &'a mut Vec<Role>,
        processed_role_ids: &'a mut std::collections::HashSet<String>,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = ApplicationResult<()>> + Send + 'a>>
    {
        Box::pin(async move {
            if let Some(parent_id) = role.parent_role() {
                let parent_id_str = parent_id.as_str().to_string();

                // Prevent infinite loops in case of circular references
                if processed_role_ids.contains(&parent_id_str) {
                    return Ok(());
                }

                processed_role_ids.insert(parent_id_str);

                if let Some(parent_role) = self
                    .role_repository
                    .find_by_id(parent_id)
                    .await
                    .map_err(|e| {
                        ApplicationError::Repository(format!("Error fetching parent role: {e}"))
                    })?
                {
                    inherited_roles.push(parent_role.clone());
                    // Recursively collect parent's parents
                    self.collect_parent_roles(&parent_role, inherited_roles, processed_role_ids)
                        .await?;
                }
            }

            Ok(())
        })
    }
}

#[async_trait::async_trait]
impl<RR, C> AsyncQueryHandler<ListUserRolesQuery> for ListUserRolesHandler<RR, C>
where
    RR: RoleRepository,
    C: QueryCache<ListUserRolesQuery>,
{
    async fn handle(&self, query: ListUserRolesQuery) -> ApplicationResult<ListUserRolesResult> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use auth_domain::{
        entities::{Permission, Role},
        errors::DomainError,
        value_objects::{RoleId, SessionId, UserId},
    };
    use std::collections::HashMap;

    // Mock role repository for testing
    struct MockRoleRepository {
        user_roles: HashMap<String, Vec<Role>>,
        roles_by_id: HashMap<String, Role>,
    }

    impl MockRoleRepository {
        fn new() -> Self {
            Self {
                user_roles: HashMap::new(),
                roles_by_id: HashMap::new(),
            }
        }

        fn add_user_roles(&mut self, user_id: &str, roles: Vec<Role>) {
            // Add roles to the roles_by_id map for lookup
            for role in &roles {
                self.roles_by_id
                    .insert(role.id().as_str().to_string(), role.clone());
            }
            self.user_roles.insert(user_id.to_string(), roles);
        }

        #[allow(dead_code)]
        fn add_role(&mut self, role: Role) {
            self.roles_by_id
                .insert(role.id().as_str().to_string(), role);
        }
    }

    #[async_trait]
    impl RoleRepository for MockRoleRepository {
        async fn save(&self, _role: &Role) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_by_id(&self, id: &RoleId) -> Result<Option<Role>, DomainError> {
            Ok(self.roles_by_id.get(id.as_str()).cloned())
        }

        async fn find_by_name(&self, _name: &str) -> Result<Option<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_by_user(&self, user_id: &UserId) -> Result<Vec<Role>, DomainError> {
            Ok(self
                .user_roles
                .get(user_id.as_str())
                .cloned()
                .unwrap_or_default())
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &RoleId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn exists_by_name(&self, _name: &str) -> Result<bool, DomainError> {
            unimplemented!()
        }

        async fn count(&self) -> Result<usize, DomainError> {
            Ok(self.roles_by_id.len())
        }

        async fn find_active(&self) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_by_permission(&self, _permission: &str) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_children(&self, _parent_id: &RoleId) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn get_hierarchy(&self) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }
    }

    // Mock cache for testing
    struct MockQueryCache;

    #[async_trait]
    impl QueryCache<ListUserRolesQuery> for MockQueryCache {
        async fn get(&self, _query: &ListUserRolesQuery) -> Option<ListUserRolesResult> {
            None // Always cache miss for simplicity
        }

        async fn set(
            &self,
            _query: &ListUserRolesQuery,
            _result: &ListUserRolesResult,
            _ttl: Duration,
        ) {
            // No-op for testing
        }

        async fn invalidate(&self, _pattern: &str) {
            // No-op for testing
        }
    }

    #[tokio::test]
    async fn test_list_user_roles_success() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-123";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create test roles
        let reader_role = Role::new(
            RoleId::builtin("reader").unwrap(),
            "Reader".to_string(),
            Some("Read access".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        let writer_role = Role::new(
            RoleId::builtin("writer").unwrap(),
            "Writer".to_string(),
            Some("Write access".to_string()),
            vec![Permission::Write],
        )
        .unwrap();

        repository.add_user_roles(user_id, vec![reader_role, writer_role]);

        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: user_id.to_string(),
            include_permissions: true,
            include_inherited: false,
            active_only: false,
        };

        // Create context for the same user (self-access)
        let context = QueryContext::for_user(
            "req-123".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, user_id);
        assert_eq!(result.total_roles, 2);
        assert_eq!(result.active_roles, 2);
        assert_eq!(result.inherited_roles, 0);
        assert_eq!(result.roles.len(), 2);

        // Check role details
        let first_role = &result.roles[0];
        assert!(first_role.permissions.is_some());
        assert!(!first_role.is_inherited);
        assert!(first_role.is_active);
    }

    #[tokio::test]
    async fn test_list_user_roles_with_inheritance() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-456";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create parent role
        let admin_role = Role::new(
            RoleId::builtin("admin").unwrap(),
            "Admin".to_string(),
            Some("Admin access".to_string()),
            vec![Permission::Admin],
        )
        .unwrap();

        // Create child role with parent
        let mut moderator_role = Role::new(
            RoleId::builtin("moderator").unwrap(),
            "Moderator".to_string(),
            Some("Moderator access".to_string()),
            vec![Permission::Write],
        )
        .unwrap();
        moderator_role
            .set_parent_role(RoleId::builtin("admin").unwrap())
            .unwrap();

        // Add both roles to repository
        repository.add_role(admin_role);
        repository.add_user_roles(user_id, vec![moderator_role]);

        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: user_id.to_string(),
            include_permissions: true,
            include_inherited: true,
            active_only: false,
        };

        let context = QueryContext::for_user(
            "req-456".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, user_id);
        assert_eq!(result.total_roles, 2); // 1 direct + 1 inherited
        assert_eq!(result.inherited_roles, 1);

        // Find the inherited role
        let inherited_role = result.roles.iter().find(|r| r.is_inherited).unwrap();
        assert_eq!(inherited_role.role_name, "Admin");
        assert!(inherited_role.is_inherited);
    }

    #[tokio::test]
    async fn test_list_user_roles_active_only() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-789";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create active role
        let active_role = Role::new(
            RoleId::builtin("active").unwrap(),
            "Active Role".to_string(),
            Some("Active role".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        // Create inactive role
        let mut inactive_role = Role::new(
            RoleId::builtin("inactive").unwrap(),
            "Inactive Role".to_string(),
            Some("Inactive role".to_string()),
            vec![Permission::Write],
        )
        .unwrap();
        inactive_role.deactivate();

        repository.add_user_roles(user_id, vec![active_role, inactive_role]);

        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: user_id.to_string(),
            include_permissions: false,
            include_inherited: false,
            active_only: true,
        };

        let context = QueryContext::for_user(
            "req-789".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, user_id);
        assert_eq!(result.total_roles, 1); // Only active role should be returned
        assert_eq!(result.active_roles, 1);
        assert_eq!(result.roles[0].role_name, "Active Role");
        assert!(result.roles[0].is_active);
    }

    #[tokio::test]
    async fn test_authorization_denied() {
        let repository = MockRoleRepository::new();
        let cache = MockQueryCache;
        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: "target-user".to_string(),
            include_permissions: false,
            include_inherited: false,
            active_only: false,
        };

        // Create context for different user without admin permissions
        let context = QueryContext::for_user(
            "req-unauthorized".to_string(),
            UserId::from_string("other-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()], // No admin or HR permissions
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_admin_can_view_any_user_roles() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let target_user_id = "target-user";
        let _parsed_target_user_id = UserId::from_string(target_user_id.to_string()).unwrap();

        // Create role for target user
        let role = Role::new(
            RoleId::builtin("reader").unwrap(),
            "Reader".to_string(),
            Some("Read access".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        repository.add_user_roles(target_user_id, vec![role]);

        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: target_user_id.to_string(),
            include_permissions: true,
            include_inherited: false,
            active_only: false,
        };

        // Create admin context
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, target_user_id);
        assert!(!result.roles.is_empty());
        assert_eq!(result.roles[0].role_name, "Reader");
    }

    #[tokio::test]
    async fn test_hr_can_view_user_roles() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let target_user_id = "employee-user";
        let _parsed_target_user_id = UserId::from_string(target_user_id.to_string()).unwrap();

        // Create role for target user
        let role = Role::new(
            RoleId::builtin("employee").unwrap(),
            "Employee".to_string(),
            Some("Employee access".to_string()),
            vec![Permission::Read],
        )
        .unwrap();

        repository.add_user_roles(target_user_id, vec![role]);

        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: target_user_id.to_string(),
            include_permissions: false,
            include_inherited: false,
            active_only: false,
        };

        // Create HR context
        let context = QueryContext::for_user(
            "req-hr".to_string(),
            UserId::from_string("hr-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["hr:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, target_user_id);
        assert!(!result.roles.is_empty());
        assert_eq!(result.roles[0].role_name, "Employee");
    }

    #[tokio::test]
    async fn test_invalid_user_id() {
        let repository = MockRoleRepository::new();
        let cache = MockQueryCache;
        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: "".to_string(),
            include_permissions: false,
            include_inherited: false,
            active_only: false,
        };

        // Create admin context to bypass authorization for this test
        let context = QueryContext::for_user(
            "req-invalid".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:roles:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));
    }

    #[tokio::test]
    async fn test_no_roles_returns_empty() {
        let repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-no-roles";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Don't add any roles for this user

        let handler = ListUserRolesHandler::new(repository, cache);

        let query = ListUserRolesQuery {
            user_id: user_id.to_string(),
            include_permissions: false,
            include_inherited: false,
            active_only: false,
        };

        let context = QueryContext::for_user(
            "req-no-roles".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, user_id);
        assert_eq!(result.total_roles, 0);
        assert_eq!(result.active_roles, 0);
        assert_eq!(result.inherited_roles, 0);
        assert!(result.roles.is_empty());
    }

    #[tokio::test]
    async fn test_permissions_included_when_requested() {
        let mut repository = MockRoleRepository::new();
        let cache = MockQueryCache;

        let user_id = "user-permissions";
        let parsed_user_id = UserId::from_string(user_id.to_string()).unwrap();

        // Create role with multiple permissions
        let role = Role::new(
            RoleId::builtin("multi").unwrap(),
            "Multi Permission".to_string(),
            Some("Multiple permissions".to_string()),
            vec![Permission::Read, Permission::Write],
        )
        .unwrap();

        repository.add_user_roles(user_id, vec![role]);

        let handler = ListUserRolesHandler::new(repository, cache);

        // Test with permissions included
        let query_with_perms = ListUserRolesQuery {
            user_id: user_id.to_string(),
            include_permissions: true,
            include_inherited: false,
            active_only: false,
        };

        let context = QueryContext::for_user(
            "req-perms".to_string(),
            parsed_user_id.clone(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result_with_perms = handler
            .handle_with_context(query_with_perms, context)
            .await
            .unwrap();

        assert!(result_with_perms.roles[0].permissions.is_some());
        let permissions = result_with_perms.roles[0].permissions.as_ref().unwrap();
        assert_eq!(permissions.len(), 2);
        assert!(permissions.contains(&"read".to_string()));
        assert!(permissions.contains(&"write".to_string()));

        // Test without permissions
        let query_without_perms = ListUserRolesQuery {
            user_id: user_id.to_string(),
            include_permissions: false,
            include_inherited: false,
            active_only: false,
        };

        let context2 = QueryContext::for_user(
            "req-no-perms".to_string(),
            parsed_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result_without_perms = handler
            .handle_with_context(query_without_perms, context2)
            .await
            .unwrap();

        assert!(result_without_perms.roles[0].permissions.is_none());
    }
}
