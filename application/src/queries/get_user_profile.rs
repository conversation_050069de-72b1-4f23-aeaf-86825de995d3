// Enhanced Get user profile query and handler
// Implements secure, cacheable user profile retrieval with proper authorization

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{AsyncQueryHandler, Query, QueryCache, QueryContext};
use auth_domain::{repositories::UserRepository, value_objects::UserId};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Query to retrieve user profile information
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct GetUserProfileQuery {
    pub user_id: String,
    pub include_roles: bool,
    pub include_sessions: bool,
    pub include_mfa_status: bool,
}

impl Query for GetUserProfileQuery {
    type Result = UserProfileResult;
}

/// User profile result with optional enriched data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserProfileResult {
    pub user_id: String,
    pub email: String,
    pub username: Option<String>,
    pub is_verified: bool,
    pub is_active: bool,
    pub mfa_enabled: bool,
    pub roles: Option<Vec<String>>,
    pub active_sessions: Option<usize>,
    pub last_login: Option<std::time::SystemTime>,
    pub created_at: std::time::SystemTime,
    pub updated_at: std::time::SystemTime,
}

/// Enhanced user profile query handler with security and caching
pub struct GetUserProfileHandler<UR, C>
where
    UR: UserRepository,
    C: QueryCache<GetUserProfileQuery>,
{
    user_repository: UR,
    cache: C,
}

impl<UR, C> GetUserProfileHandler<UR, C>
where
    UR: UserRepository,
    C: QueryCache<GetUserProfileQuery>,
{
    pub fn new(user_repository: UR, cache: C) -> Self {
        Self {
            user_repository,
            cache,
        }
    }

    /// Handle the user profile query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: GetUserProfileQuery,
        context: QueryContext,
    ) -> ApplicationResult<UserProfileResult> {
        // Start timing for SLA monitoring
        let start = Instant::now();

        // Check cache first for better performance
        if let Some(cached_result) = self.cache.get(&query).await {
            // Still check authorization for cached results to prevent stale permission issues
            self.authorize(&query, &context).await?;
            return Ok(cached_result);
        }

        // Authorization check
        self.authorize(&query, &context).await?;

        // Validate and parse user ID
        let user_id = UserId::from_string(query.user_id.clone())
            .map_err(|e| ApplicationError::Validation(format!("Invalid user ID: {e}")))?;

        // Fetch user from repository
        let user = self
            .user_repository
            .find_by_id(&user_id)
            .await
            .map_err(|e| ApplicationError::Repository(format!("Database error: {e}")))?
            .ok_or_else(|| ApplicationError::NotFound("User not found".to_string()))?;

        // Build result with requested enrichments
        let result = UserProfileResult {
            user_id: user.id().as_str().to_string(),
            email: user.email().as_str().to_string(),
            username: None, // TODO: Add username to User entity
            is_verified: user.is_verified(),
            is_active: user.is_active(),
            mfa_enabled: false, // TODO: Add MFA info to User entity
            roles: if query.include_roles {
                Some(Vec::new())
            } else {
                None
            }, // TODO: Fetch roles
            active_sessions: if query.include_sessions {
                Some(0)
            } else {
                None
            }, // TODO: Fetch session count
            last_login: None,   // TODO: Add last_login to User entity
            created_at: user.created_at().into(),
            updated_at: user.updated_at().into(),
        };

        // Check SLA compliance
        let elapsed = start.elapsed();
        if elapsed > Duration::from_millis(100) {
            tracing::warn!(
                "GetUserProfileQuery exceeded SLA: {:?} for user {}",
                elapsed,
                query.user_id
            );
        }

        // Cache the result for future requests (TTL: 5 minutes)
        self.cache
            .set(&query, &result, Duration::from_secs(300))
            .await;

        Ok(result)
    }

    async fn authorize(
        &self,
        query: &GetUserProfileQuery,
        context: &QueryContext,
    ) -> ApplicationResult<()> {
        // Allow users to view their own profile
        if let Some(ref user_id) = context.user_id {
            if user_id.as_str() == query.user_id {
                return Ok(());
            }
        }

        // Allow admins to view any profile
        if context.has_permission("admin:users:read") {
            return Ok(());
        }

        // Deny access for unauthenticated users or insufficient permissions
        Err(ApplicationError::Forbidden)
    }
}

#[async_trait::async_trait]
impl<UR, C> AsyncQueryHandler<GetUserProfileQuery> for GetUserProfileHandler<UR, C>
where
    UR: UserRepository,
    C: QueryCache<GetUserProfileQuery>,
{
    async fn handle(&self, query: GetUserProfileQuery) -> ApplicationResult<UserProfileResult> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use auth_domain::{
        entities::User,
        errors::DomainError,
        value_objects::{Email, Password, SessionId},
    };
    use std::collections::HashMap;

    // Mock repository for testing
    struct MockUserRepository {
        users: HashMap<String, User>,
    }

    impl MockUserRepository {
        fn new() -> Self {
            MockUserRepository {
                users: HashMap::new(),
            }
        }

        fn add_user(&mut self, user: User) {
            self.users.insert(user.id().as_str().to_string(), user);
        }
    }

    // Mock cache for testing
    struct MockQueryCache;

    #[async_trait]
    impl QueryCache<GetUserProfileQuery> for MockQueryCache {
        async fn get(&self, _query: &GetUserProfileQuery) -> Option<UserProfileResult> {
            None // Always cache miss for simplicity
        }

        async fn set(
            &self,
            _query: &GetUserProfileQuery,
            _result: &UserProfileResult,
            _ttl: Duration,
        ) {
            // No-op for testing
        }

        async fn invalidate(&self, _pattern: &str) {
            // No-op for testing
        }
    }

    #[async_trait::async_trait]
    impl UserRepository for MockUserRepository {
        async fn save(&self, _user: &User) -> Result<(), DomainError> {
            // For testing, we'll ignore the save operation
            Ok(())
        }

        async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
            Ok(self.users.get(id.as_str()).cloned())
        }

        async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
            Ok(self.users.values().find(|u| u.email() == email).cloned())
        }

        async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
            unimplemented!()
        }

        async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
            Ok(self.users.values().any(|u| u.email() == email))
        }

        async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn count(&self) -> Result<usize, DomainError> {
            Ok(self.users.len())
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn search_users(
            &self,
            _criteria: &auth_domain::repositories::UserSearchCriteria,
        ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
            unimplemented!()
        }
    }

    #[tokio::test]
    async fn test_get_user_profile_success() {
        let mut repository = MockUserRepository::new();
        let cache = MockQueryCache;

        // Create and add a test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.change_password("UniquePassword987!").unwrap();
        let user_id = user.id().clone();
        repository.add_user(user);

        let handler = GetUserProfileHandler::new(repository, cache);

        let query = GetUserProfileQuery {
            user_id: user_id.as_str().to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        // Create context for the same user (self-access)
        let context = QueryContext::for_user(
            "req-123".to_string(),
            user_id.clone(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.email, "<EMAIL>");
        assert!(!result.is_verified); // New users are not verified
        assert!(result.is_active); // New users are active
        assert!(!result.mfa_enabled);
        assert!(result.roles.is_none());
        assert!(result.active_sessions.is_none());
    }

    #[tokio::test]
    async fn test_get_user_profile_not_found() {
        let repository = MockUserRepository::new();
        let cache = MockQueryCache;
        let handler = GetUserProfileHandler::new(repository, cache);

        let query = GetUserProfileQuery {
            user_id: "nonexistent_user".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        // Create admin context to bypass authorization for this test
        let context = QueryContext::for_user(
            "req-456".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::NotFound(_))));
    }

    #[tokio::test]
    async fn test_get_user_profile_invalid_user_id() {
        let repository = MockUserRepository::new();
        let cache = MockQueryCache;
        let handler = GetUserProfileHandler::new(repository, cache);

        let query = GetUserProfileQuery {
            user_id: "".to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        // Create admin context to bypass authorization for this test
        let context = QueryContext::for_user(
            "req-789".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));
    }

    #[tokio::test]
    async fn test_authorization_denied() {
        let mut repository = MockUserRepository::new();
        let cache = MockQueryCache;

        // Create test users
        let email1 = Email::new("<EMAIL>").unwrap();
        let password1 = Password::new("TestPassword123!").unwrap();
        let user1 = User::new(email1, password1).unwrap();
        let user1_id = user1.id().clone();
        repository.add_user(user1);

        let handler = GetUserProfileHandler::new(repository, cache);

        let query = GetUserProfileQuery {
            user_id: user1_id.as_str().to_string(),
            include_roles: false,
            include_sessions: false,
            include_mfa_status: false,
        };

        // Create context for different user without admin permissions
        let context = QueryContext::for_user(
            "req-unauthorized".to_string(),
            UserId::from_string("other-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()], // No admin permissions
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_admin_can_view_any_profile() {
        let mut repository = MockUserRepository::new();
        let cache = MockQueryCache;

        // Create test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let user = User::new(email, password).unwrap();
        let user_id = user.id().clone();
        repository.add_user(user);

        let handler = GetUserProfileHandler::new(repository, cache);

        let query = GetUserProfileQuery {
            user_id: user_id.as_str().to_string(),
            include_roles: true,
            include_sessions: true,
            include_mfa_status: true,
        };

        // Create admin context
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:users:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.email, "<EMAIL>");
        assert!(result.roles.is_some()); // Should include roles when requested
        assert!(result.active_sessions.is_some()); // Should include sessions when requested
    }
}
