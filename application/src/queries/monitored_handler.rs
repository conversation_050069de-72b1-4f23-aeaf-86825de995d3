// Monitored query handler wrapper
// Integrates performance monitoring, SLA tracking, and tracing into query execution

use super::base::{AsyncQuery<PERSON>andler, Query, QueryContext};
use crate::errors::ApplicationResult;
use crate::monitoring::{
    MetricsCollector, PerformanceBudget, PerformanceTracer, QueryMetrics, SlaMonitor, SlaViolation,
    SpanStatus, UserContext, ViolationSeverity, ViolationType,
};
use async_trait::async_trait;
use std::sync::Arc;
use std::time::{Instant, SystemTime};
use tracing::{Instrument, debug, info_span, warn};

/// Wrapper for query handlers that adds monitoring capabilities
pub struct MonitoredQueryHandler<H, Q>
where
    H: AsyncQueryHandler<Q>,
    Q: Query,
{
    _phantom: std::marker::PhantomData<Q>,
    inner: H,
    query_name: String,
    metrics_collector: Arc<dyn MetricsCollector>,
    sla_monitor: Arc<dyn SlaMonitor>,
    tracer: Arc<dyn PerformanceTracer>,
    performance_budget: PerformanceBudget,
}

impl<H, Q> MonitoredQueryHandler<H, Q>
where
    H: AsyncQueryHandler<Q>,
    Q: Query,
{
    /// Create a new monitored query handler
    pub fn new(
        inner: H,
        query_name: String,
        metrics_collector: Arc<dyn MetricsCollector>,
        sla_monitor: Arc<dyn SlaMonitor>,
        tracer: Arc<dyn PerformanceTracer>,
    ) -> Self {
        Self {
            _phantom: std::marker::PhantomData,
            inner,
            query_name,
            metrics_collector,
            sla_monitor,
            tracer,
            performance_budget: PerformanceBudget::new(),
        }
    }

    /// Set custom performance budget
    pub fn with_budget(mut self, budget: PerformanceBudget) -> Self {
        self.performance_budget = budget;
        self
    }

    /// Extract user context from query context
    #[allow(dead_code)]
    fn extract_user_context(&self, context: &QueryContext) -> UserContext {
        UserContext {
            user_id: context.user_id.as_ref().map(|id| id.to_string()),
            session_id: context.session_id.as_ref().map(|id| id.to_string()),
            request_id: context.request_id.clone(),
            ip_address: context.ip_address.clone(),
            user_agent: context.user_agent.clone(),
        }
    }
}

#[async_trait]
impl<H, Q> AsyncQueryHandler<Q> for MonitoredQueryHandler<H, Q>
where
    H: AsyncQueryHandler<Q> + Send + Sync,
    Q: Query + Send + Sync + 'static,
    Q::Result: Send + Sync,
{
    async fn handle(&self, query: Q) -> ApplicationResult<Q::Result> {
        // Create tracing span
        let span = info_span!(
            "query",
            name = %self.query_name,
            request_id = %"", // Would be filled from context
        );

        async move {
            // Start monitoring
            let start = Instant::now();
            let trace_context = self
                .tracer
                .start_trace(&format!("query:{}", self.query_name));
            let mut budget = self.performance_budget.clone();

            // Track authorization time
            let auth_start = Instant::now();
            // Authorization would happen here in real implementation
            let auth_duration = auth_start.elapsed();
            budget.consume("authorization", auth_duration.as_millis() as u64);

            // Execute the query with monitoring
            let query_start = Instant::now();
            let result = self.inner.handle(query).await;
            let query_duration = query_start.elapsed();

            // Update budget
            budget.consume("business_logic", query_duration.as_millis() as u64);

            let total_duration = start.elapsed();
            let timestamp = SystemTime::now();

            // Determine result count and error type
            let (result_count, error_type) = match &result {
                Ok(_) => (1, None), // Would extract actual count from result
                Err(e) => (0, Some(e.to_string())),
            };

            // Create query metrics
            let metrics = QueryMetrics {
                query_name: self.query_name.clone(),
                execution_time: total_duration,
                cache_hit: false, // Would be determined from actual execution
                cache_response_time: None,
                database_response_time: Some(query_duration),
                result_count,
                error_type: error_type.clone(),
                user_context: UserContext {
                    user_id: None, // Would extract from context
                    session_id: None,
                    request_id: trace_context.trace_id.clone(),
                    ip_address: None,
                    user_agent: None,
                },
                timestamp,
            };

            // Record metrics
            self.metrics_collector
                .record_query_execution(metrics.clone());

            // Check SLA compliance
            let sla_status = self.sla_monitor.check_sla_compliance(&metrics);
            if !sla_status.compliant {
                // Create and trigger SLA violation alert
                let violation = SlaViolation {
                    query_name: self.query_name.clone(),
                    violation_type: sla_status
                        .violation_type
                        .unwrap_or(ViolationType::LatencyExceeded),
                    severity: sla_status.severity.unwrap_or(ViolationSeverity::Warning),
                    actual_value: total_duration.as_millis() as f64,
                    threshold_value: sla_status.threshold_ms as f64,
                    timestamp,
                    user_context: None,
                    request_id: trace_context.trace_id.clone(),
                    duration: total_duration,
                    auto_escalated: false,
                };

                self.sla_monitor.trigger_alert(violation);
            }

            // Complete trace
            let _span_status = match &result {
                Ok(_) => SpanStatus::Ok,
                Err(e) => SpanStatus::Error(e.to_string()),
            };

            self.tracer.complete_trace(trace_context);

            // Log performance budget utilization
            let budget_report = budget.utilization_report();
            if budget_report.is_exceeded {
                warn!(
                    "Query '{}' exceeded performance budget: {}ms > {}ms (utilization: {:.1}%)",
                    self.query_name,
                    budget_report.total_consumed_ms,
                    budget_report.total_budget_ms,
                    budget_report.utilization_percentage
                );

                // Log exceeded components
                for component in &budget_report.exceeded_components {
                    warn!(
                        "  Component '{}' exceeded budget: {}ms > {}ms",
                        component.name, component.consumed_ms, component.allocated_ms
                    );
                }
            } else {
                debug!(
                    "Query '{}' completed within budget: {}ms / {}ms ({:.1}% utilized)",
                    self.query_name,
                    budget_report.total_consumed_ms,
                    budget_report.total_budget_ms,
                    budget_report.utilization_percentage
                );
            }

            // Log query completion
            match &result {
                Ok(_) => {
                    debug!(
                        "Query '{}' completed successfully in {:?}",
                        self.query_name, total_duration
                    );
                }
                Err(e) => {
                    warn!(
                        "Query '{}' failed after {:?}: {}",
                        self.query_name, total_duration, e
                    );
                }
            }

            result
        }
        .instrument(span)
        .await
    }
}

/// Extension trait to easily wrap query handlers with monitoring
pub trait MonitoredQueryHandlerExt<Q: Query>: AsyncQueryHandler<Q> + Sized {
    /// Wrap this handler with monitoring capabilities
    fn with_monitoring(
        self,
        query_name: String,
        metrics_collector: Arc<dyn MetricsCollector>,
        sla_monitor: Arc<dyn SlaMonitor>,
        tracer: Arc<dyn PerformanceTracer>,
    ) -> MonitoredQueryHandler<Self, Q> {
        MonitoredQueryHandler::new(self, query_name, metrics_collector, sla_monitor, tracer)
    }
}

// Implement the extension trait for all async query handlers
impl<H, Q> MonitoredQueryHandlerExt<Q> for H
where
    H: AsyncQueryHandler<Q>,
    Q: Query,
{
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::monitoring::{
        DefaultPerformanceTracer, DefaultSlaMonitor, InMemoryMetricsCollector,
    };
    use async_trait::async_trait;

    // Test query
    #[derive(Debug)]
    struct TestQuery {
        id: String,
    }

    impl Query for TestQuery {
        type Result = String;
    }

    // Test handler
    struct TestHandler;

    #[async_trait]
    impl AsyncQueryHandler<TestQuery> for TestHandler {
        async fn handle(&self, query: TestQuery) -> ApplicationResult<String> {
            Ok(format!("Result for {}", query.id))
        }
    }

    #[tokio::test]
    async fn test_monitored_handler_success() {
        let handler = TestHandler;
        let metrics_collector = Arc::new(InMemoryMetricsCollector::new());
        let sla_monitor = Arc::new(DefaultSlaMonitor::new());
        let tracer = Arc::new(DefaultPerformanceTracer::new());

        let monitored = handler.with_monitoring(
            "test_query".to_string(),
            metrics_collector.clone(),
            sla_monitor,
            tracer,
        );

        let query = TestQuery {
            id: "test123".to_string(),
        };

        let result = monitored.handle(query).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "Result for test123");

        // Verify metrics were recorded
        let stats = metrics_collector.get_query_metrics("test_query");
        assert!(stats.is_some());
        let stats = stats.unwrap();
        assert_eq!(stats.total_executions, 1);
        assert_eq!(stats.error_rate, 0.0);
    }

    #[tokio::test]
    async fn test_monitored_handler_with_budget() {
        let handler = TestHandler;
        let metrics_collector = Arc::new(InMemoryMetricsCollector::new());
        let sla_monitor = Arc::new(DefaultSlaMonitor::new());
        let tracer = Arc::new(DefaultPerformanceTracer::new());

        let mut budget = PerformanceBudget::new();
        budget.consume("database_query", 30); // Pre-consume some budget

        let monitored = handler
            .with_monitoring(
                "test_query".to_string(),
                metrics_collector,
                sla_monitor,
                tracer,
            )
            .with_budget(budget);

        let query = TestQuery {
            id: "test456".to_string(),
        };

        let result = monitored.handle(query).await;
        assert!(result.is_ok());
    }

    // Test failing handler
    struct FailingHandler;

    #[async_trait]
    impl AsyncQueryHandler<TestQuery> for FailingHandler {
        async fn handle(&self, _query: TestQuery) -> ApplicationResult<String> {
            Err(crate::errors::ApplicationError::NotFound(
                "Test error".to_string(),
            ))
        }
    }

    #[tokio::test]
    async fn test_monitored_handler_failure() {
        let handler = FailingHandler;
        let metrics_collector = Arc::new(InMemoryMetricsCollector::new());
        let sla_monitor = Arc::new(DefaultSlaMonitor::new());
        let tracer = Arc::new(DefaultPerformanceTracer::new());

        let monitored = handler.with_monitoring(
            "failing_query".to_string(),
            metrics_collector.clone(),
            sla_monitor,
            tracer,
        );

        let query = TestQuery {
            id: "fail".to_string(),
        };

        let result = monitored.handle(query).await;
        assert!(result.is_err());

        // Verify error metrics were recorded
        let stats = metrics_collector.get_query_metrics("failing_query");
        assert!(stats.is_some());
        let stats = stats.unwrap();
        assert_eq!(stats.total_executions, 1);
        assert_eq!(stats.error_rate, 1.0);
    }
}
