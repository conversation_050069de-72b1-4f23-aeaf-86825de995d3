// Get current session query and handler
// Implements secure retrieval of current active session details

use crate::errors::{ApplicationError, ApplicationResult};
use crate::queries::base::{AsyncQueryHandler, Query, QueryCache, QueryContext};
use auth_domain::{repositories::SessionRepository, value_objects::SessionId};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Query to get details of the current active session
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub struct GetCurrentSessionQuery {
    pub session_id: String,
}

impl Query for GetCurrentSessionQuery {
    type Result = CurrentSessionResult;
}

/// Current session information for API responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CurrentSessionResult {
    pub session_id: String,
    pub user_id: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub is_active: bool,
    pub is_valid: bool,
    pub created_at: std::time::SystemTime,
    pub last_accessed: std::time::SystemTime,
    pub expires_at: std::time::SystemTime,
    pub device_type: Option<String>,
    pub location: Option<String>,
}

/// Handler for getting current session details with security and performance
pub struct GetCurrentSessionHandler<SR, C>
where
    SR: SessionRepository,
    C: QueryCache<GetCurrentSessionQuery>,
{
    session_repository: SR,
    cache: C,
}

impl<SR, C> GetCurrentSessionHandler<SR, C>
where
    SR: SessionRepository,
    C: QueryCache<GetCurrentSessionQuery>,
{
    pub fn new(session_repository: SR, cache: C) -> Self {
        Self {
            session_repository,
            cache,
        }
    }

    /// Handle the get current session query with full security and performance features
    pub async fn handle_with_context(
        &self,
        query: GetCurrentSessionQuery,
        context: QueryContext,
    ) -> ApplicationResult<CurrentSessionResult> {
        // Start timing for SLA monitoring
        let start = Instant::now();

        // Check cache first for better performance
        if let Some(cached_result) = self.cache.get(&query).await {
            // Still check authorization for cached results
            self.authorize(&query, &context).await?;
            return Ok(cached_result);
        }

        // Authorization check
        self.authorize(&query, &context).await?;

        // Validate and parse session ID
        let session_id = SessionId::from_string(query.session_id.clone())
            .map_err(|e| ApplicationError::Validation(format!("Invalid session ID: {e}")))?;

        // Fetch session from repository
        let session = self
            .session_repository
            .find_by_id(&session_id)
            .await
            .map_err(|e| ApplicationError::Repository(format!("Database error: {e}")))?
            .ok_or_else(|| ApplicationError::NotFound("Session not found".to_string()))?;

        // Additional security check: verify session belongs to the requesting user
        // Unless the user has admin or security permissions
        if let Some(ref user_id) = context.user_id {
            if session.user_id() != user_id
                && !context.has_permission("admin:sessions:read")
                && !context.has_permission("security:sessions:read")
            {
                return Err(ApplicationError::Forbidden);
            }
        }

        // Build result with session details
        let result = CurrentSessionResult {
            session_id: session.id().as_str().to_string(),
            user_id: session.user_id().as_str().to_string(),
            ip_address: session.ip_address().map(|s| s.to_string()),
            user_agent: session.user_agent().map(|s| s.to_string()),
            is_active: session.is_active(),
            is_valid: session.is_valid(),
            created_at: session.created_at().into(),
            last_accessed: session.last_accessed().into(),
            expires_at: session.expires_at().into(),
            device_type: session.user_agent().map(|ua| self.detect_device_type(ua)),
            location: None, // TODO: Add geolocation service integration
        };

        // Check SLA compliance
        let elapsed = start.elapsed();
        if elapsed > Duration::from_millis(100) {
            tracing::warn!(
                "GetCurrentSessionQuery exceeded SLA: {:?} for session {}",
                elapsed,
                query.session_id
            );
        }

        // Cache the result for future requests (TTL: 1 minute - sessions can change frequently)
        self.cache
            .set(&query, &result, Duration::from_secs(60))
            .await;

        Ok(result)
    }

    async fn authorize(
        &self,
        query: &GetCurrentSessionQuery,
        context: &QueryContext,
    ) -> ApplicationResult<()> {
        // Must be authenticated to view session details
        if !context.is_authenticated() {
            return Err(ApplicationError::Forbidden);
        }

        // Allow users to view their own current session
        if let Some(ref session_id) = context.session_id {
            if session_id.as_str() == query.session_id {
                return Ok(());
            }
        }

        // Allow admins to view any session
        if context.has_permission("admin:sessions:read") {
            return Ok(());
        }

        // Allow security team to view sessions
        if context.has_permission("security:sessions:read") {
            return Ok(());
        }

        // Deny access for insufficient permissions
        Err(ApplicationError::Forbidden)
    }

    fn detect_device_type(&self, user_agent: &str) -> String {
        let ua = user_agent.to_lowercase();

        // Check for tablet first (as some tablets contain "mobile" in UA)
        if ua.contains("tablet") || ua.contains("ipad") {
            "tablet".to_string()
        } else if ua.contains("mobile") || ua.contains("android") || ua.contains("iphone") {
            "mobile".to_string()
        } else if ua.contains("bot") || ua.contains("crawler") || ua.contains("spider") {
            "bot".to_string()
        } else {
            "desktop".to_string()
        }
    }
}

#[async_trait::async_trait]
impl<SR, C> AsyncQueryHandler<GetCurrentSessionQuery> for GetCurrentSessionHandler<SR, C>
where
    SR: SessionRepository,
    C: QueryCache<GetCurrentSessionQuery>,
{
    async fn handle(
        &self,
        query: GetCurrentSessionQuery,
    ) -> ApplicationResult<CurrentSessionResult> {
        // For the trait implementation, create a minimal context
        // In real usage, the context should be provided from the API layer
        let context = QueryContext::new("anonymous".to_string());
        self.handle_with_context(query, context).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use auth_domain::{
        entities::Session,
        errors::DomainError,
        value_objects::{SessionId, UserId},
    };
    use std::collections::HashMap;

    // Mock session repository for testing
    struct MockSessionRepository {
        sessions: HashMap<String, Session>,
    }

    impl MockSessionRepository {
        fn new() -> Self {
            Self {
                sessions: HashMap::new(),
            }
        }

        fn add_session(&mut self, session: Session) {
            self.sessions
                .insert(session.id().as_str().to_string(), session);
        }
    }

    #[async_trait]
    impl SessionRepository for MockSessionRepository {
        async fn save(&self, _session: &Session) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_by_id(&self, id: &SessionId) -> Result<Option<Session>, DomainError> {
            Ok(self.sessions.get(id.as_str()).cloned())
        }

        async fn find_active_by_user(
            &self,
            _user_id: &UserId,
        ) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn find_all_by_user(&self, _user_id: &UserId) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn delete_all_by_user(&self, _user_id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn count_active_by_user(&self, _user_id: &UserId) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn find_by_ip_address(&self, _ip_address: &str) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn update_last_accessed(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn invalidate(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn invalidate_all_except(
            &self,
            _user_id: &UserId,
            _except_session_id: &SessionId,
        ) -> Result<(), DomainError> {
            unimplemented!()
        }
    }

    // Mock cache for testing
    struct MockQueryCache;

    #[async_trait]
    impl QueryCache<GetCurrentSessionQuery> for MockQueryCache {
        async fn get(&self, _query: &GetCurrentSessionQuery) -> Option<CurrentSessionResult> {
            None // Always cache miss for simplicity
        }

        async fn set(
            &self,
            _query: &GetCurrentSessionQuery,
            _result: &CurrentSessionResult,
            _ttl: Duration,
        ) {
            // No-op for testing
        }

        async fn invalidate(&self, _pattern: &str) {
            // No-op for testing
        }
    }

    #[tokio::test]
    async fn test_get_current_session_success() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let user_id = UserId::from_string("user-123".to_string()).unwrap();
        let session = Session::new(
            user_id.clone(),
            3600,
            Some("*************".to_string()),
            Some("Mozilla/5.0 (Macintosh; Intel Mac OS X) Desktop Browser".to_string()),
        );
        let session_id = session.id().clone();

        repository.add_session(session);

        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: session_id.as_str().to_string(),
        };

        // Create context for the session owner
        let context = QueryContext::for_user(
            "req-123".to_string(),
            user_id,
            session_id.clone(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.session_id, session_id.as_str());
        assert_eq!(result.user_id, "user-123");
        assert_eq!(result.ip_address, Some("*************".to_string()));
        assert!(result.is_active);
        assert!(result.is_valid);
        assert_eq!(result.device_type, Some("desktop".to_string()));
    }

    #[tokio::test]
    async fn test_get_current_session_not_found() {
        let repository = MockSessionRepository::new();
        let cache = MockQueryCache;
        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: "sess_nonexistent123456789012345678901234567890".to_string(),
        };

        // Create admin context to bypass authorization for this test
        let context = QueryContext::for_user(
            "req-456".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:sessions:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::NotFound(_))));
    }

    #[tokio::test]
    async fn test_authorization_denied_unauthenticated() {
        let repository = MockSessionRepository::new();
        let cache = MockQueryCache;
        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: "sess_example123456789012345678901234567890".to_string(),
        };

        // Create unauthenticated context
        let context = QueryContext::new("req-unauthorized".to_string());

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_authorization_denied_different_user() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let owner_user_id = UserId::from_string("owner-user".to_string()).unwrap();
        let session = Session::new(
            owner_user_id,
            3600,
            Some("10.0.0.1".to_string()),
            Some("Test Browser".to_string()),
        );
        let session_id = session.id().clone();

        repository.add_session(session);

        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: session_id.as_str().to_string(),
        };

        // Create context for different user without admin permissions
        let context = QueryContext::for_user(
            "req-different".to_string(),
            UserId::from_string("different-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()], // No admin permissions
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }

    #[tokio::test]
    async fn test_admin_can_view_any_session() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let target_user_id = UserId::from_string("target-user".to_string()).unwrap();
        let session = Session::new(
            target_user_id,
            3600,
            Some("**********".to_string()),
            Some("Admin Browser".to_string()),
        );
        let session_id = session.id().clone();

        repository.add_session(session);

        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: session_id.as_str().to_string(),
        };

        // Create admin context
        let context = QueryContext::for_user(
            "req-admin".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:sessions:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, "target-user");
        assert_eq!(result.ip_address, Some("**********".to_string()));
    }

    #[tokio::test]
    async fn test_security_team_can_view_sessions() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let target_user_id = UserId::from_string("suspicious-user".to_string()).unwrap();
        let session = Session::new(
            target_user_id,
            3600,
            Some("suspicious.ip.com".to_string()),
            Some("Suspicious Agent".to_string()),
        );
        let session_id = session.id().clone();

        repository.add_session(session);

        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: session_id.as_str().to_string(),
        };

        // Create security team context
        let context = QueryContext::for_user(
            "req-security".to_string(),
            UserId::from_string("security-analyst".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["security:sessions:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await.unwrap();

        assert_eq!(result.user_id, "suspicious-user");
        assert_eq!(result.ip_address, Some("suspicious.ip.com".to_string()));
    }

    #[tokio::test]
    async fn test_invalid_session_id() {
        let repository = MockSessionRepository::new();
        let cache = MockQueryCache;
        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: "invalid-session-id".to_string(),
        };

        // Create admin context to bypass authorization for this test
        let context = QueryContext::for_user(
            "req-invalid".to_string(),
            UserId::from_string("admin-user".to_string()).unwrap(),
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["admin:sessions:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Validation(_))));
    }

    #[tokio::test]
    async fn test_device_type_detection() {
        let repository = MockSessionRepository::new();
        let cache = MockQueryCache;
        let handler = GetCurrentSessionHandler::new(repository, cache);

        // Test different user agents
        let test_cases = vec![
            ("Mozilla/5.0 (iPhone; CPU iPhone OS) Mobile", "mobile"),
            ("Mozilla/5.0 (Android; Mobile) Chrome", "mobile"),
            ("Mozilla/5.0 (iPad; CPU OS) Safari", "tablet"),
            ("Mozilla/5.0 (Tablet; Android) Chrome", "tablet"),
            ("Googlebot/2.1 (+http://www.google.com/bot.html)", "bot"),
            (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome",
                "desktop",
            ),
        ];

        for (user_agent, expected_device) in test_cases {
            let detected = handler.detect_device_type(user_agent);
            assert_eq!(
                detected, expected_device,
                "Failed for user agent: {user_agent}"
            );
        }
    }

    #[tokio::test]
    async fn test_session_ownership_validation() {
        let mut repository = MockSessionRepository::new();
        let cache = MockQueryCache;

        let owner_user_id = UserId::from_string("owner-user".to_string()).unwrap();
        let different_user_id = UserId::from_string("different-user".to_string()).unwrap();

        let session = Session::new(
            owner_user_id.clone(),
            3600,
            Some("***********".to_string()),
            Some("Browser".to_string()),
        );
        let session_id = session.id().clone();

        repository.add_session(session);

        let handler = GetCurrentSessionHandler::new(repository, cache);

        let query = GetCurrentSessionQuery {
            session_id: session_id.as_str().to_string(),
        };

        // Try to access session with different user context (not admin)
        let context = QueryContext::for_user(
            "req-ownership".to_string(),
            different_user_id,
            SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string())
                .unwrap(),
            vec!["user:read".to_string()],
        );

        let result = handler.handle_with_context(query, context).await;
        assert!(matches!(result, Err(ApplicationError::Forbidden)));
    }
}
