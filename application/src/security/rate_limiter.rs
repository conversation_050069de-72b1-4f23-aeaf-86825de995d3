// Rate limiting implementation for preventing brute force and DoS attacks
// Implements per-IP and per-account rate limiting with progressive delays

use crate::errors::ApplicationError;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// Configuration for rate limiting
#[derive(Debug, Clone)]
pub struct RateLimiterConfig {
    /// Maximum attempts per IP address per window
    pub max_attempts_per_ip: u32,
    /// Maximum attempts per account per window
    pub max_attempts_per_account: u32,
    /// Time window for rate limiting
    pub window_duration: Duration,
    /// Progressive delay multiplier for repeated failures
    pub progressive_delay_multiplier: f64,
    /// Maximum progressive delay
    pub max_progressive_delay: Duration,
    /// Whether to enable strict mode (blocks after threshold)
    pub strict_mode: bool,
    /// Number of failed attempts before progressive delays start
    pub progressive_delay_threshold: u32,
}

impl Default for RateLimiterConfig {
    fn default() -> Self {
        Self {
            max_attempts_per_ip: 10,                   // 10 attempts per IP per window
            max_attempts_per_account: 5,               // 5 attempts per account per window
            window_duration: Duration::from_secs(900), // 15 minute window
            progressive_delay_multiplier: 2.0,
            max_progressive_delay: Duration::from_secs(30),
            strict_mode: true,
            progressive_delay_threshold: 3,
        }
    }
}

/// Key for rate limiting tracking
#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub enum RateLimitKey {
    /// Rate limit by IP address
    IpAddress(String),
    /// Rate limit by account identifier (email/username)
    Account(String),
    /// Rate limit by combined IP and account
    Combined(String, String),
}

/// Tracking data for rate limiting
#[derive(Debug, Clone)]
struct RateLimitEntry {
    /// Number of attempts
    attempts: u32,
    /// Number of failed attempts
    failed_attempts: u32,
    /// First attempt timestamp
    first_attempt: Instant,
    /// Last attempt timestamp
    last_attempt: Instant,
    /// Whether the entry is currently blocked
    blocked_until: Option<Instant>,
}

impl RateLimitEntry {
    fn new() -> Self {
        let now = Instant::now();
        Self {
            attempts: 1,
            failed_attempts: 0,
            first_attempt: now,
            last_attempt: now,
            blocked_until: None,
        }
    }

    fn is_expired(&self, window: Duration) -> bool {
        self.first_attempt.elapsed() > window
    }

    fn is_blocked(&self) -> bool {
        if let Some(blocked_until) = self.blocked_until {
            blocked_until > Instant::now()
        } else {
            false
        }
    }

    fn calculate_delay(&self, config: &RateLimiterConfig) -> Duration {
        if self.failed_attempts < config.progressive_delay_threshold {
            Duration::ZERO
        } else {
            let delay_factor = config
                .progressive_delay_multiplier
                .powi((self.failed_attempts - config.progressive_delay_threshold) as i32);
            let delay = Duration::from_millis((delay_factor * 1000.0) as u64);
            delay.min(config.max_progressive_delay)
        }
    }
}

/// Rate limiter service for preventing brute force attacks
pub struct RateLimiter {
    config: RateLimiterConfig,
    entries: Arc<RwLock<HashMap<RateLimitKey, RateLimitEntry>>>,
    /// For tracking suspicious patterns across IPs
    suspicious_patterns: Arc<RwLock<HashMap<String, Vec<Instant>>>>,
}

impl Default for RateLimiter {
    fn default() -> Self {
        Self::with_config(RateLimiterConfig::default())
    }
}

impl RateLimiter {
    /// Create a new rate limiter with default configuration
    pub fn new() -> Self {
        Self::default()
    }

    /// Create a new rate limiter with custom configuration
    pub fn with_config(config: RateLimiterConfig) -> Self {
        Self {
            config,
            entries: Arc::new(RwLock::new(HashMap::new())),
            suspicious_patterns: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Check if an attempt should be allowed
    pub async fn check_rate_limit(&self, key: &RateLimitKey) -> Result<(), ApplicationError> {
        let mut entries = self.entries.write().await;

        // Clean up expired entries periodically
        if entries.len() > 1000 {
            self.cleanup_expired_entries(&mut entries).await;
        }

        // Check if we have an existing entry
        let existing_entry = entries.get_mut(key);

        match existing_entry {
            Some(entry) => {
                // Check if entry is expired and reset if needed
                if entry.is_expired(self.config.window_duration) {
                    *entry = RateLimitEntry::new();
                    return Ok(());
                }

                // Check if currently blocked
                if entry.is_blocked() {
                    return Err(ApplicationError::RateLimitExceeded);
                }

                // Check rate limits BEFORE incrementing
                let max_attempts = match key {
                    RateLimitKey::IpAddress(_) => self.config.max_attempts_per_ip,
                    RateLimitKey::Account(_) => self.config.max_attempts_per_account,
                    RateLimitKey::Combined(_, _) => self.config.max_attempts_per_account,
                };

                if entry.attempts >= max_attempts {
                    if self.config.strict_mode {
                        // Block for remaining window duration
                        entry.blocked_until =
                            Some(entry.first_attempt + self.config.window_duration);
                    }
                    return Err(ApplicationError::RateLimitExceeded);
                }

                // Apply progressive delay if needed
                let delay = entry.calculate_delay(&self.config);
                if delay > Duration::ZERO {
                    // In production, this would trigger an actual delay
                    // For now, we'll just track it
                    entry.last_attempt = Instant::now();
                }

                entry.attempts += 1;
                entry.last_attempt = Instant::now();
            }
            None => {
                // Create new entry - this is the first attempt
                entries.insert(key.clone(), RateLimitEntry::new());
            }
        }

        Ok(())
    }

    /// Record a failed attempt for progressive delay tracking
    pub async fn record_failed_attempt(&self, key: &RateLimitKey) {
        let mut entries = self.entries.write().await;

        if let Some(entry) = entries.get_mut(key) {
            entry.failed_attempts += 1;

            // Check if we should trigger blocking
            if entry.failed_attempts >= self.config.max_attempts_per_account {
                entry.blocked_until = Some(Instant::now() + self.config.window_duration);
            }
        }

        // Track suspicious patterns for all key types that have IP info
        let ip_to_track = match key {
            RateLimitKey::IpAddress(ip) => Some(ip.clone()),
            RateLimitKey::Combined(ip, _) => Some(ip.clone()),
            RateLimitKey::Account(_) => None,
        };

        if let Some(ip) = ip_to_track {
            let mut patterns = self.suspicious_patterns.write().await;
            let attempts = patterns.entry(ip).or_insert_with(Vec::new);
            attempts.push(Instant::now());

            // Keep only recent attempts
            let cutoff = Instant::now() - Duration::from_secs(3600); // 1 hour
            attempts.retain(|&t| t > cutoff);
        }
    }

    /// Record a successful attempt (resets failed attempts)
    pub async fn record_successful_attempt(&self, key: &RateLimitKey) {
        let mut entries = self.entries.write().await;

        if let Some(entry) = entries.get_mut(key) {
            entry.failed_attempts = 0;
            entry.blocked_until = None;
        }
    }

    /// Get the current delay for a key (for progressive delays)
    pub async fn get_current_delay(&self, key: &RateLimitKey) -> Duration {
        let entries = self.entries.read().await;

        if let Some(entry) = entries.get(key) {
            entry.calculate_delay(&self.config)
        } else {
            Duration::ZERO
        }
    }

    /// Check if an IP address shows suspicious patterns
    pub async fn is_suspicious_ip(&self, ip: &str) -> bool {
        let patterns = self.suspicious_patterns.read().await;

        if let Some(attempts) = patterns.get(ip) {
            // Suspicious if more than 20 attempts in the last hour
            attempts.len() > 20
        } else {
            false
        }
    }

    /// Clean up expired entries to prevent memory leaks
    async fn cleanup_expired_entries(&self, entries: &mut HashMap<RateLimitKey, RateLimitEntry>) {
        let window = self.config.window_duration;
        entries.retain(|_, entry| !entry.is_expired(window * 2));
    }

    /// Get statistics for monitoring
    pub async fn get_stats(&self) -> RateLimiterStats {
        let entries = self.entries.read().await;
        let patterns = self.suspicious_patterns.read().await;

        let blocked_count = entries.values().filter(|e| e.is_blocked()).count();
        let total_entries = entries.len();
        let suspicious_ips = patterns.len();

        RateLimiterStats {
            total_tracked_entries: total_entries,
            currently_blocked: blocked_count,
            suspicious_ip_count: suspicious_ips,
        }
    }

    /// Clear all rate limiting data (for testing)
    #[cfg(test)]
    pub async fn clear_all(&self) {
        let mut entries = self.entries.write().await;
        let mut patterns = self.suspicious_patterns.write().await;
        entries.clear();
        patterns.clear();
    }
}

/// Statistics for rate limiter monitoring
#[derive(Debug, Clone)]
pub struct RateLimiterStats {
    pub total_tracked_entries: usize,
    pub currently_blocked: usize,
    pub suspicious_ip_count: usize,
}

/// Helper function to create rate limit keys
pub fn rate_limit_key_from_ip(ip: Option<String>) -> RateLimitKey {
    RateLimitKey::IpAddress(ip.unwrap_or_else(|| "unknown".to_string()))
}

pub fn rate_limit_key_from_account(account: &str) -> RateLimitKey {
    RateLimitKey::Account(account.to_string())
}

pub fn rate_limit_key_combined(ip: Option<String>, account: &str) -> RateLimitKey {
    RateLimitKey::Combined(
        ip.unwrap_or_else(|| "unknown".to_string()),
        account.to_string(),
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_rate_limit_allows_under_threshold() {
        let limiter = RateLimiter::new();
        let key = RateLimitKey::IpAddress("***********".to_string());

        // Should allow up to max_attempts_per_ip attempts
        for i in 0..10 {
            let result = limiter.check_rate_limit(&key).await;
            assert!(result.is_ok(), "Attempt {} should be allowed", i + 1);
        }
    }

    #[tokio::test]
    async fn test_rate_limit_blocks_over_threshold() {
        let config = RateLimiterConfig {
            max_attempts_per_ip: 3,
            ..Default::default()
        };
        let limiter = RateLimiter::with_config(config);
        let key = RateLimitKey::IpAddress("***********".to_string());

        // Allow first 3 attempts
        for _ in 0..3 {
            assert!(limiter.check_rate_limit(&key).await.is_ok());
        }

        // Block 4th attempt
        assert!(matches!(
            limiter.check_rate_limit(&key).await,
            Err(ApplicationError::RateLimitExceeded)
        ));
    }

    #[tokio::test]
    async fn test_progressive_delays() {
        let config = RateLimiterConfig {
            progressive_delay_threshold: 2,
            progressive_delay_multiplier: 2.0,
            ..Default::default()
        };
        let limiter = RateLimiter::with_config(config);
        let key = RateLimitKey::Account("<EMAIL>".to_string());

        // Record failed attempts
        for _ in 0..3 {
            limiter.check_rate_limit(&key).await.ok();
            limiter.record_failed_attempt(&key).await;
        }

        // Check delay increases
        let delay = limiter.get_current_delay(&key).await;
        assert!(delay > Duration::ZERO);
    }

    #[tokio::test]
    async fn test_successful_attempt_resets_failures() {
        let limiter = RateLimiter::new();
        let key = RateLimitKey::Account("<EMAIL>".to_string());

        // Record failed attempts
        limiter.check_rate_limit(&key).await.ok();
        limiter.record_failed_attempt(&key).await;
        limiter.record_failed_attempt(&key).await;

        // Record success
        limiter.record_successful_attempt(&key).await;

        // Delay should be reset
        let delay = limiter.get_current_delay(&key).await;
        assert_eq!(delay, Duration::ZERO);
    }

    #[tokio::test]
    async fn test_suspicious_ip_detection() {
        let limiter = RateLimiter::new();
        let ip = "***********00";

        // Record many failed attempts from same IP to exceed the 20 attempt threshold
        for _ in 0..25 {
            let key = RateLimitKey::IpAddress(ip.to_string());
            limiter.record_failed_attempt(&key).await;
        }

        // Should be marked as suspicious
        assert!(limiter.is_suspicious_ip(ip).await);
    }

    #[tokio::test]
    async fn test_window_expiration() {
        let config = RateLimiterConfig {
            max_attempts_per_ip: 2,
            window_duration: Duration::from_millis(100),
            ..Default::default()
        };
        let limiter = RateLimiter::with_config(config);
        let key = RateLimitKey::IpAddress("***********".to_string());

        // Use up attempts
        limiter.check_rate_limit(&key).await.ok();
        limiter.check_rate_limit(&key).await.ok();

        // Should be blocked
        assert!(limiter.check_rate_limit(&key).await.is_err());

        // Wait for window to expire
        tokio::time::sleep(Duration::from_millis(150)).await;

        // Should be allowed again
        assert!(limiter.check_rate_limit(&key).await.is_ok());
    }

    #[tokio::test]
    async fn test_different_keys_tracked_separately() {
        let limiter = RateLimiter::new();
        let ip_key = RateLimitKey::IpAddress("***********".to_string());
        let account_key = RateLimitKey::Account("<EMAIL>".to_string());

        // Track separately
        for _ in 0..5 {
            assert!(limiter.check_rate_limit(&ip_key).await.is_ok());
            assert!(limiter.check_rate_limit(&account_key).await.is_ok());
        }

        let stats = limiter.get_stats().await;
        assert_eq!(stats.total_tracked_entries, 2);
    }
}
