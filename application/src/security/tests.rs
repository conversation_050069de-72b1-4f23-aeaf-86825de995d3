// Comprehensive security tests for authentication service
// Tests all three critical security vulnerabilities have been fixed
// Enhanced with comprehensive security testing utilities

#[cfg(test)]
mod security_integration_tests {
    use crate::commands::authenticate_user::{
        AuthToken, AuthenticateUserCommand, AuthenticateUserHandler, PasswordService, TokenService,
    };
    use crate::errors::ApplicationError;
    use crate::security::{
        constant_time_auth::ConstantTimeAuthService,
        error_sanitizer::ErrorSanitizer,
        rate_limiter::{RateLimiter, RateLimiterConfig},
    };
    use crate::testing::security::{
        ErrorSecurityTester, RateLimitTester, SecurityIntegrationTester, SecurityPerformanceTester,
        SecurityTestSuite, TimingAttackTester,
    };
    use auth_domain::{
        crypto::ConstantTimeService,
        entities::User,
        errors::DomainError,
        repositories::{SessionRepository, UserRepository},
        value_objects::{Email, Password, SessionId, UserId},
    };
    use std::collections::HashMap;
    use std::sync::Arc;
    use std::time::{Duration, Instant};

    // Mock repositories for testing
    struct TestUserRepo {
        users: HashMap<String, User>,
    }

    impl TestUserRepo {
        fn new() -> Self {
            Self {
                users: HashMap::new(),
            }
        }

        #[allow(dead_code)]
        fn with_user(mut self, user: User) -> Self {
            self.users.insert(user.email().as_str().to_string(), user);
            self
        }
    }

    #[async_trait::async_trait]
    impl UserRepository for TestUserRepo {
        async fn save(&self, _user: &User) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_by_id(&self, _id: &UserId) -> Result<Option<User>, DomainError> {
            Ok(None)
        }

        async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
            Ok(self.users.get(email.as_str()).cloned())
        }

        async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
            Ok(None)
        }

        async fn exists_by_email(&self, _email: &Email) -> Result<bool, DomainError> {
            Ok(false)
        }

        async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            Ok(vec![])
        }

        async fn count(&self) -> Result<usize, DomainError> {
            Ok(0)
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            Ok(vec![])
        }

        async fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            Ok(vec![])
        }

        async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            Ok(vec![])
        }

        async fn search_users(
            &self,
            _criteria: &auth_domain::repositories::UserSearchCriteria,
        ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
            Ok(auth_domain::repositories::UserSearchResult::new(
                Vec::new(),
                0,
                0,
                10,
            ))
        }
    }

    struct TestSessionRepo;

    #[async_trait::async_trait]
    impl SessionRepository for TestSessionRepo {
        async fn save(&self, _session: &auth_domain::entities::Session) -> Result<(), DomainError> {
            Ok(())
        }

        async fn find_by_id(
            &self,
            _id: &SessionId,
        ) -> Result<Option<auth_domain::entities::Session>, DomainError> {
            Ok(None)
        }

        async fn find_active_by_user(
            &self,
            _user_id: &UserId,
        ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
            Ok(vec![])
        }

        async fn find_all_by_user(
            &self,
            _user_id: &UserId,
        ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
            Ok(vec![])
        }

        async fn delete(&self, _id: &SessionId) -> Result<(), DomainError> {
            Ok(())
        }

        async fn delete_all_by_user(&self, _user_id: &UserId) -> Result<(), DomainError> {
            Ok(())
        }

        async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError> {
            Ok(0)
        }

        async fn count_active_by_user(&self, _user_id: &UserId) -> Result<usize, DomainError> {
            Ok(0)
        }

        async fn find_by_ip_address(
            &self,
            _ip_address: &str,
        ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
            Ok(vec![])
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
            Ok(vec![])
        }

        async fn update_last_accessed(&self, _id: &SessionId) -> Result<(), DomainError> {
            Ok(())
        }

        async fn invalidate(&self, _id: &SessionId) -> Result<(), DomainError> {
            Ok(())
        }

        async fn invalidate_all_except(
            &self,
            _user_id: &UserId,
            _except_session_id: &SessionId,
        ) -> Result<(), DomainError> {
            Ok(())
        }
    }

    struct TestPasswordService;

    #[async_trait::async_trait]
    impl PasswordService for TestPasswordService {
        async fn verify_password(
            &self,
            _user: &User,
            _password: &str,
        ) -> Result<bool, ApplicationError> {
            Ok(true)
        }
    }

    struct TestTokenService;

    #[async_trait::async_trait]
    impl TokenService for TestTokenService {
        async fn generate_tokens(
            &self,
            _user_id: &str,
            _session_id: &str,
        ) -> Result<AuthToken, ApplicationError> {
            Ok(AuthToken {
                access_token: "test_token".to_string(),
                refresh_token: "test_refresh".to_string(),
                expires_in: 3600,
            })
        }
    }

    /// ENHANCED Test 1: Comprehensive timing attack protection verification
    /// Uses advanced timing analysis with multiple scenarios and statistical validation
    #[tokio::test]
    async fn test_comprehensive_timing_attack_protection() {
        let tester = TimingAttackTester::new().expect("Failed to create timing attack tester");

        // Run comprehensive timing consistency tests
        tester
            .test_authentication_timing_consistency()
            .await
            .expect("Timing attack protection failed");
    }

    /// ENHANCED Test 1b: Email enumeration resistance through timing analysis
    #[tokio::test]
    async fn test_email_enumeration_resistance() {
        let tester = TimingAttackTester::new().expect("Failed to create timing attack tester");

        tester
            .test_email_enumeration_resistance()
            .await
            .expect("Email enumeration vulnerability detected");
    }

    /// LEGACY Test 1: Original timing test (kept for compatibility)
    #[tokio::test]
    async fn test_timing_attack_protection_legacy() {
        let ct_service = ConstantTimeService::new();
        let auth_service = ConstantTimeAuthService::new(ct_service);

        // Create test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.verify().unwrap();

        let mut timings = Vec::new();

        // Measure timing for existing user with correct password
        let start = Instant::now();
        let _ = auth_service
            .validate_authentication_attempt("<EMAIL>", "TestPassword123!", Some(&user))
            .await;
        timings.push(start.elapsed());

        // Measure timing for existing user with wrong password
        let start = Instant::now();
        let _ = auth_service
            .validate_authentication_attempt("<EMAIL>", "WrongPassword123!", Some(&user))
            .await;
        timings.push(start.elapsed());

        // Measure timing for non-existent user
        let start = Instant::now();
        let _ = auth_service
            .validate_authentication_attempt("<EMAIL>", "TestPassword123!", None)
            .await;
        timings.push(start.elapsed());

        // All timings should be within 20% of each other
        let max_time = timings.iter().max().unwrap();
        let min_time = timings.iter().min().unwrap();

        let variance_ratio = max_time.as_millis() as f64 / min_time.as_millis().max(1) as f64;

        // Assert timing consistency (allow up to 1.5x variance for test environments)
        assert!(
            variance_ratio < 1.5,
            "Timing attack vulnerability detected! Variance ratio: {:.2} (max: {:?}, min: {:?})",
            variance_ratio,
            max_time,
            min_time
        );

        // All operations should take at least the minimum duration (80ms)
        for timing in &timings {
            assert!(
                *timing >= Duration::from_millis(80),
                "Operation completed too quickly: {:?}",
                timing
            );
        }
    }

    /// ENHANCED Test 2: Comprehensive error message security validation
    /// Tests all error categories and internal information disclosure
    #[tokio::test]
    async fn test_comprehensive_error_sanitization() {
        let tester = ErrorSecurityTester::new();

        // Test authentication error sanitization
        tester
            .test_authentication_error_sanitization()
            .expect("Authentication error sanitization failed");

        // Test authorization error sanitization
        tester
            .test_authorization_error_sanitization()
            .expect("Authorization error sanitization failed");

        // Test internal error sanitization
        tester
            .test_internal_error_sanitization()
            .expect("Internal error sanitization failed");
    }

    /// LEGACY Test 2: Original error sanitization test (kept for compatibility)
    #[tokio::test]
    async fn test_information_disclosure_prevention_legacy() {
        let sanitizer = ErrorSanitizer::new();

        // Test that different authentication failures return same error
        let errors = vec![
            ApplicationError::UserNotFound,
            ApplicationError::InvalidCredentials,
            ApplicationError::AccountLocked,
            ApplicationError::AccountNotVerified,
        ];

        let mut sanitized_errors = Vec::new();
        for error in errors {
            let sanitized = sanitizer.sanitize(&error);
            sanitized_errors.push(sanitized);
        }

        // All authentication errors should return identical sanitized errors
        let first = &sanitized_errors[0];
        for sanitized in &sanitized_errors[1..] {
            assert_eq!(
                first, sanitized,
                "Error messages are leaking information about authentication failures"
            );
        }

        // Test authorization errors are also generic
        let auth_errors = vec![
            ApplicationError::InsufficientPermissions,
            ApplicationError::RoleNotFound,
        ];

        let mut auth_sanitized = Vec::new();
        for error in auth_errors {
            let sanitized = sanitizer.sanitize(&error);
            auth_sanitized.push(sanitized);
        }

        // All authorization errors should be generic
        let first_auth = &auth_sanitized[0];
        for sanitized in &auth_sanitized[1..] {
            assert_eq!(
                first_auth, sanitized,
                "Error messages are leaking information about authorization failures"
            );
        }

        // Verify specific error details are not exposed
        let internal_error = ApplicationError::Repository("Database connection failed".to_string());
        let sanitized = sanitizer.sanitize(&internal_error);
        assert!(
            !sanitized.message.contains("Database"),
            "Internal error details are being exposed to clients"
        );
    }

    /// ENHANCED Test 3: Comprehensive rate limiting validation
    /// Tests IP limits, account limits, progressive delays, and concurrent access
    #[tokio::test]
    async fn test_comprehensive_rate_limiting() {
        let tester = RateLimitTester::new();

        // Test IP-based rate limiting
        tester
            .test_ip_rate_limiting()
            .await
            .expect("IP rate limiting failed");

        // Test account-based rate limiting
        tester
            .test_account_rate_limiting()
            .await
            .expect("Account rate limiting failed");

        // Test progressive delays
        tester
            .test_progressive_delays()
            .await
            .expect("Progressive delay system failed");

        // Test concurrent rate limiting
        tester
            .test_concurrent_rate_limiting()
            .await
            .expect("Concurrent rate limiting failed");
    }

    /// LEGACY Test 3: Original rate limiting test (kept for compatibility)
    #[tokio::test]
    async fn test_rate_limiting_enforcement_legacy() {
        let config = RateLimiterConfig {
            max_attempts_per_ip: 5,
            max_attempts_per_account: 3,
            window_duration: Duration::from_secs(60),
            progressive_delay_multiplier: 2.0,
            max_progressive_delay: Duration::from_secs(10),
            strict_mode: true,
            progressive_delay_threshold: 2,
        };

        let limiter = RateLimiter::with_config(config);

        // Test IP-based rate limiting
        let ip_key = crate::security::rate_limiter::rate_limit_key_from_ip(Some(
            "*************".to_string(),
        ));

        // First 5 attempts should succeed
        for i in 0..5 {
            let result = limiter.check_rate_limit(&ip_key).await;
            assert!(
                result.is_ok(),
                "IP rate limit triggered too early at attempt {}",
                i + 1
            );
        }

        // 6th attempt should fail
        let result = limiter.check_rate_limit(&ip_key).await;
        assert!(
            matches!(result, Err(ApplicationError::RateLimitExceeded)),
            "IP rate limiting not enforced after limit exceeded"
        );

        // Test account-based rate limiting
        limiter.clear_all().await;
        let account_key =
            crate::security::rate_limiter::rate_limit_key_from_account("<EMAIL>");

        // First 3 attempts should succeed
        for i in 0..3 {
            let result = limiter.check_rate_limit(&account_key).await;
            assert!(
                result.is_ok(),
                "Account rate limit triggered too early at attempt {}",
                i + 1
            );
        }

        // 4th attempt should fail
        let result = limiter.check_rate_limit(&account_key).await;
        assert!(
            matches!(result, Err(ApplicationError::RateLimitExceeded)),
            "Account rate limiting not enforced after limit exceeded"
        );

        // Test progressive delays
        limiter.clear_all().await;

        // Record failed attempts
        for _ in 0..3 {
            limiter.check_rate_limit(&account_key).await.ok();
            limiter.record_failed_attempt(&account_key).await;
        }

        // Check that delay increases
        let delay = limiter.get_current_delay(&account_key).await;
        assert!(
            delay > Duration::ZERO,
            "Progressive delays not being applied after failed attempts"
        );

        // Test that successful attempt resets delays
        limiter.record_successful_attempt(&account_key).await;
        let delay_after_success = limiter.get_current_delay(&account_key).await;
        assert_eq!(
            delay_after_success,
            Duration::ZERO,
            "Progressive delays not reset after successful attempt"
        );
    }

    /// ENHANCED Integration Test: Full security suite validation
    #[tokio::test]
    async fn test_comprehensive_security_integration() {
        SecurityIntegrationTester::test_full_security_integration()
            .await
            .expect("Comprehensive security integration failed");
    }

    /// ENHANCED Performance Test: Security performance benchmarks
    #[tokio::test]
    async fn test_security_performance_benchmarks() {
        let perf_tester = SecurityPerformanceTester;
        let rate_limiter = RateLimiter::new();
        let error_sanitizer = ErrorSanitizer::new();

        // Benchmark rate limiting performance
        let rate_limit_duration = perf_tester
            .benchmark_rate_limiting(&rate_limiter)
            .await
            .expect("Rate limiting performance test failed");

        println!("Rate limiting average duration: {:?}", rate_limit_duration);

        // Benchmark error sanitization performance
        let sanitization_duration = perf_tester
            .benchmark_error_sanitization(&error_sanitizer)
            .expect("Error sanitization performance test failed");

        println!(
            "Error sanitization average duration: {:?}",
            sanitization_duration
        );
    }

    /// ENHANCED Load Test: Security under concurrent load
    #[tokio::test]
    async fn test_security_under_load() {
        SecurityIntegrationTester::test_security_under_load()
            .await
            .expect("Security under load test failed");
    }

    /// ENHANCED Memory Safety Test: Sensitive data handling
    #[tokio::test]
    async fn test_memory_safety() {
        SecurityIntegrationTester::test_memory_safety()
            .await
            .expect("Memory safety test failed");
    }

    /// Complete Security Test Suite Runner
    #[tokio::test]
    async fn test_complete_security_suite() {
        match SecurityTestSuite::run_all_tests().await {
            Ok(_) => println!("All security tests passed successfully!"),
            Err(errors) => {
                eprintln!("Security test failures:");
                for error in errors {
                    eprintln!("  - {}", error);
                }
                panic!("Security test suite failed");
            }
        }
    }

    /// LEGACY Integration Test: Original test (kept for compatibility)
    #[tokio::test]
    async fn test_security_integration_legacy() {
        // Create handler with security features
        let user_repo = TestUserRepo::new();
        let session_repo = TestSessionRepo;
        let password_service = TestPasswordService;
        let token_service = TestTokenService;
        let rate_limiter = Arc::new(RateLimiter::new());

        let handler = AuthenticateUserHandler::with_security_config(
            user_repo,
            session_repo,
            password_service,
            token_service,
            rate_limiter.clone(),
        );

        // Test rate limiting in authentication flow
        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: Some("***********".to_string()),
            user_agent: Some("Test".to_string()),
        };

        // Make multiple attempts to trigger rate limiting
        for _ in 0..10 {
            let _ = handler.handle(command.clone()).await;
        }

        // Verify rate limiting is active
        let ip_key =
            crate::security::rate_limiter::rate_limit_key_from_ip(Some("***********".to_string()));
        let result = rate_limiter.check_rate_limit(&ip_key).await;

        // After 10 attempts, should be rate limited
        assert!(
            result.is_err(),
            "Rate limiting not enforced in authentication handler"
        );
    }

    /// LEGACY Test: Original email enumeration test (kept for compatibility)
    #[tokio::test]
    async fn test_email_enumeration_prevention_legacy() {
        let ct_service = ConstantTimeService::new();
        let auth_service = ConstantTimeAuthService::new(ct_service);

        // Create a valid user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("ValidPassword123!").unwrap();
        let user = User::new(email, password).unwrap();

        // Measure multiple authentication attempts
        let attempts = 50;
        let mut valid_email_timings = Vec::new();
        let mut invalid_email_timings = Vec::new();

        for _ in 0..attempts {
            // Valid email, wrong password
            let start = Instant::now();
            let _ = auth_service
                .validate_authentication_attempt("<EMAIL>", "WrongPass123!", Some(&user))
                .await;
            valid_email_timings.push(start.elapsed());

            // Invalid email
            let start = Instant::now();
            let _ = auth_service
                .validate_authentication_attempt("<EMAIL>", "WrongPass123!", None)
                .await;
            invalid_email_timings.push(start.elapsed());
        }

        // Calculate average timings
        let valid_avg = valid_email_timings
            .iter()
            .map(|d| d.as_micros())
            .sum::<u128>()
            / attempts as u128;

        let invalid_avg = invalid_email_timings
            .iter()
            .map(|d| d.as_micros())
            .sum::<u128>()
            / attempts as u128;

        // Timings should be very similar (within 10% difference)
        let diff_percent =
            ((valid_avg as f64 - invalid_avg as f64).abs() / valid_avg as f64) * 100.0;

        assert!(
            diff_percent < 10.0,
            "Email enumeration possible through timing analysis! Difference: {:.2}%",
            diff_percent
        );
    }
}
