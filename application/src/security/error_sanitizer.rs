// Error sanitization service to prevent information disclosure
// Ensures error messages don't leak sensitive system state information

use crate::errors::{ApplicationError, ErrorSeverity};
use std::collections::HashMap;

/// Sanitized error response safe for client consumption
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub struct SanitizedError {
    /// Generic error code for client
    pub code: &'static str,
    /// Safe, generic message for client
    pub message: &'static str,
    /// Whether the error is retryable
    pub retryable: bool,
    /// Optional retry delay in seconds
    pub retry_after: Option<u64>,
}

impl SanitizedError {
    /// Create a generic authentication error
    pub fn authentication_failed() -> Self {
        Self {
            code: "AUTH_FAILED",
            message: "Authentication failed",
            retryable: false,
            retry_after: None,
        }
    }

    /// Create a generic access denied error
    pub fn access_denied() -> Self {
        Self {
            code: "ACCESS_DENIED",
            message: "Access denied",
            retryable: false,
            retry_after: None,
        }
    }

    /// Create a generic invalid request error
    pub fn invalid_request() -> Self {
        Self {
            code: "INVALID_REQUEST",
            message: "Invalid request",
            retryable: false,
            retry_after: None,
        }
    }

    /// Create a rate limit error
    pub fn rate_limit_exceeded(retry_after_seconds: u64) -> Self {
        Self {
            code: "RATE_LIMIT",
            message: "Too many requests. Please try again later",
            retryable: true,
            retry_after: Some(retry_after_seconds),
        }
    }

    /// Create a generic server error
    pub fn server_error() -> Self {
        Self {
            code: "SERVER_ERROR",
            message: "An error occurred. Please try again later",
            retryable: true,
            retry_after: Some(5),
        }
    }
}

/// Service for sanitizing errors before sending to clients
pub struct ErrorSanitizer {
    /// Mapping of specific errors to sanitized versions
    #[allow(dead_code)] // Reserved for custom error mappings
    error_mappings: HashMap<String, SanitizedError>,
    /// Whether to include retry information
    include_retry_info: bool,
    /// Whether we're in development mode (more detailed errors)
    development_mode: bool,
}

impl ErrorSanitizer {
    /// Create a new error sanitizer for production
    pub fn new() -> Self {
        Self {
            error_mappings: Self::create_default_mappings(),
            include_retry_info: true,
            development_mode: false,
        }
    }

    /// Create a new error sanitizer for development (slightly more detailed)
    pub fn new_development() -> Self {
        Self {
            error_mappings: Self::create_development_mappings(),
            include_retry_info: true,
            development_mode: true,
        }
    }

    /// Sanitize an application error for client consumption
    pub fn sanitize(&self, error: &ApplicationError) -> SanitizedError {
        // First, check if we should log this error internally
        if error.should_log() {
            // In production, this would send to logging/monitoring
            self.log_internal_error(error);
        }

        // Map error to sanitized version based on type
        match error {
            // Authentication errors - never reveal if user exists
            ApplicationError::InvalidCredentials
            | ApplicationError::UserNotFound
            | ApplicationError::AccountLocked
            | ApplicationError::AccountNotVerified => SanitizedError::authentication_failed(),

            // Authorization errors - generic access denied
            ApplicationError::InsufficientPermissions
            | ApplicationError::RoleNotFound
            | ApplicationError::Forbidden => SanitizedError::access_denied(),

            // Not found errors - generic not found
            ApplicationError::NotFound(_) => SanitizedError {
                code: "NOT_FOUND",
                message: "Resource not found",
                retryable: false,
                retry_after: None,
            },

            // Rate limiting - include retry information
            ApplicationError::RateLimitExceeded => {
                SanitizedError::rate_limit_exceeded(60) // Retry after 60 seconds
            }

            // Security issues - very generic response
            ApplicationError::SuspiciousActivity | ApplicationError::SecurityLock => {
                if self.development_mode {
                    SanitizedError {
                        code: "SECURITY_CHECK",
                        message: "Security verification required",
                        retryable: false,
                        retry_after: None,
                    }
                } else {
                    SanitizedError::access_denied()
                }
            }

            // Input validation - slightly more specific
            ApplicationError::InvalidInput(_)
            | ApplicationError::MissingField(_)
            | ApplicationError::Validation(_) => SanitizedError::invalid_request(),

            // Email/username exists - these are okay to be specific
            ApplicationError::EmailAlreadyExists => SanitizedError {
                code: "EMAIL_EXISTS",
                message: "An account with this email already exists",
                retryable: false,
                retry_after: None,
            },
            ApplicationError::UsernameAlreadyExists => SanitizedError {
                code: "USERNAME_EXISTS",
                message: "This username is already taken",
                retryable: false,
                retry_after: None,
            },

            // MFA errors - can be specific
            ApplicationError::MfaRequired => SanitizedError {
                code: "MFA_REQUIRED",
                message: "Multi-factor authentication required",
                retryable: false,
                retry_after: None,
            },
            ApplicationError::InvalidMfaCode => SanitizedError {
                code: "INVALID_MFA",
                message: "Invalid authentication code",
                retryable: true,
                retry_after: None,
            },

            // Token/session errors
            ApplicationError::InvalidToken
            | ApplicationError::TokenExpired
            | ApplicationError::SessionExpired
            | ApplicationError::SessionNotFound => SanitizedError {
                code: "AUTH_EXPIRED",
                message: "Authentication expired. Please log in again",
                retryable: false,
                retry_after: None,
            },

            // Performance violations - include basic info in dev mode
            ApplicationError::PerformanceViolation { .. } => {
                if self.development_mode {
                    SanitizedError {
                        code: "PERFORMANCE_ISSUE",
                        message: "Request took too long to process",
                        retryable: true,
                        retry_after: Some(5),
                    }
                } else {
                    SanitizedError::server_error()
                }
            }

            // Infrastructure errors - generic server error
            ApplicationError::Repository(_)
            | ApplicationError::ExternalService(_)
            | ApplicationError::Configuration(_)
            | ApplicationError::TransactionFailed(_)
            | ApplicationError::EventPublishingFailed(_)
            | ApplicationError::Internal(_)
            | ApplicationError::Domain(_)
            | ApplicationError::BusinessRule(_) => SanitizedError::server_error(),
        }
    }

    /// Sanitize multiple errors (returns most severe)
    pub fn sanitize_multiple(&self, errors: &[ApplicationError]) -> SanitizedError {
        if errors.is_empty() {
            return SanitizedError::server_error();
        }

        // Find the most severe error
        let most_severe = errors
            .iter()
            .max_by_key(|e| match e.severity() {
                ErrorSeverity::Critical => 4,
                ErrorSeverity::High => 3,
                ErrorSeverity::Medium => 2,
                ErrorSeverity::Low => 1,
            })
            .unwrap();

        self.sanitize(most_severe)
    }

    /// Create a sanitized error with custom retry information
    pub fn sanitize_with_retry(
        &self,
        error: &ApplicationError,
        retry_after_seconds: u64,
    ) -> SanitizedError {
        let mut sanitized = self.sanitize(error);
        if self.include_retry_info && sanitized.retryable {
            sanitized.retry_after = Some(retry_after_seconds);
        }
        sanitized
    }

    /// Log internal error details (would go to monitoring in production)
    fn log_internal_error(&self, error: &ApplicationError) {
        // In production, this would send to logging infrastructure
        // For now, we'll just track that it should be logged
        match error.severity() {
            ErrorSeverity::Critical => {
                // Alert on-call
                eprintln!("CRITICAL ERROR: {error:?}");
            }
            ErrorSeverity::High => {
                // Log with high priority
                eprintln!("HIGH PRIORITY ERROR: {error:?}");
            }
            _ => {
                // Standard logging
                eprintln!("ERROR: {error:?}");
            }
        }
    }

    /// Create default error mappings for production
    fn create_default_mappings() -> HashMap<String, SanitizedError> {
        let mut mappings = HashMap::new();

        // Add any custom mappings here
        mappings.insert("special_case".to_string(), SanitizedError::server_error());

        mappings
    }

    /// Create development error mappings (slightly more detailed)
    fn create_development_mappings() -> HashMap<String, SanitizedError> {
        let mut mappings = HashMap::new();

        // In development, we can be slightly more specific
        mappings.insert(
            "database_connection".to_string(),
            SanitizedError {
                code: "DB_ERROR",
                message: "Database connection error",
                retryable: true,
                retry_after: Some(5),
            },
        );

        mappings
    }
}

impl Default for ErrorSanitizer {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_authentication_errors() {
        let sanitizer = ErrorSanitizer::new();

        // All auth errors should return the same generic message
        let errors = vec![
            ApplicationError::InvalidCredentials,
            ApplicationError::UserNotFound,
            ApplicationError::AccountLocked,
            ApplicationError::AccountNotVerified,
        ];

        for error in errors {
            let sanitized = sanitizer.sanitize(&error);
            assert_eq!(sanitized, SanitizedError::authentication_failed());
        }
    }

    #[test]
    fn test_sanitize_authorization_errors() {
        let sanitizer = ErrorSanitizer::new();

        // Authorization errors should be generic
        let errors = vec![
            ApplicationError::InsufficientPermissions,
            ApplicationError::RoleNotFound,
        ];

        for error in errors {
            let sanitized = sanitizer.sanitize(&error);
            assert_eq!(sanitized, SanitizedError::access_denied());
        }
    }

    #[test]
    fn test_sanitize_rate_limit_error() {
        let sanitizer = ErrorSanitizer::new();
        let error = ApplicationError::RateLimitExceeded;

        let sanitized = sanitizer.sanitize(&error);
        assert_eq!(sanitized.code, "RATE_LIMIT");
        assert!(sanitized.retryable);
        assert!(sanitized.retry_after.is_some());
    }

    #[test]
    fn test_sanitize_with_custom_retry() {
        let sanitizer = ErrorSanitizer::new();
        let error = ApplicationError::RateLimitExceeded;

        let sanitized = sanitizer.sanitize_with_retry(&error, 120);
        assert_eq!(sanitized.retry_after, Some(120));
    }

    #[test]
    fn test_sanitize_infrastructure_errors() {
        let sanitizer = ErrorSanitizer::new();

        let errors = vec![
            ApplicationError::Repository("DB error".to_string()),
            ApplicationError::ExternalService("API error".to_string()),
            ApplicationError::Internal("Internal error".to_string()),
        ];

        for error in errors {
            let sanitized = sanitizer.sanitize(&error);
            assert_eq!(sanitized, SanitizedError::server_error());
        }
    }

    #[test]
    fn test_sanitize_specific_errors() {
        let sanitizer = ErrorSanitizer::new();

        // These errors can be specific
        let error = ApplicationError::EmailAlreadyExists;
        let sanitized = sanitizer.sanitize(&error);
        assert_eq!(sanitized.code, "EMAIL_EXISTS");

        let error = ApplicationError::MfaRequired;
        let sanitized = sanitizer.sanitize(&error);
        assert_eq!(sanitized.code, "MFA_REQUIRED");
    }

    #[test]
    fn test_development_mode_differences() {
        let prod_sanitizer = ErrorSanitizer::new();
        let dev_sanitizer = ErrorSanitizer::new_development();

        let error = ApplicationError::SuspiciousActivity;

        let prod_sanitized = prod_sanitizer.sanitize(&error);
        assert_eq!(prod_sanitized, SanitizedError::access_denied());

        let dev_sanitized = dev_sanitizer.sanitize(&error);
        assert_eq!(dev_sanitized.code, "SECURITY_CHECK");
    }

    #[test]
    fn test_sanitize_multiple_errors() {
        let sanitizer = ErrorSanitizer::new();

        let errors = vec![
            ApplicationError::InvalidInput("test".to_string()),
            ApplicationError::Internal("critical".to_string()),
            ApplicationError::RateLimitExceeded,
        ];

        // Should return the most severe (Internal is Critical severity)
        let sanitized = sanitizer.sanitize_multiple(&errors);
        assert_eq!(sanitized, SanitizedError::server_error());
    }

    #[test]
    fn test_no_information_leakage() {
        let sanitizer = ErrorSanitizer::new();

        // Ensure different internal errors produce same external error
        let user_not_found = ApplicationError::UserNotFound;
        let invalid_creds = ApplicationError::InvalidCredentials;

        let sanitized1 = sanitizer.sanitize(&user_not_found);
        let sanitized2 = sanitizer.sanitize(&invalid_creds);

        // Both should return identical responses
        assert_eq!(sanitized1, sanitized2);
    }
}
