// Permission sanitizer to prevent information disclosure through role hierarchy
// Implements visibility controls and role name sanitization
// OWASP A01 - Broken Access Control prevention

use std::collections::{HashMap, HashSet};

/// Configuration for permission sanitization
#[derive(Debug, <PERSON>lone)]
pub struct PermissionSanitizerConfig {
    /// Whether to hide source role names for inherited permissions
    pub hide_inherited_sources: bool,

    /// Whether to obfuscate internal role IDs
    pub obfuscate_role_ids: bool,

    /// Maximum hierarchy depth to reveal
    pub max_hierarchy_depth: usize,

    /// Roles that should never be revealed in hierarchy
    pub hidden_roles: HashSet<String>,

    /// Mapping of internal role names to public display names
    pub role_display_names: HashMap<String, String>,

    /// Whether to aggregate permissions instead of showing sources
    pub aggregate_permissions: bool,
}

impl Default for PermissionSanitizerConfig {
    fn default() -> Self {
        let mut hidden_roles = HashSet::new();
        hidden_roles.insert("system".to_string());
        hidden_roles.insert("internal_admin".to_string());
        hidden_roles.insert("service_account".to_string());
        hidden_roles.insert("debug".to_string());

        let mut role_display_names = HashMap::new();
        role_display_names.insert("admin".to_string(), "Administrator".to_string());
        role_display_names.insert("compliance".to_string(), "Compliance Officer".to_string());
        role_display_names.insert("moderator".to_string(), "Moderator".to_string());
        role_display_names.insert("user".to_string(), "Standard User".to_string());

        Self {
            hide_inherited_sources: true,
            obfuscate_role_ids: true,
            max_hierarchy_depth: 3,
            hidden_roles,
            role_display_names,
            aggregate_permissions: true,
        }
    }
}

/// Permission information with sanitization applied
#[derive(Debug, Clone, PartialEq)]
pub struct SanitizedPermission {
    /// The permission name
    pub name: String,

    /// Source of the permission (sanitized)
    pub source: PermissionSource,

    /// Whether this is a sensitive permission
    pub is_sensitive: bool,
}

/// Source of a permission after sanitization
#[derive(Debug, Clone, PartialEq)]
pub enum PermissionSource {
    /// Permission is directly assigned
    Direct,

    /// Permission is inherited (source hidden)
    Inherited,

    /// Permission is inherited from a specific role (if allowed)
    InheritedFrom(String),

    /// Permission is from multiple sources
    Multiple,
}

/// Sanitized role hierarchy information
#[derive(Debug, Clone)]
pub struct SanitizedRoleHierarchy {
    /// Current role information
    pub current_role: SanitizedRoleInfo,

    /// Parent roles (limited depth)
    pub parent_chain: Vec<SanitizedRoleInfo>,

    /// Child roles (if allowed)
    pub child_roles: Vec<SanitizedRoleInfo>,

    /// Total hierarchy depth (may be truncated)
    pub visible_depth: usize,

    /// Whether hierarchy was truncated
    pub is_truncated: bool,
}

/// Sanitized role information
#[derive(Debug, Clone)]
pub struct SanitizedRoleInfo {
    /// Public role identifier (may be obfuscated)
    pub id: String,

    /// Display name for the role
    pub display_name: String,

    /// Level in hierarchy (0 = current role)
    pub level: usize,

    /// Whether this role is active
    pub is_active: bool,
}

/// Permission sanitizer service
pub struct PermissionSanitizer {
    config: PermissionSanitizerConfig,
    sensitive_permissions: HashSet<String>,
}

impl PermissionSanitizer {
    /// Create new permission sanitizer with default config
    pub fn new() -> Self {
        Self::with_config(PermissionSanitizerConfig::default())
    }

    /// Create new permission sanitizer with custom config
    pub fn with_config(config: PermissionSanitizerConfig) -> Self {
        let mut sensitive_permissions = HashSet::new();

        // Mark security-sensitive permissions
        sensitive_permissions.insert("admin:*".to_string());
        sensitive_permissions.insert("system:*".to_string());
        sensitive_permissions.insert("security:*".to_string());
        sensitive_permissions.insert("audit:write".to_string());
        sensitive_permissions.insert("users:delete".to_string());
        sensitive_permissions.insert("roles:delete".to_string());
        sensitive_permissions.insert("config:write".to_string());

        Self {
            config,
            sensitive_permissions,
        }
    }

    /// Sanitize permission list based on viewer's context
    pub fn sanitize_permissions(
        &self,
        permissions: Vec<(String, String)>, // (permission_name, source_role)
        viewer_role: &str,
        is_admin_viewer: bool,
    ) -> Vec<SanitizedPermission> {
        let mut sanitized = Vec::new();
        let mut permission_sources: HashMap<String, Vec<String>> = HashMap::new();

        // Group permissions by name to detect multiple sources
        for (perm_name, source_role) in permissions {
            permission_sources
                .entry(perm_name)
                .or_default()
                .push(source_role);
        }

        // Process each unique permission
        for (perm_name, sources) in permission_sources {
            let is_sensitive = self.is_sensitive_permission(&perm_name);

            // Determine permission source based on config and context
            let source = if sources.len() > 1 {
                PermissionSource::Multiple
            } else if sources[0] == "direct" {
                PermissionSource::Direct
            } else if self.config.hide_inherited_sources && !is_admin_viewer {
                PermissionSource::Inherited
            } else {
                // Sanitize the source role name
                let sanitized_source = self.sanitize_role_name(&sources[0], viewer_role);
                if sanitized_source == "hidden" {
                    PermissionSource::Inherited
                } else {
                    PermissionSource::InheritedFrom(sanitized_source)
                }
            };

            // Hide sensitive permissions from non-admin viewers
            if is_sensitive && !is_admin_viewer {
                continue;
            }

            sanitized.push(SanitizedPermission {
                name: perm_name,
                source,
                is_sensitive,
            });
        }

        // Sort permissions for consistent output
        sanitized.sort_by(|a, b| a.name.cmp(&b.name));

        sanitized
    }

    /// Sanitize role hierarchy information
    pub fn sanitize_role_hierarchy(
        &self,
        current_role: &str,
        parent_chain: Vec<(String, String, usize)>, // (role_id, role_name, level)
        child_roles: Vec<(String, String)>,         // (role_id, role_name)
        viewer_role: &str,
        is_admin_viewer: bool,
    ) -> SanitizedRoleHierarchy {
        // Sanitize current role
        let current_info = SanitizedRoleInfo {
            id: self.obfuscate_role_id(current_role),
            display_name: self.get_display_name(current_role),
            level: 0,
            is_active: true,
        };

        // Sanitize parent chain with depth limit
        let mut sanitized_parents = Vec::new();
        let mut is_truncated = false;

        for (role_id, role_name, level) in parent_chain {
            if level > self.config.max_hierarchy_depth {
                is_truncated = true;
                break;
            }

            if self.config.hidden_roles.contains(&role_name) && !is_admin_viewer {
                continue;
            }

            sanitized_parents.push(SanitizedRoleInfo {
                id: self.obfuscate_role_id(&role_id),
                display_name: self.get_display_name(&role_name),
                level,
                is_active: true,
            });
        }

        // Sanitize child roles (only show to admins or role members)
        let mut sanitized_children = Vec::new();
        if is_admin_viewer || viewer_role == current_role {
            for (role_id, role_name) in child_roles {
                if self.config.hidden_roles.contains(&role_name) && !is_admin_viewer {
                    continue;
                }

                sanitized_children.push(SanitizedRoleInfo {
                    id: self.obfuscate_role_id(&role_id),
                    display_name: self.get_display_name(&role_name),
                    level: 1,
                    is_active: true,
                });
            }
        }

        let visible_depth = sanitized_parents.len();

        SanitizedRoleHierarchy {
            current_role: current_info,
            parent_chain: sanitized_parents,
            child_roles: sanitized_children,
            visible_depth,
            is_truncated,
        }
    }

    /// Check if a permission is sensitive
    fn is_sensitive_permission(&self, permission: &str) -> bool {
        // Check exact match
        if self.sensitive_permissions.contains(permission) {
            return true;
        }

        // Check wildcard patterns
        for sensitive in &self.sensitive_permissions {
            if sensitive.ends_with("*") {
                let prefix = &sensitive[..sensitive.len() - 1];
                if permission.starts_with(prefix) {
                    return true;
                }
            }
        }

        false
    }

    /// Sanitize a role name based on viewer context
    fn sanitize_role_name(&self, role_name: &str, _viewer_role: &str) -> String {
        if self.config.hidden_roles.contains(role_name) {
            return "hidden".to_string();
        }

        self.get_display_name(role_name)
    }

    /// Get display name for a role
    fn get_display_name(&self, role_name: &str) -> String {
        self.config
            .role_display_names
            .get(role_name)
            .cloned()
            .unwrap_or_else(|| {
                // Capitalize first letter as fallback
                let mut chars = role_name.chars();
                match chars.next() {
                    None => String::new(),
                    Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
                }
            })
    }

    /// Obfuscate role ID if configured
    fn obfuscate_role_id(&self, role_id: &str) -> String {
        if !self.config.obfuscate_role_ids {
            return role_id.to_string();
        }

        // For built-in roles, use a consistent hash
        if role_id.starts_with("role_builtin_") {
            let role_name = role_id.strip_prefix("role_builtin_").unwrap_or(role_id);
            return format!("role_{}", self.simple_hash(role_name));
        }

        // For UUIDs, truncate to first 8 characters
        if role_id.len() > 16 {
            return role_id.chars().take(8).collect();
        }

        role_id.to_string()
    }

    /// Simple hash function for consistent obfuscation
    fn simple_hash(&self, input: &str) -> String {
        let mut hash = 0u32;
        for byte in input.bytes() {
            hash = hash.wrapping_mul(31).wrapping_add(byte as u32);
        }
        format!("{hash:08x}")
    }
}

impl Default for PermissionSanitizer {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_sanitization_hides_sources() {
        let sanitizer = PermissionSanitizer::new();

        let permissions = vec![
            ("read".to_string(), "direct".to_string()),
            ("write".to_string(), "admin".to_string()),
            ("delete".to_string(), "moderator".to_string()),
        ];

        let sanitized = sanitizer.sanitize_permissions(permissions, "user", false);

        assert_eq!(sanitized.len(), 3);

        // Permissions are sorted alphabetically: delete, read, write
        let delete_perm = sanitized.iter().find(|p| p.name == "delete").unwrap();
        let read_perm = sanitized.iter().find(|p| p.name == "read").unwrap();
        let write_perm = sanitized.iter().find(|p| p.name == "write").unwrap();

        assert_eq!(read_perm.source, PermissionSource::Direct);
        assert_eq!(write_perm.source, PermissionSource::Inherited); // Hidden
        assert_eq!(delete_perm.source, PermissionSource::Inherited); // Hidden
    }

    #[test]
    fn test_admin_viewer_sees_sources() {
        let sanitizer = PermissionSanitizer::new();

        let permissions = vec![
            ("read".to_string(), "direct".to_string()),
            ("write".to_string(), "admin".to_string()),
        ];

        let sanitized = sanitizer.sanitize_permissions(permissions, "admin", true);

        assert_eq!(sanitized.len(), 2);
        assert_eq!(sanitized[0].source, PermissionSource::Direct);
        match &sanitized[1].source {
            PermissionSource::InheritedFrom(role) => assert_eq!(role, "Administrator"),
            _ => panic!("Expected InheritedFrom for admin viewer"),
        }
    }

    #[test]
    fn test_sensitive_permissions_hidden_from_non_admins() {
        let sanitizer = PermissionSanitizer::new();

        let permissions = vec![
            ("read".to_string(), "direct".to_string()),
            ("admin:users:delete".to_string(), "admin".to_string()),
            ("system:config".to_string(), "system".to_string()),
        ];

        let sanitized = sanitizer.sanitize_permissions(permissions, "user", false);

        // Only non-sensitive permission should be visible
        assert_eq!(sanitized.len(), 1);
        assert_eq!(sanitized[0].name, "read");
    }

    #[test]
    fn test_role_hierarchy_depth_limit() {
        let sanitizer = PermissionSanitizer::new();

        let parent_chain = vec![
            ("role1".to_string(), "parent1".to_string(), 1),
            ("role2".to_string(), "parent2".to_string(), 2),
            ("role3".to_string(), "parent3".to_string(), 3),
            ("role4".to_string(), "parent4".to_string(), 4), // Beyond default limit
            ("role5".to_string(), "parent5".to_string(), 5),
        ];

        let hierarchy =
            sanitizer.sanitize_role_hierarchy("current", parent_chain, vec![], "user", false);

        assert_eq!(hierarchy.parent_chain.len(), 3); // Limited to max_hierarchy_depth
        assert!(hierarchy.is_truncated);
    }

    #[test]
    fn test_hidden_roles_filtered() {
        let sanitizer = PermissionSanitizer::new();

        let parent_chain = vec![
            ("role1".to_string(), "admin".to_string(), 1),
            ("role2".to_string(), "system".to_string(), 2), // Should be hidden
            ("role3".to_string(), "user".to_string(), 3),
        ];

        let hierarchy =
            sanitizer.sanitize_role_hierarchy("current", parent_chain, vec![], "user", false);

        assert_eq!(hierarchy.parent_chain.len(), 2); // system role hidden
        assert!(
            !hierarchy
                .parent_chain
                .iter()
                .any(|r| r.display_name == "system")
        );
    }

    #[test]
    fn test_role_id_obfuscation() {
        let config = PermissionSanitizerConfig {
            obfuscate_role_ids: true,
            ..Default::default()
        };

        let sanitizer = PermissionSanitizer::with_config(config);

        let hierarchy =
            sanitizer.sanitize_role_hierarchy("role_builtin_admin", vec![], vec![], "admin", true);

        // Role ID should be obfuscated
        assert_ne!(hierarchy.current_role.id, "role_builtin_admin");
        assert!(hierarchy.current_role.id.starts_with("role_"));
    }

    #[test]
    fn test_multiple_permission_sources() {
        let sanitizer = PermissionSanitizer::new();

        let permissions = vec![
            ("read".to_string(), "direct".to_string()),
            ("read".to_string(), "admin".to_string()), // Same permission, different source
            ("write".to_string(), "moderator".to_string()),
        ];

        let sanitized = sanitizer.sanitize_permissions(permissions, "user", false);

        // Should detect multiple sources for "read"
        let read_perm = sanitized.iter().find(|p| p.name == "read").unwrap();
        assert_eq!(read_perm.source, PermissionSource::Multiple);
    }

    #[test]
    fn test_child_roles_visibility() {
        let sanitizer = PermissionSanitizer::new();

        let child_roles = vec![
            ("child1".to_string(), "child_role1".to_string()),
            ("child2".to_string(), "child_role2".to_string()),
        ];

        // Non-admin, non-member shouldn't see children
        let hierarchy = sanitizer.sanitize_role_hierarchy(
            "current",
            vec![],
            child_roles.clone(),
            "other_user",
            false,
        );
        assert_eq!(hierarchy.child_roles.len(), 0);

        // Admin should see children
        let hierarchy = sanitizer.sanitize_role_hierarchy(
            "current",
            vec![],
            child_roles.clone(),
            "admin",
            true,
        );
        assert_eq!(hierarchy.child_roles.len(), 2);

        // Role member should see children
        let hierarchy =
            sanitizer.sanitize_role_hierarchy("current", vec![], child_roles, "current", false);
        assert_eq!(hierarchy.child_roles.len(), 2);
    }
}
