// Adaptive rate limiting implementation with role-based and complexity-aware controls
// Implements granular rate limiting for different user types and query complexity
// OWASP A04 - Insecure Design prevention

use crate::errors::ApplicationError;
use auth_domain::value_objects::UserId;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// Configuration for adaptive rate limiting based on user role
#[derive(Debug, Clone)]
pub struct AdaptiveRateLimiterConfig {
    /// Base rate limits per role type
    pub role_limits: HashMap<String, RoleLimitConfig>,

    /// Default limits for unknown roles
    pub default_limits: RoleLimitConfig,

    /// Query complexity multipliers
    pub complexity_multipliers: ComplexityMultipliers,

    /// Time window for rate limiting
    pub window_duration: Duration,

    /// Whether to enable adaptive adjustments
    pub enable_adaptive_adjustments: bool,

    /// Maximum burst allowance percentage
    pub burst_allowance: f64,

    /// Suspicious behavior threshold
    pub suspicious_threshold: u32,
}

/// Rate limit configuration for a specific role
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct RoleLimitConfig {
    /// Base requests per window
    pub base_limit: u32,

    /// Maximum requests after all multipliers
    pub max_limit: u32,

    /// Priority level (higher = more lenient)
    pub priority: u8,

    /// Whether this role can exceed limits temporarily
    pub allow_burst: bool,

    /// Progressive delay configuration
    pub progressive_delay: ProgressiveDelayConfig,
}

/// Progressive delay configuration
#[derive(Debug, Clone)]
pub struct ProgressiveDelayConfig {
    /// Number of requests before delays start
    pub threshold: u32,

    /// Delay multiplier per request over threshold
    pub multiplier: f64,

    /// Maximum delay in milliseconds
    pub max_delay_ms: u64,
}

/// Query complexity multipliers for different operations
#[derive(Debug, Clone)]
pub struct ComplexityMultipliers {
    /// Simple read operations (e.g., get single entity)
    pub simple_read: f64,

    /// Complex search operations
    pub complex_search: f64,

    /// Aggregation operations
    pub aggregation: f64,

    /// Audit/compliance queries
    pub audit_query: f64,

    /// Administrative operations
    pub admin_operation: f64,
}

impl Default for AdaptiveRateLimiterConfig {
    fn default() -> Self {
        let mut role_limits = HashMap::new();

        // Admin users - higher limits
        role_limits.insert(
            "admin".to_string(),
            RoleLimitConfig {
                base_limit: 100,
                max_limit: 200,
                priority: 10,
                allow_burst: true,
                progressive_delay: ProgressiveDelayConfig {
                    threshold: 80,
                    multiplier: 1.5,
                    max_delay_ms: 5000,
                },
            },
        );

        // Compliance officers - moderate limits for audit queries
        role_limits.insert(
            "compliance".to_string(),
            RoleLimitConfig {
                base_limit: 50,
                max_limit: 100,
                priority: 8,
                allow_burst: true,
                progressive_delay: ProgressiveDelayConfig {
                    threshold: 40,
                    multiplier: 2.0,
                    max_delay_ms: 10000,
                },
            },
        );

        // Regular users - standard limits
        role_limits.insert(
            "user".to_string(),
            RoleLimitConfig {
                base_limit: 20,
                max_limit: 40,
                priority: 5,
                allow_burst: false,
                progressive_delay: ProgressiveDelayConfig {
                    threshold: 15,
                    multiplier: 2.5,
                    max_delay_ms: 15000,
                },
            },
        );

        // Service accounts - high limits for automation
        role_limits.insert(
            "service".to_string(),
            RoleLimitConfig {
                base_limit: 200,
                max_limit: 500,
                priority: 9,
                allow_burst: true,
                progressive_delay: ProgressiveDelayConfig {
                    threshold: 180,
                    multiplier: 1.2,
                    max_delay_ms: 3000,
                },
            },
        );

        Self {
            role_limits,
            default_limits: RoleLimitConfig {
                base_limit: 10,
                max_limit: 20,
                priority: 1,
                allow_burst: false,
                progressive_delay: ProgressiveDelayConfig {
                    threshold: 5,
                    multiplier: 3.0,
                    max_delay_ms: 20000,
                },
            },
            complexity_multipliers: ComplexityMultipliers {
                simple_read: 1.0,
                complex_search: 2.0,
                aggregation: 3.0,
                audit_query: 2.5,
                admin_operation: 1.5,
            },
            window_duration: Duration::from_secs(60), // 1 minute window
            enable_adaptive_adjustments: true,
            burst_allowance: 1.5, // 50% burst allowance
            suspicious_threshold: 100,
        }
    }
}

/// Query complexity level for rate limiting
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum QueryComplexity {
    Simple,
    Moderate,
    Complex,
    Heavy,
}

impl QueryComplexity {
    /// Calculate complexity score
    pub fn score(&self) -> f64 {
        match self {
            QueryComplexity::Simple => 1.0,
            QueryComplexity::Moderate => 2.0,
            QueryComplexity::Complex => 3.0,
            QueryComplexity::Heavy => 5.0,
        }
    }

    /// Determine complexity from query characteristics
    pub fn from_query_params(
        has_joins: bool,
        has_aggregation: bool,
        result_size_estimate: usize,
        is_audit: bool,
    ) -> Self {
        if is_audit || has_aggregation {
            QueryComplexity::Heavy
        } else if has_joins || result_size_estimate > 100 {
            QueryComplexity::Complex
        } else if result_size_estimate > 20 {
            QueryComplexity::Moderate
        } else {
            QueryComplexity::Simple
        }
    }
}

/// Rate limit entry with adaptive tracking
#[derive(Debug, Clone)]
struct AdaptiveRateLimitEntry {
    /// User identifier
    #[allow(dead_code)]
    user_id: UserId,

    /// User's primary role
    #[allow(dead_code)]
    primary_role: String,

    /// Request count in current window
    request_count: u32,

    /// Weighted request count (considering complexity)
    weighted_count: f64,

    /// Window start time
    window_start: Instant,

    /// Last request time
    last_request: Instant,

    /// Failed request count
    failed_requests: u32,

    /// Suspicious activity score
    suspicious_score: u32,

    /// Current delay penalty in milliseconds
    delay_penalty_ms: u64,

    /// Whether burst mode is active
    burst_active: bool,

    /// Query complexity history
    complexity_history: Vec<QueryComplexity>,
}

impl AdaptiveRateLimitEntry {
    fn new(user_id: UserId, primary_role: String) -> Self {
        let now = Instant::now();
        Self {
            user_id,
            primary_role,
            request_count: 0,
            weighted_count: 0.0,
            window_start: now,
            last_request: now,
            failed_requests: 0,
            suspicious_score: 0,
            delay_penalty_ms: 0,
            burst_active: false,
            complexity_history: Vec::with_capacity(10),
        }
    }

    fn is_window_expired(&self, window_duration: Duration) -> bool {
        self.window_start.elapsed() > window_duration
    }

    fn reset_window(&mut self) {
        self.window_start = Instant::now();
        self.request_count = 0;
        self.weighted_count = 0.0;
        self.failed_requests = 0;
        self.complexity_history.clear();
        self.burst_active = false;
        // Keep suspicious score across windows for tracking
    }

    fn add_request(&mut self, complexity: QueryComplexity) {
        self.request_count += 1;
        self.weighted_count += complexity.score();
        self.last_request = Instant::now();

        // Track complexity history
        if self.complexity_history.len() >= 10 {
            self.complexity_history.remove(0);
        }
        self.complexity_history.push(complexity);
    }

    fn calculate_effective_limit(&self, config: &RoleLimitConfig, burst_allowance: f64) -> f64 {
        let base = config.base_limit as f64;

        if self.burst_active && config.allow_burst {
            (base * burst_allowance).min(config.max_limit as f64)
        } else {
            base
        }
    }

    fn should_apply_delay(&self, config: &RoleLimitConfig) -> bool {
        self.request_count > config.progressive_delay.threshold
    }

    fn calculate_delay(&self, config: &RoleLimitConfig) -> Duration {
        if !self.should_apply_delay(config) {
            return Duration::ZERO;
        }

        let over_threshold = self.request_count - config.progressive_delay.threshold;
        let delay_ms = (config
            .progressive_delay
            .multiplier
            .powi(over_threshold as i32)
            * 100.0)
            .min(config.progressive_delay.max_delay_ms as f64) as u64;

        Duration::from_millis(delay_ms + self.delay_penalty_ms)
    }

    fn is_suspicious(&self, threshold: u32) -> bool {
        self.suspicious_score >= threshold || self.failed_requests > 10
    }

    fn update_suspicious_score(&mut self, failed: bool) {
        if failed {
            self.suspicious_score += 5;
            self.failed_requests += 1;
        } else if self.suspicious_score > 0 {
            // Slowly decay suspicious score on successful requests
            self.suspicious_score = self.suspicious_score.saturating_sub(1);
        }
    }
}

/// Adaptive rate limiter with role-based and complexity-aware controls
pub struct AdaptiveRateLimiter {
    config: AdaptiveRateLimiterConfig,
    entries: Arc<RwLock<HashMap<UserId, AdaptiveRateLimitEntry>>>,
    /// Global metrics for adaptive adjustments
    global_metrics: Arc<RwLock<GlobalMetrics>>,
}

#[derive(Debug, Default)]
struct GlobalMetrics {
    total_requests: u64,
    total_denials: u64,
    average_complexity: f64,
    peak_load_active: bool,
}

impl Default for AdaptiveRateLimiter {
    fn default() -> Self {
        Self::new()
    }
}

impl AdaptiveRateLimiter {
    /// Create new adaptive rate limiter with default config
    pub fn new() -> Self {
        Self::with_config(AdaptiveRateLimiterConfig::default())
    }

    /// Create new adaptive rate limiter with custom config
    pub fn with_config(config: AdaptiveRateLimiterConfig) -> Self {
        Self {
            config,
            entries: Arc::new(RwLock::new(HashMap::new())),
            global_metrics: Arc::new(RwLock::new(GlobalMetrics::default())),
        }
    }

    /// Check if request should be allowed with adaptive controls
    pub async fn check_adaptive_limit(
        &self,
        user_id: &UserId,
        user_role: &str,
        query_complexity: QueryComplexity,
    ) -> Result<(), ApplicationError> {
        let mut entries = self.entries.write().await;
        let mut metrics = self.global_metrics.write().await;

        // Clean up old entries periodically
        if entries.len() > 1000 {
            self.cleanup_expired_entries(&mut entries).await;
        }

        // Get or create entry for user
        let entry = entries
            .entry(user_id.clone())
            .or_insert_with(|| AdaptiveRateLimitEntry::new(user_id.clone(), user_role.to_string()));

        // Reset window if expired
        if entry.is_window_expired(self.config.window_duration) {
            entry.reset_window();
        }

        // Get role-specific config
        let role_config = self
            .config
            .role_limits
            .get(user_role)
            .unwrap_or(&self.config.default_limits);

        // Calculate effective limit based on role and burst status
        let effective_limit =
            entry.calculate_effective_limit(role_config, self.config.burst_allowance);

        // Check if user is suspicious
        if entry.is_suspicious(self.config.suspicious_threshold) {
            entry.delay_penalty_ms = 5000; // Add 5 second penalty for suspicious users
            return Err(ApplicationError::RateLimitExceeded);
        }

        // Check weighted count against limit
        let complexity_weight = self.get_complexity_multiplier(query_complexity);
        let projected_weighted = entry.weighted_count + complexity_weight;

        if projected_weighted > effective_limit {
            // Check if we can enable burst mode
            if !entry.burst_active && role_config.allow_burst && entry.failed_requests == 0 {
                entry.burst_active = true;
                // Recheck with burst allowance
                let burst_limit =
                    entry.calculate_effective_limit(role_config, self.config.burst_allowance);
                if projected_weighted > burst_limit {
                    metrics.total_denials += 1;
                    return Err(ApplicationError::RateLimitExceeded);
                }
            } else {
                metrics.total_denials += 1;
                return Err(ApplicationError::RateLimitExceeded);
            }
        }

        // Apply progressive delay if needed
        let delay = entry.calculate_delay(role_config);
        if delay > Duration::ZERO {
            // In production, this would actually delay the request
            // For now, we just track it
            tracing::warn!(
                "Applying {:?} delay for user {} (role: {})",
                delay,
                user_id.as_str(),
                user_role
            );
        }

        // Update entry
        entry.add_request(query_complexity);

        // Update global metrics
        metrics.total_requests += 1;
        metrics.average_complexity = (metrics.average_complexity
            * (metrics.total_requests - 1) as f64
            + query_complexity.score())
            / metrics.total_requests as f64;

        // Check for peak load
        if metrics.total_requests > 1000 && metrics.average_complexity > 3.0 {
            metrics.peak_load_active = true;
        }

        Ok(())
    }

    /// Record a failed request for suspicious activity tracking
    pub async fn record_failure(&self, user_id: &UserId) {
        let mut entries = self.entries.write().await;

        if let Some(entry) = entries.get_mut(user_id) {
            entry.update_suspicious_score(true);
        }
    }

    /// Record a successful request to reduce suspicious score
    pub async fn record_success(&self, user_id: &UserId) {
        let mut entries = self.entries.write().await;

        if let Some(entry) = entries.get_mut(user_id) {
            entry.update_suspicious_score(false);
        }
    }

    /// Get current user metrics for monitoring
    pub async fn get_user_metrics(&self, user_id: &UserId) -> Option<UserMetrics> {
        let entries = self.entries.read().await;

        entries.get(user_id).map(|entry| UserMetrics {
            request_count: entry.request_count,
            weighted_count: entry.weighted_count,
            failed_requests: entry.failed_requests,
            suspicious_score: entry.suspicious_score,
            burst_active: entry.burst_active,
            average_complexity: if entry.complexity_history.is_empty() {
                0.0
            } else {
                entry
                    .complexity_history
                    .iter()
                    .map(|c| c.score())
                    .sum::<f64>()
                    / entry.complexity_history.len() as f64
            },
        })
    }

    /// Get global metrics for monitoring
    pub async fn get_global_metrics(&self) -> GlobalMetricsSnapshot {
        let metrics = self.global_metrics.read().await;

        GlobalMetricsSnapshot {
            total_requests: metrics.total_requests,
            total_denials: metrics.total_denials,
            average_complexity: metrics.average_complexity,
            peak_load_active: metrics.peak_load_active,
        }
    }

    fn get_complexity_multiplier(&self, complexity: QueryComplexity) -> f64 {
        match complexity {
            QueryComplexity::Simple => self.config.complexity_multipliers.simple_read,
            QueryComplexity::Moderate => self.config.complexity_multipliers.complex_search,
            QueryComplexity::Complex => self.config.complexity_multipliers.aggregation,
            QueryComplexity::Heavy => self.config.complexity_multipliers.audit_query,
        }
    }

    async fn cleanup_expired_entries(&self, entries: &mut HashMap<UserId, AdaptiveRateLimitEntry>) {
        let window = self.config.window_duration * 2; // Keep entries for 2 windows
        entries.retain(|_, entry| !entry.is_window_expired(window));
    }

    /// Clear all entries (for testing)
    #[cfg(test)]
    pub async fn clear_all(&self) {
        let mut entries = self.entries.write().await;
        let mut metrics = self.global_metrics.write().await;
        entries.clear();
        *metrics = GlobalMetrics::default();
    }
}

/// User metrics for monitoring
#[derive(Debug, Clone)]
pub struct UserMetrics {
    pub request_count: u32,
    pub weighted_count: f64,
    pub failed_requests: u32,
    pub suspicious_score: u32,
    pub burst_active: bool,
    pub average_complexity: f64,
}

/// Global metrics snapshot
#[derive(Debug, Clone)]
pub struct GlobalMetricsSnapshot {
    pub total_requests: u64,
    pub total_denials: u64,
    pub average_complexity: f64,
    pub peak_load_active: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_adaptive_rate_limiting_by_role() {
        let limiter = AdaptiveRateLimiter::new();
        let admin_id = UserId::from_string("admin-123".to_string()).unwrap();
        let user_id = UserId::from_string("user-456".to_string()).unwrap();

        // Admin should have higher limits
        for i in 0..50 {
            let result = limiter
                .check_adaptive_limit(&admin_id, "admin", QueryComplexity::Simple)
                .await;
            assert!(result.is_ok(), "Admin request {} should succeed", i + 1);
        }

        // Regular user should have lower limits
        for i in 0..20 {
            let result = limiter
                .check_adaptive_limit(&user_id, "user", QueryComplexity::Simple)
                .await;

            if i < 20 {
                assert!(result.is_ok(), "User request {} should succeed", i + 1);
            }
        }

        // User should be rate limited after 20 requests
        let result = limiter
            .check_adaptive_limit(&user_id, "user", QueryComplexity::Simple)
            .await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_query_complexity_scoring() {
        let limiter = AdaptiveRateLimiter::new();
        let user_id = UserId::from_string("user-789".to_string()).unwrap();

        // Complex queries should consume more of the limit
        // User base limit is 20, heavy complexity uses audit_query multiplier of 2.5
        // Heavy has score of 5.0, multiplied by 2.5 = 12.5 units per heavy query
        // But wait, we're using get_complexity_multiplier which maps Heavy to audit_query
        let result = limiter
            .check_adaptive_limit(&user_id, "user", QueryComplexity::Heavy)
            .await;
        assert!(result.is_ok(), "First heavy query should succeed");

        // Check how much was consumed
        let metrics = limiter.get_user_metrics(&user_id).await.unwrap();
        let first_weighted = metrics.weighted_count;

        // Do another heavy query to see the actual multiplier
        let result = limiter
            .check_adaptive_limit(&user_id, "user", QueryComplexity::Heavy)
            .await;
        assert!(result.is_ok(), "Second heavy query should succeed");

        let metrics = limiter.get_user_metrics(&user_id).await.unwrap();
        let _per_heavy = metrics.weighted_count - first_weighted;

        // Now we know how much each heavy query costs, let's test the limit
        // User limit is 20, so we should be able to do a few more simple queries
        for i in 0..5 {
            let result = limiter
                .check_adaptive_limit(&user_id, "user", QueryComplexity::Simple)
                .await;
            if metrics.weighted_count + (i as f64) < 20.0 {
                assert!(result.is_ok(), "Simple query {} should succeed", i + 1);
            }
        }

        // Eventually we should hit the limit
        for _ in 0..10 {
            let result = limiter
                .check_adaptive_limit(&user_id, "user", QueryComplexity::Heavy)
                .await;
            if result.is_err() {
                // Good, we hit the limit
                return;
            }
        }

        panic!("Should have hit rate limit by now");
    }

    #[tokio::test]
    async fn test_burst_mode_activation() {
        let limiter = AdaptiveRateLimiter::new();
        let admin_id = UserId::from_string("admin-burst".to_string()).unwrap();

        // Fill up to base limit
        for _ in 0..100 {
            limiter
                .check_adaptive_limit(&admin_id, "admin", QueryComplexity::Simple)
                .await
                .ok();
        }

        // Admin should be able to burst beyond base limit
        let metrics = limiter.get_user_metrics(&admin_id).await.unwrap();
        assert_eq!(metrics.request_count, 100);

        // Continue with burst
        for _ in 0..50 {
            limiter
                .check_adaptive_limit(&admin_id, "admin", QueryComplexity::Simple)
                .await
                .ok();
        }

        let metrics = limiter.get_user_metrics(&admin_id).await.unwrap();
        assert!(metrics.burst_active);
    }

    #[tokio::test]
    async fn test_suspicious_activity_tracking() {
        let limiter = AdaptiveRateLimiter::new();
        let user_id = UserId::from_string("suspicious-user".to_string()).unwrap();

        // First create an entry by making a normal request
        limiter
            .check_adaptive_limit(&user_id, "user", QueryComplexity::Simple)
            .await
            .ok();

        // Record multiple failures to build up suspicious score
        // Default threshold is 100, each failure adds 5 points
        for _ in 0..20 {
            limiter.record_failure(&user_id).await;
        }

        // Check request should be denied due to suspicious activity (20 * 5 = 100)
        let result = limiter
            .check_adaptive_limit(&user_id, "user", QueryComplexity::Simple)
            .await;
        assert!(
            result.is_err(),
            "Should be denied due to suspicious score >= 100"
        );

        // Record successes to reduce suspicious score
        for _ in 0..10 {
            limiter.record_success(&user_id).await;
        }

        let metrics = limiter.get_user_metrics(&user_id).await;
        assert!(metrics.is_some(), "Metrics should exist after requests");
        let score = metrics.unwrap().suspicious_score;
        assert!(
            score < 100,
            "Score should be reduced after successes: {score}"
        );
    }

    #[tokio::test]
    async fn test_window_expiration_and_reset() {
        let config = AdaptiveRateLimiterConfig {
            window_duration: Duration::from_millis(100), // Short window for testing
            ..Default::default()
        };

        let limiter = AdaptiveRateLimiter::with_config(config);
        let user_id = UserId::from_string("window-test".to_string()).unwrap();

        // Use up some requests
        for _ in 0..5 {
            limiter
                .check_adaptive_limit(&user_id, "user", QueryComplexity::Simple)
                .await
                .ok();
        }

        let metrics = limiter.get_user_metrics(&user_id).await.unwrap();
        assert_eq!(metrics.request_count, 5);

        // Wait for window to expire
        tokio::time::sleep(Duration::from_millis(150)).await;

        // Should be able to make requests again
        let result = limiter
            .check_adaptive_limit(&user_id, "user", QueryComplexity::Simple)
            .await;
        assert!(result.is_ok());

        let metrics = limiter.get_user_metrics(&user_id).await.unwrap();
        assert_eq!(metrics.request_count, 1); // Reset after window expiration
    }

    #[tokio::test]
    async fn test_service_account_high_limits() {
        let limiter = AdaptiveRateLimiter::new();
        let service_id = UserId::from_string("service-account".to_string()).unwrap();

        // Service accounts should have very high limits
        for i in 0..150 {
            let result = limiter
                .check_adaptive_limit(&service_id, "service", QueryComplexity::Simple)
                .await;
            assert!(result.is_ok(), "Service request {} should succeed", i + 1);
        }

        // Even complex queries should be allowed for service accounts
        for i in 0..20 {
            let result = limiter
                .check_adaptive_limit(&service_id, "service", QueryComplexity::Heavy)
                .await;
            assert!(
                result.is_ok(),
                "Service heavy request {} should succeed",
                i + 1
            );
        }
    }

    #[tokio::test]
    async fn test_compliance_officer_audit_query_limits() {
        let limiter = AdaptiveRateLimiter::new();
        let compliance_id = UserId::from_string("compliance-officer".to_string()).unwrap();

        // Compliance officers should have moderate limits for audit queries
        for i in 0..10 {
            let result = limiter
                .check_adaptive_limit(
                    &compliance_id,
                    "compliance",
                    QueryComplexity::Heavy, // Audit queries are heavy
                )
                .await;

            if i < 10 {
                assert!(
                    result.is_ok(),
                    "Compliance audit query {} should succeed",
                    i + 1
                );
            }
        }

        // Should still have room for simple queries
        for i in 0..10 {
            let result = limiter
                .check_adaptive_limit(&compliance_id, "compliance", QueryComplexity::Simple)
                .await;
            assert!(
                result.is_ok(),
                "Compliance simple query {} should succeed",
                i + 1
            );
        }
    }

    #[tokio::test]
    async fn test_global_metrics_tracking() {
        let limiter = AdaptiveRateLimiter::new();

        // Generate some traffic
        let user1 = UserId::from_string("user1".to_string()).unwrap();
        let user2 = UserId::from_string("user2".to_string()).unwrap();

        for _ in 0..5 {
            limiter
                .check_adaptive_limit(&user1, "user", QueryComplexity::Simple)
                .await
                .ok();
            limiter
                .check_adaptive_limit(&user2, "admin", QueryComplexity::Complex)
                .await
                .ok();
        }

        let metrics = limiter.get_global_metrics().await;
        assert_eq!(metrics.total_requests, 10);
        assert!(metrics.average_complexity > 1.0); // Mix of simple and complex
        assert!(metrics.average_complexity < 3.0);
    }
}
