// Security module for application layer
// Provides rate limiting, timing attack protection, and secure error handling

pub mod adaptive_rate_limiter;
pub mod constant_time_auth;
pub mod error_sanitizer;
pub mod permission_sanitizer;
pub mod rate_limiter;

#[cfg(test)]
mod security_integration_tests;

#[cfg(test)]
mod comprehensive_security_test;

pub use adaptive_rate_limiter::{
    AdaptiveRateLimiter, AdaptiveRateLimiterConfig, GlobalMetricsSnapshot, QueryComplexity,
    RoleLimitConfig, UserMetrics,
};
pub use constant_time_auth::ConstantTimeAuthService;
pub use error_sanitizer::{ErrorSanitizer, SanitizedError};
pub use permission_sanitizer::{
    PermissionSanitizer, PermissionSanitizerConfig, PermissionSource, SanitizedPermission,
    SanitizedRoleHierarchy, SanitizedRoleInfo,
};
pub use rate_limiter::{RateLimit<PERSON>ey, RateLimiter, RateLimiterConfig};
