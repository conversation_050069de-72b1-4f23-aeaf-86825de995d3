// Application layer error types
// Defines errors that can occur in the application layer (use cases)

use auth_domain::DomainError;

/// Application layer error types
/// These represent failures in use case execution
#[derive(Debug, thiserror::Error, PartialEq)]
pub enum ApplicationError {
    /// Domain error that bubbled up from domain layer
    #[error("Domain error: {0}")]
    Domain(DomainError),

    /// User registration errors
    #[error("Email already exists")]
    EmailAlreadyExists,

    #[error("Username already exists")]
    UsernameAlreadyExists,

    /// Authentication errors
    #[error("Invalid credentials")]
    InvalidCredentials,

    #[error("Account locked")]
    AccountLocked,

    #[error("Account not verified")]
    AccountNotVerified,

    #[error("MFA required")]
    MfaRequired,

    #[error("Invalid MFA code")]
    InvalidMfaCode,

    /// Authorization errors
    #[error("User not found")]
    UserNotFound,

    #[error("Role not found")]
    RoleNotFound,

    #[error("Session not found")]
    SessionNotFound,

    /// Generic not found error
    #[error("Not found: {0}")]
    NotFound(String),

    /// Forbidden access
    #[error("Forbidden")]
    Forbidden,

    /// Validation errors  
    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Insufficient permissions")]
    InsufficientPermissions,

    /// Rate limiting and security errors
    #[error("Rate limit exceeded")]
    RateLimitExceeded,

    #[error("Suspicious activity detected")]
    SuspiciousActivity,

    #[error("Account temporarily locked due to security concerns")]
    SecurityLock,

    /// Performance SLA violations
    #[error("Performance SLA exceeded: {operation} took {duration}ms")]
    PerformanceViolation { operation: String, duration: u64 },

    /// Infrastructure errors
    #[error("Repository error: {0}")]
    Repository(String),

    #[error("External service error: {0}")]
    ExternalService(String),

    /// Transaction and event errors
    #[error("Transaction failed: {0}")]
    TransactionFailed(String),

    #[error("Event publishing failed: {0}")]
    EventPublishingFailed(String),

    #[error("Configuration error: {0}")]
    Configuration(String),

    /// Token and session errors
    #[error("Invalid token")]
    InvalidToken,

    #[error("Token expired")]
    TokenExpired,

    #[error("Session expired")]
    SessionExpired,

    /// Input validation errors
    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Missing required field: {0}")]
    MissingField(String),

    /// Business rule violations
    #[error("Business rule violation: {0}")]
    BusinessRule(String),

    /// Internal errors
    #[error("Internal error: {0}")]
    Internal(String),
}

impl ApplicationError {
    /// Check if error is retryable
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            ApplicationError::Repository(_)
                | ApplicationError::ExternalService(_)
                | ApplicationError::TransactionFailed(_)
                | ApplicationError::EventPublishingFailed(_)
                | ApplicationError::Internal(_)
        )
    }

    /// Check if error should be logged
    pub fn should_log(&self) -> bool {
        match self {
            ApplicationError::InvalidCredentials => false, // Common, don't spam logs
            ApplicationError::UserNotFound => false,       // Common, don't spam logs
            ApplicationError::RateLimitExceeded => true,   // Security concern
            ApplicationError::SuspiciousActivity => true,  // Security concern
            ApplicationError::TransactionFailed(_) => true, // Always log transaction failures
            ApplicationError::EventPublishingFailed(_) => true, // Log event failures for debugging
            ApplicationError::Internal(_) => true,         // Always log internal errors
            _ => true,
        }
    }

    /// Get error severity level
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            ApplicationError::Internal(_) => ErrorSeverity::Critical,
            ApplicationError::Repository(_) => ErrorSeverity::High,
            ApplicationError::ExternalService(_) => ErrorSeverity::High,
            ApplicationError::TransactionFailed(_) => ErrorSeverity::High,
            ApplicationError::EventPublishingFailed(_) => ErrorSeverity::Medium,
            ApplicationError::SuspiciousActivity => ErrorSeverity::High,
            ApplicationError::SecurityLock => ErrorSeverity::Medium,
            ApplicationError::RateLimitExceeded => ErrorSeverity::Medium,
            ApplicationError::InvalidCredentials => ErrorSeverity::Low,
            ApplicationError::UserNotFound => ErrorSeverity::Low,
            _ => ErrorSeverity::Medium,
        }
    }

    /// Get user-friendly error message (safe for client display)
    pub fn user_message(&self) -> &'static str {
        match self {
            ApplicationError::EmailAlreadyExists => "An account with this email already exists",
            ApplicationError::UsernameAlreadyExists => {
                "An account with this username already exists"
            }
            ApplicationError::InvalidCredentials => "Invalid email or password",
            ApplicationError::AccountLocked => "Account is temporarily locked",
            ApplicationError::AccountNotVerified => "Please verify your email address",
            ApplicationError::MfaRequired => "Multi-factor authentication required",
            ApplicationError::InvalidMfaCode => "Invalid authentication code",
            ApplicationError::InsufficientPermissions => {
                "You don't have permission for this action"
            }
            ApplicationError::RateLimitExceeded => "Too many requests. Please try again later",
            ApplicationError::SuspiciousActivity => {
                "Suspicious activity detected. Please contact support"
            }
            ApplicationError::SecurityLock => "Account locked for security reasons",
            ApplicationError::InvalidToken => "Invalid or expired authentication token",
            ApplicationError::TokenExpired => "Authentication token has expired",
            ApplicationError::SessionExpired => "Your session has expired. Please log in again",
            ApplicationError::InvalidInput(_) => "Invalid input provided",
            ApplicationError::MissingField(_) => "Required field is missing",
            _ => "An error occurred. Please try again later",
        }
    }
}

/// Error severity levels for logging and monitoring
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Convert domain errors to application errors with context
impl From<auth_domain::errors::DomainError> for ApplicationError {
    fn from(domain_error: DomainError) -> Self {
        // Add application-specific context or mapping
        match domain_error {
            DomainError::SessionExpired => ApplicationError::SessionExpired,
            DomainError::InvalidEmail(msg) => {
                ApplicationError::InvalidInput(format!("Invalid email format: {msg}"))
            }
            DomainError::InvalidPassword(msg) => {
                ApplicationError::InvalidInput(format!("Invalid password format: {msg}"))
            }
            other => ApplicationError::Domain(other),
        }
    }
}

/// Result type alias for application operations
pub type ApplicationResult<T> = Result<T, ApplicationError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn should_determine_error_retryability() {
        assert!(ApplicationError::Repository("DB error".to_string()).is_retryable());
        assert!(ApplicationError::ExternalService("API error".to_string()).is_retryable());
        assert!(ApplicationError::Internal("Internal error".to_string()).is_retryable());

        assert!(!ApplicationError::InvalidCredentials.is_retryable());
        assert!(!ApplicationError::EmailAlreadyExists.is_retryable());
        assert!(!ApplicationError::InsufficientPermissions.is_retryable());
    }

    #[test]
    fn should_determine_logging_requirements() {
        assert!(ApplicationError::SuspiciousActivity.should_log());
        assert!(ApplicationError::RateLimitExceeded.should_log());
        assert!(ApplicationError::Internal("Error".to_string()).should_log());

        assert!(!ApplicationError::InvalidCredentials.should_log());
        assert!(!ApplicationError::UserNotFound.should_log());
    }

    #[test]
    fn should_assign_correct_severity() {
        assert_eq!(
            ApplicationError::Internal("Error".to_string()).severity(),
            ErrorSeverity::Critical
        );
        assert_eq!(
            ApplicationError::SuspiciousActivity.severity(),
            ErrorSeverity::High
        );
        assert_eq!(
            ApplicationError::RateLimitExceeded.severity(),
            ErrorSeverity::Medium
        );
        assert_eq!(
            ApplicationError::InvalidCredentials.severity(),
            ErrorSeverity::Low
        );
    }

    #[test]
    fn should_provide_safe_user_messages() {
        assert_eq!(
            ApplicationError::InvalidCredentials.user_message(),
            "Invalid email or password"
        );
        assert_eq!(
            ApplicationError::Internal("Database connection failed".to_string()).user_message(),
            "An error occurred. Please try again later"
        );
        assert_eq!(
            ApplicationError::SuspiciousActivity.user_message(),
            "Suspicious activity detected. Please contact support"
        );
    }

    #[test]
    fn should_convert_domain_errors_appropriately() {
        let domain_error = DomainError::InvalidEmail("Bad format".to_string());
        let app_error: ApplicationError = domain_error.into();

        match app_error {
            ApplicationError::InvalidInput(msg) => {
                assert_eq!(msg, "Invalid email format: Bad format")
            }
            _ => panic!("Expected InvalidInput error"),
        }
    }

    #[test]
    fn should_maintain_error_equality() {
        let error1 = ApplicationError::EmailAlreadyExists;
        let error2 = ApplicationError::EmailAlreadyExists;
        assert_eq!(error1, error2);

        let error3 = ApplicationError::InvalidCredentials;
        assert_ne!(error1, error3);
    }
}
