// Base command handler traits and types
// Provides the foundation for CQRS command handling

use crate::errors::{ApplicationError, ApplicationResult};
use auth_domain::events::DomainEvent;

/// Trait for command objects
/// Commands represent write operations in the system
pub trait Command: Send + Sync + std::fmt::Debug {
    type Result: Send;
}

/// Synchronous command handler trait
pub trait CommandHandler<C: Command>: Send + Sync {
    fn handle(&self, command: C) -> ApplicationResult<C::Result>;
}

/// Asynchronous command handler trait (most handlers will use this)
#[async_trait::async_trait]
pub trait AsyncCommandHandler<C: Command>: Send + Sync {
    async fn handle(&self, command: C) -> ApplicationResult<C::Result>;
}

/// Event publishing trait for domain events (legacy - use transaction::EventPublisher instead)
#[async_trait::async_trait]
pub trait EventPublisher: Send + Sync {
    async fn publish(&self, event: DomainEvent) -> Result<(), ApplicationError>;
    async fn publish_batch(&self, events: Vec<DomainEvent>) -> Result<(), ApplicationError>;
}

/// Enhanced command handler trait with transaction support
/// Provides automatic transaction management and event publishing
#[async_trait::async_trait]
pub trait TransactionalCommandHandler<C: Command>: Send + Sync {
    type Dependencies: Send + Sync;

    /// Execute command within a transaction context
    /// Transaction is automatically managed - committed on success, rolled back on failure
    /// Events are published after successful transaction commit
    async fn handle_transactional(
        &self,
        command: C,
        transaction: &mut dyn crate::transaction::Transaction,
        dependencies: &Self::Dependencies,
    ) -> ApplicationResult<C::Result>;
}

/// Command validation trait
pub trait CommandValidator<C: Command>: Send + Sync {
    fn validate(&self, command: &C) -> ApplicationResult<()>;
}

/// Base command result with optional events
#[derive(Debug, Clone)]
pub struct CommandResult<T> {
    pub result: T,
    pub events: Vec<DomainEvent>,
}

impl<T> CommandResult<T> {
    pub fn new(result: T) -> Self {
        Self {
            result,
            events: Vec::new(),
        }
    }

    pub fn with_events(result: T, events: Vec<DomainEvent>) -> Self {
        Self { result, events }
    }

    pub fn add_event(mut self, event: DomainEvent) -> Self {
        self.events.push(event);
        self
    }
}

/// Command execution context with metadata
#[derive(Debug, Clone)]
pub struct CommandContext {
    pub request_id: String,
    pub user_id: Option<auth_domain::value_objects::UserId>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub timestamp: std::time::SystemTime,
}

impl CommandContext {
    pub fn new(request_id: String) -> Self {
        Self {
            request_id,
            user_id: None,
            ip_address: None,
            user_agent: None,
            timestamp: std::time::SystemTime::now(),
        }
    }

    pub fn with_user(mut self, user_id: auth_domain::value_objects::UserId) -> Self {
        self.user_id = Some(user_id);
        self
    }

    pub fn with_request_info(mut self, ip_address: String, user_agent: String) -> Self {
        self.ip_address = Some(ip_address);
        self.user_agent = Some(user_agent);
        self
    }
}

/// Enhanced command handler trait with context
#[async_trait::async_trait]
pub trait ContextualCommandHandler<C: Command>: Send + Sync {
    async fn handle(
        &self,
        command: C,
        context: CommandContext,
    ) -> ApplicationResult<CommandResult<C::Result>>;
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::value_objects::UserId;

    #[derive(Debug)]
    #[allow(dead_code)]
    struct TestCommand {
        value: String,
    }

    impl Command for TestCommand {
        type Result = String;
    }

    #[test]
    fn should_create_command_result() {
        let result = CommandResult::new("test_result".to_string());
        assert_eq!(result.result, "test_result");
        assert_eq!(result.events.len(), 0);
    }

    #[test]
    fn should_create_command_result_with_events() {
        let events = vec![]; // Would be actual DomainEvent instances
        let result = CommandResult::with_events("test_result".to_string(), events);
        assert_eq!(result.result, "test_result");
        assert_eq!(result.events.len(), 0);
    }

    #[test]
    fn should_create_command_context() {
        let context = CommandContext::new("request_123".to_string());
        assert_eq!(context.request_id, "request_123");
        assert!(context.user_id.is_none());
        assert!(context.ip_address.is_none());
        assert!(context.user_agent.is_none());
    }

    #[test]
    fn should_build_command_context_with_user() {
        let user_id = UserId::new();
        let context = CommandContext::new("request_123".to_string()).with_user(user_id.clone());

        assert_eq!(context.user_id, Some(user_id));
    }

    #[test]
    fn should_build_command_context_with_request_info() {
        let context = CommandContext::new("request_123".to_string())
            .with_request_info("***********".to_string(), "Mozilla/5.0".to_string());

        assert_eq!(context.ip_address, Some("***********".to_string()));
        assert_eq!(context.user_agent, Some("Mozilla/5.0".to_string()));
    }
}
