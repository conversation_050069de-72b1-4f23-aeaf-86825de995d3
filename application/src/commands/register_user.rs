// User registration command and handler
// Handles the use case of registering a new user

use crate::commands::base::{AsyncCommandHandler, Command, EventPublisher};
use crate::errors::{ApplicationError, ApplicationResult};
use crate::transaction::{Transaction, TransactionManager, UnitOfWork};
use auth_domain::{
    entities::User,
    events::{DomainEvent, user_registered::UserRegistered},
    repositories::UserRepository,
    value_objects::{Email, Password, UserId},
};
use std::sync::Arc;

/// Command to register a new user
#[derive(Debug, Clone)]
pub struct RegisterUserCommand {
    pub email: String,
    pub password: String,
}

impl Command for RegisterUserCommand {
    type Result = UserId;
}

/// Handler for user registration
pub struct RegisterUserHandler<U, E>
where
    U: UserRepository,
    E: EventPublisher,
{
    user_repository: U,
    event_publisher: E,
}

impl<U, E> RegisterUserHandler<U, E>
where
    U: UserRepository,
    E: EventPublisher,
{
    pub fn new(user_repository: U, event_publisher: E) -> Self {
        Self {
            user_repository,
            event_publisher,
        }
    }
}

#[async_trait::async_trait]
impl<U, E> AsyncCommandHandler<RegisterUserCommand> for RegisterUserHandler<U, E>
where
    U: UserRepository,
    E: EventPublisher,
{
    async fn handle(&self, command: RegisterUserCommand) -> ApplicationResult<UserId> {
        // Parse and validate input
        let email = Email::new(&command.email)
            .map_err(|e| ApplicationError::InvalidInput(format!("Invalid email: {e}")))?;

        let password = Password::new(&command.password)
            .map_err(|e| ApplicationError::InvalidInput(format!("Invalid password: {e}")))?;

        // Check email uniqueness
        if self.user_repository.find_by_email(&email).await?.is_some() {
            return Err(ApplicationError::EmailAlreadyExists);
        }

        // Create new user
        let user = User::new(email.clone(), password)?;
        let user_id = user.id().clone();

        // Save user
        self.user_repository.save(&user).await?;

        // Create and publish domain event
        let event = UserRegistered::new(user_id.clone(), email, None, None);
        if let Err(e) = self.event_publisher.publish(DomainEvent::from(event)).await {
            tracing::warn!("Failed to publish UserRegistered event: {}", e);
            // Continue execution - event publishing failure should not block user registration
        }

        Ok(user_id)
    }
}

/// Enhanced register user handler with proper transaction and event management
/// This is the recommended handler that ensures data consistency and proper event publishing
pub struct TransactionalRegisterUserHandler<TM, UR>
where
    TM: TransactionManager,
    UR: UserRepository + Clone + 'static,
{
    transaction_manager: TM,
    user_repository: UR,
    event_publisher: Arc<dyn crate::transaction::EventPublisher>,
}

impl<TM, UR> TransactionalRegisterUserHandler<TM, UR>
where
    TM: TransactionManager,
    UR: UserRepository + Clone + 'static,
{
    pub fn new(
        transaction_manager: TM,
        user_repository: UR,
        event_publisher: Arc<dyn crate::transaction::EventPublisher>,
    ) -> Self {
        Self {
            transaction_manager,
            user_repository,
            event_publisher,
        }
    }

    /// Execute user registration within a transaction with proper event publishing
    pub async fn execute(&self, command: RegisterUserCommand) -> ApplicationResult<UserId> {
        let uow = UnitOfWork::new(
            self.transaction_manager.clone(),
            self.event_publisher.clone(),
        );

        let user_repository = self.user_repository.clone();
        let cmd = command.clone();

        uow.execute(move |transaction| {
            Box::pin(async move {
                // Parse and validate input
                let email = Email::new(&cmd.email)
                    .map_err(|e| ApplicationError::InvalidInput(format!("Invalid email: {e}")))?;

                let password = Password::new(&cmd.password).map_err(|e| {
                    ApplicationError::InvalidInput(format!("Invalid password: {e}"))
                })?;

                // Check email uniqueness
                if user_repository.find_by_email(&email).await?.is_some() {
                    return Err(ApplicationError::EmailAlreadyExists);
                }

                // Create new user
                let user = User::new(email.clone(), password)?;
                let user_id = user.id().clone();

                // Save user (within transaction)
                user_repository.save(&user).await?;

                // Add domain event to transaction
                let event = UserRegistered::new(user_id.clone(), email, None, None);
                transaction.add_event(DomainEvent::from(event));

                Ok(user_id)
            })
        })
        .await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        assert_async_err, assert_timeout,
        testing::mocks::{MockEventBus, MockUserRepo},
    };
    use auth_domain::{
        entities::User,
        value_objects::{Email, Password},
    };
    use mockall::predicate::*;

    #[allow(dead_code)]
    fn setup_handler() -> (
        RegisterUserHandler<MockUserRepo, MockEventBus>,
        MockUserRepo,
        MockEventBus,
    ) {
        let user_repo = MockUserRepo::new();
        let event_bus = MockEventBus::new();
        let handler = RegisterUserHandler::new(user_repo, event_bus);

        // Note: We need to extract the mocks after creating the handler
        // This is a simplified approach for testing
        let user_repo_mock = MockUserRepo::new();
        let event_bus_mock = MockEventBus::new();

        (handler, user_repo_mock, event_bus_mock)
    }

    #[tokio::test]
    async fn should_register_new_user_successfully() {
        // Arrange
        let mut user_repo = MockUserRepo::new();
        let mut event_bus = MockEventBus::new();

        user_repo
            .expect_find_by_email()
            .with(eq(Email::new("<EMAIL>").unwrap()))
            .times(1)
            .returning(|_| Ok(None));

        user_repo.expect_save().times(1).returning(|_| Ok(()));

        event_bus.expect_publish().times(1).returning(|_| Ok(()));

        let handler = RegisterUserHandler::new(user_repo, event_bus);
        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = assert_timeout!(handler.handle(command));

        // Assert
        assert!(result.is_ok());
        let user_id = result.unwrap();
        assert!(!user_id.to_string().is_empty());
    }

    #[tokio::test]
    async fn should_fail_when_email_already_exists() {
        // Arrange
        let mut user_repo = MockUserRepo::new();
        let event_bus = MockEventBus::new();

        // Mock existing user
        let existing_user = User::new(
            Email::new("<EMAIL>").unwrap(),
            Password::new("SecurePass123!").unwrap(),
        )
        .unwrap();

        user_repo
            .expect_find_by_email()
            .with(eq(Email::new("<EMAIL>").unwrap()))
            .times(1)
            .returning(move |_| Ok(Some(existing_user.clone())));

        let handler = RegisterUserHandler::new(user_repo, event_bus);
        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act & Assert
        assert_async_err!(
            handler.handle(command),
            ApplicationError::EmailAlreadyExists
        );
    }

    // Username uniqueness test removed since User::new generates unique usernames from email

    #[tokio::test]
    async fn should_fail_with_invalid_email() {
        // Arrange
        let user_repo = MockUserRepo::new();
        let event_bus = MockEventBus::new();

        let handler = RegisterUserHandler::new(user_repo, event_bus);
        let command = RegisterUserCommand {
            email: "invalid-email".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act & Assert
        assert_async_err!(handler.handle(command), ApplicationError::InvalidInput(_));
    }

    #[tokio::test]
    async fn should_fail_with_weak_password() {
        // Arrange
        let user_repo = MockUserRepo::new();
        let event_bus = MockEventBus::new();

        let handler = RegisterUserHandler::new(user_repo, event_bus);
        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "weak".to_string(),
        };

        // Act & Assert
        assert_async_err!(handler.handle(command), ApplicationError::InvalidInput(_));
    }

    #[tokio::test]
    async fn should_fail_with_empty_fields() {
        // Arrange
        let user_repo = MockUserRepo::new();
        let event_bus = MockEventBus::new();

        let handler = RegisterUserHandler::new(user_repo, event_bus);
        let command = RegisterUserCommand {
            email: "".to_string(),
            password: "".to_string(),
        };

        // Act & Assert
        assert_async_err!(handler.handle(command), ApplicationError::InvalidInput(_));
    }

    // Event publishing test will be added after implementing the event system
}

// Fixture conversion removed since we're using direct command construction in tests
