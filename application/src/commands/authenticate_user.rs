// User authentication command and handler
// Handles the use case of authenticating a user

use crate::errors::ApplicationError;
use crate::security::{
    constant_time_auth::{AuthenticationResult, ConstantTimeAuthService},
    error_sanitizer::<PERSON>rro<PERSON><PERSON><PERSON>tizer,
    rate_limiter::{RateLimiter, rate_limit_key_combined, rate_limit_key_from_ip},
};
use crate::transaction::{Transaction, TransactionManager, UnitOfWork};
use auth_domain::{
    crypto::ConstantTimeService,
    events::{
        DomainEvent,
        user_logged_in::{AuthenticationMethod, UserLoggedIn},
    },
    repositories::{SessionRepository, UserRepository},
    services::AuthService,
    value_objects::Email,
};
use std::sync::Arc;

/// Command for authenticating a user
#[derive(Debug, Clone)]
pub struct AuthenticateUserCommand {
    pub email: String,
    pub password: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

/// Authentication token response
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub struct AuthToken {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64, // seconds
}

/// Result of successful authentication
pub struct AuthenticateUserResult {
    pub auth_token: AuthToken,
    pub events: Vec<UserLoggedIn>,
}

/// Service traits for authentication dependencies
#[async_trait::async_trait]
pub trait PasswordService: Send + Sync {
    async fn verify_password(
        &self,
        user: &auth_domain::entities::User,
        password: &str,
    ) -> Result<bool, ApplicationError>;
}

#[async_trait::async_trait]
pub trait TokenService: Send + Sync {
    async fn generate_tokens(
        &self,
        user_id: &str,
        session_id: &str,
    ) -> Result<AuthToken, ApplicationError>;
}

/// Authentication command handler with dependency injection and security features
pub struct AuthenticateUserHandler<UR, SR, PS, TS>
where
    UR: UserRepository,
    SR: SessionRepository,
    PS: PasswordService,
    TS: TokenService,
{
    user_repository: UR,
    session_repository: SR,
    #[allow(dead_code)] // Used for password verification in some configurations
    password_service: PS,
    token_service: TS,
    rate_limiter: Arc<RateLimiter>,
    constant_time_auth: ConstantTimeAuthService<ConstantTimeService>,
    #[allow(dead_code)] // Will be used for error sanitization in API layer
    error_sanitizer: ErrorSanitizer,
}

impl<UR, SR, PS, TS> AuthenticateUserHandler<UR, SR, PS, TS>
where
    UR: UserRepository,
    SR: SessionRepository,
    PS: PasswordService,
    TS: TokenService,
{
    pub fn new(
        user_repository: UR,
        session_repository: SR,
        password_service: PS,
        token_service: TS,
    ) -> Self {
        AuthenticateUserHandler {
            user_repository,
            session_repository,
            password_service,
            token_service,
            rate_limiter: Arc::new(RateLimiter::new()),
            constant_time_auth: ConstantTimeAuthService::new(ConstantTimeService::new()),
            error_sanitizer: ErrorSanitizer::new(),
        }
    }

    /// Create handler with custom security configuration
    pub fn with_security_config(
        user_repository: UR,
        session_repository: SR,
        password_service: PS,
        token_service: TS,
        rate_limiter: Arc<RateLimiter>,
    ) -> Self {
        AuthenticateUserHandler {
            user_repository,
            session_repository,
            password_service,
            token_service,
            rate_limiter,
            constant_time_auth: ConstantTimeAuthService::new(ConstantTimeService::new()),
            error_sanitizer: ErrorSanitizer::new(),
        }
    }

    /// Handle user authentication command with comprehensive security
    pub async fn handle(
        &self,
        command: AuthenticateUserCommand,
    ) -> Result<AuthenticateUserResult, ApplicationError> {
        // Step 1: Rate limiting checks
        // Check IP-based rate limiting
        if let Some(ref ip) = command.ip_address {
            let ip_key = rate_limit_key_from_ip(Some(ip.clone()));
            if self.rate_limiter.check_rate_limit(&ip_key).await.is_err() {
                return Err(ApplicationError::RateLimitExceeded);
            }
        }

        // Check account-based rate limiting
        let account_key = rate_limit_key_combined(command.ip_address.clone(), &command.email);
        if self
            .rate_limiter
            .check_rate_limit(&account_key)
            .await
            .is_err()
        {
            return Err(ApplicationError::RateLimitExceeded);
        }

        // Step 2: Validate email format (always do this)
        let email = match Email::new(&command.email) {
            Ok(e) => e,
            Err(_) => {
                // Still perform constant-time operations even for invalid email
                let _ = self
                    .constant_time_auth
                    .validate_authentication_attempt(&command.email, &command.password, None)
                    .await;

                // Record failed attempt for rate limiting
                self.rate_limiter.record_failed_attempt(&account_key).await;

                return Err(ApplicationError::InvalidCredentials);
            }
        };

        // Step 3: Find user (but don't reveal if they exist)
        let user = self
            .user_repository
            .find_by_email(&email)
            .await
            .map_err(|e| ApplicationError::Repository(e.to_string()))?;

        // Step 4: Perform constant-time authentication validation
        // This ensures timing is consistent whether user exists or not
        let auth_result = self
            .constant_time_auth
            .validate_authentication_attempt(&command.email, &command.password, user.as_ref())
            .await?;

        // Step 5: Handle authentication result
        match auth_result {
            AuthenticationResult::Success => {
                // User must exist if authentication succeeded
                let user = user.unwrap();

                // Record successful attempt (clears failed attempts)
                self.rate_limiter
                    .record_successful_attempt(&account_key)
                    .await;

                // Determine session duration (8 hours for regular sessions)
                let duration = AuthService::determine_session_duration(false);

                // Create session
                let session = AuthService::create_session(
                    user.id().clone(),
                    duration,
                    command.ip_address.clone(),
                    command.user_agent.clone(),
                );

                let session_id = session.id().clone();

                // Save session
                if let Err(e) = self.session_repository.save(&session).await {
                    return Err(ApplicationError::Repository(e.to_string()));
                }

                // Generate JWT tokens
                let auth_token = match self
                    .token_service
                    .generate_tokens(user.id().as_str(), session_id.as_str())
                    .await
                {
                    Ok(token) => token,
                    Err(e) => return Err(e),
                };

                // Create domain event
                let event = UserLoggedIn::new(
                    user.id().clone(),
                    session_id.clone(),
                    command.ip_address.clone(),
                    command.user_agent.clone(),
                    AuthenticationMethod::Password,
                );

                // Check for suspicious patterns
                if let Some(ref ip) = command.ip_address {
                    if self.rate_limiter.is_suspicious_ip(ip).await {
                        // Log suspicious activity but still allow login
                        // In production, this would trigger security monitoring
                    }
                }

                Ok(AuthenticateUserResult {
                    auth_token,
                    events: vec![event],
                })
            }
            AuthenticationResult::Failed => {
                // Record failed attempt for rate limiting
                self.rate_limiter.record_failed_attempt(&account_key).await;

                // Return generic error (doesn't reveal why authentication failed)
                Err(ApplicationError::InvalidCredentials)
            }
        }
    }
}

/// Enhanced authentication handler with proper transaction and event management
/// This is the recommended handler that ensures data consistency and proper event publishing
pub struct TransactionalAuthenticateUserHandler<TM, UR, SR, PS, TS>
where
    TM: TransactionManager,
    UR: UserRepository + Clone + 'static,
    SR: SessionRepository + Clone + 'static,
    PS: PasswordService + Clone + 'static,
    TS: TokenService + Clone + 'static,
{
    transaction_manager: TM,
    user_repository: UR,
    session_repository: SR,
    #[allow(dead_code)] // May be used in future extensions
    password_service: PS,
    token_service: TS,
    rate_limiter: Arc<RateLimiter>,
    constant_time_auth: ConstantTimeAuthService<ConstantTimeService>,
    #[allow(dead_code)] // Will be used for error sanitization in API layer
    error_sanitizer: ErrorSanitizer,
    event_publisher: Arc<dyn crate::transaction::EventPublisher>,
}

impl<TM, UR, SR, PS, TS> TransactionalAuthenticateUserHandler<TM, UR, SR, PS, TS>
where
    TM: TransactionManager,
    UR: UserRepository + Clone + 'static,
    SR: SessionRepository + Clone + 'static,
    PS: PasswordService + Clone + 'static,
    TS: TokenService + Clone + 'static,
{
    pub fn new(
        transaction_manager: TM,
        user_repository: UR,
        session_repository: SR,
        password_service: PS,
        token_service: TS,
        event_publisher: Arc<dyn crate::transaction::EventPublisher>,
    ) -> Self {
        Self {
            transaction_manager,
            user_repository,
            session_repository,
            password_service,
            token_service,
            rate_limiter: Arc::new(RateLimiter::new()),
            constant_time_auth: ConstantTimeAuthService::new(ConstantTimeService::new()),
            error_sanitizer: ErrorSanitizer::new(),
            event_publisher,
        }
    }

    /// Create handler with custom security configuration
    pub fn with_security_config(
        transaction_manager: TM,
        user_repository: UR,
        session_repository: SR,
        password_service: PS,
        token_service: TS,
        event_publisher: Arc<dyn crate::transaction::EventPublisher>,
        rate_limiter: Arc<RateLimiter>,
    ) -> Self {
        Self {
            transaction_manager,
            user_repository,
            session_repository,
            password_service,
            token_service,
            rate_limiter,
            constant_time_auth: ConstantTimeAuthService::new(ConstantTimeService::new()),
            error_sanitizer: ErrorSanitizer::new(),
            event_publisher,
        }
    }

    /// Execute user authentication within a transaction with proper event publishing
    pub async fn execute(
        &self,
        command: AuthenticateUserCommand,
    ) -> Result<AuthToken, ApplicationError> {
        // Step 1: Rate limiting checks (outside transaction)
        // Check IP-based rate limiting
        if let Some(ref ip) = command.ip_address {
            let ip_key = rate_limit_key_from_ip(Some(ip.clone()));
            if self.rate_limiter.check_rate_limit(&ip_key).await.is_err() {
                return Err(ApplicationError::RateLimitExceeded);
            }
        }

        // Check account-based rate limiting
        let account_key = rate_limit_key_combined(command.ip_address.clone(), &command.email);
        if self
            .rate_limiter
            .check_rate_limit(&account_key)
            .await
            .is_err()
        {
            return Err(ApplicationError::RateLimitExceeded);
        }

        // Step 2: Validate email format (always do this)
        let email = match Email::new(&command.email) {
            Ok(e) => e,
            Err(_) => {
                // Still perform constant-time operations even for invalid email
                let _ = self
                    .constant_time_auth
                    .validate_authentication_attempt(&command.email, &command.password, None)
                    .await;

                // Record failed attempt for rate limiting
                self.rate_limiter.record_failed_attempt(&account_key).await;

                return Err(ApplicationError::InvalidCredentials);
            }
        };

        // Step 3: Find user (but don't reveal if they exist)
        let user = self
            .user_repository
            .find_by_email(&email)
            .await
            .map_err(|e| ApplicationError::Repository(e.to_string()))?;

        // Step 4: Perform constant-time authentication validation
        // This ensures timing is consistent whether user exists or not
        let auth_result = self
            .constant_time_auth
            .validate_authentication_attempt(&command.email, &command.password, user.as_ref())
            .await?;

        // Step 5: Handle authentication result
        match auth_result {
            AuthenticationResult::Success => {
                // User must exist if authentication succeeded
                let user = user.unwrap();

                // Record successful attempt (clears failed attempts)
                self.rate_limiter
                    .record_successful_attempt(&account_key)
                    .await;

                // Execute session creation and event publishing within transaction
                let uow = UnitOfWork::new(
                    self.transaction_manager.clone(),
                    self.event_publisher.clone(),
                );
                let session_repo = self.session_repository.clone();
                let token_service = self.token_service.clone();
                let user_id = user.id().clone();
                let cmd = command.clone();

                uow.execute(move |transaction| {
                    Box::pin(async move {
                        // Determine session duration (8 hours for regular sessions)
                        let duration = AuthService::determine_session_duration(false);

                        // Create session
                        let session = AuthService::create_session(
                            user_id.clone(),
                            duration,
                            cmd.ip_address.clone(),
                            cmd.user_agent.clone(),
                        );

                        let session_id = session.id().clone();

                        // Save session (within transaction)
                        session_repo
                            .save(&session)
                            .await
                            .map_err(|e| ApplicationError::Repository(e.to_string()))?;

                        // Generate JWT tokens
                        let auth_token = token_service
                            .generate_tokens(user_id.as_str(), session_id.as_str())
                            .await?;

                        // Add domain event to transaction
                        let event = UserLoggedIn::new(
                            user_id,
                            session_id,
                            cmd.ip_address,
                            cmd.user_agent,
                            AuthenticationMethod::Password,
                        );
                        transaction.add_event(DomainEvent::from(event));

                        Ok(auth_token)
                    })
                })
                .await
            }
            AuthenticationResult::Failed => {
                // Record failed attempt for rate limiting
                self.rate_limiter.record_failed_attempt(&account_key).await;

                // Return generic error (doesn't reveal why authentication failed)
                Err(ApplicationError::InvalidCredentials)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::entities::{Session, User};
    use auth_domain::errors::DomainError;
    use auth_domain::value_objects::{Password, SessionId, UserId};
    use std::collections::HashMap;

    // Simple mock repositories for testing
    struct TestUserRepo {
        users: HashMap<String, User>,
        should_fail: bool,
    }

    impl TestUserRepo {
        fn new() -> Self {
            Self {
                users: HashMap::new(),
                should_fail: false,
            }
        }

        fn with_user(mut self, user: User) -> Self {
            self.users.insert(user.email().as_str().to_string(), user);
            self
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }
    }

    #[async_trait::async_trait]
    impl UserRepository for TestUserRepo {
        async fn save(&self, _user: &User) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn find_by_id(&self, _id: &UserId) -> Result<Option<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Database error".to_string()));
            }
            Ok(self.users.get(email.as_str()).cloned())
        }

        async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
            unimplemented!()
        }

        async fn exists_by_email(&self, _email: &Email) -> Result<bool, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn count(&self) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn search_users(
            &self,
            _criteria: &auth_domain::repositories::UserSearchCriteria,
        ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
            unimplemented!()
        }
    }

    struct TestSessionRepo {
        should_fail: bool,
    }

    impl TestSessionRepo {
        fn new() -> Self {
            Self { should_fail: false }
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }
    }

    #[async_trait::async_trait]
    impl SessionRepository for TestSessionRepo {
        async fn save(&self, _session: &Session) -> Result<(), DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Session save failed".to_string()));
            }
            Ok(())
        }

        async fn find_by_id(&self, _id: &SessionId) -> Result<Option<Session>, DomainError> {
            unimplemented!()
        }

        async fn find_active_by_user(
            &self,
            _user_id: &UserId,
        ) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn find_all_by_user(&self, _user_id: &UserId) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn delete_all_by_user(&self, _user_id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn count_active_by_user(&self, _user_id: &UserId) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn find_by_ip_address(&self, _ip_address: &str) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        async fn update_last_accessed(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn invalidate(&self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn invalidate_all_except(
            &self,
            _user_id: &UserId,
            _except_session_id: &SessionId,
        ) -> Result<(), DomainError> {
            unimplemented!()
        }
    }

    // Mock service implementations
    struct TestPasswordService {
        should_succeed: bool,
    }

    impl TestPasswordService {
        fn new(should_succeed: bool) -> Self {
            Self { should_succeed }
        }
    }

    #[async_trait::async_trait]
    impl PasswordService for TestPasswordService {
        async fn verify_password(
            &self,
            _user: &User,
            _password: &str,
        ) -> Result<bool, ApplicationError> {
            Ok(self.should_succeed)
        }
    }

    struct TestTokenService {
        should_succeed: bool,
    }

    impl TestTokenService {
        fn new(should_succeed: bool) -> Self {
            Self { should_succeed }
        }
    }

    #[async_trait::async_trait]
    impl TokenService for TestTokenService {
        async fn generate_tokens(
            &self,
            _user_id: &str,
            _session_id: &str,
        ) -> Result<AuthToken, ApplicationError> {
            if self.should_succeed {
                Ok(AuthToken {
                    access_token: "test_access_token".to_string(),
                    refresh_token: "test_refresh_token".to_string(),
                    expires_in: 3600,
                })
            } else {
                Err(ApplicationError::Internal(
                    "Token generation failed".to_string(),
                ))
            }
        }
    }

    #[tokio::test]
    async fn test_authenticate_user_success() {
        // Arrange
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email.clone(), password).unwrap();

        // Ensure user is verified and can authenticate
        user.verify().unwrap();

        let user_repo = TestUserRepo::new().with_user(user);
        let session_repo = TestSessionRepo::new();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(true);

        let handler =
            AuthenticateUserHandler::new(user_repo, session_repo, password_service, token_service);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: Some("127.0.0.1".to_string()),
            user_agent: Some("Test Agent".to_string()),
        };

        // Act
        let result = handler.handle(command).await.unwrap();

        // Assert
        assert_eq!(result.auth_token.access_token, "test_access_token");
        assert_eq!(result.auth_token.refresh_token, "test_refresh_token");
        assert_eq!(result.auth_token.expires_in, 3600);
        assert_eq!(result.events.len(), 1);
    }

    #[tokio::test]
    async fn test_authenticate_user_invalid_email() {
        // Arrange - user repository with no users
        let user_repo = TestUserRepo::new();
        let session_repo = TestSessionRepo::new();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(true);

        let handler =
            AuthenticateUserHandler::new(user_repo, session_repo, password_service, token_service);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: None,
            user_agent: None,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::InvalidCredentials)));
    }

    #[tokio::test]
    async fn test_authenticate_user_wrong_password() {
        // Arrange
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let user = User::new(email.clone(), password).unwrap();

        let user_repo = TestUserRepo::new().with_user(user);
        let session_repo = TestSessionRepo::new();
        let password_service = TestPasswordService::new(false); // Password verification fails
        let token_service = TestTokenService::new(true);

        let handler =
            AuthenticateUserHandler::new(user_repo, session_repo, password_service, token_service);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "WrongPassword123!".to_string(),
            ip_address: None,
            user_agent: None,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::InvalidCredentials)));
    }

    #[tokio::test]
    async fn test_authenticate_user_repository_error() {
        // Arrange
        let user_repo = TestUserRepo::new().with_failure();
        let session_repo = TestSessionRepo::new();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(true);

        let handler =
            AuthenticateUserHandler::new(user_repo, session_repo, password_service, token_service);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: None,
            user_agent: None,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::Repository(_))));
    }

    #[tokio::test]
    async fn test_authenticate_user_session_save_failure() {
        // Arrange
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email.clone(), password).unwrap();

        // Ensure user is verified and can authenticate
        user.verify().unwrap();

        let user_repo = TestUserRepo::new().with_user(user);
        let session_repo = TestSessionRepo::new().with_failure();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(true);

        let handler =
            AuthenticateUserHandler::new(user_repo, session_repo, password_service, token_service);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: None,
            user_agent: None,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::Repository(_))));
    }

    #[tokio::test]
    async fn test_authenticate_user_token_generation_failure() {
        // Arrange
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email.clone(), password).unwrap();

        // Ensure user is verified and can authenticate
        user.verify().unwrap();

        let user_repo = TestUserRepo::new().with_user(user);
        let session_repo = TestSessionRepo::new();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(false); // Token generation fails

        let handler =
            AuthenticateUserHandler::new(user_repo, session_repo, password_service, token_service);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: None,
            user_agent: None,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::Internal(_))));
    }

    #[tokio::test]
    async fn test_authenticate_user_account_locked() {
        // Arrange
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email.clone(), password).unwrap();

        // Deactivate the user account to simulate a locked account
        user.deactivate().unwrap();
        assert!(!user.is_active());

        let user_repo = TestUserRepo::new().with_user(user);
        let session_repo = TestSessionRepo::new();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(true);

        let handler =
            AuthenticateUserHandler::new(user_repo, session_repo, password_service, token_service);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: None,
            user_agent: None,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert - Should return InvalidCredentials to not reveal account status
        assert!(matches!(result, Err(ApplicationError::InvalidCredentials)));
    }
}
