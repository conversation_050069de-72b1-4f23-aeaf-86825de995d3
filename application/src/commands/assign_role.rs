// Role assignment command and handler
// Handles the use case of assigning roles to users

use crate::commands::base::{AsyncCommandHandler, Command};
use crate::errors::{ApplicationError, ApplicationResult};
use crate::security::error_sanitizer::ErrorSanitizer;
use auth_domain::{
    entities::role::Permission,
    repositories::{RoleRepository, UserRepository},
    value_objects::{RoleId, UserId},
};

/// Command to assign a role to a user
#[derive(Debug, Clone)]
pub struct AssignRoleCommand {
    pub user_id: String,
    pub role_id: String,
    pub assigned_by: String,
}

impl Command for AssignRoleCommand {
    type Result = ();
}

/// Handler for role assignment with secure error handling
pub struct AssignRoleHandler<UR, RR>
where
    UR: UserRepository,
    RR: RoleRepository,
{
    user_repository: UR,
    role_repository: RR,
    #[allow(dead_code)] // Will be used for error sanitization in API layer
    error_sanitizer: ErrorSanitizer,
}

impl<UR, RR> AssignRoleHandler<UR, RR>
where
    UR: UserRepository,
    RR: RoleRepository,
{
    pub fn new(user_repository: UR, role_repository: RR) -> Self {
        Self {
            user_repository,
            role_repository,
            error_sanitizer: ErrorSanitizer::new(),
        }
    }
}

#[async_trait::async_trait]
impl<UR, RR> AsyncCommandHandler<AssignRoleCommand> for AssignRoleHandler<UR, RR>
where
    UR: UserRepository,
    RR: RoleRepository,
{
    async fn handle(&self, command: AssignRoleCommand) -> ApplicationResult<()> {
        // Parse and validate input - use generic error for all validation failures
        let user_id = UserId::from_string(command.user_id).map_err(|_| {
            ApplicationError::InvalidInput("Invalid request parameters".to_string())
        })?;

        let role_id = RoleId::from_string(command.role_id).map_err(|_| {
            ApplicationError::InvalidInput("Invalid request parameters".to_string())
        })?;

        let assigned_by_id = UserId::from_string(command.assigned_by).map_err(|_| {
            ApplicationError::InvalidInput("Invalid request parameters".to_string())
        })?;

        // Perform all lookups regardless of intermediate failures to maintain consistent timing
        let user_result = self.user_repository.find_by_id(&user_id).await;
        let role_result = self.role_repository.find_by_id(&role_id).await;
        let assigner_result = self.user_repository.find_by_id(&assigned_by_id).await;

        // Now check results and return generic errors that don't reveal what's missing
        let _user = match user_result {
            Ok(Some(u)) => u,
            Ok(None) => {
                return Err(ApplicationError::InvalidInput(
                    "Invalid request".to_string(),
                ));
            }
            Err(_) => return Err(ApplicationError::Repository("Operation failed".to_string())),
        };

        let role = match role_result {
            Ok(Some(r)) => r,
            Ok(None) => {
                return Err(ApplicationError::InvalidInput(
                    "Invalid request".to_string(),
                ));
            }
            Err(_) => return Err(ApplicationError::Repository("Operation failed".to_string())),
        };

        let _assigner = match assigner_result {
            Ok(Some(a)) => a,
            Ok(None) => return Err(ApplicationError::InsufficientPermissions),
            Err(_) => return Err(ApplicationError::Repository("Operation failed".to_string())),
        };

        // Check if assigner has permission to assign this role
        // Key business rule: Admin role assignment requires admin permission
        if role.has_permission(&Permission::Admin) {
            let assigner_roles = self
                .role_repository
                .find_by_user(&assigned_by_id)
                .await
                .map_err(|_| ApplicationError::Repository("Operation failed".to_string()))?;

            let has_admin_permission = assigner_roles
                .iter()
                .any(|r| r.has_permission(&Permission::Admin));

            if !has_admin_permission {
                // Don't reveal why permission was denied
                return Err(ApplicationError::InsufficientPermissions);
            }
        }

        // TODO: In a full implementation, we would:
        // 1. Check if user already has this role (prevent duplicates)
        // 2. Create/update the user-role assignment relationship
        // 3. Save the assignment to a UserRole repository
        // 4. Emit domain events for audit logging

        // For now, we validate the business rules and return success
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::{
        entities::role::Permission,
        entities::{Role, User},
        errors::DomainError,
        value_objects::{Email, Password, RoleId, UserId},
    };
    use std::collections::HashMap;

    // Simple mock repositories for testing
    struct TestUserRepo {
        users: HashMap<UserId, User>,
        should_fail: bool,
    }

    impl TestUserRepo {
        fn new() -> Self {
            Self {
                users: HashMap::new(),
                should_fail: false,
            }
        }

        fn with_user(mut self, user: User) -> Self {
            self.users.insert(user.id().clone(), user);
            self
        }

        #[allow(dead_code)]
        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }
    }

    #[async_trait::async_trait]
    impl UserRepository for TestUserRepo {
        async fn save(&self, _user: &User) -> Result<(), DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Repository error".to_string()));
            }
            Ok(())
        }

        async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Repository error".to_string()));
            }
            Ok(self.users.get(id).cloned())
        }

        async fn find_by_email(&self, _email: &Email) -> Result<Option<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
            unimplemented!()
        }

        async fn exists_by_email(&self, _email: &Email) -> Result<bool, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn count(&self) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn search_users(
            &self,
            _criteria: &auth_domain::repositories::UserSearchCriteria,
        ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
            unimplemented!()
        }
    }

    struct TestRoleRepo {
        roles: HashMap<RoleId, Role>,
        user_roles: HashMap<UserId, Vec<Role>>,
        should_fail: bool,
    }

    impl TestRoleRepo {
        fn new() -> Self {
            Self {
                roles: HashMap::new(),
                user_roles: HashMap::new(),
                should_fail: false,
            }
        }

        fn with_role(mut self, role: Role) -> Self {
            self.roles.insert(role.id().clone(), role);
            self
        }

        fn with_user_roles(mut self, user_id: UserId, roles: Vec<Role>) -> Self {
            self.user_roles.insert(user_id, roles);
            self
        }

        #[allow(dead_code)]
        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }
    }

    #[async_trait::async_trait]
    impl RoleRepository for TestRoleRepo {
        async fn save(&self, _role: &Role) -> Result<(), DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Repository error".to_string()));
            }
            Ok(())
        }

        async fn find_by_id(&self, id: &RoleId) -> Result<Option<Role>, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Repository error".to_string()));
            }
            Ok(self.roles.get(id).cloned())
        }

        async fn find_by_name(&self, _name: &str) -> Result<Option<Role>, DomainError> {
            unimplemented!()
        }

        async fn exists_by_name(&self, _name: &str) -> Result<bool, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &RoleId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn count(&self) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn find_active(&self) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_by_user(&self, user_id: &UserId) -> Result<Vec<Role>, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Repository error".to_string()));
            }
            Ok(self.user_roles.get(user_id).cloned().unwrap_or_default())
        }

        async fn find_by_permission(&self, _permission: &str) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn find_children(&self, _parent_id: &RoleId) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }

        async fn get_hierarchy(&self) -> Result<Vec<Role>, DomainError> {
            unimplemented!()
        }
    }

    #[tokio::test]
    async fn should_assign_role_successfully() {
        // Arrange
        let target_user = create_test_user("<EMAIL>");
        let assigner_user = create_test_user("<EMAIL>");
        let regular_role = create_test_role("user", vec![Permission::Read]);
        let admin_role = create_test_role("admin", vec![Permission::Admin]);

        let user_repo = TestUserRepo::new()
            .with_user(target_user.clone())
            .with_user(assigner_user.clone());

        let role_repo = TestRoleRepo::new()
            .with_role(regular_role.clone())
            .with_user_roles(assigner_user.id().clone(), vec![admin_role]);

        let handler = AssignRoleHandler::new(user_repo, role_repo);
        let command = AssignRoleCommand {
            user_id: target_user.id().as_str().to_string(),
            role_id: "role_builtin_user".to_string(), // Use the actual builtin role ID format
            assigned_by: assigner_user.id().as_str().to_string(),
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        match result {
            Ok(_) => {}
            Err(e) => panic!("Expected Ok, got error: {e:?}"),
        }
    }

    #[tokio::test]
    async fn should_fail_when_admin_role_assigned_by_non_admin() {
        // Arrange
        let target_user = create_test_user("<EMAIL>");
        let assigner_user = create_test_user("<EMAIL>");
        let admin_role = create_test_role("admin", vec![Permission::Admin]);
        let user_role = create_test_role("user", vec![Permission::Read]);

        let user_repo = TestUserRepo::new()
            .with_user(target_user.clone())
            .with_user(assigner_user.clone());

        let role_repo = TestRoleRepo::new()
            .with_role(admin_role.clone())
            .with_user_roles(assigner_user.id().clone(), vec![user_role]);

        let handler = AssignRoleHandler::new(user_repo, role_repo);
        let command = AssignRoleCommand {
            user_id: target_user.id().as_str().to_string(),
            role_id: "role_builtin_admin".to_string(),
            assigned_by: assigner_user.id().as_str().to_string(),
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(
            result,
            Err(ApplicationError::InsufficientPermissions)
        ));
    }

    #[tokio::test]
    async fn should_fail_when_user_not_found() {
        // Arrange
        let assigner_user = create_test_user("<EMAIL>");
        let role = create_test_role("user", vec![Permission::Read]);
        let admin_role = create_test_role("admin", vec![Permission::Admin]);

        let user_repo = TestUserRepo::new().with_user(assigner_user.clone());

        let role_repo = TestRoleRepo::new()
            .with_role(role.clone())
            .with_user_roles(assigner_user.id().clone(), vec![admin_role]);

        let handler = AssignRoleHandler::new(user_repo, role_repo);
        let command = AssignRoleCommand {
            user_id: "nonexistent_user".to_string(),
            role_id: "role_builtin_user".to_string(),
            assigned_by: assigner_user.id().as_str().to_string(),
        };

        // Act
        let result = handler.handle(command).await;

        // Assert - Now returns generic error to not reveal user doesn't exist
        assert!(matches!(result, Err(ApplicationError::InvalidInput(_))));
    }

    #[tokio::test]
    async fn should_fail_when_role_not_found() {
        // Arrange
        let target_user = create_test_user("<EMAIL>");
        let assigner_user = create_test_user("<EMAIL>");
        let admin_role = create_test_role("admin", vec![Permission::Admin]);

        let user_repo = TestUserRepo::new()
            .with_user(target_user.clone())
            .with_user(assigner_user.clone());

        let role_repo =
            TestRoleRepo::new().with_user_roles(assigner_user.id().clone(), vec![admin_role]);

        let handler = AssignRoleHandler::new(user_repo, role_repo);
        let command = AssignRoleCommand {
            user_id: target_user.id().as_str().to_string(),
            role_id: "role_builtin_nonexistent".to_string(),
            assigned_by: assigner_user.id().as_str().to_string(),
        };

        // Act
        let result = handler.handle(command).await;

        // Assert - Now returns generic error to not reveal role doesn't exist
        match result {
            Err(ApplicationError::InvalidInput(_)) => {}
            other => panic!("Expected InvalidInput, got: {other:?}"),
        }
    }

    #[tokio::test]
    async fn should_fail_when_assigner_not_found() {
        // Arrange
        let target_user = create_test_user("<EMAIL>");
        let role = create_test_role("user", vec![Permission::Read]);

        let user_repo = TestUserRepo::new().with_user(target_user.clone());

        let role_repo = TestRoleRepo::new().with_role(role.clone());

        let handler = AssignRoleHandler::new(user_repo, role_repo);
        let command = AssignRoleCommand {
            user_id: target_user.id().as_str().to_string(),
            role_id: "role_builtin_user".to_string(),
            assigned_by: "nonexistent_assigner".to_string(),
        };

        // Act
        let result = handler.handle(command).await;

        // Assert - Now returns InsufficientPermissions for missing assigner
        assert!(matches!(
            result,
            Err(ApplicationError::InsufficientPermissions)
        ));
    }

    #[tokio::test]
    async fn should_fail_with_repository_error() {
        // Arrange
        let user_repo = TestUserRepo::new().with_failure();
        let role_repo = TestRoleRepo::new();

        let handler = AssignRoleHandler::new(user_repo, role_repo);
        let command = AssignRoleCommand {
            user_id: "user_12345678901234567890123456789012".to_string(), // Valid user ID format
            role_id: "role_builtin_user".to_string(),                     // Valid role ID format
            assigned_by: "user_12345678901234567890123456789013".to_string(), // Valid user ID format
        };

        // Act
        let result = handler.handle(command).await;

        // Assert - Now returns generic Repository error
        match result {
            Err(ApplicationError::Repository(_)) => {}
            other => panic!("Expected Repository error, got: {other:?}"),
        }
    }

    // Helper functions
    fn create_test_user(email: &str) -> User {
        let email = Email::new(email).unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        User::new(email, password).unwrap()
    }

    fn create_test_role(name: &str, permissions: Vec<Permission>) -> Role {
        let role_id = RoleId::builtin(name).unwrap();
        Role::new(role_id, name.to_string(), None, permissions).unwrap()
    }
}
