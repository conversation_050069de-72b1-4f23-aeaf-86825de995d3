// MFA enablement command and handler
// Handles the use case of enabling multi-factor authentication for users

use crate::commands::base::{AsyncCommandHandler, Command};
use crate::errors::{ApplicationError, ApplicationResult};
use auth_domain::{repositories::UserRepository, value_objects::UserId};

/// MFA type enumeration
#[derive(Debug, Clone, PartialEq)]
pub enum MfaType {
    Totp,
    Sms { phone_number: String },
    Email,
}

/// Command to enable MFA for a user
#[derive(Debug, <PERSON><PERSON>)]
pub struct EnableMfaCommand {
    pub user_id: String,
    pub mfa_type: MfaType,
}

impl Command for EnableMfaCommand {
    type Result = MfaSetupData;
}

/// MFA setup data returned after enabling MFA
#[derive(Debug, Clone, PartialEq)]
pub enum MfaSetupData {
    Totp {
        secret: String,
        qr_code: String,
    },
    Sms {
        phone_number: String,
        verification_code_sent: bool,
    },
    Email {
        email: String,
        verification_code_sent: bool,
    },
}

/// Service trait for MFA operations
#[async_trait::async_trait]
pub trait MfaService: Send + Sync {
    async fn generate_totp_secret(&self) -> Result<String, ApplicationError>;
    async fn generate_qr_code(
        &self,
        secret: &str,
        user_email: &str,
    ) -> Result<String, ApplicationError>;
    async fn send_sms_verification(&self, phone_number: &str) -> Result<bool, ApplicationError>;
    async fn send_email_verification(&self, email: &str) -> Result<bool, ApplicationError>;
    async fn validate_phone_number(&self, phone_number: &str) -> Result<bool, ApplicationError>;
}

/// Handler for MFA enablement
pub struct EnableMfaHandler<UR, MS>
where
    UR: UserRepository,
    MS: MfaService,
{
    user_repository: UR,
    mfa_service: MS,
}

impl<UR, MS> EnableMfaHandler<UR, MS>
where
    UR: UserRepository,
    MS: MfaService,
{
    pub fn new(user_repository: UR, mfa_service: MS) -> Self {
        Self {
            user_repository,
            mfa_service,
        }
    }
}

#[async_trait::async_trait]
impl<UR, MS> AsyncCommandHandler<EnableMfaCommand> for EnableMfaHandler<UR, MS>
where
    UR: UserRepository,
    MS: MfaService,
{
    async fn handle(&self, command: EnableMfaCommand) -> ApplicationResult<MfaSetupData> {
        // Parse and validate input
        let user_id = UserId::from_string(command.user_id)
            .map_err(|e| ApplicationError::InvalidInput(format!("Invalid user_id: {e}")))?;

        // Load user
        let user = self
            .user_repository
            .find_by_id(&user_id)
            .await?
            .ok_or(ApplicationError::UserNotFound)?;

        // Handle different MFA types
        match command.mfa_type {
            MfaType::Totp => {
                // Generate TOTP secret
                let secret = self.mfa_service.generate_totp_secret().await?;

                // Generate QR code for easy setup
                let qr_code = self
                    .mfa_service
                    .generate_qr_code(&secret, user.email().as_str())
                    .await?;

                // TODO: In a full implementation:
                // 1. Save the TOTP secret to the user entity or MFA settings
                // 2. Mark TOTP as enabled (pending verification)
                // 3. Save updated user to repository
                // 4. Emit domain events

                Ok(MfaSetupData::Totp { secret, qr_code })
            }
            MfaType::Sms { phone_number } => {
                // Validate phone number format
                if !self
                    .mfa_service
                    .validate_phone_number(&phone_number)
                    .await?
                {
                    return Err(ApplicationError::InvalidInput(
                        "Invalid phone number format".to_string(),
                    ));
                }

                // Send verification code
                let verification_sent = self
                    .mfa_service
                    .send_sms_verification(&phone_number)
                    .await?;

                // TODO: In a full implementation:
                // 1. Save the phone number to the user entity or MFA settings
                // 2. Mark SMS MFA as enabled (pending verification)
                // 3. Store verification code temporarily
                // 4. Save updated user to repository
                // 5. Emit domain events

                Ok(MfaSetupData::Sms {
                    phone_number,
                    verification_code_sent: verification_sent,
                })
            }
            MfaType::Email => {
                // Send email verification code
                let verification_sent = self
                    .mfa_service
                    .send_email_verification(user.email().as_str())
                    .await?;

                // TODO: In a full implementation:
                // 1. Mark email MFA as enabled (pending verification)
                // 2. Store verification code temporarily
                // 3. Save updated user to repository
                // 4. Emit domain events

                Ok(MfaSetupData::Email {
                    email: user.email().as_str().to_string(),
                    verification_code_sent: verification_sent,
                })
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::{
        entities::User,
        errors::DomainError,
        value_objects::{Email, Password, UserId},
    };
    use std::collections::HashMap;

    // Simple mock repositories for testing
    struct TestUserRepo {
        users: HashMap<UserId, User>,
        should_fail: bool,
    }

    impl TestUserRepo {
        fn new() -> Self {
            Self {
                users: HashMap::new(),
                should_fail: false,
            }
        }

        fn with_user(mut self, user: User) -> Self {
            self.users.insert(user.id().clone(), user);
            self
        }

        #[allow(dead_code)]
        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }
    }

    #[async_trait::async_trait]
    impl UserRepository for TestUserRepo {
        async fn save(&self, _user: &User) -> Result<(), DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Repository error".to_string()));
            }
            Ok(())
        }

        async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
            if self.should_fail {
                return Err(DomainError::InvalidInput("Repository error".to_string()));
            }
            Ok(self.users.get(id).cloned())
        }

        async fn find_by_email(&self, _email: &Email) -> Result<Option<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
            unimplemented!()
        }

        async fn exists_by_email(&self, _email: &Email) -> Result<bool, DomainError> {
            unimplemented!()
        }

        async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn count(&self) -> Result<usize, DomainError> {
            unimplemented!()
        }

        async fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        async fn search_users(
            &self,
            _criteria: &auth_domain::repositories::UserSearchCriteria,
        ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
            unimplemented!()
        }
    }

    struct TestMfaService {
        should_fail: bool,
        phone_validation_result: bool,
    }

    impl TestMfaService {
        fn new() -> Self {
            Self {
                should_fail: false,
                phone_validation_result: true,
            }
        }

        #[allow(dead_code)]
        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }

        fn with_invalid_phone(mut self) -> Self {
            self.phone_validation_result = false;
            self
        }
    }

    #[async_trait::async_trait]
    impl MfaService for TestMfaService {
        async fn generate_totp_secret(&self) -> Result<String, ApplicationError> {
            if self.should_fail {
                return Err(ApplicationError::ExternalService(
                    "TOTP service error".to_string(),
                ));
            }
            Ok("JBSWY3DPEHPK3PXP".to_string())
        }

        async fn generate_qr_code(
            &self,
            _secret: &str,
            _user_email: &str,
        ) -> Result<String, ApplicationError> {
            if self.should_fail {
                return Err(ApplicationError::ExternalService(
                    "QR code generation error".to_string(),
                ));
            }
            Ok("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...".to_string())
        }

        async fn send_sms_verification(
            &self,
            _phone_number: &str,
        ) -> Result<bool, ApplicationError> {
            if self.should_fail {
                return Err(ApplicationError::ExternalService(
                    "SMS service error".to_string(),
                ));
            }
            Ok(true)
        }

        async fn send_email_verification(&self, _email: &str) -> Result<bool, ApplicationError> {
            if self.should_fail {
                return Err(ApplicationError::ExternalService(
                    "Email service error".to_string(),
                ));
            }
            Ok(true)
        }

        async fn validate_phone_number(
            &self,
            _phone_number: &str,
        ) -> Result<bool, ApplicationError> {
            if self.should_fail {
                return Err(ApplicationError::ExternalService(
                    "Phone validation error".to_string(),
                ));
            }
            Ok(self.phone_validation_result)
        }
    }

    #[tokio::test]
    async fn should_enable_totp_mfa_successfully() {
        // Arrange
        let user = create_test_user("<EMAIL>");
        let user_repo = TestUserRepo::new().with_user(user.clone());
        let mfa_service = TestMfaService::new();

        let handler = EnableMfaHandler::new(user_repo, mfa_service);
        let command = EnableMfaCommand {
            user_id: user.id().as_str().to_string(),
            mfa_type: MfaType::Totp,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(result.is_ok());
        let setup_data = result.unwrap();
        match setup_data {
            MfaSetupData::Totp { secret, qr_code } => {
                assert_eq!(secret, "JBSWY3DPEHPK3PXP");
                assert!(qr_code.starts_with("data:image/png;base64,"));
            }
            _ => panic!("Expected TOTP setup data"),
        }
    }

    #[tokio::test]
    async fn should_enable_sms_mfa_successfully() {
        // Arrange
        let user = create_test_user("<EMAIL>");
        let user_repo = TestUserRepo::new().with_user(user.clone());
        let mfa_service = TestMfaService::new();

        let handler = EnableMfaHandler::new(user_repo, mfa_service);
        let command = EnableMfaCommand {
            user_id: user.id().as_str().to_string(),
            mfa_type: MfaType::Sms {
                phone_number: "+1234567890".to_string(),
            },
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(result.is_ok());
        let setup_data = result.unwrap();
        match setup_data {
            MfaSetupData::Sms {
                phone_number,
                verification_code_sent,
            } => {
                assert_eq!(phone_number, "+1234567890");
                assert!(verification_code_sent);
            }
            _ => panic!("Expected SMS setup data"),
        }
    }

    #[tokio::test]
    async fn should_enable_email_mfa_successfully() {
        // Arrange
        let user = create_test_user("<EMAIL>");
        let user_repo = TestUserRepo::new().with_user(user.clone());
        let mfa_service = TestMfaService::new();

        let handler = EnableMfaHandler::new(user_repo, mfa_service);
        let command = EnableMfaCommand {
            user_id: user.id().as_str().to_string(),
            mfa_type: MfaType::Email,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(result.is_ok());
        let setup_data = result.unwrap();
        match setup_data {
            MfaSetupData::Email {
                email,
                verification_code_sent,
            } => {
                assert_eq!(email, "<EMAIL>");
                assert!(verification_code_sent);
            }
            _ => panic!("Expected Email setup data"),
        }
    }

    #[tokio::test]
    async fn should_fail_when_user_not_found() {
        // Arrange
        let user_repo = TestUserRepo::new();
        let mfa_service = TestMfaService::new();

        let handler = EnableMfaHandler::new(user_repo, mfa_service);
        let command = EnableMfaCommand {
            user_id: "nonexistent_user".to_string(),
            mfa_type: MfaType::Totp,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::UserNotFound)));
    }

    #[tokio::test]
    async fn should_fail_with_invalid_phone_number() {
        // Arrange
        let user = create_test_user("<EMAIL>");
        let user_repo = TestUserRepo::new().with_user(user.clone());
        let mfa_service = TestMfaService::new().with_invalid_phone();

        let handler = EnableMfaHandler::new(user_repo, mfa_service);
        let command = EnableMfaCommand {
            user_id: user.id().as_str().to_string(),
            mfa_type: MfaType::Sms {
                phone_number: "invalid-phone".to_string(),
            },
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::InvalidInput(_))));
    }

    #[tokio::test]
    async fn should_fail_with_mfa_service_error() {
        // Arrange
        let user = create_test_user("<EMAIL>");
        let user_repo = TestUserRepo::new().with_user(user.clone());
        let mfa_service = TestMfaService::new().with_failure();

        let handler = EnableMfaHandler::new(user_repo, mfa_service);
        let command = EnableMfaCommand {
            user_id: user.id().as_str().to_string(),
            mfa_type: MfaType::Totp,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::ExternalService(_))));
    }

    #[tokio::test]
    async fn should_fail_with_repository_error() {
        // Arrange
        let user_repo = TestUserRepo::new().with_failure();
        let mfa_service = TestMfaService::new();

        let handler = EnableMfaHandler::new(user_repo, mfa_service);
        let command = EnableMfaCommand {
            user_id: "user_123".to_string(),
            mfa_type: MfaType::Email,
        };

        // Act
        let result = handler.handle(command).await;

        // Assert
        assert!(matches!(result, Err(ApplicationError::Domain(_))));
    }

    // Helper function
    fn create_test_user(email: &str) -> User {
        let email = Email::new(email).unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        User::new(email, password).unwrap()
    }
}
