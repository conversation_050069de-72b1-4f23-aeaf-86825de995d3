// Command handlers module
// Contains handlers for write operations (commands)

pub mod assign_role;
pub mod authenticate_user;
pub mod base;
pub mod enable_mfa;
pub mod register_user;
pub mod register_user_simple;

pub use assign_role::{Assign<PERSON><PERSON>Command, AssignRoleHandler};
pub use authenticate_user::{
    <PERSON>th<PERSON><PERSON>, AuthenticateU<PERSON>Command, Authenticate<PERSON>serHandler,
    TransactionalAuthenticateUserHandler,
};
pub use base::*;
pub use enable_mfa::{EnableMfaCommand, EnableMfaHandler, MfaSetupData, MfaType};
pub use register_user::{
    Register<PERSON>ser<PERSON>ommand, Register<PERSON>serHandler, TransactionalRegisterUserHandler,
};
pub use register_user_simple::{
    RegisterUserCommand as SimpleRegisterU<PERSON>Command,
    RegisterUserHandler as SimpleRegisterUserHandler,
};
