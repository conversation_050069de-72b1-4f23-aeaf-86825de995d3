// Testing utilities and mocks for application layer
// This module provides comprehensive testing infrastructure for CQRS command handlers

// Core testing utilities
pub mod helpers;
pub mod mocks;

// Advanced testing modules - temporarily disabled for build stability
// pub mod fixtures;
// pub mod integration_tests;
// pub mod security;
// pub mod security_attack_scenarios;
// pub mod security_benchmarks;
// pub mod transactional_tests;

// Re-export core testing utilities
pub use helpers::*;
pub use mocks::*;

// Advanced re-exports - temporarily disabled
// pub use fixtures::*;
// pub use security::*;
// pub use security_attack_scenarios::*;
// pub use security_benchmarks::*;
