// Test fixtures for commands and expected responses
// Provides reusable test data for different scenarios

use auth_domain::{
    entities::{Session, User},
    value_objects::{UserId, RoleId},
};
use std::collections::HashMap;

/// Command fixtures for testing different scenarios
pub mod commands {
    use super::*;

    /// RegisterUserCommand test fixtures
    pub mod register_user {
        #[derive(Debu<PERSON>, <PERSON>lone)]
        pub struct TestRegisterUserCommand {
            pub email: String,
            pub username: String,
            pub password: String,
        }

        impl TestRegisterUserCommand {
            pub fn valid() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    username: "newuser".to_string(),
                    password: "SecurePass123!".to_string(),
                }
            }

            pub fn with_weak_password() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    username: "weakuser".to_string(),
                    password: "123".to_string(),
                }
            }

            pub fn with_invalid_email() -> Self {
                Self {
                    email: "invalid-email".to_string(),
                    username: "validuser".to_string(),
                    password: "SecurePass123!".to_string(),
                }
            }

            pub fn with_existing_email() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    username: "newuser".to_string(),
                    password: "SecurePass123!".to_string(),
                }
            }

            pub fn with_existing_username() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    username: "existinguser".to_string(),
                    password: "SecurePass123!".to_string(),
                }
            }

            pub fn with_empty_fields() -> Self {
                Self {
                    email: "".to_string(),
                    username: "".to_string(),
                    password: "".to_string(),
                }
            }

            pub fn with_sql_injection() -> Self {
                Self {
                    email: "test'; DROP TABLE users; --@example.com".to_string(),
                    username: "admin'; DROP TABLE users; --".to_string(),
                    password: "password'; DROP TABLE users; --".to_string(),
                }
            }

            pub fn with_xss_attempt() -> Self {
                Self {
                    email: "<script>alert('xss')</script>@example.com".to_string(),
                    username: "<script>alert('xss')</script>".to_string(),
                    password: "<script>alert('xss')</script>".to_string(),
                }
            }
        }
    }

    /// AuthenticateUserCommand test fixtures
    pub mod authenticate_user {
        #[derive(Debug, Clone)]
        pub struct TestAuthenticateUserCommand {
            pub email: String,
            pub password: String,
            pub ip_address: String,
            pub user_agent: String,
        }

        impl TestAuthenticateUserCommand {
            pub fn valid() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    password: "correctpassword".to_string(),
                    ip_address: "***********".to_string(),
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                        .to_string(),
                }
            }

            pub fn with_wrong_password() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    password: "wrongpassword".to_string(),
                    ip_address: "***********".to_string(),
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                        .to_string(),
                }
            }

            pub fn with_nonexistent_user() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    password: "anypassword".to_string(),
                    ip_address: "***********".to_string(),
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                        .to_string(),
                }
            }

            pub fn with_suspicious_ip() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    password: "correctpassword".to_string(),
                    ip_address: "192.168.999.999".to_string(), // Invalid IP
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                        .to_string(),
                }
            }

            pub fn with_bot_user_agent() -> Self {
                Self {
                    email: "<EMAIL>".to_string(),
                    password: "correctpassword".to_string(),
                    ip_address: "***********".to_string(),
                    user_agent: "curl/7.68.0".to_string(),
                }
            }

            pub fn brute_force_attempts() -> Vec<Self> {
                (0..10)
                    .map(|i| Self {
                        email: "<EMAIL>".to_string(),
                        password: format!("attempt{}", i),
                        ip_address: "***********00".to_string(),
                        user_agent: "AttackBot/1.0".to_string(),
                    })
                    .collect()
            }
        }
    }

    /// AssignRoleCommand test fixtures
    pub mod assign_role {
        use super::*;

        #[derive(Debug, Clone)]
        pub struct TestAssignRoleCommand {
            pub user_id: String,
            pub role_id: String,
            pub assigned_by: String,
        }

        impl TestAssignRoleCommand {
            pub fn valid() -> Self {
                Self {
                    user_id: UserId::new().to_string(),
                    role_id: RoleId::builtin("user").unwrap().to_string(),
                    assigned_by: UserId::new().to_string(),
                }
            }

            pub fn admin_role() -> Self {
                Self {
                    user_id: UserId::new().to_string(),
                    role_id: RoleId::builtin("admin").unwrap().to_string(),
                    assigned_by: UserId::new().to_string(),
                }
            }

            pub fn with_nonexistent_user() -> Self {
                Self {
                    user_id: "nonexistent_user_id".to_string(),
                    role_id: RoleId::builtin("user").unwrap().to_string(),
                    assigned_by: UserId::new().to_string(),
                }
            }

            pub fn with_nonexistent_role() -> Self {
                Self {
                    user_id: UserId::new().to_string(),
                    role_id: "nonexistent_role_id".to_string(),
                    assigned_by: UserId::new().to_string(),
                }
            }

            pub fn privilege_escalation_attempt() -> Self {
                let regular_user = UserId::new();
                Self {
                    user_id: regular_user.to_string(),
                    role_id: RoleId::builtin("admin").unwrap().to_string(),
                    assigned_by: regular_user.to_string(), // Same user trying to assign admin to themselves
                }
            }
        }
    }
}

/// Response fixtures for expected test outcomes
pub mod responses {
    use super::*;
    use crate::testing::mocks::AuthToken;

    /// Expected successful authentication response
    pub fn valid_auth_token() -> AuthToken {
        AuthToken {
            access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test_token".to_string(),
            refresh_token: "refresh_token_example".to_string(),
            expires_in: 900, // 15 minutes
        }
    }

    /// Expected user ID after successful registration
    pub fn new_user_id() -> UserId {
        UserId::new()
    }

    /// Expected session data after login
    pub fn login_session(_user_id: UserId) -> Session {
        // This would use Session::new() from domain
        todo!("Session::new() implementation needed")
    }
}

/// Domain entity fixtures for testing
pub mod entities {
    use super::*;

    /// User entity fixtures
    pub mod users {
        use super::*;

        pub fn active_user() -> User {
            // This would use User::new() from domain
            todo!("User::new() implementation needed")
        }

        pub fn inactive_user() -> User {
            todo!("User::new() implementation needed")
        }

        pub fn locked_user() -> User {
            todo!("User::new() implementation needed")
        }

        pub fn unverified_user() -> User {
            todo!("User::new() implementation needed")
        }

        pub fn admin_user() -> User {
            todo!("User::new() implementation needed")
        }

        pub fn users_with_same_email() -> Vec<User> {
            // For testing uniqueness constraints
            vec![]
        }
    }

    /// Session entity fixtures
    pub mod sessions {
        use super::*;

        pub fn active_session(_user_id: UserId) -> Session {
            todo!("Session::new() implementation needed")
        }

        pub fn expired_session(_user_id: UserId) -> Session {
            todo!("Session::new() implementation needed")
        }

        pub fn multiple_sessions(user_id: UserId, count: usize) -> Vec<Session> {
            (0..count)
                .map(|_| active_session(user_id.clone()))
                .collect()
        }

        pub fn suspicious_sessions() -> Vec<Session> {
            // Sessions from different countries, unusual user agents, etc.
            vec![]
        }
    }

    /// Role entity fixtures
    pub mod roles {
        use super::*;
        use auth_domain::entities::{Permission, Role};

        pub fn admin_role() -> Role {
            Role::new(
                RoleId::builtin("admin").unwrap(),
                "Admin".to_string(),
                Some("Full system access".to_string()),
                vec![Permission::Admin],
            )
            .unwrap()
        }

        pub fn user_role() -> Role {
            Role::new(
                RoleId::builtin("user").unwrap(),
                "User".to_string(),
                Some("Basic user access".to_string()),
                vec![Permission::Read],
            )
            .unwrap()
        }

        pub fn moderator_role() -> Role {
            Role::new(
                RoleId::builtin("moderator").unwrap(),
                "Moderator".to_string(),
                Some("Content moderation access".to_string()),
                vec![Permission::Read, Permission::Write, Permission::Delete],
            )
            .unwrap()
        }

        pub fn custom_role() -> Role {
            Role::new(
                RoleId::generate(),
                "CustomRole".to_string(),
                Some("Custom business role".to_string()),
                vec![
                    Permission::Read,
                    Permission::Custom("custom_permission".to_string()),
                ],
            )
            .unwrap()
        }

        pub fn role_hierarchy() -> Vec<Role> {
            // Create a hierarchy: admin -> moderator -> user
            let admin = admin_role();
            let moderator = moderator_role();
            let user = user_role();

            // For now, return basic hierarchy without parent relationships
            // until the domain model supports parent role functionality
            vec![admin, moderator, user]
        }

        pub fn inactive_role() -> Role {
            let mut role = user_role();
            role.deactivate();
            role
        }
    }
}

/// Error scenarios for testing error handling
pub mod errors {
    use super::*;

    /// Common error scenarios for registration
    pub mod registration {
        use crate::errors::ApplicationError;
        use auth_domain::errors::DomainError;

        pub fn email_already_exists() -> ApplicationError {
            ApplicationError::EmailAlreadyExists
        }

        pub fn username_already_exists() -> ApplicationError {
            ApplicationError::UsernameAlreadyExists
        }

        pub fn weak_password() -> ApplicationError {
            ApplicationError::Domain(DomainError::InvalidPassword(
                "Password does not meet security requirements".to_string(),
            ))
        }

        pub fn invalid_email() -> ApplicationError {
            ApplicationError::Domain(DomainError::InvalidEmail(
                "Invalid email format".to_string(),
            ))
        }
    }

    /// Common error scenarios for authentication
    pub mod authentication {
        use crate::errors::ApplicationError;
        use auth_domain::errors::DomainError;

        pub fn invalid_credentials() -> ApplicationError {
            ApplicationError::InvalidCredentials
        }

        pub fn account_locked() -> ApplicationError {
            ApplicationError::AccountLocked
        }

        pub fn user_not_found() -> ApplicationError {
            ApplicationError::UserNotFound
        }

        pub fn session_expired() -> ApplicationError {
            ApplicationError::Domain(DomainError::SessionExpired(
                "Session has expired".to_string(),
            ))
        }
    }

    /// Common error scenarios for authorization
    pub mod authorization {
        use crate::errors::ApplicationError;

        pub fn insufficient_permissions() -> ApplicationError {
            ApplicationError::InsufficientPermissions
        }

        pub fn role_not_found() -> ApplicationError {
            ApplicationError::RoleNotFound
        }
    }
}

/// Performance test scenarios
pub mod performance {
    use super::*;
    use std::time::Duration;

    /// Performance expectations for different operations
    pub struct PerformanceExpectations;

    impl PerformanceExpectations {
        /// Authentication should complete within 100ms
        pub const AUTH_MAX_DURATION: Duration = Duration::from_millis(100);

        /// Registration should complete within 200ms
        pub const REGISTRATION_MAX_DURATION: Duration = Duration::from_millis(200);

        /// Role assignment should complete within 50ms
        pub const ROLE_ASSIGNMENT_MAX_DURATION: Duration = Duration::from_millis(50);

        /// Database operations should complete within 50ms
        pub const DB_OPERATION_MAX_DURATION: Duration = Duration::from_millis(50);
    }

    /// Load test scenarios
    pub fn concurrent_registrations(
        count: usize,
    ) -> Vec<commands::register_user::TestRegisterUserCommand> {
        (0..count)
            .map(|i| commands::register_user::TestRegisterUserCommand {
                email: format!("user{}@example.com", i),
                username: format!("user{}", i),
                password: "SecurePass123!".to_string(),
            })
            .collect()
    }

    pub fn concurrent_logins(
        count: usize,
    ) -> Vec<commands::authenticate_user::TestAuthenticateUserCommand> {
        (0..count)
            .map(
                |i| commands::authenticate_user::TestAuthenticateUserCommand {
                    email: format!("user{}@example.com", i),
                    password: "correctpassword".to_string(),
                    ip_address: format!("192.168.1.{}", i % 255),
                    user_agent: "LoadTestClient/1.0".to_string(),
                },
            )
            .collect()
    }
}

/// Security test scenarios
pub mod security {
    use super::*;

    /// SQL injection attack patterns
    pub fn sql_injection_payloads() -> Vec<String> {
        vec![
            "'; DROP TABLE users; --".to_string(),
            "' OR '1'='1' --".to_string(),
            "'; SELECT * FROM users WHERE ''='".to_string(),
            "' UNION SELECT username, password FROM users --".to_string(),
            "admin'; UPDATE users SET password='hacked' WHERE username='admin' --".to_string(),
        ]
    }

    /// XSS attack patterns
    pub fn xss_payloads() -> Vec<String> {
        vec![
            "<script>alert('xss')</script>".to_string(),
            "javascript:alert('xss')".to_string(),
            "<img src=x onerror=alert('xss')>".to_string(),
            "<svg onload=alert('xss')>".to_string(),
            "';alert('xss');//".to_string(),
        ]
    }

    /// Timing attack test data
    pub fn timing_attack_data() -> HashMap<String, String> {
        let mut data = HashMap::new();
        data.insert(
            "<EMAIL>".to_string(),
            "wrongpassword".to_string(),
        );
        data.insert(
            "<EMAIL>".to_string(),
            "anypassword".to_string(),
        );
        data
    }

    /// Rate limiting test scenarios
    pub fn rate_limit_test_requests(
        count: usize,
    ) -> Vec<commands::authenticate_user::TestAuthenticateUserCommand> {
        (0..count)
            .map(
                |_| commands::authenticate_user::TestAuthenticateUserCommand {
                    email: "<EMAIL>".to_string(),
                    password: "anypassword".to_string(),
                    ip_address: "***********00".to_string(),
                    user_agent: "AttackBot/1.0".to_string(),
                },
            )
            .collect()
    }

    /// Privilege escalation test scenarios
    pub fn privilege_escalation_attempts() -> Vec<commands::assign_role::TestAssignRoleCommand> {
        vec![
            // Regular user trying to assign admin role to themselves
            commands::assign_role::TestAssignRoleCommand::privilege_escalation_attempt(),
            // Trying to assign role with malicious role ID
            commands::assign_role::TestAssignRoleCommand {
                user_id: UserId::new().to_string(),
                role_id: "'; DROP TABLE roles; --".to_string(),
                assigned_by: UserId::new().to_string(),
            },
        ]
    }
}
