// Integration tests for transaction management and event publishing
// Tests the proper coordination between data changes and event publishing

use crate::{
    commands::{
        authenticate_user::{
            AuthToken, AuthenticateUserCommand, PasswordService, TokenService,
            TransactionalAuthenticateUserHandler,
        },
        register_user::{RegisterUserCommand, TransactionalRegisterUserHandler},
    },
    errors::ApplicationError,
    transaction::{InMemoryTransactionManager, MockEventPublisher, Transaction, UnitOfWork},
};
use auth_domain::{
    entities::User,
    errors::DomainError,
    events::{
        DomainEvent,
        user_logged_in::{AuthenticationMethod, UserLoggedIn},
        user_registered::UserRegistered,
    },
    repositories::{SessionRepository, UserRepository},
    value_objects::{Email, Password, SessionId, UserId},
};
use std::{collections::HashMap, sync::Arc};

// Mock repository implementations for testing
#[derive(Clone)]
pub struct TestUserRepository {
    users: Arc<std::sync::Mutex<HashMap<String, User>>>,
    should_fail_save: bool,
    should_fail_find: bool,
}

impl TestUserRepository {
    pub fn new() -> Self {
        Self {
            users: Arc::new(std::sync::Mutex::new(HashMap::new())),
            should_fail_save: false,
            should_fail_find: false,
        }
    }

    pub fn with_save_failure() -> Self {
        Self {
            users: Arc::new(std::sync::Mutex::new(HashMap::new())),
            should_fail_save: true,
            should_fail_find: false,
        }
    }

    pub fn with_find_failure() -> Self {
        Self {
            users: Arc::new(std::sync::Mutex::new(HashMap::new())),
            should_fail_save: false,
            should_fail_find: true,
        }
    }

    pub fn user_count(&self) -> usize {
        self.users.lock().unwrap().len()
    }

    pub fn add_user(&self, user: User) {
        self.users
            .lock()
            .unwrap()
            .insert(user.email().as_str().to_string(), user);
    }
}

#[async_trait::async_trait]
impl UserRepository for TestUserRepository {
    async fn save(&self, user: &User) -> Result<(), DomainError> {
        if self.should_fail_save {
            return Err(DomainError::InvalidInput(
                "Save operation failed".to_string(),
            ));
        }
        self.users
            .lock()
            .unwrap()
            .insert(user.email().as_str().to_string(), user.clone());
        Ok(())
    }

    async fn find_by_id(&self, _id: &UserId) -> Result<Option<User>, DomainError> {
        if self.should_fail_find {
            return Err(DomainError::InvalidInput(
                "Find operation failed".to_string(),
            ));
        }
        // Simplified implementation for testing
        Ok(None)
    }

    async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
        if self.should_fail_find {
            return Err(DomainError::InvalidInput(
                "Find operation failed".to_string(),
            ));
        }
        Ok(self.users.lock().unwrap().get(email.as_str()).cloned())
    }

    async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
        unimplemented!()
    }

    async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
        Ok(self.users.lock().unwrap().contains_key(email.as_str()))
    }

    async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
        unimplemented!()
    }

    async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
        unimplemented!()
    }

    async fn count(&self) -> Result<usize, DomainError> {
        Ok(self.users.lock().unwrap().len())
    }

    async fn find_by_created_date_range(
        &self,
        _start: std::time::SystemTime,
        _end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError> {
        unimplemented!()
    }

    async fn find_by_verification_status(
        &self,
        _is_verified: bool,
    ) -> Result<Vec<User>, DomainError> {
        unimplemented!()
    }

    async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
        unimplemented!()
    }

    async fn search_users(
        &self,
        _criteria: &auth_domain::repositories::UserSearchCriteria,
    ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
        unimplemented!()
    }
}

#[derive(Clone)]
pub struct TestSessionRepository {
    should_fail: bool,
}

impl TestSessionRepository {
    pub fn new() -> Self {
        Self { should_fail: false }
    }

    pub fn with_failure() -> Self {
        Self { should_fail: true }
    }
}

#[async_trait::async_trait]
impl SessionRepository for TestSessionRepository {
    async fn save(&self, _session: &auth_domain::entities::Session) -> Result<(), DomainError> {
        if self.should_fail {
            return Err(DomainError::InvalidInput("Session save failed".to_string()));
        }
        Ok(())
    }

    async fn find_by_id(
        &self,
        _id: &SessionId,
    ) -> Result<Option<auth_domain::entities::Session>, DomainError> {
        unimplemented!()
    }

    async fn find_active_by_user(
        &self,
        _user_id: &UserId,
    ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
        unimplemented!()
    }

    async fn find_all_by_user(
        &self,
        _user_id: &UserId,
    ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
        unimplemented!()
    }

    async fn delete(&self, _id: &SessionId) -> Result<(), DomainError> {
        unimplemented!()
    }

    async fn delete_all_by_user(&self, _user_id: &UserId) -> Result<(), DomainError> {
        unimplemented!()
    }

    async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError> {
        unimplemented!()
    }

    async fn count_active_by_user(&self, _user_id: &UserId) -> Result<usize, DomainError> {
        unimplemented!()
    }

    async fn find_by_ip_address(
        &self,
        _ip_address: &str,
    ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
        unimplemented!()
    }

    async fn find_by_created_date_range(
        &self,
        _start: std::time::SystemTime,
        _end: std::time::SystemTime,
    ) -> Result<Vec<auth_domain::entities::Session>, DomainError> {
        unimplemented!()
    }

    async fn update_last_accessed(&self, _id: &SessionId) -> Result<(), DomainError> {
        unimplemented!()
    }

    async fn invalidate(&self, _id: &SessionId) -> Result<(), DomainError> {
        unimplemented!()
    }

    async fn invalidate_all_except(
        &self,
        _user_id: &UserId,
        _except_session_id: &SessionId,
    ) -> Result<(), DomainError> {
        unimplemented!()
    }
}

// Mock services for authentication testing
#[derive(Clone)]
pub struct TestPasswordService {
    should_succeed: bool,
}

impl TestPasswordService {
    pub fn new(should_succeed: bool) -> Self {
        Self { should_succeed }
    }
}

#[async_trait::async_trait]
impl PasswordService for TestPasswordService {
    async fn verify_password(
        &self,
        _user: &User,
        _password: &str,
    ) -> Result<bool, ApplicationError> {
        Ok(self.should_succeed)
    }
}

#[derive(Clone)]
pub struct TestTokenService {
    should_succeed: bool,
}

impl TestTokenService {
    pub fn new(should_succeed: bool) -> Self {
        Self { should_succeed }
    }
}

#[async_trait::async_trait]
impl TokenService for TestTokenService {
    async fn generate_tokens(
        &self,
        _user_id: &str,
        _session_id: &str,
    ) -> Result<AuthToken, ApplicationError> {
        if self.should_succeed {
            Ok(AuthToken {
                access_token: "test_access_token".to_string(),
                refresh_token: "test_refresh_token".to_string(),
                expires_in: 3600,
            })
        } else {
            Err(ApplicationError::Internal(
                "Token generation failed".to_string(),
            ))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn should_register_user_and_publish_event_successfully() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::new();
        let event_publisher = Arc::new(MockEventPublisher::new());

        let handler = TransactionalRegisterUserHandler::new(
            transaction_manager,
            user_repository.clone(),
            event_publisher.clone(),
        );

        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = handler.execute(command).await;

        // Assert
        assert!(result.is_ok());
        assert_eq!(user_repository.user_count(), 1);
        assert_eq!(event_publisher.event_count(), 1);

        let events = event_publisher.published_events();
        match &events[0] {
            DomainEvent::UserRegistered { user_id, email, .. } => {
                assert_eq!(email.as_str(), "<EMAIL>");
                assert!(!user_id.to_string().is_empty());
            }
            _ => panic!("Expected UserRegistered event"),
        }
    }

    #[tokio::test]
    async fn should_rollback_registration_on_repository_failure() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::with_save_failure();
        let event_publisher = Arc::new(MockEventPublisher::new());

        let handler = TransactionalRegisterUserHandler::new(
            transaction_manager,
            user_repository.clone(),
            event_publisher.clone(),
        );

        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = handler.execute(command).await;

        // Assert
        assert!(result.is_err());
        assert_eq!(user_repository.user_count(), 0); // Should not save user
        assert_eq!(event_publisher.event_count(), 0); // Should not publish events
    }

    #[tokio::test]
    async fn should_commit_data_but_return_error_on_event_publishing_failure() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::new();
        let event_publisher = Arc::new(MockEventPublisher::with_failure());

        let handler = TransactionalRegisterUserHandler::new(
            transaction_manager,
            user_repository.clone(),
            event_publisher.clone(),
        );

        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = handler.execute(command).await;

        // Assert
        assert!(result.is_err());
        // Data should still be committed even if event publishing fails
        assert_eq!(user_repository.user_count(), 1);
        // Event publishing should have been attempted but failed
        assert_eq!(event_publisher.event_count(), 0);

        match result.unwrap_err() {
            ApplicationError::EventPublishingFailed(_) => {} // Expected
            _ => panic!("Expected EventPublishingFailed error"),
        }
    }

    #[tokio::test]
    async fn should_prevent_duplicate_email_registration() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::new();
        let event_publisher = Arc::new(MockEventPublisher::new());

        // Add existing user
        let existing_user = User::new(
            Email::new("<EMAIL>").unwrap(),
            Password::new("ExistingPass123!").unwrap(),
        )
        .unwrap();
        user_repository.add_user(existing_user);

        let handler = TransactionalRegisterUserHandler::new(
            transaction_manager,
            user_repository.clone(),
            event_publisher.clone(),
        );

        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = handler.execute(command).await;

        // Assert
        assert!(result.is_err());
        assert_eq!(user_repository.user_count(), 1); // Only the original user
        assert_eq!(event_publisher.event_count(), 0); // No events published

        match result.unwrap_err() {
            ApplicationError::EmailAlreadyExists => {} // Expected
            _ => panic!("Expected EmailAlreadyExists error"),
        }
    }

    #[tokio::test]
    async fn should_authenticate_user_and_publish_event_successfully() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::new();
        let session_repository = TestSessionRepository::new();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(true);
        let event_publisher = Arc::new(MockEventPublisher::new());

        // Add existing user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let user = User::new(email.clone(), password).unwrap();
        user_repository.add_user(user);

        let handler = TransactionalAuthenticateUserHandler::new(
            transaction_manager,
            user_repository,
            session_repository,
            password_service,
            token_service,
            event_publisher.clone(),
        );

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: Some("127.0.0.1".to_string()),
            user_agent: Some("Test Agent".to_string()),
        };

        // Act
        let result = handler.execute(command).await;

        // Assert
        assert!(result.is_ok());
        let auth_token = result.unwrap();
        assert_eq!(auth_token.access_token, "test_access_token");
        assert_eq!(auth_token.refresh_token, "test_refresh_token");
        assert_eq!(auth_token.expires_in, 3600);

        assert_eq!(event_publisher.event_count(), 1);
        let events = event_publisher.published_events();
        match &events[0] {
            DomainEvent::UserLoggedIn {
                authentication_method,
                ..
            } => {
                assert_eq!(*authentication_method, AuthenticationMethod::Password);
            }
            _ => panic!("Expected UserLoggedIn event"),
        }
    }

    #[tokio::test]
    async fn should_rollback_authentication_on_session_save_failure() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::new();
        let session_repository = TestSessionRepository::with_failure();
        let password_service = TestPasswordService::new(true);
        let token_service = TestTokenService::new(true);
        let event_publisher = Arc::new(MockEventPublisher::new());

        // Add existing user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let user = User::new(email.clone(), password).unwrap();
        user_repository.add_user(user);

        let handler = TransactionalAuthenticateUserHandler::new(
            transaction_manager,
            user_repository,
            session_repository,
            password_service,
            token_service,
            event_publisher.clone(),
        );

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            ip_address: Some("127.0.0.1".to_string()),
            user_agent: Some("Test Agent".to_string()),
        };

        // Act
        let result = handler.execute(command).await;

        // Assert
        assert!(result.is_err());
        assert_eq!(event_publisher.event_count(), 0); // No events should be published

        match result.unwrap_err() {
            ApplicationError::Repository(_) => {} // Expected
            _ => panic!("Expected Repository error"),
        }
    }

    #[tokio::test]
    async fn should_fail_authentication_with_invalid_credentials() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::new();
        let session_repository = TestSessionRepository::new();
        let password_service = TestPasswordService::new(false); // Password verification fails
        let token_service = TestTokenService::new(true);
        let event_publisher = Arc::new(MockEventPublisher::new());

        // Add existing user with different password
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("CorrectPassword123!").unwrap();
        let user = User::new(email.clone(), password).unwrap();
        user_repository.add_user(user);

        let handler = TransactionalAuthenticateUserHandler::new(
            transaction_manager,
            user_repository,
            session_repository,
            password_service,
            token_service,
            event_publisher.clone(),
        );

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "WrongPassword123!".to_string(),
            ip_address: Some("127.0.0.1".to_string()),
            user_agent: Some("Test Agent".to_string()),
        };

        // Act
        let result = handler.execute(command).await;

        // Assert
        assert!(result.is_err());
        assert_eq!(event_publisher.event_count(), 0); // No events should be published

        match result.unwrap_err() {
            ApplicationError::InvalidCredentials => {} // Expected
            _ => panic!("Expected InvalidCredentials error"),
        }
    }

    #[tokio::test]
    async fn should_handle_multiple_transactions_concurrently() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let user_repository = TestUserRepository::new();
        let event_publisher = Arc::new(MockEventPublisher::new());

        let handler = Arc::new(TransactionalRegisterUserHandler::new(
            transaction_manager,
            user_repository.clone(),
            event_publisher.clone(),
        ));

        // Act - Execute multiple registrations concurrently
        let handles: Vec<_> = (0..5)
            .map(|i| {
                let handler = handler.clone();
                tokio::spawn(async move {
                    let command = RegisterUserCommand {
                        email: format!("user{}@example.com", i),
                        password: "SecurePass123!".to_string(),
                    };
                    handler.execute(command).await
                })
            })
            .collect();

        let results: Vec<_> = futures::future::join_all(handles)
            .await
            .into_iter()
            .map(|handle| handle.unwrap())
            .collect();

        // Assert
        for result in results {
            assert!(result.is_ok());
        }

        assert_eq!(user_repository.user_count(), 5);
        assert_eq!(event_publisher.event_count(), 5);
    }

    #[tokio::test]
    async fn should_maintain_event_order_in_batch_publishing() {
        // Arrange
        let transaction_manager = InMemoryTransactionManager::new();
        let event_publisher = Arc::new(MockEventPublisher::new());
        let uow = UnitOfWork::new(transaction_manager, event_publisher.clone());

        // Act - Add multiple events in sequence
        let result = uow
            .execute(|transaction| {
                Box::pin(async move {
                    transaction.add_event(DomainEvent::UserActivated {
                        user_id: UserId::new(),
                    });
                    transaction.add_event(DomainEvent::UserDeactivated {
                        user_id: UserId::new(),
                    });
                    transaction.add_event(DomainEvent::UserActivated {
                        user_id: UserId::new(),
                    });
                    Ok("success".to_string())
                })
            })
            .await;

        // Assert
        assert!(result.is_ok());
        assert_eq!(event_publisher.event_count(), 3);

        let events = event_publisher.published_events();
        assert!(matches!(events[0], DomainEvent::UserActivated { .. }));
        assert!(matches!(events[1], DomainEvent::UserDeactivated { .. }));
        assert!(matches!(events[2], DomainEvent::UserActivated { .. }));
    }
}
