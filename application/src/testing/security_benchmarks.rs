// Security performance benchmarks and stress testing
// Validates that security features maintain performance requirements

// Note: Some imports may not be available yet in current implementation
// use crate::commands::authenticate_user::{AuthenticateUserCommand, AuthToken};
use crate::errors::ApplicationError;
use crate::security::{ConstantTimeAuthService, Error<PERSON><PERSON><PERSON>zer, RateLimiter, RateLimiterConfig};
use crate::testing::security::AuthToken; // Use temporary mock type
use crate::testing::security::SecurityTestConfig;
use auth_domain::{
    crypto::ConstantTimeService,
    entities::User,
    errors::DomainError,
    value_objects::{Email, Password},
};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::task::JoinSet;

/// Security performance benchmark suite
pub struct SecurityBenchmarks;

impl SecurityBenchmarks {
    /// Benchmark constant-time authentication under load
    pub async fn benchmark_constant_time_auth_load() -> Result<BenchmarkResult, String> {
        let ct_service = ConstantTimeService::new();
        let auth_service = Arc::new(ConstantTimeAuthService::new(ct_service));

        // Create test user
        let email = Email::new("<EMAIL>")
            .map_err(|e| format!("Failed to create email: {}", e))?;
        let password = Password::new("BenchmarkPassword123!")
            .map_err(|e| format!("Failed to create password: {}", e))?;
        let mut user =
            User::new(email, password).map_err(|e| format!("Failed to create user: {}", e))?;
        user.verify()
            .map_err(|e| format!("Failed to verify user: {}", e))?;
        let user = Arc::new(user);

        // Benchmark parameters
        let concurrent_users = 50;
        let requests_per_user = 100;
        let total_requests = concurrent_users * requests_per_user;

        println!(
            "Starting constant-time auth benchmark: {} concurrent users, {} requests each",
            concurrent_users, requests_per_user
        );

        let start_time = Instant::now();
        let mut join_set = JoinSet::new();

        // Launch concurrent authentication attempts
        for user_id in 0..concurrent_users {
            let auth_service = auth_service.clone();
            let user = user.clone();

            join_set.spawn(async move {
                let mut user_timings = Vec::new();

                for request_id in 0..requests_per_user {
                    let password = if request_id % 2 == 0 {
                        "BenchmarkPassword123!" // Correct password
                    } else {
                        "WrongPassword123!" // Wrong password  
                    };

                    let request_start = Instant::now();
                    let _ = auth_service
                        .validate_authentication_attempt(
                            "<EMAIL>",
                            password,
                            Some(&*user),
                        )
                        .await;

                    user_timings.push(request_start.elapsed());
                }

                user_timings
            });
        }

        // Collect all results
        let mut all_timings = Vec::new();
        while let Some(result) = join_set.join_next().await {
            let user_timings = result.unwrap();
            all_timings.extend(user_timings);
        }

        let total_duration = start_time.elapsed();

        // Calculate statistics
        let avg_latency = all_timings.iter().sum::<Duration>() / all_timings.len() as u32;
        let min_latency = *all_timings.iter().min().unwrap();
        let max_latency = *all_timings.iter().max().unwrap();

        // Sort for percentile calculations
        let mut sorted_timings = all_timings.clone();
        sorted_timings.sort();

        let p95_index = (sorted_timings.len() as f64 * 0.95) as usize;
        let p99_index = (sorted_timings.len() as f64 * 0.99) as usize;
        let p95_latency = sorted_timings[p95_index.min(sorted_timings.len() - 1)];
        let p99_latency = sorted_timings[p99_index.min(sorted_timings.len() - 1)];

        let throughput = total_requests as f64 / total_duration.as_secs_f64();

        // Validate performance requirements
        if avg_latency > SecurityTestConfig::AUTH_WITH_SECURITY_MAX_DURATION {
            return Err(format!(
                "Average latency too high: {:?} > {:?}",
                avg_latency,
                SecurityTestConfig::AUTH_WITH_SECURITY_MAX_DURATION
            ));
        }

        // Check for timing consistency (security requirement)
        let timing_variance =
            max_latency.as_micros() as f64 / min_latency.as_micros().max(1) as f64;
        if timing_variance > SecurityTestConfig::MAX_TIMING_VARIANCE {
            return Err(format!(
                "Timing variance too high under load: {:.2} > {:.2}",
                timing_variance,
                SecurityTestConfig::MAX_TIMING_VARIANCE
            ));
        }

        Ok(BenchmarkResult {
            test_name: "Constant-Time Authentication Load Test".to_string(),
            total_requests,
            total_duration,
            throughput,
            avg_latency,
            min_latency,
            max_latency,
            p95_latency,
            p99_latency,
            success_rate: 100.0, // All requests should complete
        })
    }

    /// Benchmark rate limiting performance under attack
    pub async fn benchmark_rate_limiting_under_attack() -> Result<BenchmarkResult, String> {
        let config = RateLimiterConfig {
            max_attempts_per_ip: 10,
            max_attempts_per_account: 5,
            window_duration: Duration::from_secs(60),
            progressive_delay_multiplier: 1.5,
            max_progressive_delay: Duration::from_secs(5),
            strict_mode: true,
            progressive_delay_threshold: 3,
        };

        let rate_limiter = Arc::new(RateLimiter::with_config(config));

        // Simulate attack scenario
        let attack_ips = 100; // 100 attacking IPs
        let requests_per_ip = 50; // 50 requests per IP
        let total_requests = attack_ips * requests_per_ip;

        println!(
            "Starting rate limiting benchmark: {} attacking IPs, {} requests each",
            attack_ips, requests_per_ip
        );

        let start_time = Instant::now();
        let mut join_set = JoinSet::new();

        // Launch concurrent attack simulation
        for ip_id in 0..attack_ips {
            let rate_limiter = rate_limiter.clone();

            join_set.spawn(async move {
                let ip = format!("192.168.{}.{}", ip_id / 255, ip_id % 255);
                let ip_key = crate::security::rate_limiter::rate_limit_key_from_ip(Some(ip));

                let mut ip_timings = Vec::new();
                let mut successful_requests = 0;

                for _ in 0..requests_per_ip {
                    let request_start = Instant::now();
                    let result = rate_limiter.check_rate_limit(&ip_key).await;
                    let request_duration = request_start.elapsed();

                    ip_timings.push(request_duration);

                    if result.is_ok() {
                        successful_requests += 1;
                    }
                }

                (ip_timings, successful_requests)
            });
        }

        // Collect results
        let mut all_timings = Vec::new();
        let mut total_successful = 0;

        while let Some(result) = join_set.join_next().await {
            let (ip_timings, successful) = result.unwrap();
            all_timings.extend(ip_timings);
            total_successful += successful;
        }

        let total_duration = start_time.elapsed();

        // Calculate statistics
        let avg_latency = all_timings.iter().sum::<Duration>() / all_timings.len() as u32;
        let min_latency = *all_timings.iter().min().unwrap();
        let max_latency = *all_timings.iter().max().unwrap();

        let mut sorted_timings = all_timings.clone();
        sorted_timings.sort();

        let p95_index = (sorted_timings.len() as f64 * 0.95) as usize;
        let p99_index = (sorted_timings.len() as f64 * 0.99) as usize;
        let p95_latency = sorted_timings[p95_index.min(sorted_timings.len() - 1)];
        let p99_latency = sorted_timings[p99_index.min(sorted_timings.len() - 1)];

        let throughput = total_requests as f64 / total_duration.as_secs_f64();
        let success_rate = (total_successful as f64 / total_requests as f64) * 100.0;

        // Validate rate limiting is working
        if success_rate > 20.0 {
            // Should block most requests
            return Err(format!(
                "Rate limiting not effective: {:.1}% requests succeeded",
                success_rate
            ));
        }

        // Validate performance under attack
        if avg_latency > SecurityTestConfig::RATE_LIMIT_CHECK_MAX_DURATION * 2 {
            return Err(format!(
                "Rate limiting too slow under attack: {:?} > {:?}",
                avg_latency,
                SecurityTestConfig::RATE_LIMIT_CHECK_MAX_DURATION * 2
            ));
        }

        Ok(BenchmarkResult {
            test_name: "Rate Limiting Under Attack".to_string(),
            total_requests,
            total_duration,
            throughput,
            avg_latency,
            min_latency,
            max_latency,
            p95_latency,
            p99_latency,
            success_rate,
        })
    }

    /// Benchmark error sanitization performance
    pub fn benchmark_error_sanitization_throughput() -> Result<BenchmarkResult, String> {
        let sanitizer = ErrorSanitizer::new();

        // Create various error types to sanitize
        let test_errors = vec![
            ApplicationError::UserNotFound,
            ApplicationError::InvalidCredentials,
            ApplicationError::AccountLocked,
            ApplicationError::InsufficientPermissions,
            ApplicationError::Repository(
                "Database connection failed: ********************************/db".to_string(),
            ),
            ApplicationError::Repository(
                "SQL execution error: SELECT * FROM users WHERE password = 'secret123'".to_string(),
            ),
            ApplicationError::RateLimitExceeded,
            ApplicationError::Domain(DomainError::InvalidEmail(
                "Invalid email format".to_string(),
            )),
            ApplicationError::Domain(DomainError::InvalidPassword(
                "Password too weak".to_string(),
            )),
        ];

        let iterations_per_error = 10000;
        let total_requests = test_errors.len() * iterations_per_error;

        println!(
            "Starting error sanitization benchmark: {} error types, {} iterations each",
            test_errors.len(),
            iterations_per_error
        );

        let start_time = Instant::now();
        let mut all_timings = Vec::new();

        // Benchmark each error type
        for error in &test_errors {
            for _ in 0..iterations_per_error {
                let request_start = Instant::now();
                let _sanitized = sanitizer.sanitize(error);
                let request_duration = request_start.elapsed();
                all_timings.push(request_duration);
            }
        }

        let total_duration = start_time.elapsed();

        // Calculate statistics
        let avg_latency = all_timings.iter().sum::<Duration>() / all_timings.len() as u32;
        let min_latency = *all_timings.iter().min().unwrap();
        let max_latency = *all_timings.iter().max().unwrap();

        let mut sorted_timings = all_timings.clone();
        sorted_timings.sort();

        let p95_index = (sorted_timings.len() as f64 * 0.95) as usize;
        let p99_index = (sorted_timings.len() as f64 * 0.99) as usize;
        let p95_latency = sorted_timings[p95_index.min(sorted_timings.len() - 1)];
        let p99_latency = sorted_timings[p99_index.min(sorted_timings.len() - 1)];

        let throughput = total_requests as f64 / total_duration.as_secs_f64();

        // Validate performance
        if avg_latency > SecurityTestConfig::ERROR_SANITIZATION_MAX_DURATION {
            return Err(format!(
                "Error sanitization too slow: {:?} > {:?}",
                avg_latency,
                SecurityTestConfig::ERROR_SANITIZATION_MAX_DURATION
            ));
        }

        Ok(BenchmarkResult {
            test_name: "Error Sanitization Throughput".to_string(),
            total_requests,
            total_duration,
            throughput,
            avg_latency,
            min_latency,
            max_latency,
            p95_latency,
            p99_latency,
            success_rate: 100.0,
        })
    }

    /// Memory usage benchmark for security features
    pub async fn benchmark_memory_usage() -> Result<MemoryBenchmarkResult, String> {
        // This would ideally use a memory profiler, but for now we'll simulate
        // memory-intensive security operations and measure timing consistency

        let iterations = 1000;
        let ct_service = ConstantTimeService::new();
        let auth_service = ConstantTimeAuthService::new(ct_service);

        // Create test user
        let email = Email::new("<EMAIL>")
            .map_err(|e| format!("Failed to create email: {}", e))?;
        let password = Password::new("MemoryTestPassword123!")
            .map_err(|e| format!("Failed to create password: {}", e))?;
        let mut user =
            User::new(email, password).map_err(|e| format!("Failed to create user: {}", e))?;
        user.verify()
            .map_err(|e| format!("Failed to verify user: {}", e))?;

        let start_time = Instant::now();
        let mut timings = Vec::new();

        // Run many authentication attempts to test memory consistency
        for i in 0..iterations {
            let iteration_start = Instant::now();

            let _ = auth_service
                .validate_authentication_attempt(
                    "<EMAIL>",
                    "MemoryTestPassword123!",
                    Some(&user),
                )
                .await;

            let iteration_duration = iteration_start.elapsed();
            timings.push(iteration_duration);

            // Check for memory leaks by monitoring timing degradation
            if i > 100 && i % 100 == 0 {
                let recent_avg = timings[i - 100..].iter().sum::<Duration>() / 100;
                let early_avg = timings[0..100].iter().sum::<Duration>() / 100;

                let degradation_ratio =
                    recent_avg.as_micros() as f64 / early_avg.as_micros() as f64;

                if degradation_ratio > 1.5 {
                    return Err(format!(
                        "Potential memory leak detected: performance degraded by {:.2}x at iteration {}",
                        degradation_ratio, i
                    ));
                }
            }
        }

        let total_duration = start_time.elapsed();
        let avg_latency = timings.iter().sum::<Duration>() / timings.len() as u32;
        let min_latency = *timings.iter().min().unwrap();
        let max_latency = *timings.iter().max().unwrap();

        // Check timing consistency (should not degrade significantly)
        let first_100_avg = timings[0..100].iter().sum::<Duration>() / 100;
        let last_100_avg = timings[iterations - 100..].iter().sum::<Duration>() / 100;
        let consistency_ratio = last_100_avg.as_micros() as f64 / first_100_avg.as_micros() as f64;

        Ok(MemoryBenchmarkResult {
            test_name: "Memory Usage Consistency".to_string(),
            total_iterations: iterations,
            total_duration,
            avg_latency,
            min_latency,
            max_latency,
            consistency_ratio,
            memory_stable: consistency_ratio < 1.2, // Less than 20% degradation
        })
    }
}

/// Benchmark result structure
#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    pub test_name: String,
    pub total_requests: usize,
    pub total_duration: Duration,
    pub throughput: f64, // requests per second
    pub avg_latency: Duration,
    pub min_latency: Duration,
    pub max_latency: Duration,
    pub p95_latency: Duration,
    pub p99_latency: Duration,
    pub success_rate: f64, // percentage
}

impl BenchmarkResult {
    pub fn print_summary(&self) {
        println!("\n=== {} ===", self.test_name);
        println!("Total Requests: {}", self.total_requests);
        println!("Total Duration: {:.2}s", self.total_duration.as_secs_f64());
        println!("Throughput: {:.0} req/s", self.throughput);
        println!("Success Rate: {:.1}%", self.success_rate);
        println!("Latency Statistics:");
        println!(
            "  Average: {:.2}ms",
            self.avg_latency.as_secs_f64() * 1000.0
        );
        println!(
            "  Minimum: {:.2}ms",
            self.min_latency.as_secs_f64() * 1000.0
        );
        println!(
            "  Maximum: {:.2}ms",
            self.max_latency.as_secs_f64() * 1000.0
        );
        println!(
            "  95th percentile: {:.2}ms",
            self.p95_latency.as_secs_f64() * 1000.0
        );
        println!(
            "  99th percentile: {:.2}ms",
            self.p99_latency.as_secs_f64() * 1000.0
        );
    }
}

/// Memory benchmark result structure
#[derive(Debug, Clone)]
pub struct MemoryBenchmarkResult {
    pub test_name: String,
    pub total_iterations: usize,
    pub total_duration: Duration,
    pub avg_latency: Duration,
    pub min_latency: Duration,
    pub max_latency: Duration,
    pub consistency_ratio: f64,
    pub memory_stable: bool,
}

impl MemoryBenchmarkResult {
    pub fn print_summary(&self) {
        println!("\n=== {} ===", self.test_name);
        println!("Total Iterations: {}", self.total_iterations);
        println!("Total Duration: {:.2}s", self.total_duration.as_secs_f64());
        println!(
            "Memory Stability: {}",
            if self.memory_stable {
                "STABLE"
            } else {
                "UNSTABLE"
            }
        );
        println!("Consistency Ratio: {:.2}x", self.consistency_ratio);
        println!("Latency Statistics:");
        println!(
            "  Average: {:.2}ms",
            self.avg_latency.as_secs_f64() * 1000.0
        );
        println!(
            "  Minimum: {:.2}ms",
            self.min_latency.as_secs_f64() * 1000.0
        );
        println!(
            "  Maximum: {:.2}ms",
            self.max_latency.as_secs_f64() * 1000.0
        );
    }
}

/// Security stress testing utilities
pub struct SecurityStressTester;

impl SecurityStressTester {
    /// Run comprehensive security performance tests
    pub async fn run_security_stress_tests() -> Result<Vec<BenchmarkResult>, String> {
        let mut results = Vec::new();

        println!("Starting comprehensive security stress tests...\n");

        // Test 1: Constant-time authentication under load
        match SecurityBenchmarks::benchmark_constant_time_auth_load().await {
            Ok(result) => {
                result.print_summary();
                results.push(result);
            }
            Err(e) => return Err(format!("Constant-time auth benchmark failed: {}", e)),
        }

        // Test 2: Rate limiting under attack
        match SecurityBenchmarks::benchmark_rate_limiting_under_attack().await {
            Ok(result) => {
                result.print_summary();
                results.push(result);
            }
            Err(e) => return Err(format!("Rate limiting benchmark failed: {}", e)),
        }

        // Test 3: Error sanitization throughput
        match SecurityBenchmarks::benchmark_error_sanitization_throughput() {
            Ok(result) => {
                result.print_summary();
                results.push(result);
            }
            Err(e) => return Err(format!("Error sanitization benchmark failed: {}", e)),
        }

        // Test 4: Memory usage stability
        match SecurityBenchmarks::benchmark_memory_usage().await {
            Ok(result) => {
                result.print_summary();
            }
            Err(e) => return Err(format!("Memory usage benchmark failed: {}", e)),
        }

        println!("\nAll security stress tests completed successfully!");
        Ok(results)
    }
}

#[cfg(test)]
mod security_benchmark_tests {
    use super::*;

    #[tokio::test]
    async fn test_constant_time_auth_benchmark() {
        let result = SecurityBenchmarks::benchmark_constant_time_auth_load()
            .await
            .expect("Constant-time auth benchmark failed");

        result.print_summary();

        // Validate benchmark results
        assert!(
            result.throughput > 100.0,
            "Throughput too low: {}",
            result.throughput
        );
        assert!(result.success_rate == 100.0, "Some requests failed");
        assert!(result.avg_latency < SecurityTestConfig::AUTH_WITH_SECURITY_MAX_DURATION);
    }

    #[tokio::test]
    async fn test_rate_limiting_benchmark() {
        let result = SecurityBenchmarks::benchmark_rate_limiting_under_attack()
            .await
            .expect("Rate limiting benchmark failed");

        result.print_summary();

        // Validate that rate limiting is working
        assert!(
            result.success_rate < 50.0,
            "Rate limiting not effective enough"
        );
        assert!(
            result.throughput > 1000.0,
            "Rate limiting processing too slow"
        );
    }

    #[tokio::test]
    async fn test_error_sanitization_benchmark() {
        let result = SecurityBenchmarks::benchmark_error_sanitization_throughput()
            .expect("Error sanitization benchmark failed");

        result.print_summary();

        // Validate performance
        assert!(result.throughput > 10000.0, "Error sanitization too slow");
        assert!(result.success_rate == 100.0, "Some sanitizations failed");
    }

    #[tokio::test]
    async fn test_memory_stability_benchmark() {
        let result = SecurityBenchmarks::benchmark_memory_usage()
            .await
            .expect("Memory stability benchmark failed");

        result.print_summary();

        // Validate memory stability
        assert!(result.memory_stable, "Memory usage not stable");
        assert!(
            result.consistency_ratio < 1.5,
            "Performance degraded too much"
        );
    }

    #[tokio::test]
    async fn test_complete_stress_suite() {
        let results = SecurityStressTester::run_security_stress_tests()
            .await
            .expect("Security stress tests failed");

        assert!(!results.is_empty(), "No benchmark results returned");

        // Verify all tests met minimum performance requirements
        for result in &results {
            assert!(
                result.avg_latency < Duration::from_millis(200),
                "Test '{}' too slow: {:?}",
                result.test_name,
                result.avg_latency
            );
        }
    }
}
