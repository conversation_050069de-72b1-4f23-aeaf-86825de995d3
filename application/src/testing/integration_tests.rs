// Integration tests for security testing infrastructure
// Validates that the enhanced security testing framework works correctly

#[cfg(test)]
mod security_infrastructure_tests {
    use crate::errors::ApplicationError;
    use crate::security::{ConstantTimeAuthService, ErrorSanitizer, RateLimiter};
    use crate::testing::security::{
        ErrorSecurityTester, RateLimitTester, SecurityTestConfig, TimingAttackTester,
    };
    use auth_domain::{
        crypto::ConstantTimeService,
        entities::User,
        value_objects::{Email, Password},
    };
    use std::time::Duration;

    /// Test that TimingAttackTester can be created successfully
    #[tokio::test]
    async fn test_timing_attack_tester_creation() {
        let result = TimingAttackTester::new();
        assert!(
            result.is_ok(),
            "TimingAttackTester creation failed: {:?}",
            result.err()
        );
    }

    /// Test that RateLimitTester can be created and basic functionality works
    #[tokio::test]
    async fn test_rate_limit_tester_creation() {
        let tester = RateLimitTester::new();

        // Test basic IP rate limiting
        let result = tester.test_ip_rate_limiting().await;
        assert!(
            result.is_ok(),
            "IP rate limiting test failed: {:?}",
            result.err()
        );
    }

    /// Test that ErrorSecurityTester can validate error sanitization
    #[tokio::test]
    async fn test_error_security_tester() {
        let tester = ErrorSecurityTester::new();

        // Test authentication error sanitization
        let result = tester.test_authentication_error_sanitization();
        assert!(
            result.is_ok(),
            "Authentication error sanitization test failed: {:?}",
            result.err()
        );
    }

    /// Test SecurityTestConfig constants are reasonable
    #[test]
    fn test_security_config_constants() {
        // Verify timing constraints are reasonable
        assert!(SecurityTestConfig::MAX_TIMING_VARIANCE > 1.0);
        assert!(SecurityTestConfig::MAX_TIMING_VARIANCE < 10.0);

        // Verify minimum operation duration is set
        assert!(SecurityTestConfig::MIN_OPERATION_DURATION > Duration::from_millis(50));
        assert!(SecurityTestConfig::MIN_OPERATION_DURATION < Duration::from_millis(200));

        // Verify rate limiting parameters are reasonable
        assert!(SecurityTestConfig::RATE_LIMIT_IP_MAX > 1);
        assert!(SecurityTestConfig::RATE_LIMIT_IP_MAX < 100);
        assert!(SecurityTestConfig::RATE_LIMIT_ACCOUNT_MAX > 1);
        assert!(SecurityTestConfig::RATE_LIMIT_ACCOUNT_MAX < SecurityTestConfig::RATE_LIMIT_IP_MAX);

        // Verify performance thresholds are achievable
        assert!(SecurityTestConfig::AUTH_WITH_SECURITY_MAX_DURATION < Duration::from_millis(500));
        assert!(SecurityTestConfig::RATE_LIMIT_CHECK_MAX_DURATION < Duration::from_millis(50));
        assert!(SecurityTestConfig::ERROR_SANITIZATION_MAX_DURATION < Duration::from_millis(10));
    }

    /// Test basic ConstantTimeAuthService functionality
    #[tokio::test]
    async fn test_constant_time_auth_service_basic() {
        let ct_service = ConstantTimeService::new();
        let auth_service = ConstantTimeAuthService::new(ct_service);

        // Create test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("BasicTestPassword123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.verify().unwrap();

        // Test that authentication takes reasonable time (not too fast or too slow)
        let start = std::time::Instant::now();
        let _result = auth_service
            .validate_authentication_attempt(
                "<EMAIL>",
                "BasicTestPassword123!",
                Some(&user),
            )
            .await;
        let duration = start.elapsed();

        // Should take at least minimum security duration
        assert!(
            duration >= SecurityTestConfig::MIN_OPERATION_DURATION,
            "Authentication completed too quickly: {:?}",
            duration
        );

        // Should not take excessive time (under 500ms)
        assert!(
            duration < Duration::from_millis(500),
            "Authentication took too long: {:?}",
            duration
        );
    }

    /// Test basic RateLimiter functionality
    #[tokio::test]
    async fn test_rate_limiter_basic() {
        let limiter = RateLimiter::new();

        let test_key =
            crate::security::rate_limiter::rate_limit_key_from_ip(Some("***********".to_string()));

        // First few requests should succeed
        for i in 0..3 {
            let result = limiter.check_rate_limit(&test_key).await;
            assert!(
                result.is_ok(),
                "Rate limit check failed on attempt {}: {:?}",
                i + 1,
                result.err()
            );
        }

        // After many requests, should be rate limited
        for _ in 0..10 {
            let _ = limiter.check_rate_limit(&test_key).await;
        }

        let result = limiter.check_rate_limit(&test_key).await;
        // This might succeed or fail depending on exact configuration, just check it doesn't panic
        let _ = result;
    }

    /// Test basic ErrorSanitizer functionality
    #[test]
    fn test_error_sanitizer_basic() {
        let sanitizer = ErrorSanitizer::new();

        // Test that sensitive errors are sanitized
        let sensitive_error = ApplicationError::UserNotFound;
        let sanitized = sanitizer.sanitize(&sensitive_error);

        // Should not contain "not found" or similar sensitive terms
        let message_lower = sanitized.message.to_lowercase();
        assert!(
            !message_lower.contains("not found"),
            "Sanitized error contains sensitive information: '{}'",
            sanitized.message
        );
        assert!(
            !message_lower.contains("user"),
            "Sanitized error contains user information: '{}'",
            sanitized.message
        );
    }

    /// Test that security infrastructure integrates with existing security module
    #[tokio::test]
    async fn test_security_integration() {
        // Create all security components
        let ct_service = ConstantTimeService::new();
        let auth_service = ConstantTimeAuthService::new(ct_service);
        let rate_limiter = RateLimiter::new();
        let error_sanitizer = ErrorSanitizer::new();

        // Test that they can work together without conflicts
        let test_key =
            crate::security::rate_limiter::rate_limit_key_from_ip(Some("************".to_string()));

        // Rate limiting check
        let _rate_result = rate_limiter.check_rate_limit(&test_key).await;

        // Error sanitization
        let error = ApplicationError::InvalidCredentials;
        let _sanitized = error_sanitizer.sanitize(&error);

        // Authentication timing (with dummy user data)
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("IntegrationTestPass123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.verify().unwrap();

        let _auth_result = auth_service
            .validate_authentication_attempt(
                "<EMAIL>",
                "TestPassword123!",
                Some(&user),
            )
            .await;

        // If we got here without panicking, integration is working
        assert!(true, "Security components integrated successfully");
    }

    /// Test timing consistency between different scenarios
    #[tokio::test]
    async fn test_basic_timing_consistency() {
        let ct_service = ConstantTimeService::new();
        let auth_service = ConstantTimeAuthService::new(ct_service);

        // Create test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TimingTestPass123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.verify().unwrap();

        let iterations = 10;
        let mut valid_user_timings = Vec::new();
        let mut invalid_user_timings = Vec::new();

        // Test timing for valid user scenarios
        for _ in 0..iterations {
            let start = std::time::Instant::now();
            let _ = auth_service
                .validate_authentication_attempt(
                    "<EMAIL>",
                    "WrongPassword123!",
                    Some(&user),
                )
                .await;
            valid_user_timings.push(start.elapsed());
        }

        // Test timing for invalid user scenarios
        for _ in 0..iterations {
            let start = std::time::Instant::now();
            let _ = auth_service
                .validate_authentication_attempt("<EMAIL>", "AnyPassword123!", None)
                .await;
            invalid_user_timings.push(start.elapsed());
        }

        // Calculate basic timing statistics
        let valid_avg: u128 = valid_user_timings.iter().map(|d| d.as_micros()).sum();
        let invalid_avg: u128 = invalid_user_timings.iter().map(|d| d.as_micros()).sum();

        let valid_avg = valid_avg / iterations as u128;
        let invalid_avg = invalid_avg / iterations as u128;

        // Basic consistency check (allow reasonable variance)
        let diff_ratio = if valid_avg > invalid_avg {
            valid_avg as f64 / invalid_avg.max(1) as f64
        } else {
            invalid_avg as f64 / valid_avg.max(1) as f64
        };

        // Allow up to 3x variance for basic test (more strict tests are in specialized modules)
        assert!(
            diff_ratio < 3.0,
            "Basic timing consistency failed: {:.2}x variance",
            diff_ratio
        );
    }
}
