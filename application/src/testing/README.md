# Application Layer Testing Infrastructure

This document explains how to use the comprehensive testing infrastructure for CQRS command handlers in the auth-application crate.

## Overview

The testing infrastructure provides:
- Mock implementations for all repository traits using `mockall`
- Async test helpers and utilities
- Test fixtures for common scenarios
- Security and performance testing helpers
- TDD workflow templates

## Directory Structure

```
testing/
├── mod.rs           # Module exports
├── mocks.rs         # Mock repository implementations
├── helpers.rs       # Async test utilities and performance helpers
├── fixtures.rs      # Test data and scenarios
└── README.md        # This documentation
```

## Using Mock Repositories

### Basic Setup

```rust
use crate::testing::mocks::{MockUserRepo, MockEventBus, MockPasswordService};
use mockall::predicate::*;

#[tokio::test]
async fn test_user_registration() {
    // Arrange
    let mut user_repo = MockUserRepo::new();
    let mut event_bus = MockEventBus::new();
    let mut password_service = MockPasswordService::new();
    
    // Configure mock expectations
    user_repo.expect_find_by_email()
        .with(eq(Email::parse("<EMAIL>").unwrap()))
        .times(1)
        .returning(|_| Ok(None));
        
    user_repo.expect_save()
        .times(1)
        .returning(|_| Ok(()));
    
    // Create handler with mocks
    let handler = RegisterUserHandler::new(user_repo, event_bus, password_service);
    
    // Act & Assert
    let result = handler.handle(command).await;
    assert!(result.is_ok());
}
```

### Available Mocks

- `MockUserRepo` - Implements `UserRepository`
- `MockSessionRepo` - Implements `SessionRepository`  
- `MockRoleRepo` - Implements `RoleRepository`
- `MockEventBus` - For publishing domain events
- `MockPasswordService` - For password hashing/verification
- `MockTokenService` - For JWT token operations

## Async Test Helpers

### Timeout Handling

```rust
use crate::testing::helpers::{with_timeout, TEST_TIMEOUT};

#[tokio::test]
async fn test_with_timeout() {
    let result = with_timeout(async_operation()).await;
    assert!(result.is_ok());
}
```

### Assertion Macros

```rust
use crate::testing::helpers::{assert_async_ok, assert_async_err};

#[tokio::test]
async fn test_success() {
    let result = assert_async_ok!(handler.handle(command));
    assert_eq!(result, expected_value);
}

#[tokio::test] 
async fn test_failure() {
    assert_async_err!(
        handler.handle(invalid_command),
        ApplicationError::InvalidCredentials
    );
}
```

## Test Fixtures

### Command Fixtures

```rust
use crate::testing::fixtures::commands::register_user::TestRegisterUserCommand;

#[tokio::test]
async fn test_registration_scenarios() {
    // Valid registration
    let valid_cmd = TestRegisterUserCommand::valid();
    
    // Invalid scenarios
    let weak_password = TestRegisterUserCommand::with_weak_password();
    let existing_email = TestRegisterUserCommand::with_existing_email();
    let sql_injection = TestRegisterUserCommand::with_sql_injection();
}
```

### Entity Fixtures

```rust
use crate::testing::fixtures::entities::{users, roles, sessions};

#[tokio::test]
async fn test_with_entities() {
    let admin_user = users::admin_user();
    let admin_role = roles::admin_role();
    let active_session = sessions::active_session(user.id().clone());
}
```

## Security Testing

### Timing Attack Prevention

```rust
use crate::testing::helpers::security;

#[tokio::test]
async fn test_constant_time_operations() {
    security::assert_constant_time(
        valid_login_attempt(),
        invalid_login_attempt(),
        50, // 50ms tolerance
    ).await;
}
```

### SQL Injection Testing

```rust
use crate::testing::fixtures::security;

#[tokio::test]
async fn test_sql_injection_prevention() {
    for payload in security::sql_injection_payloads() {
        let command = RegisterUserCommand {
            email: payload,
            username: "test".to_string(),
            password: "pass".to_string(),
        };
        
        // Should reject or sanitize malicious input
        assert_async_err!(
            handler.handle(command),
            ApplicationError::InvalidInput(_)
        );
    }
}
```

## Performance Testing

### Response Time Assertions

```rust
use crate::testing::helpers::performance;
use crate::testing::fixtures::performance::PerformanceExpectations;

#[tokio::test]
async fn test_authentication_performance() {
    let result = performance::assert_performance(
        handler.handle(auth_command),
        PerformanceExpectations::AUTH_MAX_DURATION,
    ).await;
    
    assert!(result.is_ok());
}
```

### Load Testing

```rust
use crate::testing::fixtures::performance;

#[tokio::test]
async fn test_concurrent_operations() {
    let commands = performance::concurrent_registrations(100);
    
    let tasks: Vec<_> = commands.into_iter()
        .map(|cmd| tokio::spawn(handler.handle(cmd)))
        .collect();
        
    let results = futures::future::join_all(tasks).await;
    
    // Verify all completed successfully
    for result in results {
        assert!(result.is_ok());
    }
}
```

## TDD Workflow

### 1. Write Failing Tests First

```rust
#[tokio::test]
async fn should_register_user_successfully() {
    // Arrange
    let (handler, mut user_repo, mut event_bus, mut password_service) = setup_mocks();
    
    // Configure mock expectations
    user_repo.expect_find_by_email().returning(|_| Ok(None));
    user_repo.expect_save().returning(|_| Ok(()));
    
    let command = TestRegisterUserCommand::valid();
    
    // Act & Assert - This will fail until implementation is complete
    let result = handler.handle(command.into()).await;
    assert!(result.is_ok());
}
```

### 2. Implement Minimum Code to Pass

```rust
impl<U, E, P> AsyncCommandHandler<RegisterUserCommand> for RegisterUserHandler<U, E, P>
where
    U: UserRepository,
    E: EventPublisher,
    P: PasswordHasher,
{
    async fn handle(&self, command: RegisterUserCommand) -> ApplicationResult<UserId> {
        // Minimal implementation to pass tests
        let email = Email::parse(&command.email)?;
        
        // Check if user exists
        if self.user_repository.find_by_email(&email).await?.is_some() {
            return Err(ApplicationError::EmailAlreadyExists);
        }
        
        // Create and save user
        let user = User::new(&command.email, &command.username)?;
        let user_id = user.id().clone();
        
        self.user_repository.save(&user).await?;
        
        Ok(user_id)
    }
}
```

### 3. Refactor and Add More Tests

- Add error condition tests
- Add security tests  
- Add performance tests
- Add edge case tests

## Test Categories

### Unit Tests
- In-file with `#[cfg(test)]`
- Test individual functions and methods
- Use mocks for all dependencies
- Fast execution (<1ms per test)

### Integration Tests  
- Test component interactions
- Use mocks for external dependencies
- Test full command/query flows
- Moderate execution time (<100ms per test)

### Security Tests
- Test against common attack vectors
- Verify timing attack resistance
- Test input validation and sanitization
- Test authorization and access control

### Performance Tests
- Verify response time requirements
- Test under load conditions
- Memory usage validation
- Concurrent operation testing

## Best Practices

### Mock Configuration
- Always set explicit expectations with `.times()`
- Use `.with()` predicates to verify parameters
- Use `.returning()` for deterministic responses
- Configure mocks in test setup, not in assertions

### Test Organization
- Group related tests in modules
- Use descriptive test names that explain the scenario
- Follow AAA pattern: Arrange, Act, Assert
- Keep tests independent and isolated

### Error Testing
- Test all error conditions explicitly
- Use specific error type assertions
- Test error messages when relevant
- Verify error context and details

### Async Testing
- Always use timeouts for async operations
- Test concurrent scenarios where relevant
- Use proper async test runners (`#[tokio::test]`)
- Handle async errors appropriately

## Running Tests

```bash
# Run all tests
cargo test --lib

# Run specific test module
cargo test register_user_tests

# Run with output
cargo test -- --nocapture

# Run performance tests
cargo test performance

# Run security tests
cargo test security
```

## Troubleshooting

### Common Issues

1. **Mock Expectations Not Met**
   - Check that all `.expect_*()` calls have matching invocations
   - Verify `.times()` matches actual call count
   - Use `.debug()` on mocks to see what was called

2. **Async Test Timeouts**
   - Increase `TEST_TIMEOUT` for slow operations
   - Check for deadlocks in async code
   - Verify all futures are properly awaited

3. **Compilation Errors**
   - Ensure all trait bounds are satisfied
   - Check async-trait usage on traits
   - Verify mockall macro syntax

4. **Test Flakiness**
   - Use deterministic test data
   - Avoid timing-dependent assertions
   - Properly clean up test state

### Debugging Tips

- Use `#[ignore]` to temporarily skip flaky tests
- Add `.debug()` to mock objects for troubleshooting
- Use `tracing` for test logging
- Run tests with `RUST_LOG=debug` for verbose output