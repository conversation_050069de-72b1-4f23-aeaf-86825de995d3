// Performance monitoring and metrics collection module
// Provides comprehensive performance tracking, SLA monitoring, and alerting

pub mod alert_manager;
pub mod dashboard_integration;
pub mod metrics_collector;
pub mod performance_tracer;
pub mod query_metrics;
pub mod sla_monitor;

#[cfg(test)]
pub mod tests;

pub use alert_manager::{
    Alert, AlertChannel, AlertConfig, AlertLevel, AlertManager, DefaultAlertManager,
};
pub use dashboard_integration::{
    DashboardExporter, ExportedMetrics, MetricsExportFormat, PrometheusExporter,
};
pub use metrics_collector::{
    CacheMetrics, CacheOperationType, DatabaseMetrics, InMemoryMetricsCollector, MetricsCollector,
    PerformanceSnapshot, QueryMetrics, UserContext,
};
pub use performance_tracer::{
    DefaultPerformanceTracer, PerformanceSpan, PerformanceTracer, SpanStatus, SpanType,
    TraceContext, trace_cache_operation,
};
pub use query_metrics::{MetricAggregator, PerformanceBudget, QueryPerformanceMetrics};
pub use sla_monitor::{
    DefaultSlaMonitor, SlaConfig, SlaMonitor, SlaReport, SlaStatus, SlaViolation, TimePeriod,
    ViolationSeverity, ViolationType,
};
