// Comprehensive tests for performance monitoring system
// Tests metrics collection, SLA monitoring, tracing, and alerting

#[cfg(test)]
mod monitoring_tests {
    use crate::errors::ApplicationResult;
    use crate::monitoring::*;
    use crate::queries::{AsyncQ<PERSON>y<PERSON><PERSON>ler, MonitoredQueryHandlerExt, Query};
    use async_trait::async_trait;
    use std::sync::Arc;
    use std::time::{Duration, SystemTime};

    // Test query for monitoring
    #[derive(Debug)]
    struct TestQuery {
        pub delay_ms: u64,
        pub should_fail: bool,
        pub name: String,
    }

    impl Query for TestQuery {
        type Result = TestResult;
    }

    #[derive(Debug, Clone)]
    #[allow(dead_code)]
    struct TestResult {
        pub data: String,
        pub count: usize,
    }

    // Test handler that simulates various scenarios
    struct TestHandler;

    #[async_trait]
    impl AsyncQueryHandler<TestQuery> for TestHandler {
        async fn handle(&self, query: TestQuery) -> ApplicationResult<TestResult> {
            // Simulate processing delay
            tokio::time::sleep(Duration::from_millis(query.delay_ms)).await;

            if query.should_fail {
                Err(crate::errors::ApplicationError::Internal(format!(
                    "Test failure for {}",
                    query.name
                )))
            } else {
                Ok(TestResult {
                    data: format!("Success for {}", query.name),
                    count: 42,
                })
            }
        }
    }

    /// Test basic metrics collection
    #[tokio::test]
    async fn test_metrics_collection() {
        let collector = Arc::new(InMemoryMetricsCollector::new());

        // Record some query executions
        for i in 0..10 {
            let metrics = QueryMetrics {
                query_name: "test_query".to_string(),
                execution_time: Duration::from_millis(10 + i * 5),
                cache_hit: i % 2 == 0,
                cache_response_time: if i % 2 == 0 {
                    Some(Duration::from_millis(2))
                } else {
                    None
                },
                database_response_time: if i % 2 == 1 {
                    Some(Duration::from_millis(8 + i * 5))
                } else {
                    None
                },
                result_count: 10,
                error_type: if i == 9 {
                    Some("TestError".to_string())
                } else {
                    None
                },
                user_context: UserContext {
                    user_id: Some(format!("user{i}")),
                    session_id: Some(format!("session{i}")),
                    request_id: format!("req{i}"),
                    ip_address: None,
                    user_agent: None,
                },
                timestamp: SystemTime::now(),
            };

            collector.record_query_execution(metrics);
        }

        // Verify metrics
        let stats = collector.get_query_metrics("test_query").unwrap();
        assert_eq!(stats.total_executions, 10);
        assert_eq!(stats.cache_hit_ratio, 0.5); // 5 hits out of 10
        assert_eq!(stats.error_rate, 0.1); // 1 error out of 10
        assert!(stats.p50_latency.is_some());
        assert!(stats.p95_latency.is_some());
    }

    /// Test SLA monitoring and violations
    #[tokio::test]
    async fn test_sla_monitoring() {
        let sla_monitor = Arc::new(DefaultSlaMonitor::new());

        // Test compliant query
        let compliant_metrics = QueryMetrics {
            query_name: "fast_query".to_string(),
            execution_time: Duration::from_millis(50),
            cache_hit: true,
            cache_response_time: Some(Duration::from_millis(2)),
            database_response_time: None,
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req1".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        let status = sla_monitor.check_sla_compliance(&compliant_metrics);
        assert!(status.compliant);

        // Test SLA violation
        let violation_metrics = QueryMetrics {
            query_name: "slow_query".to_string(),
            execution_time: Duration::from_millis(150),
            cache_hit: false,
            cache_response_time: None,
            database_response_time: Some(Duration::from_millis(140)),
            result_count: 100,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req2".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        let status = sla_monitor.check_sla_compliance(&violation_metrics);
        assert!(!status.compliant);
        assert_eq!(status.violation_type, Some(ViolationType::LatencyExceeded));

        // Test critical query with strict SLA
        let auth_metrics = QueryMetrics {
            query_name: "authenticate_user".to_string(),
            execution_time: Duration::from_millis(12), // Exceeds 10ms SLA
            cache_hit: false,
            cache_response_time: None,
            database_response_time: Some(Duration::from_millis(11)),
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req3".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        let status = sla_monitor.check_sla_compliance(&auth_metrics);
        assert!(!status.compliant);
        assert_eq!(status.threshold_ms, 10); // Strict SLA for auth
    }

    /// Test distributed tracing
    #[tokio::test]
    async fn test_distributed_tracing() {
        let tracer = Arc::new(DefaultPerformanceTracer::new());

        // Create a trace with multiple spans
        let root_context = tracer.start_trace("test_operation");

        // Simulate cache operation
        let cache_result =
            trace_cache_operation(tracer.as_ref(), &root_context, "get_user", || {
                Ok("cached_data".to_string())
            });
        assert!(cache_result.is_ok());

        // Simulate database operation
        let db_context = root_context.child("database_query");
        tokio::time::sleep(Duration::from_millis(10)).await;
        let db_span = DefaultPerformanceTracer::span_from_context(&db_context, SpanStatus::Ok);
        tracer.record_span(db_span);

        // Complete the trace
        tracer.complete_trace(root_context.clone());

        // Verify trace details
        let trace_details = tracer.get_trace(&root_context.trace_id).unwrap();
        assert!(trace_details.total_spans >= 3);
        assert_eq!(trace_details.error_count, 0);
        assert!(!trace_details.critical_path.is_empty());
    }

    /// Test alert management
    #[tokio::test]
    async fn test_alert_management() {
        let alert_manager = Arc::new(DefaultAlertManager::new());

        // Test different alert levels
        let info_alert = Alert::new(
            AlertLevel::Info,
            "Test Info".to_string(),
            "Informational message".to_string(),
            "test".to_string(),
        );
        alert_manager.send_alert(info_alert);

        let warning_alert = Alert::new(
            AlertLevel::Warning,
            "High Latency".to_string(),
            "Query latency exceeding threshold".to_string(),
            "performance".to_string(),
        )
        .with_metric("latency_ms", 150.0)
        .with_tag("query", "slow_query");
        alert_manager.send_alert(warning_alert);

        let critical_alert = Alert::new(
            AlertLevel::Critical,
            "SLA Violation".to_string(),
            "Critical SLA violation detected".to_string(),
            "sla_monitor".to_string(),
        )
        .with_runbook("https://wiki.example.com/sla-violations");
        alert_manager.send_alert(critical_alert);

        // Verify alerts were recorded
        let recent_alerts = alert_manager.get_recent_alerts(10);
        assert_eq!(recent_alerts.len(), 3);

        // Test alert throttling
        let duplicate_alert = Alert::new(
            AlertLevel::Warning,
            "High Latency".to_string(),
            "Same alert, should be throttled".to_string(),
            "performance".to_string(),
        );

        // Should be throttled
        assert!(alert_manager.should_throttle(&duplicate_alert.dedup_key()));

        // Clear and verify
        alert_manager.clear_alerts();
        assert_eq!(alert_manager.get_recent_alerts(10).len(), 0);
    }

    /// Test performance budget tracking
    #[tokio::test]
    async fn test_performance_budget() {
        let mut budget = PerformanceBudget::new();

        // Simulate component time consumption
        budget.consume("authorization", 4);
        budget.consume("business_logic", 8);
        budget.consume("database_query", 45);
        budget.consume("serialization", 3);

        assert_eq!(budget.total_consumed(), 60);
        assert_eq!(budget.remaining_budget(), 40);
        assert!(!budget.is_exceeded());

        // Test budget exceeded scenario
        budget.consume("database_query", 50); // Now 95ms for database
        assert!(budget.is_exceeded());

        let report = budget.utilization_report();
        assert!(report.is_exceeded);
        assert_eq!(report.exceeded_components.len(), 1);
        assert_eq!(report.exceeded_components[0].name, "Database Query");
    }

    /// Test end-to-end monitoring integration
    #[tokio::test]
    async fn test_end_to_end_monitoring() {
        let metrics_collector = Arc::new(InMemoryMetricsCollector::new());
        let sla_monitor = Arc::new(DefaultSlaMonitor::new());
        let tracer = Arc::new(DefaultPerformanceTracer::new());

        // Create monitored handler
        let handler = TestHandler;
        let monitored = handler.with_monitoring(
            "test_e2e_query".to_string(),
            metrics_collector.clone(),
            sla_monitor.clone(),
            tracer.clone(),
        );

        // Execute successful query
        let query = TestQuery {
            delay_ms: 20,
            should_fail: false,
            name: "success_test".to_string(),
        };

        let result = monitored.handle(query).await;
        assert!(result.is_ok());

        // Verify metrics were collected
        let stats = metrics_collector
            .get_query_metrics("test_e2e_query")
            .unwrap();
        assert_eq!(stats.total_executions, 1);
        assert_eq!(stats.error_rate, 0.0);

        // Execute failing query
        let failing_query = TestQuery {
            delay_ms: 5,
            should_fail: true,
            name: "failure_test".to_string(),
        };

        let result = monitored.handle(failing_query).await;
        assert!(result.is_err());

        // Verify error metrics
        let stats = metrics_collector
            .get_query_metrics("test_e2e_query")
            .unwrap();
        assert_eq!(stats.total_executions, 2);
        assert_eq!(stats.error_rate, 0.5); // 1 error out of 2

        // Execute slow query to trigger SLA violation
        let slow_query = TestQuery {
            delay_ms: 150,
            should_fail: false,
            name: "slow_test".to_string(),
        };

        let result = monitored.handle(slow_query).await;
        assert!(result.is_ok());

        // Verify SLA violation was recorded
        let stats = metrics_collector
            .get_query_metrics("test_e2e_query")
            .unwrap();
        assert_eq!(stats.sla_violations, 1);

        // Get performance snapshot
        let snapshot = metrics_collector.get_performance_snapshot();
        assert_eq!(snapshot.sla_violations, 1);
        assert!(snapshot.error_rate > 0.0);
    }

    /// Test cache metrics collection
    #[tokio::test]
    async fn test_cache_metrics() {
        let collector = Arc::new(InMemoryMetricsCollector::new());

        // Record cache operations
        collector.record_cache_operation(CacheMetrics {
            operation_type: CacheOperationType::Get,
            cache_key: "user:123".to_string(),
            duration: Duration::from_millis(2),
            success: true,
            size_bytes: Some(512),
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        });

        collector.record_cache_operation(CacheMetrics {
            operation_type: CacheOperationType::Get,
            cache_key: "user:456".to_string(),
            duration: Duration::from_millis(1),
            success: false,
            size_bytes: None,
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        });

        collector.record_cache_operation(CacheMetrics {
            operation_type: CacheOperationType::Set,
            cache_key: "user:789".to_string(),
            duration: Duration::from_millis(3),
            success: true,
            size_bytes: Some(1024),
            ttl_seconds: Some(300),
            timestamp: SystemTime::now(),
        });

        // Slow cache operation (exceeds 5ms SLA)
        collector.record_cache_operation(CacheMetrics {
            operation_type: CacheOperationType::Get,
            cache_key: "user:slow".to_string(),
            duration: Duration::from_millis(10),
            success: true,
            size_bytes: Some(2048),
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        });

        let snapshot = collector.get_performance_snapshot();
        assert_eq!(snapshot.cache_hit_ratio, 0.5); // 2 hits out of 4 GET operations
    }

    /// Test database metrics collection
    #[tokio::test]
    async fn test_database_metrics() {
        let collector = Arc::new(InMemoryMetricsCollector::new());

        // Record database operations
        collector.record_database_operation(DatabaseMetrics {
            operation_type: "SELECT".to_string(),
            table_name: Some("users".to_string()),
            duration: Duration::from_millis(15),
            rows_affected: 1,
            connection_acquisition_time: Some(Duration::from_millis(2)),
            prepared_statement_used: true,
            timestamp: SystemTime::now(),
        });

        // Slow database operation (exceeds 50ms budget)
        collector.record_database_operation(DatabaseMetrics {
            operation_type: "SELECT".to_string(),
            table_name: Some("audit_logs".to_string()),
            duration: Duration::from_millis(75),
            rows_affected: 1000,
            connection_acquisition_time: Some(Duration::from_millis(1)),
            prepared_statement_used: false,
            timestamp: SystemTime::now(),
        });

        collector.record_database_operation(DatabaseMetrics {
            operation_type: "INSERT".to_string(),
            table_name: Some("sessions".to_string()),
            duration: Duration::from_millis(8),
            rows_affected: 1,
            connection_acquisition_time: Some(Duration::from_millis(1)),
            prepared_statement_used: true,
            timestamp: SystemTime::now(),
        });

        // Verify metrics were recorded
        let snapshot = collector.get_performance_snapshot();
        assert!(snapshot.timestamp > SystemTime::UNIX_EPOCH);
    }

    /// Test dashboard export functionality
    #[tokio::test]
    async fn test_dashboard_export() {
        let exporter = PrometheusExporter::new("auth".to_string(), "service".to_string());

        // Create a performance snapshot
        let snapshot = PerformanceSnapshot {
            p50_latency: Duration::from_millis(10),
            p95_latency: Duration::from_millis(50),
            p99_latency: Duration::from_millis(100),
            cache_hit_ratio: 0.85,
            error_rate: 0.001,
            queries_per_second: 1000.0,
            sla_violations: 5,
            timestamp: SystemTime::now(),
            period_seconds: 3600,
        };

        let exported = exporter.export_metrics(snapshot);
        assert_eq!(exported.format, MetricsExportFormat::Prometheus);
        assert!(!exported.metrics.is_empty());

        // Test Prometheus text format
        let prometheus_text = exporter.to_prometheus_format(&exported);
        assert!(prometheus_text.contains("auth_service_cache_hit_ratio"));
        assert!(prometheus_text.contains("auth_service_error_rate"));
        assert!(prometheus_text.contains("auth_service_queries_per_second"));
        assert!(prometheus_text.contains("auth_service_sla_violations_total"));
    }

    /// Test metric aggregation
    #[tokio::test]
    async fn test_metric_aggregation() {
        let mut aggregator = MetricAggregator::new(100);

        // Add various latency samples
        let samples = vec![
            5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100,
        ];

        for ms in samples {
            aggregator.add_sample(Duration::from_millis(ms));
        }

        assert_eq!(aggregator.count(), 20);
        assert_eq!(aggregator.min(), Some(Duration::from_millis(5)));
        assert_eq!(aggregator.max(), Some(Duration::from_millis(100)));
        assert_eq!(aggregator.p50(), Some(Duration::from_millis(50)));
        assert_eq!(aggregator.p95(), Some(Duration::from_millis(95)));
        assert_eq!(aggregator.p99(), Some(Duration::from_millis(95))); // With 20 samples, p99 = 19th value

        let avg = aggregator.average().unwrap();
        assert!(avg.as_millis() >= 50 && avg.as_millis() <= 55);
    }
}

// Export test module
// pub use monitoring_tests::*; // Unused
