// Alert management system for performance monitoring
// Handles alert routing, throttling, and escalation

use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tracing::{error, info, warn};

/// Alert manager for handling performance alerts
pub trait AlertManager: Send + Sync {
    /// Send an alert
    fn send_alert(&self, alert: Alert);

    /// Check if alert should be throttled
    fn should_throttle(&self, alert_key: &str) -> bool;

    /// Get recent alerts
    fn get_recent_alerts(&self, count: usize) -> Vec<Alert>;

    /// Clear alert history
    fn clear_alerts(&self);

    /// Update alert configuration
    fn update_config(&self, config: AlertConfig);
}

/// Alert configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertConfig {
    pub channels: Vec<AlertChannel>,
    pub throttle_duration_seconds: u64,
    pub max_alerts_per_hour: usize,
    pub enable_escalation: bool,
    pub escalation_timeout_minutes: u64,
    pub quiet_hours: Option<QuietHours>,
    pub alert_rules: Vec<AlertRule>,
}

/// Alert channels for notification delivery
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertChannel {
    Log,
    Email {
        recipients: Vec<String>,
    },
    Slack {
        webhook_url: String,
        channel: String,
    },
    PagerDuty {
        service_key: String,
    },
    Webhook {
        url: String,
        headers: HashMap<String, String>,
    },
    Console,
}

/// Quiet hours configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuietHours {
    pub start_hour: u8,
    pub end_hour: u8,
    pub timezone: String,
    pub days: Vec<DayOfWeek>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DayOfWeek {
    Monday,
    Tuesday,
    Wednesday,
    Thursday,
    Friday,
    Saturday,
    Sunday,
}

/// Alert rules for automatic alert generation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRule {
    pub name: String,
    pub condition: AlertCondition,
    pub level: AlertLevel,
    pub channels: Vec<AlertChannel>,
}

/// Alert conditions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertCondition {
    QueryLatencyAbove {
        query_name: String,
        threshold_ms: u64,
    },
    CacheHitRatioBelow {
        threshold: f64,
    },
    ErrorRateAbove {
        threshold: f64,
    },
    SlaViolationCount {
        count: u64,
        window_minutes: u64,
    },
    ConnectionPoolUtilization {
        threshold: f64,
    },
}

/// Alert severity levels
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum AlertLevel {
    Info,
    Warning,
    Error,
    Critical,
    Emergency,
}

/// Alert structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    pub id: String,
    pub timestamp: SystemTime,
    pub level: AlertLevel,
    pub title: String,
    pub message: String,
    pub source: String,
    pub tags: HashMap<String, String>,
    pub metrics: HashMap<String, f64>,
    pub runbook_url: Option<String>,
    pub acknowledgeable: bool,
    pub auto_resolve_minutes: Option<u64>,
}

impl Alert {
    /// Create a new alert
    pub fn new(level: AlertLevel, title: String, message: String, source: String) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: SystemTime::now(),
            level,
            title,
            message,
            source,
            tags: HashMap::new(),
            metrics: HashMap::new(),
            runbook_url: None,
            acknowledgeable: true,
            auto_resolve_minutes: None,
        }
    }

    /// Add a tag to the alert
    pub fn with_tag(mut self, key: &str, value: &str) -> Self {
        self.tags.insert(key.to_string(), value.to_string());
        self
    }

    /// Add a metric to the alert
    pub fn with_metric(mut self, key: &str, value: f64) -> Self {
        self.metrics.insert(key.to_string(), value);
        self
    }

    /// Set runbook URL
    pub fn with_runbook(mut self, url: &str) -> Self {
        self.runbook_url = Some(url.to_string());
        self
    }

    /// Get alert key for deduplication
    pub fn dedup_key(&self) -> String {
        format!("{}:{}:{}", self.source, self.title, self.level as u8)
    }
}

/// Default alert manager implementation
pub struct DefaultAlertManager {
    config: Arc<RwLock<AlertConfig>>,
    alert_history: Arc<RwLock<VecDeque<Alert>>>,
    throttle_state: Arc<RwLock<HashMap<String, SystemTime>>>,
    hourly_counts: Arc<RwLock<HashMap<String, VecDeque<SystemTime>>>>,
}

impl DefaultAlertManager {
    /// Create new alert manager
    pub fn new() -> Self {
        Self::with_config(AlertConfig::default())
    }

    /// Create new alert manager with configuration
    pub fn with_config(config: AlertConfig) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            alert_history: Arc::new(RwLock::new(VecDeque::with_capacity(10000))),
            throttle_state: Arc::new(RwLock::new(HashMap::new())),
            hourly_counts: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Check if within quiet hours
    fn is_quiet_hours(&self, config: &AlertConfig) -> bool {
        if let Some(_quiet_hours) = &config.quiet_hours {
            // In production, would check actual time and timezone
            // For now, return false to allow alerts
            false
        } else {
            false
        }
    }

    /// Route alert to configured channels
    fn route_alert(&self, alert: &Alert, channels: &[AlertChannel]) {
        for channel in channels {
            match channel {
                AlertChannel::Log => self.send_to_log(alert),
                AlertChannel::Console => self.send_to_console(alert),
                AlertChannel::Email { recipients } => self.send_to_email(alert, recipients),
                AlertChannel::Slack {
                    webhook_url,
                    channel,
                } => self.send_to_slack(alert, webhook_url, channel),
                AlertChannel::PagerDuty { service_key } => {
                    self.send_to_pagerduty(alert, service_key)
                }
                AlertChannel::Webhook { url, headers } => self.send_to_webhook(alert, url, headers),
            }
        }
    }

    /// Send alert to log
    fn send_to_log(&self, alert: &Alert) {
        match alert.level {
            AlertLevel::Emergency | AlertLevel::Critical => {
                error!(
                    "ALERT [{}] {}: {}",
                    alert.level as u8, alert.title, alert.message
                );
            }
            AlertLevel::Error => {
                error!(
                    "ALERT [{}] {}: {}",
                    alert.level as u8, alert.title, alert.message
                );
            }
            AlertLevel::Warning => {
                warn!(
                    "ALERT [{}] {}: {}",
                    alert.level as u8, alert.title, alert.message
                );
            }
            AlertLevel::Info => {
                info!(
                    "ALERT [{}] {}: {}",
                    alert.level as u8, alert.title, alert.message
                );
            }
        }
    }

    /// Send alert to console
    fn send_to_console(&self, alert: &Alert) {
        println!("=== ALERT: {} ===", alert.title);
        println!("Level: {:?}", alert.level);
        println!("Time: {:?}", alert.timestamp);
        println!("Message: {}", alert.message);
        println!("Source: {}", alert.source);
        if !alert.tags.is_empty() {
            println!("Tags: {:?}", alert.tags);
        }
        if !alert.metrics.is_empty() {
            println!("Metrics: {:?}", alert.metrics);
        }
        if let Some(runbook) = &alert.runbook_url {
            println!("Runbook: {runbook}");
        }
        println!("==================");
    }

    /// Send alert to email (placeholder)
    fn send_to_email(&self, alert: &Alert, recipients: &[String]) {
        info!(
            "Would send email alert '{}' to {:?}",
            alert.title, recipients
        );
        // In production, would integrate with SMTP service
    }

    /// Send alert to Slack (placeholder)
    fn send_to_slack(&self, alert: &Alert, webhook_url: &str, channel: &str) {
        info!(
            "Would send Slack alert '{}' to {} via {}",
            alert.title, channel, webhook_url
        );
        // In production, would make HTTP POST to webhook
    }

    /// Send alert to PagerDuty (placeholder)
    fn send_to_pagerduty(&self, alert: &Alert, service_key: &str) {
        if alert.level >= AlertLevel::Critical {
            info!(
                "Would trigger PagerDuty incident '{}' with key {}",
                alert.title, service_key
            );
            // In production, would integrate with PagerDuty API
        }
    }

    /// Send alert to webhook (placeholder)
    fn send_to_webhook(&self, alert: &Alert, url: &str, headers: &HashMap<String, String>) {
        info!(
            "Would send webhook alert '{}' to {} with headers {:?}",
            alert.title, url, headers
        );
        // In production, would make HTTP POST with alert JSON
    }

    /// Update hourly count for rate limiting
    fn update_hourly_count(&self, alert_key: &str) -> usize {
        let mut counts = self.hourly_counts.write().unwrap();
        let timestamps = counts.entry(alert_key.to_string()).or_default();

        // Remove timestamps older than 1 hour
        let one_hour_ago = SystemTime::now() - Duration::from_secs(3600);
        while let Some(front) = timestamps.front() {
            if *front < one_hour_ago {
                timestamps.pop_front();
            } else {
                break;
            }
        }

        // Add current timestamp
        timestamps.push_back(SystemTime::now());
        timestamps.len()
    }
}

impl AlertManager for DefaultAlertManager {
    fn send_alert(&self, alert: Alert) {
        let config = self.config.read().unwrap();

        // Check quiet hours
        if self.is_quiet_hours(&config) && alert.level < AlertLevel::Critical {
            info!("Alert suppressed during quiet hours: {}", alert.title);
            return;
        }

        // Check throttling
        let dedup_key = alert.dedup_key();
        if self.should_throttle(&dedup_key) {
            return;
        }

        // Check rate limiting
        let hourly_count = self.update_hourly_count(&dedup_key);
        if hourly_count > config.max_alerts_per_hour {
            warn!(
                "Alert rate limit exceeded for {}: {} alerts in past hour",
                dedup_key, hourly_count
            );
            return;
        }

        // Store alert in history
        {
            let mut history = self.alert_history.write().unwrap();
            history.push_back(alert.clone());

            // Maintain size limit
            if history.len() > 10000 {
                history.pop_front();
            }
        }

        // Route to appropriate channels based on level
        let channels = if alert.level >= AlertLevel::Critical {
            // Critical alerts go to all channels
            config.channels.clone()
        } else {
            // Other alerts use configured channels
            config
                .channels
                .iter()
                .filter(|c| !matches!(c, AlertChannel::PagerDuty { .. }))
                .cloned()
                .collect()
        };

        self.route_alert(&alert, &channels);

        // Update throttle state
        {
            let mut throttle = self.throttle_state.write().unwrap();
            throttle.insert(dedup_key, SystemTime::now());
        }
    }

    fn should_throttle(&self, alert_key: &str) -> bool {
        let config = self.config.read().unwrap();
        let throttle_state = self.throttle_state.read().unwrap();

        if let Some(last_sent) = throttle_state.get(alert_key) {
            if let Ok(elapsed) = SystemTime::now().duration_since(*last_sent) {
                if elapsed.as_secs() < config.throttle_duration_seconds {
                    return true;
                }
            }
        }

        false
    }

    fn get_recent_alerts(&self, count: usize) -> Vec<Alert> {
        let history = self.alert_history.read().unwrap();
        history.iter().rev().take(count).cloned().collect()
    }

    fn clear_alerts(&self) {
        self.alert_history.write().unwrap().clear();
        self.throttle_state.write().unwrap().clear();
        self.hourly_counts.write().unwrap().clear();
    }

    fn update_config(&self, config: AlertConfig) {
        *self.config.write().unwrap() = config;
        info!("Alert configuration updated");
    }
}

impl Default for AlertConfig {
    fn default() -> Self {
        Self {
            channels: vec![AlertChannel::Log, AlertChannel::Console],
            throttle_duration_seconds: 300, // 5 minutes
            max_alerts_per_hour: 100,
            enable_escalation: true,
            escalation_timeout_minutes: 15,
            quiet_hours: None,
            alert_rules: vec![
                AlertRule {
                    name: "High Query Latency".to_string(),
                    condition: AlertCondition::QueryLatencyAbove {
                        query_name: "any".to_string(),
                        threshold_ms: 200,
                    },
                    level: AlertLevel::Warning,
                    channels: vec![AlertChannel::Log],
                },
                AlertRule {
                    name: "Low Cache Hit Ratio".to_string(),
                    condition: AlertCondition::CacheHitRatioBelow { threshold: 0.70 },
                    level: AlertLevel::Warning,
                    channels: vec![AlertChannel::Log],
                },
                AlertRule {
                    name: "High Error Rate".to_string(),
                    condition: AlertCondition::ErrorRateAbove { threshold: 0.01 },
                    level: AlertLevel::Error,
                    channels: vec![AlertChannel::Log, AlertChannel::Console],
                },
            ],
        }
    }
}

impl Default for DefaultAlertManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_alert_creation() {
        let alert = Alert::new(
            AlertLevel::Warning,
            "Test Alert".to_string(),
            "This is a test alert".to_string(),
            "test_source".to_string(),
        )
        .with_tag("component", "auth")
        .with_metric("latency_ms", 150.0)
        .with_runbook("https://wiki.example.com/runbook/auth");

        assert_eq!(alert.level, AlertLevel::Warning);
        assert_eq!(alert.title, "Test Alert");
        assert_eq!(alert.tags.get("component"), Some(&"auth".to_string()));
        assert_eq!(alert.metrics.get("latency_ms"), Some(&150.0));
        assert!(alert.runbook_url.is_some());
    }

    #[test]
    fn test_alert_dedup_key() {
        let alert1 = Alert::new(
            AlertLevel::Warning,
            "High Latency".to_string(),
            "Query latency is high".to_string(),
            "query_monitor".to_string(),
        );

        let alert2 = Alert::new(
            AlertLevel::Warning,
            "High Latency".to_string(),
            "Different message".to_string(),
            "query_monitor".to_string(),
        );

        assert_eq!(alert1.dedup_key(), alert2.dedup_key());
    }

    #[test]
    fn test_alert_manager_send() {
        let manager = DefaultAlertManager::new();

        let alert = Alert::new(
            AlertLevel::Info,
            "Test Info".to_string(),
            "Info message".to_string(),
            "test".to_string(),
        );

        manager.send_alert(alert.clone());

        let recent = manager.get_recent_alerts(1);
        assert_eq!(recent.len(), 1);
        assert_eq!(recent[0].title, "Test Info");
    }

    #[test]
    fn test_alert_throttling() {
        let config = AlertConfig {
            throttle_duration_seconds: 1,
            ..Default::default()
        }; // 1 second for testing

        let manager = DefaultAlertManager::with_config(config);

        let alert = Alert::new(
            AlertLevel::Warning,
            "Throttled Alert".to_string(),
            "This should be throttled".to_string(),
            "test".to_string(),
        );

        // First alert should go through
        manager.send_alert(alert.clone());
        assert_eq!(manager.get_recent_alerts(10).len(), 1);

        // Second alert should be throttled
        manager.send_alert(alert.clone());
        assert_eq!(manager.get_recent_alerts(10).len(), 1);

        // After throttle duration, alert should go through
        std::thread::sleep(Duration::from_millis(1100));
        manager.send_alert(alert);
        assert_eq!(manager.get_recent_alerts(10).len(), 2);
    }

    #[test]
    fn test_rate_limiting() {
        let config = AlertConfig {
            max_alerts_per_hour: 2,
            throttle_duration_seconds: 0,
            ..Default::default()
        }; // Disable throttling

        let manager = DefaultAlertManager::with_config(config);

        let alert = Alert::new(
            AlertLevel::Warning,
            "Rate Limited".to_string(),
            "Testing rate limits".to_string(),
            "test".to_string(),
        );

        // First two alerts should go through
        manager.send_alert(alert.clone());
        manager.send_alert(alert.clone());
        assert_eq!(manager.get_recent_alerts(10).len(), 2);

        // Third alert should be rate limited
        manager.send_alert(alert);
        assert_eq!(manager.get_recent_alerts(10).len(), 2);
    }

    #[test]
    fn test_alert_levels() {
        let manager = DefaultAlertManager::new();

        let info = Alert::new(
            AlertLevel::Info,
            "Info".to_string(),
            "msg".to_string(),
            "test".to_string(),
        );
        let warning = Alert::new(
            AlertLevel::Warning,
            "Warning".to_string(),
            "msg".to_string(),
            "test".to_string(),
        );
        let error = Alert::new(
            AlertLevel::Error,
            "Error".to_string(),
            "msg".to_string(),
            "test".to_string(),
        );
        let critical = Alert::new(
            AlertLevel::Critical,
            "Critical".to_string(),
            "msg".to_string(),
            "test".to_string(),
        );

        manager.send_alert(info);
        manager.send_alert(warning);
        manager.send_alert(error);
        manager.send_alert(critical);

        let recent = manager.get_recent_alerts(10);
        assert_eq!(recent.len(), 4);

        // Verify alerts are ordered correctly
        assert!(recent[0].level >= recent[1].level);
    }

    #[test]
    fn test_clear_alerts() {
        let manager = DefaultAlertManager::new();

        for i in 0..5 {
            let alert = Alert::new(
                AlertLevel::Info,
                format!("Alert {i}"),
                "Test".to_string(),
                "test".to_string(),
            );
            manager.send_alert(alert);
        }

        assert_eq!(manager.get_recent_alerts(10).len(), 5);

        manager.clear_alerts();
        assert_eq!(manager.get_recent_alerts(10).len(), 0);
    }
}
