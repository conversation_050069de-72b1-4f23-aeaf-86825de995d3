// Metrics collection infrastructure
// Provides centralized metrics collection with minimal overhead

use super::query_metrics::MetricAggregator;
// use crate::errors::ApplicationError; // Unused
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant, SystemTime};
use tracing::{debug, warn};

/// Core metrics collected for query execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryMetrics {
    pub query_name: String,
    pub execution_time: Duration,
    pub cache_hit: bool,
    pub cache_response_time: Option<Duration>,
    pub database_response_time: Option<Duration>,
    pub result_count: usize,
    pub error_type: Option<String>,
    pub user_context: UserContext,
    pub timestamp: SystemTime,
}

/// User context for metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserContext {
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub request_id: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

/// Cache operation metrics
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CacheMetrics {
    pub operation_type: CacheOperationType,
    pub cache_key: String,
    pub duration: Duration,
    pub success: bool,
    pub size_bytes: Option<usize>,
    pub ttl_seconds: Option<u64>,
    pub timestamp: SystemTime,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheOperationType {
    Get,
    Set,
    Delete,
    Invalidate,
}

/// Database operation metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseMetrics {
    pub operation_type: String,
    pub table_name: Option<String>,
    pub duration: Duration,
    pub rows_affected: usize,
    pub connection_acquisition_time: Option<Duration>,
    pub prepared_statement_used: bool,
    pub timestamp: SystemTime,
}

/// Performance snapshot at a point in time
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSnapshot {
    pub p50_latency: Duration,
    pub p95_latency: Duration,
    pub p99_latency: Duration,
    pub cache_hit_ratio: f64,
    pub error_rate: f64,
    pub queries_per_second: f64,
    pub sla_violations: u64,
    pub timestamp: SystemTime,
    pub period_seconds: u64,
}

/// Trait for metrics collection
pub trait MetricsCollector: Send + Sync {
    /// Record query execution metrics
    fn record_query_execution(&self, metrics: QueryMetrics);

    /// Record cache operation metrics
    fn record_cache_operation(&self, cache_metrics: CacheMetrics);

    /// Record database operation metrics
    fn record_database_operation(&self, db_metrics: DatabaseMetrics);

    /// Get current performance snapshot
    fn get_performance_snapshot(&self) -> PerformanceSnapshot;

    /// Get metrics for a specific query
    fn get_query_metrics(&self, query_name: &str) -> Option<QueryStatistics>;

    /// Clear all metrics (useful for testing)
    fn clear_metrics(&self);
}

/// In-memory implementation of MetricsCollector
pub struct InMemoryMetricsCollector {
    query_metrics: Arc<RwLock<HashMap<String, QueryMetricsData>>>,
    cache_metrics: Arc<RwLock<CacheMetricsData>>,
    database_metrics: Arc<RwLock<DatabaseMetricsData>>,
    start_time: Instant,
}

/// Query-specific metrics data
struct QueryMetricsData {
    aggregator: MetricAggregator,
    total_executions: u64,
    cache_hits: u64,
    errors: u64,
    sla_violations: u64,
    last_execution: Option<SystemTime>,
}

/// Cache metrics aggregation
struct CacheMetricsData {
    total_operations: u64,
    hits: u64,
    misses: u64,
    errors: u64,
    response_time_aggregator: MetricAggregator,
}

/// Database metrics aggregation
#[allow(dead_code)]
struct DatabaseMetricsData {
    total_operations: u64,
    errors: u64,
    response_time_aggregator: MetricAggregator,
    connection_acquisition_aggregator: MetricAggregator,
}

/// Query statistics for external consumption
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryStatistics {
    pub query_name: String,
    pub total_executions: u64,
    pub cache_hit_ratio: f64,
    pub error_rate: f64,
    pub p50_latency: Option<Duration>,
    pub p95_latency: Option<Duration>,
    pub p99_latency: Option<Duration>,
    pub min_latency: Option<Duration>,
    pub max_latency: Option<Duration>,
    pub avg_latency: Option<Duration>,
    pub sla_violations: u64,
    pub last_execution: Option<SystemTime>,
}

impl InMemoryMetricsCollector {
    /// Create a new in-memory metrics collector
    pub fn new() -> Self {
        Self {
            query_metrics: Arc::new(RwLock::new(HashMap::new())),
            cache_metrics: Arc::new(RwLock::new(CacheMetricsData {
                total_operations: 0,
                hits: 0,
                misses: 0,
                errors: 0,
                response_time_aggregator: MetricAggregator::new(10000),
            })),
            database_metrics: Arc::new(RwLock::new(DatabaseMetricsData {
                total_operations: 0,
                errors: 0,
                response_time_aggregator: MetricAggregator::new(10000),
                connection_acquisition_aggregator: MetricAggregator::new(10000),
            })),
            start_time: Instant::now(),
        }
    }
}

impl MetricsCollector for InMemoryMetricsCollector {
    fn record_query_execution(&self, metrics: QueryMetrics) {
        let mut query_metrics = match self.query_metrics.write() {
            Ok(guard) => guard,
            Err(e) => {
                warn!("Failed to acquire metrics lock: {}", e);
                return;
            }
        };

        let data = query_metrics
            .entry(metrics.query_name.clone())
            .or_insert_with(|| QueryMetricsData {
                aggregator: MetricAggregator::new(10000),
                total_executions: 0,
                cache_hits: 0,
                errors: 0,
                sla_violations: 0,
                last_execution: None,
            });

        data.total_executions += 1;
        data.aggregator.add_sample(metrics.execution_time);
        data.last_execution = Some(metrics.timestamp);

        if metrics.cache_hit {
            data.cache_hits += 1;
        }

        if metrics.error_type.is_some() {
            data.errors += 1;
        }

        // Check SLA violation (100ms threshold)
        if metrics.execution_time > Duration::from_millis(100) {
            data.sla_violations += 1;
            warn!(
                "SLA violation: {} took {:?}",
                metrics.query_name, metrics.execution_time
            );
        }

        debug!(
            "Recorded query metrics: {} - {:?}, cache_hit: {}, error: {}",
            metrics.query_name,
            metrics.execution_time,
            metrics.cache_hit,
            metrics.error_type.is_some()
        );
    }

    fn record_cache_operation(&self, cache_metrics: CacheMetrics) {
        let mut metrics = match self.cache_metrics.write() {
            Ok(guard) => guard,
            Err(e) => {
                warn!("Failed to acquire cache metrics lock: {}", e);
                return;
            }
        };

        metrics.total_operations += 1;
        metrics
            .response_time_aggregator
            .add_sample(cache_metrics.duration);

        match cache_metrics.operation_type {
            CacheOperationType::Get => {
                if cache_metrics.success {
                    metrics.hits += 1;
                } else {
                    metrics.misses += 1;
                }
            }
            _ => {
                if !cache_metrics.success {
                    metrics.errors += 1;
                }
            }
        }

        // Warn if cache response exceeds 5ms SLA
        if cache_metrics.duration > Duration::from_millis(5) {
            warn!(
                "Cache operation exceeded 5ms SLA: {:?} took {:?}",
                cache_metrics.operation_type, cache_metrics.duration
            );
        }
    }

    fn record_database_operation(&self, db_metrics: DatabaseMetrics) {
        let mut metrics = match self.database_metrics.write() {
            Ok(guard) => guard,
            Err(e) => {
                warn!("Failed to acquire database metrics lock: {}", e);
                return;
            }
        };

        metrics.total_operations += 1;
        metrics
            .response_time_aggregator
            .add_sample(db_metrics.duration);

        if let Some(conn_time) = db_metrics.connection_acquisition_time {
            metrics
                .connection_acquisition_aggregator
                .add_sample(conn_time);
        }

        // Count errors based on duration threshold
        if db_metrics.duration > Duration::from_millis(50) {
            warn!(
                "Database operation exceeded budget: {} took {:?}",
                db_metrics.operation_type, db_metrics.duration
            );
        }
    }

    fn get_performance_snapshot(&self) -> PerformanceSnapshot {
        let query_metrics = self.query_metrics.read().unwrap();
        let cache_metrics = self.cache_metrics.read().unwrap();

        // Calculate aggregated statistics
        let mut all_latencies = Vec::new();
        let mut total_queries = 0u64;
        let mut total_errors = 0u64;
        let mut total_sla_violations = 0u64;

        for data in query_metrics.values() {
            total_queries += data.total_executions;
            total_errors += data.errors;
            total_sla_violations += data.sla_violations;

            // Collect sample latencies for percentile calculation
            if let Some(p50) = data.aggregator.p50() {
                all_latencies.push(p50);
            }
        }

        // Calculate cache hit ratio
        let cache_hit_ratio = if cache_metrics.total_operations > 0 {
            cache_metrics.hits as f64 / cache_metrics.total_operations as f64
        } else {
            0.0
        };

        // Calculate error rate
        let error_rate = if total_queries > 0 {
            total_errors as f64 / total_queries as f64
        } else {
            0.0
        };

        // Calculate queries per second
        let elapsed_seconds = self.start_time.elapsed().as_secs_f64();
        let queries_per_second = if elapsed_seconds > 0.0 {
            total_queries as f64 / elapsed_seconds
        } else {
            0.0
        };

        // Create aggregator for overall percentiles
        let mut overall_aggregator = MetricAggregator::new(10000);
        for latency in all_latencies {
            overall_aggregator.add_sample(latency);
        }

        PerformanceSnapshot {
            p50_latency: overall_aggregator.p50().unwrap_or_default(),
            p95_latency: overall_aggregator.p95().unwrap_or_default(),
            p99_latency: overall_aggregator.p99().unwrap_or_default(),
            cache_hit_ratio,
            error_rate,
            queries_per_second,
            sla_violations: total_sla_violations,
            timestamp: SystemTime::now(),
            period_seconds: self.start_time.elapsed().as_secs(),
        }
    }

    fn get_query_metrics(&self, query_name: &str) -> Option<QueryStatistics> {
        let metrics = self.query_metrics.read().ok()?;
        let data = metrics.get(query_name)?;

        let cache_hit_ratio = if data.total_executions > 0 {
            data.cache_hits as f64 / data.total_executions as f64
        } else {
            0.0
        };

        let error_rate = if data.total_executions > 0 {
            data.errors as f64 / data.total_executions as f64
        } else {
            0.0
        };

        Some(QueryStatistics {
            query_name: query_name.to_string(),
            total_executions: data.total_executions,
            cache_hit_ratio,
            error_rate,
            p50_latency: data.aggregator.p50(),
            p95_latency: data.aggregator.p95(),
            p99_latency: data.aggregator.p99(),
            min_latency: data.aggregator.min(),
            max_latency: data.aggregator.max(),
            avg_latency: data.aggregator.average(),
            sla_violations: data.sla_violations,
            last_execution: data.last_execution,
        })
    }

    fn clear_metrics(&self) {
        if let Ok(mut metrics) = self.query_metrics.write() {
            metrics.clear();
        }

        if let Ok(mut metrics) = self.cache_metrics.write() {
            *metrics = CacheMetricsData {
                total_operations: 0,
                hits: 0,
                misses: 0,
                errors: 0,
                response_time_aggregator: MetricAggregator::new(10000),
            };
        }

        if let Ok(mut metrics) = self.database_metrics.write() {
            *metrics = DatabaseMetricsData {
                total_operations: 0,
                errors: 0,
                response_time_aggregator: MetricAggregator::new(10000),
                connection_acquisition_aggregator: MetricAggregator::new(10000),
            };
        }
    }
}

impl Default for InMemoryMetricsCollector {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_metrics_collector_creation() {
        let collector = InMemoryMetricsCollector::new();
        let snapshot = collector.get_performance_snapshot();

        assert_eq!(snapshot.sla_violations, 0);
        assert_eq!(snapshot.error_rate, 0.0);
        assert_eq!(snapshot.cache_hit_ratio, 0.0);
    }

    #[test]
    fn test_query_metrics_recording() {
        let collector = InMemoryMetricsCollector::new();

        let metrics = QueryMetrics {
            query_name: "get_user_profile".to_string(),
            execution_time: Duration::from_millis(25),
            cache_hit: true,
            cache_response_time: Some(Duration::from_millis(2)),
            database_response_time: None,
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: Some("user123".to_string()),
                session_id: Some("session456".to_string()),
                request_id: "req789".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        collector.record_query_execution(metrics);

        let stats = collector.get_query_metrics("get_user_profile").unwrap();
        assert_eq!(stats.total_executions, 1);
        assert_eq!(stats.cache_hit_ratio, 1.0);
        assert_eq!(stats.error_rate, 0.0);
        assert_eq!(stats.sla_violations, 0);
    }

    #[test]
    fn test_sla_violation_detection() {
        let collector = InMemoryMetricsCollector::new();

        let metrics = QueryMetrics {
            query_name: "slow_query".to_string(),
            execution_time: Duration::from_millis(150), // Exceeds 100ms SLA
            cache_hit: false,
            cache_response_time: None,
            database_response_time: Some(Duration::from_millis(140)),
            result_count: 100,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req123".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        collector.record_query_execution(metrics);

        let stats = collector.get_query_metrics("slow_query").unwrap();
        assert_eq!(stats.sla_violations, 1);

        let snapshot = collector.get_performance_snapshot();
        assert_eq!(snapshot.sla_violations, 1);
    }

    #[test]
    fn test_cache_metrics_recording() {
        let collector = InMemoryMetricsCollector::new();

        // Record cache hit
        collector.record_cache_operation(CacheMetrics {
            operation_type: CacheOperationType::Get,
            cache_key: "user:123".to_string(),
            duration: Duration::from_millis(2),
            success: true,
            size_bytes: Some(1024),
            ttl_seconds: Some(300),
            timestamp: SystemTime::now(),
        });

        // Record cache miss
        collector.record_cache_operation(CacheMetrics {
            operation_type: CacheOperationType::Get,
            cache_key: "user:456".to_string(),
            duration: Duration::from_millis(1),
            success: false,
            size_bytes: None,
            ttl_seconds: None,
            timestamp: SystemTime::now(),
        });

        let snapshot = collector.get_performance_snapshot();
        assert_eq!(snapshot.cache_hit_ratio, 0.5); // 1 hit, 1 miss
    }

    #[test]
    fn test_database_metrics_recording() {
        let collector = InMemoryMetricsCollector::new();

        collector.record_database_operation(DatabaseMetrics {
            operation_type: "SELECT".to_string(),
            table_name: Some("users".to_string()),
            duration: Duration::from_millis(15),
            rows_affected: 1,
            connection_acquisition_time: Some(Duration::from_millis(2)),
            prepared_statement_used: true,
            timestamp: SystemTime::now(),
        });

        // Database metrics don't directly affect snapshot, but are recorded
        let snapshot = collector.get_performance_snapshot();
        assert!(snapshot.timestamp > SystemTime::UNIX_EPOCH);
    }

    #[test]
    fn test_error_rate_calculation() {
        let collector = InMemoryMetricsCollector::new();

        // Record successful query
        collector.record_query_execution(QueryMetrics {
            query_name: "test_query".to_string(),
            execution_time: Duration::from_millis(10),
            cache_hit: false,
            cache_response_time: None,
            database_response_time: None,
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req1".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        });

        // Record failed query
        collector.record_query_execution(QueryMetrics {
            query_name: "test_query".to_string(),
            execution_time: Duration::from_millis(5),
            cache_hit: false,
            cache_response_time: None,
            database_response_time: None,
            result_count: 0,
            error_type: Some("DatabaseError".to_string()),
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req2".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        });

        let stats = collector.get_query_metrics("test_query").unwrap();
        assert_eq!(stats.total_executions, 2);
        assert_eq!(stats.error_rate, 0.5); // 1 error out of 2 executions
    }

    #[test]
    fn test_clear_metrics() {
        let collector = InMemoryMetricsCollector::new();

        // Add some metrics
        collector.record_query_execution(QueryMetrics {
            query_name: "test".to_string(),
            execution_time: Duration::from_millis(10),
            cache_hit: true,
            cache_response_time: None,
            database_response_time: None,
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req1".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        });

        // Clear metrics
        collector.clear_metrics();

        // Verify metrics are cleared
        assert!(collector.get_query_metrics("test").is_none());

        let snapshot = collector.get_performance_snapshot();
        assert_eq!(snapshot.sla_violations, 0);
        assert_eq!(snapshot.queries_per_second, 0.0);
    }
}
