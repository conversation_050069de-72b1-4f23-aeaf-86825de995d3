// SLA monitoring and compliance tracking
// Provides real-time SLA violation detection and reporting

use super::metrics_collector::QueryMetrics;
// use crate::errors::ApplicationError; // Unused
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tracing::{error, info, warn};

/// SLA monitoring trait
pub trait SlaMonitor: Send + Sync {
    /// Check if query execution meets SLA requirements
    fn check_sla_compliance(&self, metrics: &QueryMetrics) -> SlaStatus;

    /// Trigger alert for SLA violation
    fn trigger_alert(&self, violation: SlaViolation);

    /// Get SLA compliance report for a time period
    fn get_sla_report(&self, period: TimePeriod) -> SlaReport;

    /// Update SLA configuration
    fn update_config(&self, config: SlaConfig);
}

/// SLA configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlaConfig {
    pub query_thresholds: HashMap<String, QuerySlaConfig>,
    pub default_threshold_ms: u64,
    pub warning_threshold_ms: u64,
    pub critical_threshold_ms: u64,
    pub cache_hit_ratio_threshold: f64,
    pub error_rate_threshold: f64,
    pub connection_pool_threshold: f64,
    pub alert_cooldown_seconds: u64,
    pub enable_auto_escalation: bool,
}

/// Query-specific SLA configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuerySlaConfig {
    pub name: String,
    pub max_latency_ms: u64,
    pub warning_latency_ms: u64,
    pub required_cache_hit_ratio: Option<f64>,
    pub max_error_rate: Option<f64>,
    pub priority: SlaPriority,
}

/// SLA priority levels
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum SlaPriority {
    Low,
    Medium,
    High,
    Critical,
}

/// SLA compliance status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlaStatus {
    pub compliant: bool,
    pub latency_ms: u64,
    pub threshold_ms: u64,
    pub violation_type: Option<ViolationType>,
    pub severity: Option<ViolationSeverity>,
    pub message: String,
}

/// Types of SLA violations
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ViolationType {
    LatencyExceeded,
    CacheHitRatioLow,
    ErrorRateHigh,
    ConnectionPoolExhausted,
    ResourceLimitExceeded,
}

/// Severity of SLA violations
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum ViolationSeverity {
    Info,
    Warning,
    Critical,
    Emergency,
}

/// SLA violation details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlaViolation {
    pub query_name: String,
    pub violation_type: ViolationType,
    pub severity: ViolationSeverity,
    pub actual_value: f64,
    pub threshold_value: f64,
    pub timestamp: SystemTime,
    pub user_context: Option<String>,
    pub request_id: String,
    pub duration: Duration,
    pub auto_escalated: bool,
}

/// Time period for reporting
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum TimePeriod {
    LastHour,
    Last24Hours,
    Last7Days,
    Last30Days,
    Custom { start: SystemTime, end: SystemTime },
}

/// SLA compliance report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlaReport {
    pub period: TimePeriod,
    pub total_queries: u64,
    pub compliant_queries: u64,
    pub compliance_percentage: f64,
    pub violations_by_type: HashMap<ViolationType, u64>,
    pub violations_by_severity: HashMap<ViolationSeverity, u64>,
    pub worst_performers: Vec<QuerySlaStats>,
    pub trend_analysis: TrendAnalysis,
    pub recommendations: Vec<String>,
}

/// Query-specific SLA statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuerySlaStats {
    pub query_name: String,
    pub total_executions: u64,
    pub violations: u64,
    pub compliance_rate: f64,
    pub avg_latency_ms: u64,
    pub p95_latency_ms: u64,
    pub worst_latency_ms: u64,
}

/// Trend analysis for SLA compliance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysis {
    pub improving: bool,
    pub compliance_trend: Vec<f64>, // Historical compliance percentages
    pub violation_trend: Vec<u64>,  // Historical violation counts
    pub prediction: CompliancePrediction,
}

/// Compliance prediction
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompliancePrediction {
    pub next_hour_compliance: f64,
    pub risk_level: RiskLevel,
    pub recommended_actions: Vec<String>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// Default SLA monitor implementation
pub struct DefaultSlaMonitor {
    config: Arc<RwLock<SlaConfig>>,
    violations: Arc<RwLock<VecDeque<SlaViolation>>>,
    query_stats: Arc<RwLock<HashMap<String, QuerySlaTracking>>>,
    last_alert_times: Arc<RwLock<HashMap<String, SystemTime>>>,
}

/// Internal tracking for query SLA statistics
struct QuerySlaTracking {
    executions: u64,
    violations: u64,
    total_latency_ms: u64,
    latencies: Vec<u64>,
    last_violation: Option<SystemTime>,
}

impl Default for DefaultSlaMonitor {
    fn default() -> Self {
        Self::new()
    }
}

impl DefaultSlaMonitor {
    /// Create new SLA monitor with default configuration
    pub fn new() -> Self {
        Self::with_config(SlaConfig::default())
    }

    /// Create new SLA monitor with custom configuration
    pub fn with_config(config: SlaConfig) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            violations: Arc::new(RwLock::new(VecDeque::with_capacity(10000))),
            query_stats: Arc::new(RwLock::new(HashMap::new())),
            last_alert_times: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Check if alert should be throttled
    fn should_throttle_alert(&self, query_name: &str) -> bool {
        let config = self.config.read().unwrap();
        let last_alerts = self.last_alert_times.read().unwrap();

        if let Some(last_alert) = last_alerts.get(query_name) {
            if let Ok(elapsed) = SystemTime::now().duration_since(*last_alert) {
                return elapsed.as_secs() < config.alert_cooldown_seconds;
            }
        }
        false
    }

    /// Record alert time
    fn record_alert_time(&self, query_name: &str) {
        let mut last_alerts = self.last_alert_times.write().unwrap();
        last_alerts.insert(query_name.to_string(), SystemTime::now());
    }

    /// Determine violation severity based on thresholds
    fn determine_severity(
        &self,
        latency_ms: u64,
        config: &SlaConfig,
        query_config: Option<&QuerySlaConfig>,
    ) -> ViolationSeverity {
        if let Some(qc) = query_config {
            match qc.priority {
                SlaPriority::Critical => {
                    if latency_ms > qc.max_latency_ms * 2 {
                        ViolationSeverity::Emergency
                    } else if latency_ms > qc.max_latency_ms {
                        ViolationSeverity::Critical
                    } else {
                        ViolationSeverity::Warning
                    }
                }
                SlaPriority::High => {
                    if latency_ms > qc.max_latency_ms * 2 {
                        ViolationSeverity::Critical
                    } else if latency_ms > qc.max_latency_ms {
                        ViolationSeverity::Warning
                    } else {
                        ViolationSeverity::Info
                    }
                }
                _ => {
                    if latency_ms > config.critical_threshold_ms {
                        ViolationSeverity::Warning
                    } else {
                        ViolationSeverity::Info
                    }
                }
            }
        } else {
            // Use default thresholds
            if latency_ms >= config.critical_threshold_ms * 2 {
                ViolationSeverity::Emergency
            } else if latency_ms >= config.critical_threshold_ms {
                ViolationSeverity::Critical
            } else if latency_ms >= config.warning_threshold_ms {
                ViolationSeverity::Warning
            } else {
                ViolationSeverity::Info
            }
        }
    }
}

impl SlaMonitor for DefaultSlaMonitor {
    fn check_sla_compliance(&self, metrics: &QueryMetrics) -> SlaStatus {
        let config = self.config.read().unwrap();
        let latency_ms = metrics.execution_time.as_millis() as u64;

        // Get query-specific configuration
        let query_config = config.query_thresholds.get(&metrics.query_name);
        let threshold_ms = query_config
            .map(|c| c.max_latency_ms)
            .unwrap_or(config.default_threshold_ms);

        // Update statistics
        {
            let mut stats = self.query_stats.write().unwrap();
            let tracking =
                stats
                    .entry(metrics.query_name.clone())
                    .or_insert_with(|| QuerySlaTracking {
                        executions: 0,
                        violations: 0,
                        total_latency_ms: 0,
                        latencies: Vec::new(),
                        last_violation: None,
                    });

            tracking.executions += 1;
            tracking.total_latency_ms += latency_ms;
            tracking.latencies.push(latency_ms);

            // Keep only recent latencies (last 1000)
            if tracking.latencies.len() > 1000 {
                tracking.latencies.remove(0);
            }
        }

        // Check latency compliance
        if latency_ms > threshold_ms {
            let severity = self.determine_severity(latency_ms, &config, query_config);

            // Update violation tracking
            {
                let mut stats = self.query_stats.write().unwrap();
                if let Some(tracking) = stats.get_mut(&metrics.query_name) {
                    tracking.violations += 1;
                    tracking.last_violation = Some(metrics.timestamp);
                }
            }

            SlaStatus {
                compliant: false,
                latency_ms,
                threshold_ms,
                violation_type: Some(ViolationType::LatencyExceeded),
                severity: Some(severity),
                message: format!(
                    "Query '{}' exceeded SLA: {}ms > {}ms threshold",
                    metrics.query_name, latency_ms, threshold_ms
                ),
            }
        } else {
            SlaStatus {
                compliant: true,
                latency_ms,
                threshold_ms,
                violation_type: None,
                severity: None,
                message: format!(
                    "Query '{}' within SLA: {}ms <= {}ms threshold",
                    metrics.query_name, latency_ms, threshold_ms
                ),
            }
        }
    }

    fn trigger_alert(&self, violation: SlaViolation) {
        // Check throttling
        if self.should_throttle_alert(&violation.query_name) {
            return;
        }

        // Record violation
        {
            let mut violations = self.violations.write().unwrap();
            violations.push_back(violation.clone());

            // Maintain size limit
            if violations.len() > 10000 {
                violations.pop_front();
            }
        }

        // Log alert based on severity
        match violation.severity {
            ViolationSeverity::Emergency => {
                error!(
                    "EMERGENCY SLA violation: {} - {:?} ({}ms > {}ms)",
                    violation.query_name,
                    violation.violation_type,
                    violation.actual_value,
                    violation.threshold_value
                );
            }
            ViolationSeverity::Critical => {
                error!(
                    "CRITICAL SLA violation: {} - {:?} ({}ms > {}ms)",
                    violation.query_name,
                    violation.violation_type,
                    violation.actual_value,
                    violation.threshold_value
                );
            }
            ViolationSeverity::Warning => {
                warn!(
                    "SLA warning: {} - {:?} ({}ms > {}ms)",
                    violation.query_name,
                    violation.violation_type,
                    violation.actual_value,
                    violation.threshold_value
                );
            }
            ViolationSeverity::Info => {
                info!(
                    "SLA info: {} - {:?} ({}ms > {}ms)",
                    violation.query_name,
                    violation.violation_type,
                    violation.actual_value,
                    violation.threshold_value
                );
            }
        }

        // Record alert time
        self.record_alert_time(&violation.query_name);

        // Auto-escalation logic
        let config = self.config.read().unwrap();
        if config.enable_auto_escalation && violation.severity >= ViolationSeverity::Critical {
            // In production, this would trigger PagerDuty, Slack, etc.
            warn!(
                "Auto-escalating critical SLA violation for {}",
                violation.query_name
            );
        }
    }

    fn get_sla_report(&self, period: TimePeriod) -> SlaReport {
        let stats = self.query_stats.read().unwrap();
        let violations = self.violations.read().unwrap();

        // Calculate time bounds
        let (start_time, end_time) = match period {
            TimePeriod::LastHour => {
                let end = SystemTime::now();
                let start = end - Duration::from_secs(3600);
                (start, end)
            }
            TimePeriod::Last24Hours => {
                let end = SystemTime::now();
                let start = end - Duration::from_secs(86400);
                (start, end)
            }
            TimePeriod::Last7Days => {
                let end = SystemTime::now();
                let start = end - Duration::from_secs(604800);
                (start, end)
            }
            TimePeriod::Last30Days => {
                let end = SystemTime::now();
                let start = end - Duration::from_secs(2592000);
                (start, end)
            }
            TimePeriod::Custom { start, end } => (start, end),
        };

        // Filter violations within period
        let period_violations: Vec<_> = violations
            .iter()
            .filter(|v| v.timestamp >= start_time && v.timestamp <= end_time)
            .cloned()
            .collect();

        // Calculate statistics
        let total_queries: u64 = stats.values().map(|s| s.executions).sum();
        let total_violations: u64 = period_violations.len() as u64;
        let compliant_queries = total_queries.saturating_sub(total_violations);
        let compliance_percentage = if total_queries > 0 {
            (compliant_queries as f64 / total_queries as f64) * 100.0
        } else {
            100.0
        };

        // Count violations by type
        let mut violations_by_type = HashMap::new();
        for violation in &period_violations {
            *violations_by_type
                .entry(violation.violation_type.clone())
                .or_insert(0) += 1;
        }

        // Count violations by severity
        let mut violations_by_severity = HashMap::new();
        for violation in &period_violations {
            *violations_by_severity
                .entry(violation.severity)
                .or_insert(0) += 1;
        }

        // Find worst performers
        let mut worst_performers: Vec<_> = stats
            .iter()
            .map(|(name, tracking)| {
                let compliance_rate = if tracking.executions > 0 {
                    ((tracking.executions - tracking.violations) as f64
                        / tracking.executions as f64)
                        * 100.0
                } else {
                    100.0
                };

                let avg_latency_ms = if tracking.executions > 0 {
                    tracking.total_latency_ms / tracking.executions
                } else {
                    0
                };

                let p95_latency_ms = if !tracking.latencies.is_empty() {
                    let mut sorted = tracking.latencies.clone();
                    sorted.sort_unstable();
                    sorted[sorted.len() * 95 / 100]
                } else {
                    0
                };

                QuerySlaStats {
                    query_name: name.clone(),
                    total_executions: tracking.executions,
                    violations: tracking.violations,
                    compliance_rate,
                    avg_latency_ms,
                    p95_latency_ms,
                    worst_latency_ms: tracking.latencies.iter().max().copied().unwrap_or(0),
                }
            })
            .filter(|s| s.violations > 0)
            .collect();

        worst_performers.sort_by(|a, b| a.compliance_rate.partial_cmp(&b.compliance_rate).unwrap());
        worst_performers.truncate(10);

        // Generate trend analysis (simplified)
        let trend_analysis = TrendAnalysis {
            improving: compliance_percentage > 95.0,
            compliance_trend: vec![compliance_percentage], // In production, would have historical data
            violation_trend: vec![total_violations],
            prediction: CompliancePrediction {
                next_hour_compliance: compliance_percentage,
                risk_level: if compliance_percentage < 90.0 {
                    RiskLevel::High
                } else if compliance_percentage < 95.0 {
                    RiskLevel::Medium
                } else {
                    RiskLevel::Low
                },
                recommended_actions: generate_recommendations(
                    compliance_percentage,
                    &worst_performers,
                ),
            },
        };

        SlaReport {
            period,
            total_queries,
            compliant_queries,
            compliance_percentage,
            violations_by_type,
            violations_by_severity,
            worst_performers: worst_performers.clone(),
            trend_analysis,
            recommendations: generate_recommendations(compliance_percentage, &worst_performers),
        }
    }

    fn update_config(&self, config: SlaConfig) {
        let mut current_config = self.config.write().unwrap();
        *current_config = config;
        info!("SLA configuration updated");
    }
}

impl Default for SlaConfig {
    fn default() -> Self {
        let mut query_thresholds = HashMap::new();

        // Configure specific query SLAs based on requirements
        query_thresholds.insert(
            "authenticate_user".to_string(),
            QuerySlaConfig {
                name: "authenticate_user".to_string(),
                max_latency_ms: 10,
                warning_latency_ms: 8,
                required_cache_hit_ratio: None,
                max_error_rate: Some(0.001),
                priority: SlaPriority::Critical,
            },
        );

        query_thresholds.insert(
            "check_permissions".to_string(),
            QuerySlaConfig {
                name: "check_permissions".to_string(),
                max_latency_ms: 8,
                warning_latency_ms: 6,
                required_cache_hit_ratio: Some(0.80),
                max_error_rate: Some(0.001),
                priority: SlaPriority::Critical,
            },
        );

        query_thresholds.insert(
            "get_user_profile".to_string(),
            QuerySlaConfig {
                name: "get_user_profile".to_string(),
                max_latency_ms: 25,
                warning_latency_ms: 20,
                required_cache_hit_ratio: Some(0.70),
                max_error_rate: Some(0.01),
                priority: SlaPriority::High,
            },
        );

        Self {
            query_thresholds,
            default_threshold_ms: 100,
            warning_threshold_ms: 75,
            critical_threshold_ms: 200,
            cache_hit_ratio_threshold: 0.80,
            error_rate_threshold: 0.01,
            connection_pool_threshold: 0.80,
            alert_cooldown_seconds: 300, // 5 minutes
            enable_auto_escalation: true,
        }
    }
}

/// Generate recommendations based on SLA report
fn generate_recommendations(
    compliance_percentage: f64,
    worst_performers: &[QuerySlaStats],
) -> Vec<String> {
    let mut recommendations = Vec::new();

    if compliance_percentage < 95.0 {
        recommendations
            .push("Overall SLA compliance below 95% target - investigate root causes".to_string());
    }

    for query in worst_performers.iter().take(3) {
        if query.avg_latency_ms > 50 {
            recommendations.push(format!(
                "Optimize '{}' query - average latency {}ms exceeds budget",
                query.query_name, query.avg_latency_ms
            ));
        }

        if query.compliance_rate < 90.0 {
            recommendations.push(format!(
                "Critical: '{}' has only {:.1}% SLA compliance",
                query.query_name, query.compliance_rate
            ));
        }
    }

    if worst_performers.iter().any(|q| q.worst_latency_ms > 500) {
        recommendations
            .push("Investigate queries with extreme latency spikes (>500ms)".to_string());
    }

    recommendations
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::monitoring::metrics_collector::UserContext;

    #[test]
    fn test_sla_compliance_check() {
        let monitor = DefaultSlaMonitor::new();

        let metrics = QueryMetrics {
            query_name: "test_query".to_string(),
            execution_time: Duration::from_millis(50),
            cache_hit: false,
            cache_response_time: None,
            database_response_time: None,
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req123".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        let status = monitor.check_sla_compliance(&metrics);
        assert!(status.compliant);
        assert_eq!(status.latency_ms, 50);
        assert_eq!(status.threshold_ms, 100);
    }

    #[test]
    fn test_sla_violation_detection() {
        let monitor = DefaultSlaMonitor::new();

        let metrics = QueryMetrics {
            query_name: "slow_query".to_string(),
            execution_time: Duration::from_millis(150),
            cache_hit: false,
            cache_response_time: None,
            database_response_time: None,
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req456".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        let status = monitor.check_sla_compliance(&metrics);
        assert!(!status.compliant);
        assert_eq!(status.violation_type, Some(ViolationType::LatencyExceeded));
        assert!(status.severity.is_some());
    }

    #[test]
    fn test_query_specific_sla() {
        let monitor = DefaultSlaMonitor::new();

        // Test critical query with strict SLA
        let metrics = QueryMetrics {
            query_name: "authenticate_user".to_string(),
            execution_time: Duration::from_millis(12), // Exceeds 10ms limit
            cache_hit: false,
            cache_response_time: None,
            database_response_time: None,
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: None,
                session_id: None,
                request_id: "req789".to_string(),
                ip_address: None,
                user_agent: None,
            },
            timestamp: SystemTime::now(),
        };

        let status = monitor.check_sla_compliance(&metrics);
        assert!(!status.compliant);
        assert_eq!(status.threshold_ms, 10); // Query-specific threshold
    }

    #[test]
    fn test_alert_throttling() {
        let monitor = DefaultSlaMonitor::new();

        let violation = SlaViolation {
            query_name: "test_query".to_string(),
            violation_type: ViolationType::LatencyExceeded,
            severity: ViolationSeverity::Warning,
            actual_value: 150.0,
            threshold_value: 100.0,
            timestamp: SystemTime::now(),
            user_context: None,
            request_id: "req123".to_string(),
            duration: Duration::from_millis(150),
            auto_escalated: false,
        };

        // First alert should not be throttled
        assert!(!monitor.should_throttle_alert(&violation.query_name));

        // Record alert
        monitor.record_alert_time(&violation.query_name);

        // Immediate second alert should be throttled
        assert!(monitor.should_throttle_alert(&violation.query_name));
    }

    #[test]
    fn test_sla_report_generation() {
        let monitor = DefaultSlaMonitor::new();

        // Record some metrics
        for i in 0..10 {
            let metrics = QueryMetrics {
                query_name: "test_query".to_string(),
                execution_time: Duration::from_millis(if i % 3 == 0 { 150 } else { 50 }),
                cache_hit: i % 2 == 0,
                cache_response_time: None,
                database_response_time: None,
                result_count: 1,
                error_type: None,
                user_context: UserContext {
                    user_id: None,
                    session_id: None,
                    request_id: format!("req{i}"),
                    ip_address: None,
                    user_agent: None,
                },
                timestamp: SystemTime::now(),
            };

            let status = monitor.check_sla_compliance(&metrics);
            if !status.compliant {
                monitor.trigger_alert(SlaViolation {
                    query_name: metrics.query_name,
                    violation_type: ViolationType::LatencyExceeded,
                    severity: status.severity.unwrap(),
                    actual_value: status.latency_ms as f64,
                    threshold_value: status.threshold_ms as f64,
                    timestamp: metrics.timestamp,
                    user_context: None,
                    request_id: metrics.user_context.request_id,
                    duration: metrics.execution_time,
                    auto_escalated: false,
                });
            }
        }

        let report = monitor.get_sla_report(TimePeriod::LastHour);
        assert!(report.total_queries > 0);
        assert!(report.compliance_percentage <= 100.0);
        assert!(!report.worst_performers.is_empty());
    }
}
