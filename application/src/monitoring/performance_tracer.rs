// Distributed tracing integration for performance monitoring
// Provides end-to-end request tracing across cache and database layers

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tracing::{debug, info_span};
use uuid::Uuid;

/// Performance tracing trait
pub trait PerformanceTracer: Send + Sync {
    /// Start a new trace for an operation
    fn start_trace(&self, operation: &str) -> TraceContext;

    /// Record a span within a trace
    fn record_span(&self, span: PerformanceSpan);

    /// Complete a trace and finalize metrics
    fn complete_trace(&self, context: TraceContext);

    /// Get trace details for analysis
    fn get_trace(&self, trace_id: &str) -> Option<TraceDetails>;
}

/// Trace context for tracking distributed operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TraceContext {
    pub trace_id: String,
    pub parent_span_id: Option<String>,
    pub span_id: String,
    pub operation: String,
    #[serde(skip, default = "Instant::now")]
    pub start_time: Instant,
    pub tags: std::collections::HashMap<String, String>,
    pub baggage: std::collections::HashMap<String, String>,
}

impl TraceContext {
    /// Create a new root trace context
    pub fn new(operation: &str) -> Self {
        Self {
            trace_id: Uuid::new_v4().to_string(),
            parent_span_id: None,
            span_id: Uuid::new_v4().to_string(),
            operation: operation.to_string(),
            start_time: Instant::now(),
            tags: std::collections::HashMap::new(),
            baggage: std::collections::HashMap::new(),
        }
    }

    /// Create a child span context
    pub fn child(&self, operation: &str) -> Self {
        Self {
            trace_id: self.trace_id.clone(),
            parent_span_id: Some(self.span_id.clone()),
            span_id: Uuid::new_v4().to_string(),
            operation: operation.to_string(),
            start_time: Instant::now(),
            tags: std::collections::HashMap::new(),
            baggage: self.baggage.clone(), // Propagate baggage
        }
    }

    /// Add a tag to the context
    pub fn with_tag(mut self, key: &str, value: &str) -> Self {
        self.tags.insert(key.to_string(), value.to_string());
        self
    }

    /// Add baggage item (propagated to child spans)
    pub fn with_baggage(mut self, key: &str, value: &str) -> Self {
        self.baggage.insert(key.to_string(), value.to_string());
        self
    }

    /// Get elapsed time since span start
    pub fn elapsed(&self) -> Duration {
        self.start_time.elapsed()
    }
}

/// Performance span representing a unit of work
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSpan {
    pub trace_id: String,
    pub span_id: String,
    pub parent_span_id: Option<String>,
    pub operation: String,
    pub start_time: SystemTime,
    pub duration: Duration,
    pub span_type: SpanType,
    pub status: SpanStatus,
    pub tags: std::collections::HashMap<String, String>,
    pub events: Vec<SpanEvent>,
    pub resource_usage: Option<ResourceUsage>,
}

/// Types of spans in the system
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum SpanType {
    Query,
    Cache,
    Database,
    Authorization,
    Serialization,
    Network,
    BusinessLogic,
}

/// Span completion status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SpanStatus {
    Ok,
    Error(String),
    Timeout,
    Cancelled,
}

/// Events within a span
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpanEvent {
    pub timestamp: SystemTime,
    pub name: String,
    pub attributes: std::collections::HashMap<String, String>,
}

/// Resource usage for a span
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub cpu_time_ms: Option<u64>,
    pub memory_bytes: Option<usize>,
    pub network_bytes_sent: Option<usize>,
    pub network_bytes_received: Option<usize>,
}

/// Complete trace details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TraceDetails {
    pub trace_id: String,
    pub root_operation: String,
    pub start_time: SystemTime,
    pub total_duration: Duration,
    pub spans: Vec<PerformanceSpan>,
    pub critical_path: Vec<String>, // Span IDs in critical path
    pub total_spans: usize,
    pub error_count: usize,
    pub warnings: Vec<String>,
}

/// Default implementation of performance tracer
pub struct DefaultPerformanceTracer {
    traces: Arc<std::sync::RwLock<std::collections::HashMap<String, Vec<PerformanceSpan>>>>,
    max_traces: usize,
}

impl DefaultPerformanceTracer {
    /// Create a new performance tracer
    pub fn new() -> Self {
        Self {
            traces: Arc::new(std::sync::RwLock::new(std::collections::HashMap::new())),
            max_traces: 10000,
        }
    }

    /// Create a span from context
    pub fn span_from_context(context: &TraceContext, status: SpanStatus) -> PerformanceSpan {
        PerformanceSpan {
            trace_id: context.trace_id.clone(),
            span_id: context.span_id.clone(),
            parent_span_id: context.parent_span_id.clone(),
            operation: context.operation.clone(),
            start_time: SystemTime::now() - context.elapsed(),
            duration: context.elapsed(),
            span_type: determine_span_type(&context.operation),
            status,
            tags: context.tags.clone(),
            events: Vec::new(),
            resource_usage: None,
        }
    }

    /// Analyze trace to find critical path
    fn analyze_critical_path(&self, spans: &[PerformanceSpan]) -> Vec<String> {
        if spans.is_empty() {
            return Vec::new();
        }

        // Find root span
        let root = spans
            .iter()
            .find(|s| s.parent_span_id.is_none())
            .unwrap_or(&spans[0]);

        // Build span tree and find longest path
        let mut critical_path = vec![root.span_id.clone()];
        let mut current_span_id = Some(root.span_id.clone());

        while let Some(span_id) = current_span_id {
            // Find child with longest duration
            let longest_child = spans
                .iter()
                .filter(|s| s.parent_span_id.as_ref() == Some(&span_id))
                .max_by_key(|s| s.duration);

            if let Some(child) = longest_child {
                critical_path.push(child.span_id.clone());
                current_span_id = Some(child.span_id.clone());
            } else {
                current_span_id = None;
            }
        }

        critical_path
    }

    /// Generate warnings for trace
    fn generate_warnings(&self, spans: &[PerformanceSpan]) -> Vec<String> {
        let mut warnings = Vec::new();

        // Check for slow operations
        for span in spans {
            match span.span_type {
                SpanType::Cache if span.duration > Duration::from_millis(5) => {
                    warnings.push(format!(
                        "Cache operation '{}' exceeded 5ms SLA: {:?}",
                        span.operation, span.duration
                    ));
                }
                SpanType::Database if span.duration > Duration::from_millis(50) => {
                    warnings.push(format!(
                        "Database operation '{}' exceeded budget: {:?}",
                        span.operation, span.duration
                    ));
                }
                SpanType::Query if span.duration > Duration::from_millis(100) => {
                    warnings.push(format!(
                        "Query '{}' exceeded 100ms SLA: {:?}",
                        span.operation, span.duration
                    ));
                }
                _ => {}
            }
        }

        // Check for error rate
        let error_count = spans
            .iter()
            .filter(|s| matches!(&s.status, SpanStatus::Error(_)))
            .count();

        if error_count > 0 {
            warnings.push(format!("{error_count} spans failed in this trace"));
        }

        warnings
    }
}

impl PerformanceTracer for DefaultPerformanceTracer {
    fn start_trace(&self, operation: &str) -> TraceContext {
        let context = TraceContext::new(operation);

        // Create tracing span for integration with tracing crate
        let span = info_span!(
            "trace",
            trace_id = %context.trace_id,
            operation = %operation
        );
        let _enter = span.enter();

        debug!(
            "Started trace {} for operation: {}",
            context.trace_id, operation
        );

        context
    }

    fn record_span(&self, span: PerformanceSpan) {
        let mut traces = match self.traces.write() {
            Ok(guard) => guard,
            Err(e) => {
                tracing::warn!("Failed to acquire trace lock: {}", e);
                return;
            }
        };

        traces
            .entry(span.trace_id.clone())
            .or_insert_with(Vec::new)
            .push(span);

        // Limit trace storage
        if traces.len() > self.max_traces {
            // Remove oldest traces (simple FIFO for now)
            let oldest_key = traces.keys().next().cloned();
            if let Some(key) = oldest_key {
                traces.remove(&key);
            }
        }
    }

    fn complete_trace(&self, context: TraceContext) {
        let span = Self::span_from_context(&context, SpanStatus::Ok);
        self.record_span(span);

        debug!(
            "Completed trace {} for operation: {} in {:?}",
            context.trace_id,
            context.operation,
            context.elapsed()
        );
    }

    fn get_trace(&self, trace_id: &str) -> Option<TraceDetails> {
        let traces = self.traces.read().ok()?;
        let spans = traces.get(trace_id)?;

        if spans.is_empty() {
            return None;
        }

        // Find root span and calculate total duration
        let root_span = spans.iter().find(|s| s.parent_span_id.is_none())?;

        let total_duration = spans
            .iter()
            .filter(|s| s.parent_span_id.is_none())
            .map(|s| s.duration)
            .sum();

        let error_count = spans
            .iter()
            .filter(|s| matches!(&s.status, SpanStatus::Error(_)))
            .count();

        let critical_path = self.analyze_critical_path(spans);
        let warnings = self.generate_warnings(spans);

        Some(TraceDetails {
            trace_id: trace_id.to_string(),
            root_operation: root_span.operation.clone(),
            start_time: root_span.start_time,
            total_duration,
            spans: spans.clone(),
            critical_path,
            total_spans: spans.len(),
            error_count,
            warnings,
        })
    }
}

impl Default for DefaultPerformanceTracer {
    fn default() -> Self {
        Self::new()
    }
}

/// Determine span type from operation name
fn determine_span_type(operation: &str) -> SpanType {
    if operation.contains("cache") || operation.contains("redis") {
        SpanType::Cache
    } else if operation.contains("database")
        || operation.contains("sql")
        || operation.contains("query")
    {
        SpanType::Database
    } else if operation.contains("auth") || operation.contains("permission") {
        SpanType::Authorization
    } else if operation.contains("serialize") || operation.contains("deserialize") {
        SpanType::Serialization
    } else if operation.contains("http") || operation.contains("grpc") {
        SpanType::Network
    } else if operation.starts_with("get_")
        || operation.starts_with("list_")
        || operation.starts_with("search_")
    {
        SpanType::Query
    } else {
        SpanType::BusinessLogic
    }
}

/// Helper to create a span for a cache operation
pub fn trace_cache_operation<T, F>(
    tracer: &dyn PerformanceTracer,
    parent: &TraceContext,
    operation: &str,
    f: F,
) -> Result<T, String>
where
    F: FnOnce() -> Result<T, String>,
{
    let context = parent.child(&format!("cache:{operation}"));
    let result = f();

    let status = match &result {
        Ok(_) => SpanStatus::Ok,
        Err(e) => SpanStatus::Error(e.clone()),
    };

    let mut span = DefaultPerformanceTracer::span_from_context(&context, status);
    span.span_type = SpanType::Cache;

    tracer.record_span(span);
    result
}

/// Helper to trace a database operation
pub fn trace_database_operation<T, F>(
    tracer: &dyn PerformanceTracer,
    parent: &TraceContext,
    operation: &str,
    f: F,
) -> Result<T, String>
where
    F: FnOnce() -> Result<T, String>,
{
    let context = parent.child(&format!("db:{operation}"));
    let result = f();

    let status = match &result {
        Ok(_) => SpanStatus::Ok,
        Err(e) => SpanStatus::Error(e.clone()),
    };

    let mut span = DefaultPerformanceTracer::span_from_context(&context, status);
    span.span_type = SpanType::Database;

    tracer.record_span(span);
    result
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_trace_context_creation() {
        let context = TraceContext::new("test_operation");

        assert!(!context.trace_id.is_empty());
        assert!(!context.span_id.is_empty());
        assert_eq!(context.operation, "test_operation");
        assert!(context.parent_span_id.is_none());
    }

    #[test]
    fn test_child_span_creation() {
        let parent = TraceContext::new("parent_op");
        let child = parent.child("child_op");

        assert_eq!(child.trace_id, parent.trace_id);
        assert_eq!(child.parent_span_id, Some(parent.span_id.clone()));
        assert_ne!(child.span_id, parent.span_id);
        assert_eq!(child.operation, "child_op");
    }

    #[test]
    fn test_context_with_tags() {
        let context = TraceContext::new("test")
            .with_tag("user_id", "123")
            .with_tag("request_id", "req456");

        assert_eq!(context.tags.get("user_id"), Some(&"123".to_string()));
        assert_eq!(context.tags.get("request_id"), Some(&"req456".to_string()));
    }

    #[test]
    fn test_baggage_propagation() {
        let parent = TraceContext::new("parent").with_baggage("tenant_id", "tenant123");

        let child = parent.child("child");

        assert_eq!(
            child.baggage.get("tenant_id"),
            Some(&"tenant123".to_string())
        );
    }

    #[test]
    fn test_span_type_determination() {
        assert_eq!(determine_span_type("cache_get"), SpanType::Cache);
        assert_eq!(determine_span_type("redis_set"), SpanType::Cache);
        assert_eq!(determine_span_type("database_query"), SpanType::Database);
        assert_eq!(determine_span_type("sql_select"), SpanType::Database);
        assert_eq!(determine_span_type("check_auth"), SpanType::Authorization);
        assert_eq!(
            determine_span_type("serialize_response"),
            SpanType::Serialization
        );
        assert_eq!(determine_span_type("http_request"), SpanType::Network);
        assert_eq!(determine_span_type("get_user_profile"), SpanType::Query);
        assert_eq!(determine_span_type("process_data"), SpanType::BusinessLogic);
    }

    #[test]
    fn test_performance_tracer() {
        let tracer = DefaultPerformanceTracer::new();

        // Start a trace
        let root_context = tracer.start_trace("test_query");

        // Simulate some work
        std::thread::sleep(Duration::from_millis(10));

        // Create child spans
        let cache_context = root_context.child("cache_lookup");
        std::thread::sleep(Duration::from_millis(2));

        let cache_span =
            DefaultPerformanceTracer::span_from_context(&cache_context, SpanStatus::Ok);
        tracer.record_span(cache_span);

        let db_context = root_context.child("database_query");
        std::thread::sleep(Duration::from_millis(15));

        let db_span = DefaultPerformanceTracer::span_from_context(&db_context, SpanStatus::Ok);
        tracer.record_span(db_span);

        // Complete the trace
        tracer.complete_trace(root_context.clone());

        // Retrieve trace details
        let trace_details = tracer.get_trace(&root_context.trace_id).unwrap();

        assert_eq!(trace_details.trace_id, root_context.trace_id);
        assert_eq!(trace_details.root_operation, "test_query");
        assert_eq!(trace_details.total_spans, 3); // root + cache + db
        assert_eq!(trace_details.error_count, 0);
        assert!(!trace_details.critical_path.is_empty());
    }

    #[test]
    fn test_trace_with_errors() {
        let tracer = DefaultPerformanceTracer::new();

        let context = tracer.start_trace("failing_operation");

        let error_span = PerformanceSpan {
            trace_id: context.trace_id.clone(),
            span_id: Uuid::new_v4().to_string(),
            parent_span_id: Some(context.span_id.clone()),
            operation: "database_error".to_string(),
            start_time: SystemTime::now(),
            duration: Duration::from_millis(50),
            span_type: SpanType::Database,
            status: SpanStatus::Error("Connection timeout".to_string()),
            tags: std::collections::HashMap::new(),
            events: Vec::new(),
            resource_usage: None,
        };

        tracer.record_span(error_span);
        tracer.complete_trace(context.clone());

        let trace_details = tracer.get_trace(&context.trace_id).unwrap();
        assert_eq!(trace_details.error_count, 1);
        assert!(!trace_details.warnings.is_empty());
    }

    #[test]
    fn test_trace_helpers() {
        let tracer = DefaultPerformanceTracer::new();
        let parent = tracer.start_trace("main_operation");

        // Test cache operation tracing
        let cache_result = trace_cache_operation(&tracer, &parent, "get_user", || {
            Ok("cached_user".to_string())
        });
        assert!(cache_result.is_ok());

        // Test database operation tracing
        let db_result =
            trace_database_operation(
                &tracer,
                &parent,
                "select_user",
                || Ok("db_user".to_string()),
            );
        assert!(db_result.is_ok());

        tracer.complete_trace(parent.clone());

        let trace_details = tracer.get_trace(&parent.trace_id).unwrap();
        assert!(trace_details.total_spans >= 3); // parent + cache + db
    }
}
