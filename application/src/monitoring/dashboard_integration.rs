// Dashboard integration for exporting metrics
// Supports various monitoring systems like Prometheus, Grafana, DataDog

use super::metrics_collector::{PerformanceSnapshot, QueryStatistics};
use super::performance_tracer::TraceDetails;
use super::sla_monitor::SlaReport;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

/// Dashboard exporter trait
pub trait DashboardExporter: Send + Sync {
    /// Export current metrics snapshot
    fn export_metrics(&self, snapshot: PerformanceSnapshot) -> ExportedMetrics;

    /// Export query-specific metrics
    fn export_query_metrics(&self, stats: Vec<QueryStatistics>) -> ExportedMetrics;

    /// Export SLA report
    fn export_sla_report(&self, report: SlaReport) -> ExportedMetrics;

    /// Export trace data
    fn export_trace(&self, trace: TraceDetails) -> ExportedMetrics;

    /// Get export format
    fn format(&self) -> MetricsExportFormat;
}

/// Supported metrics export formats
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum MetricsExportFormat {
    Prometheus,
    Json,
    OpenTelemetry,
    StatsD,
    CloudWatch,
}

/// Exported metrics container
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportedMetrics {
    pub format: MetricsExportFormat,
    pub timestamp: SystemTime,
    pub metrics: Vec<Metric>,
    pub metadata: HashMap<String, String>,
}

/// Individual metric
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Metric {
    pub name: String,
    pub value: MetricValue,
    pub metric_type: MetricType,
    pub labels: HashMap<String, String>,
    pub description: Option<String>,
    pub unit: Option<String>,
}

/// Metric value types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MetricValue {
    Counter(u64),
    Gauge(f64),
    Histogram(HistogramValue),
    Summary(SummaryValue),
}

/// Histogram metric value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistogramValue {
    pub buckets: Vec<(f64, u64)>, // (upper_bound, count)
    pub sum: f64,
    pub count: u64,
}

/// Summary metric value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SummaryValue {
    pub quantiles: Vec<(f64, f64)>, // (quantile, value)
    pub sum: f64,
    pub count: u64,
}

/// Metric types
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum MetricType {
    Counter,
    Gauge,
    Histogram,
    Summary,
}

/// Prometheus exporter implementation
pub struct PrometheusExporter {
    namespace: String,
    subsystem: String,
    labels: HashMap<String, String>,
}

impl PrometheusExporter {
    pub fn new(namespace: String, subsystem: String) -> Self {
        let mut labels = HashMap::new();
        labels.insert("service".to_string(), "auth_service".to_string());
        labels.insert("version".to_string(), env!("CARGO_PKG_VERSION").to_string());

        Self {
            namespace,
            subsystem,
            labels,
        }
    }

    /// Format metric name for Prometheus
    fn format_metric_name(&self, name: &str) -> String {
        format!(
            "{}_{}_{}",
            self.namespace,
            self.subsystem,
            name.replace(['-', '.'], "_")
        )
    }

    /// Convert to Prometheus text format
    pub fn to_prometheus_format(&self, metrics: &ExportedMetrics) -> String {
        let mut output = String::new();

        for metric in &metrics.metrics {
            // Add description as comment
            if let Some(desc) = &metric.description {
                output.push_str(&format!("# HELP {} {}\n", metric.name, desc));
            }

            // Add type declaration
            output.push_str(&format!(
                "# TYPE {} {}\n",
                metric.name,
                match metric.metric_type {
                    MetricType::Counter => "counter",
                    MetricType::Gauge => "gauge",
                    MetricType::Histogram => "histogram",
                    MetricType::Summary => "summary",
                }
            ));

            // Add metric value(s)
            match &metric.value {
                MetricValue::Counter(v) => {
                    output.push_str(&self.format_metric_line(
                        &metric.name,
                        *v as f64,
                        &metric.labels,
                    ));
                }
                MetricValue::Gauge(v) => {
                    output.push_str(&self.format_metric_line(&metric.name, *v, &metric.labels));
                }
                MetricValue::Histogram(h) => {
                    // Histogram buckets
                    for (le, count) in &h.buckets {
                        let mut labels = metric.labels.clone();
                        labels.insert("le".to_string(), le.to_string());
                        output.push_str(&self.format_metric_line(
                            &format!("{}_bucket", metric.name),
                            *count as f64,
                            &labels,
                        ));
                    }
                    // Histogram sum and count
                    output.push_str(&self.format_metric_line(
                        &format!("{}_sum", metric.name),
                        h.sum,
                        &metric.labels,
                    ));
                    output.push_str(&self.format_metric_line(
                        &format!("{}_count", metric.name),
                        h.count as f64,
                        &metric.labels,
                    ));
                }
                MetricValue::Summary(s) => {
                    // Summary quantiles
                    for (quantile, value) in &s.quantiles {
                        let mut labels = metric.labels.clone();
                        labels.insert("quantile".to_string(), quantile.to_string());
                        output.push_str(&self.format_metric_line(&metric.name, *value, &labels));
                    }
                    // Summary sum and count
                    output.push_str(&self.format_metric_line(
                        &format!("{}_sum", metric.name),
                        s.sum,
                        &metric.labels,
                    ));
                    output.push_str(&self.format_metric_line(
                        &format!("{}_count", metric.name),
                        s.count as f64,
                        &metric.labels,
                    ));
                }
            }
        }

        output
    }

    /// Format a single metric line
    fn format_metric_line(
        &self,
        name: &str,
        value: f64,
        labels: &HashMap<String, String>,
    ) -> String {
        let mut all_labels = self.labels.clone();
        all_labels.extend(labels.clone());

        let label_str = if all_labels.is_empty() {
            String::new()
        } else {
            let labels: Vec<String> = all_labels
                .iter()
                .map(|(k, v)| format!("{k}=\"{v}\""))
                .collect();
            format!("{{{}}}", labels.join(","))
        };

        format!("{name}{label_str} {value}\n")
    }
}

impl DashboardExporter for PrometheusExporter {
    fn export_metrics(&self, snapshot: PerformanceSnapshot) -> ExportedMetrics {
        let mut metrics = Vec::new();
        let timestamp = snapshot.timestamp;

        // Latency metrics
        metrics.push(Metric {
            name: self.format_metric_name("query_latency_seconds"),
            value: MetricValue::Summary(SummaryValue {
                quantiles: vec![
                    (0.5, snapshot.p50_latency.as_secs_f64()),
                    (0.95, snapshot.p95_latency.as_secs_f64()),
                    (0.99, snapshot.p99_latency.as_secs_f64()),
                ],
                sum: 0.0, // Would need to track this
                count: 0, // Would need to track this
            }),
            metric_type: MetricType::Summary,
            labels: HashMap::new(),
            description: Some("Query latency in seconds".to_string()),
            unit: Some("seconds".to_string()),
        });

        // Cache hit ratio
        metrics.push(Metric {
            name: self.format_metric_name("cache_hit_ratio"),
            value: MetricValue::Gauge(snapshot.cache_hit_ratio),
            metric_type: MetricType::Gauge,
            labels: HashMap::new(),
            description: Some("Cache hit ratio (0-1)".to_string()),
            unit: None,
        });

        // Error rate
        metrics.push(Metric {
            name: self.format_metric_name("error_rate"),
            value: MetricValue::Gauge(snapshot.error_rate),
            metric_type: MetricType::Gauge,
            labels: HashMap::new(),
            description: Some("Query error rate (0-1)".to_string()),
            unit: None,
        });

        // Queries per second
        metrics.push(Metric {
            name: self.format_metric_name("queries_per_second"),
            value: MetricValue::Gauge(snapshot.queries_per_second),
            metric_type: MetricType::Gauge,
            labels: HashMap::new(),
            description: Some("Queries per second".to_string()),
            unit: Some("qps".to_string()),
        });

        // SLA violations
        metrics.push(Metric {
            name: self.format_metric_name("sla_violations_total"),
            value: MetricValue::Counter(snapshot.sla_violations),
            metric_type: MetricType::Counter,
            labels: HashMap::new(),
            description: Some("Total SLA violations".to_string()),
            unit: None,
        });

        ExportedMetrics {
            format: MetricsExportFormat::Prometheus,
            timestamp,
            metrics,
            metadata: HashMap::new(),
        }
    }

    fn export_query_metrics(&self, stats: Vec<QueryStatistics>) -> ExportedMetrics {
        let mut metrics = Vec::new();

        for stat in stats {
            let mut labels = HashMap::new();
            labels.insert("query".to_string(), stat.query_name.clone());

            // Query execution count
            metrics.push(Metric {
                name: self.format_metric_name("query_executions_total"),
                value: MetricValue::Counter(stat.total_executions),
                metric_type: MetricType::Counter,
                labels: labels.clone(),
                description: Some("Total query executions".to_string()),
                unit: None,
            });

            // Query-specific cache hit ratio
            metrics.push(Metric {
                name: self.format_metric_name("query_cache_hit_ratio"),
                value: MetricValue::Gauge(stat.cache_hit_ratio),
                metric_type: MetricType::Gauge,
                labels: labels.clone(),
                description: Some("Query-specific cache hit ratio".to_string()),
                unit: None,
            });

            // Query-specific error rate
            metrics.push(Metric {
                name: self.format_metric_name("query_error_rate"),
                value: MetricValue::Gauge(stat.error_rate),
                metric_type: MetricType::Gauge,
                labels: labels.clone(),
                description: Some("Query-specific error rate".to_string()),
                unit: None,
            });

            // Query latency histogram
            if let (Some(_p50), Some(_p95), Some(_p99)) =
                (stat.p50_latency, stat.p95_latency, stat.p99_latency)
            {
                metrics.push(Metric {
                    name: self.format_metric_name("query_latency_histogram_seconds"),
                    value: MetricValue::Histogram(HistogramValue {
                        buckets: vec![
                            (0.001, 0), // 1ms
                            (0.005, 0), // 5ms
                            (0.010, 0), // 10ms
                            (0.025, 0), // 25ms
                            (0.050, 0), // 50ms
                            (0.100, 0), // 100ms
                            (0.250, 0), // 250ms
                            (0.500, 0), // 500ms
                            (1.000, 0), // 1s
                            (f64::INFINITY, stat.total_executions),
                        ],
                        sum: stat
                            .avg_latency
                            .map(|d| d.as_secs_f64() * stat.total_executions as f64)
                            .unwrap_or(0.0),
                        count: stat.total_executions,
                    }),
                    metric_type: MetricType::Histogram,
                    labels: labels.clone(),
                    description: Some("Query latency distribution".to_string()),
                    unit: Some("seconds".to_string()),
                });
            }

            // SLA violations
            metrics.push(Metric {
                name: self.format_metric_name("query_sla_violations_total"),
                value: MetricValue::Counter(stat.sla_violations),
                metric_type: MetricType::Counter,
                labels,
                description: Some("Query-specific SLA violations".to_string()),
                unit: None,
            });
        }

        ExportedMetrics {
            format: MetricsExportFormat::Prometheus,
            timestamp: SystemTime::now(),
            metrics,
            metadata: HashMap::new(),
        }
    }

    fn export_sla_report(&self, report: SlaReport) -> ExportedMetrics {
        let mut metrics = Vec::new();

        // Overall compliance
        metrics.push(Metric {
            name: self.format_metric_name("sla_compliance_percentage"),
            value: MetricValue::Gauge(report.compliance_percentage),
            metric_type: MetricType::Gauge,
            labels: HashMap::new(),
            description: Some("Overall SLA compliance percentage".to_string()),
            unit: Some("percent".to_string()),
        });

        // Violations by type
        for (violation_type, count) in report.violations_by_type {
            let mut labels = HashMap::new();
            labels.insert("type".to_string(), format!("{violation_type:?}"));

            metrics.push(Metric {
                name: self.format_metric_name("sla_violations_by_type_total"),
                value: MetricValue::Counter(count),
                metric_type: MetricType::Counter,
                labels,
                description: Some("SLA violations by type".to_string()),
                unit: None,
            });
        }

        // Violations by severity
        for (severity, count) in report.violations_by_severity {
            let mut labels = HashMap::new();
            labels.insert("severity".to_string(), format!("{severity:?}"));

            metrics.push(Metric {
                name: self.format_metric_name("sla_violations_by_severity_total"),
                value: MetricValue::Counter(count),
                metric_type: MetricType::Counter,
                labels,
                description: Some("SLA violations by severity".to_string()),
                unit: None,
            });
        }

        // Worst performers
        for query_stat in report.worst_performers.iter().take(10) {
            let mut labels = HashMap::new();
            labels.insert("query".to_string(), query_stat.query_name.clone());

            metrics.push(Metric {
                name: self.format_metric_name("worst_performer_compliance_rate"),
                value: MetricValue::Gauge(query_stat.compliance_rate),
                metric_type: MetricType::Gauge,
                labels,
                description: Some("Compliance rate for worst performing queries".to_string()),
                unit: Some("percent".to_string()),
            });
        }

        ExportedMetrics {
            format: MetricsExportFormat::Prometheus,
            timestamp: SystemTime::now(),
            metrics,
            metadata: HashMap::new(),
        }
    }

    fn export_trace(&self, trace: TraceDetails) -> ExportedMetrics {
        let mut metrics = Vec::new();
        let mut labels = HashMap::new();
        labels.insert("trace_id".to_string(), trace.trace_id.clone());
        labels.insert("operation".to_string(), trace.root_operation.clone());

        // Trace duration
        metrics.push(Metric {
            name: self.format_metric_name("trace_duration_seconds"),
            value: MetricValue::Gauge(trace.total_duration.as_secs_f64()),
            metric_type: MetricType::Gauge,
            labels: labels.clone(),
            description: Some("Total trace duration".to_string()),
            unit: Some("seconds".to_string()),
        });

        // Span count
        metrics.push(Metric {
            name: self.format_metric_name("trace_span_count"),
            value: MetricValue::Gauge(trace.total_spans as f64),
            metric_type: MetricType::Gauge,
            labels: labels.clone(),
            description: Some("Number of spans in trace".to_string()),
            unit: None,
        });

        // Error count
        metrics.push(Metric {
            name: self.format_metric_name("trace_error_count"),
            value: MetricValue::Gauge(trace.error_count as f64),
            metric_type: MetricType::Gauge,
            labels,
            description: Some("Number of errors in trace".to_string()),
            unit: None,
        });

        // Span breakdown by type
        let mut span_counts: HashMap<String, u64> = HashMap::new();
        for span in &trace.spans {
            *span_counts
                .entry(format!("{:?}", span.span_type))
                .or_insert(0) += 1;
        }

        for (span_type, count) in span_counts {
            let mut labels = HashMap::new();
            labels.insert("trace_id".to_string(), trace.trace_id.clone());
            labels.insert("span_type".to_string(), span_type);

            metrics.push(Metric {
                name: self.format_metric_name("trace_spans_by_type"),
                value: MetricValue::Gauge(count as f64),
                metric_type: MetricType::Gauge,
                labels,
                description: Some("Spans by type in trace".to_string()),
                unit: None,
            });
        }

        ExportedMetrics {
            format: MetricsExportFormat::Prometheus,
            timestamp: SystemTime::now(),
            metrics,
            metadata: HashMap::new(),
        }
    }

    fn format(&self) -> MetricsExportFormat {
        MetricsExportFormat::Prometheus
    }
}

/// JSON exporter for custom dashboards
pub struct JsonExporter;

impl DashboardExporter for JsonExporter {
    fn export_metrics(&self, snapshot: PerformanceSnapshot) -> ExportedMetrics {
        let mut metadata = HashMap::new();
        metadata.insert(
            "export_time".to_string(),
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs()
                .to_string(),
        );

        ExportedMetrics {
            format: MetricsExportFormat::Json,
            timestamp: snapshot.timestamp,
            metrics: vec![Metric {
                name: "performance_snapshot".to_string(),
                value: MetricValue::Gauge(0.0), // Placeholder
                metric_type: MetricType::Gauge,
                labels: HashMap::new(),
                description: Some("JSON export of performance snapshot".to_string()),
                unit: None,
            }],
            metadata,
        }
    }

    fn export_query_metrics(&self, _stats: Vec<QueryStatistics>) -> ExportedMetrics {
        ExportedMetrics {
            format: MetricsExportFormat::Json,
            timestamp: SystemTime::now(),
            metrics: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    fn export_sla_report(&self, _report: SlaReport) -> ExportedMetrics {
        ExportedMetrics {
            format: MetricsExportFormat::Json,
            timestamp: SystemTime::now(),
            metrics: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    fn export_trace(&self, _trace: TraceDetails) -> ExportedMetrics {
        ExportedMetrics {
            format: MetricsExportFormat::Json,
            timestamp: SystemTime::now(),
            metrics: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    fn format(&self) -> MetricsExportFormat {
        MetricsExportFormat::Json
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_prometheus_metric_name_formatting() {
        let exporter = PrometheusExporter::new("auth".to_string(), "service".to_string());

        assert_eq!(
            exporter.format_metric_name("query-latency"),
            "auth_service_query_latency"
        );

        assert_eq!(
            exporter.format_metric_name("cache.hit.ratio"),
            "auth_service_cache_hit_ratio"
        );
    }

    #[test]
    fn test_prometheus_export() {
        let exporter = PrometheusExporter::new("auth".to_string(), "service".to_string());

        let snapshot = PerformanceSnapshot {
            p50_latency: Duration::from_millis(10),
            p95_latency: Duration::from_millis(50),
            p99_latency: Duration::from_millis(100),
            cache_hit_ratio: 0.85,
            error_rate: 0.001,
            queries_per_second: 1000.0,
            sla_violations: 5,
            timestamp: SystemTime::now(),
            period_seconds: 3600,
        };

        let exported = exporter.export_metrics(snapshot);
        assert_eq!(exported.format, MetricsExportFormat::Prometheus);
        assert!(!exported.metrics.is_empty());

        // Test Prometheus text format generation
        let prometheus_text = exporter.to_prometheus_format(&exported);
        assert!(prometheus_text.contains("auth_service_cache_hit_ratio"));
        assert!(prometheus_text.contains("0.85"));
    }

    #[test]
    fn test_query_metrics_export() {
        let exporter = PrometheusExporter::new("auth".to_string(), "service".to_string());

        let stats = vec![QueryStatistics {
            query_name: "get_user_profile".to_string(),
            total_executions: 1000,
            cache_hit_ratio: 0.9,
            error_rate: 0.001,
            p50_latency: Some(Duration::from_millis(5)),
            p95_latency: Some(Duration::from_millis(20)),
            p99_latency: Some(Duration::from_millis(50)),
            min_latency: Some(Duration::from_millis(1)),
            max_latency: Some(Duration::from_millis(100)),
            avg_latency: Some(Duration::from_millis(10)),
            sla_violations: 2,
            last_execution: Some(SystemTime::now()),
        }];

        let exported = exporter.export_query_metrics(stats);

        // Find query execution metric
        let exec_metric = exported
            .metrics
            .iter()
            .find(|m| m.name.contains("query_executions_total"))
            .unwrap();

        assert_eq!(
            exec_metric.labels.get("query"),
            Some(&"get_user_profile".to_string())
        );
        match &exec_metric.value {
            MetricValue::Counter(v) => assert_eq!(*v, 1000),
            _ => panic!("Expected counter metric"),
        }
    }

    #[test]
    fn test_metric_value_types() {
        // Test counter
        let counter = MetricValue::Counter(42);
        match counter {
            MetricValue::Counter(v) => assert_eq!(v, 42),
            _ => panic!("Expected counter"),
        }

        // Test gauge
        let gauge = MetricValue::Gauge(std::f64::consts::PI);
        match gauge {
            MetricValue::Gauge(v) => assert!((v - std::f64::consts::PI).abs() < 0.001),
            _ => panic!("Expected gauge"),
        }

        // Test histogram
        let histogram = MetricValue::Histogram(HistogramValue {
            buckets: vec![(0.1, 10), (0.5, 50), (1.0, 90), (f64::INFINITY, 100)],
            sum: 45.0,
            count: 100,
        });

        match histogram {
            MetricValue::Histogram(h) => {
                assert_eq!(h.count, 100);
                assert_eq!(h.buckets.len(), 4);
            }
            _ => panic!("Expected histogram"),
        }
    }
}
