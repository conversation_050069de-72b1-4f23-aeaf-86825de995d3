// Query performance metrics and budget tracking
// Core types for monitoring query execution performance

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime};

/// Core metrics collected for each query execution
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct QueryPerformanceMetrics {
    pub query_name: String,
    pub execution_time: Duration,
    pub cache_hit: bool,
    pub cache_response_time: Option<Duration>,
    pub database_response_time: Option<Duration>,
    pub result_count: usize,
    pub error_type: Option<String>,
    pub timestamp: SystemTime,
    pub request_id: String,
    pub user_id: Option<String>,
    pub resource_utilization: ResourceMetrics,
}

/// Resource utilization metrics for a query
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ResourceMetrics {
    pub memory_used_bytes: Option<usize>,
    pub cpu_time_ms: Option<u64>,
    pub connections_used: Option<u32>,
    pub cache_memory_bytes: Option<usize>,
}

/// Performance budget allocation and tracking
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerformanceBudget {
    pub total_budget_ms: u64,
    pub components: HashMap<String, BudgetComponent>,
}

/// Individual component budget allocation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BudgetComponent {
    pub name: String,
    pub allocated_ms: u64,
    pub consumed_ms: u64,
    pub percentage_used: f64,
}

impl Default for PerformanceBudget {
    fn default() -> Self {
        Self::new()
    }
}

impl PerformanceBudget {
    /// Create a new performance budget with standard allocations
    pub fn new() -> Self {
        let mut components = HashMap::new();

        // Standard budget allocation per requirements
        components.insert(
            "network_http".to_string(),
            BudgetComponent {
                name: "Network/HTTP".to_string(),
                allocated_ms: 10,
                consumed_ms: 0,
                percentage_used: 0.0,
            },
        );

        components.insert(
            "authorization".to_string(),
            BudgetComponent {
                name: "Authorization".to_string(),
                allocated_ms: 5,
                consumed_ms: 0,
                percentage_used: 0.0,
            },
        );

        components.insert(
            "business_logic".to_string(),
            BudgetComponent {
                name: "Business Logic".to_string(),
                allocated_ms: 10,
                consumed_ms: 0,
                percentage_used: 0.0,
            },
        );

        components.insert(
            "database_query".to_string(),
            BudgetComponent {
                name: "Database Query".to_string(),
                allocated_ms: 50,
                consumed_ms: 0,
                percentage_used: 0.0,
            },
        );

        components.insert(
            "serialization".to_string(),
            BudgetComponent {
                name: "Serialization".to_string(),
                allocated_ms: 5,
                consumed_ms: 0,
                percentage_used: 0.0,
            },
        );

        components.insert(
            "buffer".to_string(),
            BudgetComponent {
                name: "Buffer".to_string(),
                allocated_ms: 20,
                consumed_ms: 0,
                percentage_used: 0.0,
            },
        );

        Self {
            total_budget_ms: 100,
            components,
        }
    }

    /// Update consumed time for a component
    pub fn consume(&mut self, component: &str, duration_ms: u64) {
        if let Some(budget) = self.components.get_mut(component) {
            budget.consumed_ms += duration_ms;
            budget.percentage_used =
                (budget.consumed_ms as f64 / budget.allocated_ms as f64) * 100.0;
        }
    }

    /// Get total consumed time across all components
    pub fn total_consumed(&self) -> u64 {
        self.components.values().map(|c| c.consumed_ms).sum()
    }

    /// Get remaining budget
    pub fn remaining_budget(&self) -> i64 {
        self.total_budget_ms as i64 - self.total_consumed() as i64
    }

    /// Check if budget is exceeded
    pub fn is_exceeded(&self) -> bool {
        self.total_consumed() > self.total_budget_ms
    }

    /// Get components that exceeded their allocation
    pub fn exceeded_components(&self) -> Vec<&BudgetComponent> {
        self.components
            .values()
            .filter(|c| c.consumed_ms > c.allocated_ms)
            .collect()
    }

    /// Generate budget utilization report
    pub fn utilization_report(&self) -> BudgetUtilizationReport {
        BudgetUtilizationReport {
            total_budget_ms: self.total_budget_ms,
            total_consumed_ms: self.total_consumed(),
            remaining_ms: self.remaining_budget(),
            is_exceeded: self.is_exceeded(),
            utilization_percentage: (self.total_consumed() as f64 / self.total_budget_ms as f64)
                * 100.0,
            component_breakdown: self.components.values().cloned().collect(),
            exceeded_components: self.exceeded_components().into_iter().cloned().collect(),
        }
    }
}

/// Budget utilization report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BudgetUtilizationReport {
    pub total_budget_ms: u64,
    pub total_consumed_ms: u64,
    pub remaining_ms: i64,
    pub is_exceeded: bool,
    pub utilization_percentage: f64,
    pub component_breakdown: Vec<BudgetComponent>,
    pub exceeded_components: Vec<BudgetComponent>,
}

/// Metric aggregator for calculating percentiles and statistics
pub struct MetricAggregator {
    samples: Vec<Duration>,
    max_samples: usize,
}

impl MetricAggregator {
    /// Create a new metric aggregator
    pub fn new(max_samples: usize) -> Self {
        Self {
            samples: Vec::with_capacity(max_samples),
            max_samples,
        }
    }

    /// Add a sample to the aggregator
    pub fn add_sample(&mut self, duration: Duration) {
        self.samples.push(duration);

        // Maintain size limit by removing oldest samples
        if self.samples.len() > self.max_samples {
            self.samples.remove(0);
        }
    }

    /// Calculate percentile (0-100)
    pub fn percentile(&self, p: usize) -> Option<Duration> {
        if self.samples.is_empty() || p > 100 {
            return None;
        }

        let mut sorted = self.samples.clone();
        sorted.sort();

        let index = (sorted.len() - 1) * p / 100;
        sorted.get(index).cloned()
    }

    /// Get p50 (median) latency
    pub fn p50(&self) -> Option<Duration> {
        self.percentile(50)
    }

    /// Get p95 latency
    pub fn p95(&self) -> Option<Duration> {
        self.percentile(95)
    }

    /// Get p99 latency
    pub fn p99(&self) -> Option<Duration> {
        self.percentile(99)
    }

    /// Get minimum latency
    pub fn min(&self) -> Option<Duration> {
        self.samples.iter().min().cloned()
    }

    /// Get maximum latency
    pub fn max(&self) -> Option<Duration> {
        self.samples.iter().max().cloned()
    }

    /// Get average latency
    pub fn average(&self) -> Option<Duration> {
        if self.samples.is_empty() {
            return None;
        }

        let total: Duration = self.samples.iter().sum();
        Some(total / self.samples.len() as u32)
    }

    /// Get sample count
    pub fn count(&self) -> usize {
        self.samples.len()
    }

    /// Clear all samples
    pub fn clear(&mut self) {
        self.samples.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_performance_budget_creation() {
        let budget = PerformanceBudget::new();

        assert_eq!(budget.total_budget_ms, 100);
        assert_eq!(budget.components.len(), 6);
        assert_eq!(budget.total_consumed(), 0);
        assert_eq!(budget.remaining_budget(), 100);
        assert!(!budget.is_exceeded());
    }

    #[test]
    fn test_performance_budget_consumption() {
        let mut budget = PerformanceBudget::new();

        budget.consume("database_query", 30);
        budget.consume("authorization", 3);

        assert_eq!(budget.total_consumed(), 33);
        assert_eq!(budget.remaining_budget(), 67);
        assert!(!budget.is_exceeded());

        let db_component = budget.components.get("database_query").unwrap();
        assert_eq!(db_component.consumed_ms, 30);
        assert_eq!(db_component.percentage_used, 60.0);
    }

    #[test]
    fn test_performance_budget_exceeded() {
        let mut budget = PerformanceBudget::new();

        budget.consume("database_query", 60); // Exceeds 50ms allocation
        budget.consume("business_logic", 50); // Exceeds 10ms allocation

        assert!(budget.is_exceeded());
        assert_eq!(budget.total_consumed(), 110);
        assert_eq!(budget.remaining_budget(), -10);

        let exceeded = budget.exceeded_components();
        assert_eq!(exceeded.len(), 2);
    }

    #[test]
    fn test_budget_utilization_report() {
        let mut budget = PerformanceBudget::new();

        budget.consume("database_query", 40);
        budget.consume("authorization", 4);
        budget.consume("business_logic", 8);

        let report = budget.utilization_report();

        assert_eq!(report.total_consumed_ms, 52);
        assert_eq!(report.utilization_percentage, 52.0);
        assert!(!report.is_exceeded);
        assert_eq!(report.exceeded_components.len(), 0);
    }

    #[test]
    fn test_metric_aggregator() {
        let mut aggregator = MetricAggregator::new(100);

        // Add samples
        for i in 1..=10 {
            aggregator.add_sample(Duration::from_millis(i * 10));
        }

        assert_eq!(aggregator.count(), 10);
        assert_eq!(aggregator.min(), Some(Duration::from_millis(10)));
        assert_eq!(aggregator.max(), Some(Duration::from_millis(100)));
        assert_eq!(aggregator.average(), Some(Duration::from_millis(55)));
        assert_eq!(aggregator.p50(), Some(Duration::from_millis(50)));
        assert_eq!(aggregator.p95(), Some(Duration::from_millis(90)));
        assert_eq!(aggregator.p99(), Some(Duration::from_millis(90)));
    }

    #[test]
    fn test_metric_aggregator_max_samples() {
        let mut aggregator = MetricAggregator::new(5);

        // Add more than max samples
        for i in 1..=10 {
            aggregator.add_sample(Duration::from_millis(i));
        }

        // Should only keep last 5 samples (6-10)
        assert_eq!(aggregator.count(), 5);
        assert_eq!(aggregator.min(), Some(Duration::from_millis(6)));
        assert_eq!(aggregator.max(), Some(Duration::from_millis(10)));
    }
}
