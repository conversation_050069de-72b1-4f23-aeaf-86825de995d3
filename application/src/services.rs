// Application service coordination
// Coordinates between different application services

pub mod query_handler_factory;

pub use query_handler_factory::{CacheTTLConfig, QueryHandlerFactory};

// use auth_domain::errors::DomainError; // Temporarily disabled - unused
// use crate::errors::ApplicationError; // Temporarily disabled - unused

/// Main application service that coordinates use cases
pub struct ApplicationService<UR, SR>
where
    UR: auth_domain::repositories::UserRepository,
    SR: auth_domain::repositories::SessionRepository,
{
    user_repository: UR,
    session_repository: SR,
}

impl<UR, SR> ApplicationService<UR, SR>
where
    UR: auth_domain::repositories::UserRepository,
    SR: auth_domain::repositories::SessionRepository,
{
    pub fn new(user_repository: UR, session_repository: SR) -> Self {
        ApplicationService {
            user_repository,
            session_repository,
        }
    }

    pub fn user_repository(&self) -> &UR {
        &self.user_repository
    }

    pub fn user_repository_mut(&mut self) -> &mut UR {
        &mut self.user_repository
    }

    pub fn session_repository(&self) -> &SR {
        &self.session_repository
    }

    pub fn session_repository_mut(&mut self) -> &mut SR {
        &mut self.session_repository
    }
}

// ApplicationError is now defined in errors.rs and imported
