# Domain Layer API Documentation

This document provides comprehensive API documentation for the AuthService domain layer, including usage examples, patterns, and best practices.

## 📚 Quick Start

```rust
use auth_domain::{
    entities::{User, Session},
    value_objects::{Email, Password, UserId, Username},
    services::{AuthService, PasswordPolicy},
};

// Create a new user
let email = Email::parse("<EMAIL>")?;
let password = Password::new("SecureP@ssw0rd123")?;
let user = User::new(email, password)?;

// Authenticate
let login_password = Password::new("SecureP@ssw0rd123")?;
let session = user.authenticate(login_password)?;
```

## 🏗️ Core Types

### Value Objects

#### Email

Represents a validated email address with normalization.

```rust
use auth_domain::value_objects::Email;

// Valid email creation
let email = Email::parse("<EMAIL>")?;
assert_eq!(email.as_str(), "<EMAIL>"); // Normalized

// Access components
assert_eq!(email.local_part(), "user");
assert_eq!(email.domain_part(), "example.com");

// Common validation errors
assert!(Email::parse("").is_err());           // Empty
assert!(Email::parse("notanemail").is_err()); // No @
assert!(Email::parse("user@").is_err());      // No domain
```

#### Password

Secure password handling with OWASP-compliant validation.

```rust
use auth_domain::value_objects::Password;

// Create password (validates strength)
let password = Password::new("MySecure123!")?;

// Check strength score (0-100)
let score = password.strength_score();
assert!(score > 70); // Strong password

// Hash for storage
let hash = password.clone().into_hash();
assert!(hash.starts_with("$argon2id$"));

// Verify against hash
assert!(password.verify_against_hash(&hash));

// Password strength requirements
assert!(Password::new("weak").is_err());      // Too short
assert!(Password::new("nouppercase123!").is_err()); // Missing uppercase
assert!(Password::new("password123").is_err()); // Common pattern
```

#### UserId

Strongly-typed unique identifier for users.

```rust
use auth_domain::value_objects::UserId;

// Generate new ID
let user_id = UserId::new();

// Create from string (validation)
let user_id = UserId::from_string("existing_user_id_here")?;

// Access as string
let id_str = user_id.as_str();

// IDs are comparable and hashable
let id1 = UserId::new();
let id2 = UserId::new();
assert_ne!(id1, id2); // Always unique
```

#### Username

Validated username with character and length constraints.

```rust
use auth_domain::value_objects::Username;

// Valid username
let username = Username::new("john_doe")?;
let username = Username::new("user123")?;
let username = Username::new("my-handle")?;

// Username requirements
assert!(Username::new("ab").is_err());        // Too short (< 3)
assert!(Username::new("a".repeat(31)).is_err()); // Too long (> 30)
assert!(Username::new("123user").is_err());   // Must start with letter
assert!(Username::new("user@name").is_err()); // Invalid characters
```

### Entities

#### User

The central aggregate root for user management.

```rust
use auth_domain::{
    entities::User,
    value_objects::{Email, Password},
};

// Create new user
let email = Email::parse("<EMAIL>")?;
let password = Password::new("SecurePassword123!")?;
let mut user = User::new(email, password)?;

// User starts unverified
assert!(!user.is_verified());
assert!(user.is_active());

// Verify email
user.verify_email()?;
assert!(user.is_verified());

// Authenticate user
let login_password = Password::new("SecurePassword123!")?;
let session = user.authenticate(login_password)?;

// Failed authentication tracking
let wrong_password = Password::new("WrongPassword123!")?;
assert!(user.authenticate(wrong_password).is_err());
assert_eq!(user.failed_login_attempts(), 1);

// Change password
let new_password = Password::new("NewSecurePassword456!")?;
user.change_password(new_password)?;

// Password history prevents reuse
let old_password = Password::new("SecurePassword123!")?;
assert!(user.change_password(old_password).is_err()); // Recently used

// Account management
user.suspend("Policy violation")?;
assert!(!user.is_active());

user.reactivate()?;
assert!(user.is_active());
```

#### Session

Represents an authentication session with security controls.

```rust
use auth_domain::{
    entities::Session,
    value_objects::{UserId, DateTime},
};

// Create session (typically done by User::authenticate)
let user_id = UserId::new();
let session = Session::new(user_id, "192.168.1.1")?;

// Session properties
assert!(session.is_valid());
assert!(!session.is_expired());

// Session token access
let token = session.token();
assert_eq!(token.len(), 64); // 32 bytes hex-encoded

// Session expiration
let expired_session = Session::new_with_expiry(
    user_id,
    "192.168.1.1",
    DateTime::now() - Duration::hours(25) // Expired
)?;
assert!(expired_session.is_expired());
assert!(!expired_session.is_valid());

// Session refresh
let mut session = Session::new(user_id, "192.168.1.1")?;
session.refresh()?;
// Session timeout is reset

// Manual invalidation
session.invalidate("User logout")?;
assert!(!session.is_valid());
```

### Domain Services

#### AuthService

Coordinates authentication business logic.

```rust
use auth_domain::{
    services::AuthService,
    value_objects::{Email, Password},
};

let auth_service = AuthService::new();

// Register user with validation
let email = Email::parse("<EMAIL>")?;
let password = Password::new("StrongPassword123!")?;

let user = auth_service.register_user(email, password)?;
assert!(!user.is_verified()); // Requires email verification

// Login attempt with rate limiting
let login_email = Email::parse("<EMAIL>")?;
let login_password = Password::new("UserPassword123!")?;

match auth_service.authenticate(login_email, login_password) {
    Ok(session) => println!("Login successful"),
    Err(AuthError::InvalidCredentials) => println!("Wrong password"),
    Err(AuthError::AccountLocked) => println!("Too many failed attempts"),
    Err(AuthError::UserNotFound) => println!("User doesn't exist"),
}

// Check if user is locked out
let is_locked = auth_service.is_user_locked_out(&user_id)?;
if is_locked {
    let lockout_expires = auth_service.lockout_expires_at(&user_id)?;
    println!("Account locked until: {}", lockout_expires);
}
```

#### PasswordPolicy

Enforces password security policies.

```rust
use auth_domain::services::PasswordPolicy;

let policy = PasswordPolicy::strict(); // High security settings

// Validate password strength
let password = Password::new("TestPassword123!")?;
let validation = policy.validate(&password)?;

assert!(validation.meets_requirements());
assert!(validation.strength_score() > 80);

// Check password history
let user = get_user(); // Your user instance
let new_password = Password::new("NewPassword456!")?;

if policy.is_password_recently_used(&user, &new_password) {
    return Err("Password was recently used");
}

// Generate password requirements description
let requirements = policy.requirements_text();
println!("Password must: {}", requirements);
// "Password must: be 8-128 characters, contain 3 of: uppercase, lowercase, digits, symbols"
```

## 🔄 Common Patterns

### User Registration Flow

```rust
use auth_domain::{
    entities::User,
    services::AuthService,
    value_objects::{Email, Password},
    events::DomainEvent,
};

async fn register_user_flow(
    email_str: &str,
    password_str: &str
) -> Result<(User, Vec<DomainEvent>), DomainError> {
    let auth_service = AuthService::new();
    
    // 1. Validate inputs
    let email = Email::parse(email_str)?;
    let password = Password::new(password_str)?;
    
    // 2. Check if user already exists
    if auth_service.user_exists(&email).await? {
        return Err(DomainError::UserAlreadyExists);
    }
    
    // 3. Create user
    let mut user = User::new(email, password)?;
    
    // 4. Collect domain events
    let events = vec![
        DomainEvent::UserRegistered {
            user_id: user.id().clone(),
            email: user.email().clone(),
        }
    ];
    
    Ok((user, events))
}
```

### Authentication Flow

```rust
async fn authenticate_user_flow(
    email_str: &str,
    password_str: &str,
    ip_address: &str
) -> Result<Session, AuthenticationError> {
    let auth_service = AuthService::new();
    
    // 1. Validate inputs
    let email = Email::parse(email_str)
        .map_err(|_| AuthenticationError::InvalidCredentials)?;
    let password = Password::new(password_str)
        .map_err(|_| AuthenticationError::InvalidCredentials)?;
    
    // 2. Check rate limiting
    auth_service.check_rate_limit(&email, ip_address).await?;
    
    // 3. Find user
    let mut user = auth_service.find_user_by_email(&email).await?
        .ok_or(AuthenticationError::UserNotFound)?;
    
    // 4. Attempt authentication
    match user.authenticate(password) {
        Ok(session) => {
            // Reset failed attempts on success
            user.reset_failed_attempts();
            
            // Publish success event
            publish_event(DomainEvent::UserLoggedIn {
                user_id: user.id().clone(),
                ip_address: ip_address.to_string(),
            });
            
            Ok(session)
        }
        Err(_) => {
            // Track failed attempt
            user.record_failed_attempt();
            
            // Check if account should be locked
            if user.failed_login_attempts() >= 5 {
                user.lock_account("Too many failed attempts")?;
                
                publish_event(DomainEvent::SuspiciousActivity {
                    user_id: user.id().clone(),
                    event_type: "account_locked".to_string(),
                    ip_address: ip_address.to_string(),
                });
            }
            
            Err(AuthenticationError::InvalidCredentials)
        }
    }
}
```

### Password Change Flow

```rust
async fn change_password_flow(
    user_id: &UserId,
    current_password_str: &str,
    new_password_str: &str
) -> Result<Vec<DomainEvent>, DomainError> {
    // 1. Load user
    let mut user = load_user(user_id).await?;
    
    // 2. Verify current password
    let current_password = Password::new(current_password_str)?;
    if !user.verify_password(&current_password) {
        return Err(DomainError::InvalidCredentials);
    }
    
    // 3. Validate new password
    let new_password = Password::new(new_password_str)?;
    let policy = PasswordPolicy::default();
    
    if policy.is_password_recently_used(&user, &new_password) {
        return Err(DomainError::PasswordRecentlyUsed);
    }
    
    // 4. Change password
    user.change_password(new_password)?;
    
    // 5. Generate events
    let events = vec![
        DomainEvent::PasswordChanged {
            user_id: user.id().clone(),
        }
    ];
    
    Ok(events)
}
```

## 🧪 Testing Utilities

### Mock Builders

```rust
use auth_domain::entities::User;

// User builder for tests
pub struct UserBuilder {
    email: Option<Email>,
    password: Option<Password>,
    verified: bool,
    active: bool,
}

impl UserBuilder {
    pub fn new() -> Self {
        Self {
            email: None,
            password: None,
            verified: false,
            active: true,
        }
    }
    
    pub fn with_email(mut self, email: &str) -> Self {
        self.email = Some(Email::parse(email).unwrap());
        self
    }
    
    pub fn with_password(mut self, password: &str) -> Self {
        self.password = Some(Password::new(password).unwrap());
        self
    }
    
    pub fn verified(mut self) -> Self {
        self.verified = true;
        self
    }
    
    pub fn build(self) -> User {
        let email = self.email.unwrap_or_else(|| 
            Email::parse("<EMAIL>").unwrap()
        );
        let password = self.password.unwrap_or_else(|| 
            Password::new("TestPassword123!").unwrap()
        );
        
        let mut user = User::new(email, password).unwrap();
        
        if self.verified {
            user.verify_email().unwrap();
        }
        
        if !self.active {
            user.suspend("Test").unwrap();
        }
        
        user
    }
}

// Usage in tests
#[test]
fn test_user_authentication() {
    let user = UserBuilder::new()
        .with_email("<EMAIL>")
        .with_password("SecurePass123!")
        .verified()
        .build();
    
    let password = Password::new("SecurePass123!").unwrap();
    let session = user.authenticate(password).unwrap();
    
    assert!(session.is_valid());
}
```

### Test Helpers

```rust
// Common test data
pub mod test_data {
    use super::*;
    
    pub fn valid_email() -> Email {
        Email::parse("<EMAIL>").unwrap()
    }
    
    pub fn valid_password() -> Password {
        Password::new("TestPassword123!").unwrap()
    }
    
    pub fn weak_password() -> &'static str {
        "weak"
    }
    
    pub fn common_password() -> &'static str {
        "password123"
    }
    
    pub fn create_test_user() -> User {
        User::new(valid_email(), valid_password()).unwrap()
    }
}
```

## ⚠️ Error Handling

### Error Types

```rust
use auth_domain::errors::{DomainError, UserError, ValidationError};

// Handling domain errors
match user.authenticate(password) {
    Ok(session) => handle_success(session),
    Err(DomainError::User(UserError::InvalidCredentials)) => {
        // Wrong password
        handle_invalid_credentials()
    }
    Err(DomainError::User(UserError::AccountLocked)) => {
        // Account is locked
        handle_account_locked()
    }
    Err(DomainError::User(UserError::AccountSuspended)) => {
        // Account is suspended
        handle_account_suspended()
    }
    Err(error) => {
        // Other errors
        handle_generic_error(error)
    }
}

// Validation errors
match Email::parse(email_input) {
    Ok(email) => use_email(email),
    Err(ValidationError::EmptyEmail) => show_error("Email cannot be empty"),
    Err(ValidationError::InvalidEmailFormat) => show_error("Invalid email format"),
    Err(error) => show_error(&format!("Email error: {}", error)),
}
```

## 🔧 Configuration

### Security Configuration

```rust
use auth_domain::config::{SecurityConfig, PasswordPolicyConfig};

let security_config = SecurityConfig {
    argon2_memory_cost: 65536,    // 64MB
    argon2_time_cost: 3,          // 3 iterations  
    argon2_parallelism: 4,        // 4 threads
    session_timeout_hours: 24,    // 24 hour sessions
    max_login_attempts: 5,        // Lock after 5 attempts
    lockout_duration_minutes: 15, // 15 minute lockout
};

let password_policy = PasswordPolicyConfig {
    min_length: 8,
    max_length: 128,
    require_uppercase: true,
    require_lowercase: true,
    require_digits: true,
    require_special_chars: true,
    min_character_types: 3,
    password_history_size: 5,
    check_common_passwords: true,
};
```

---

This API documentation covers the essential patterns and usage for the AuthService domain layer. For implementation examples and integration patterns, see the application and infrastructure layer documentation.