//! AuthService Domain Layer
//!
//! This crate implements the core business logic for the AuthService authentication
//! system using Domain-Driven Design (DDD) principles with hexagonal architecture.
//!
//! # Architecture Principles
//!
//! - **Zero External Dependencies**: Pure business logic only
//! - **Hexagonal Architecture**: Ports and adapters pattern
//! - **Domain-Driven Design**: Rich domain model with ubiquitous language
//! - **Test-Driven Development**: 100% test coverage with comprehensive test suites
//!
//! # Core Components
//!
//! - [`entities`]: Aggregate roots and entities with business identity
//! - [`value_objects`]: Immutable types with validation and business rules
//! - [`services`]: Domain services for complex business logic coordination
//! - [`repositories`]: Data persistence contracts (interfaces only)
//! - [`events`]: Domain events for integration and audit logging
//! - [`crypto`]: Cryptographic utilities with security-first implementation
//!
//! # Security Features
//!
//! This domain layer implements production-grade security:
//!
//! - **Argon2id Password Hashing** with OWASP 2025 parameters
//! - **Timing Attack Prevention** using constant-time operations
//! - **Rate Limiting** with sliding window algorithms
//! - **Session Security** using ChaCha20 cryptographic tokens
//! - **Input Validation** embedded in value objects
//! - **Audit Trail** via comprehensive domain events
//!
//! # Performance Requirements
//!
//! The domain layer meets strict performance requirements:
//!
//! - Authentication operations: <100ms
//! - Session validation: <10ms
//! - Password hashing: Optimized Argon2id parameters
//! - Memory efficient: Static dispatch, minimal allocations
//!
//! # Example Usage
//!
//! ```rust,no_run
//! use auth_domain::entities::User;
//! use auth_domain::value_objects::{Email, Password};
//!
//! // Create a new user with validation
//! let email = Email::new("<EMAIL>").unwrap();
//! let password = Password::new("SecureP@ssw0rd123").unwrap();
//! let user = User::new(email, password).unwrap();
//!
//! // Authentication requires mutable reference
//! // let mut user = user;
//! // let login_password = Password::new("SecureP@ssw0rd123").unwrap();
//! // let result = user.verify_password(&login_password);
//! ```

// Documentation: Comprehensive documentation available in README.md, SECURITY.md, and API.md
// #![warn(missing_docs)] // Temporarily disabled - extensive documentation exists externally
#![allow(clippy::uninlined_format_args)]
#![allow(clippy::useless_vec)]

pub mod crypto;
pub mod entities;
pub mod errors;
pub mod events;
pub mod repositories;
pub mod services;
pub mod value_objects;

// Re-export commonly used types for convenience
pub use errors::{DomainError, UserError};
