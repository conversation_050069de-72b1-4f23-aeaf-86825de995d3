// Domain-specific error types
// These errors represent business rule violations and domain constraints

#[derive(Debug, Clone, PartialEq)]
pub enum DomainError {
    InvalidEmail(String),
    InvalidPassword(String),
    InvalidUserId(String),
    UserAlreadyExists,
    UserNotFound,
    InvalidSession,
    SessionExpired,
    InvalidRole(String),
    AuthenticationFailed,
    AuthorizationFailed,
    InvalidInput(String),
    InvalidId(String),
    CryptoError(String),
    // Infrastructure layer errors
    Infrastructure(String),
    Configuration(String),
    External(String),
    Database { source: String, context: String },
    Cache { operation: String, reason: String },
}

#[derive(Debug, Clone, PartialEq)]
pub enum UserError {
    AlreadyVerified,
    AlreadyActive,
    AlreadyInactive,
    CannotChangeVerifiedEmail,
    PasswordRecentlyUsed,
    RestrictedEmailDomain,
    InappropriateUsername,
    AccountLocked,
}

impl std::fmt::Display for DomainError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DomainError::InvalidEmail(msg) => write!(f, "Invalid email: {msg}"),
            DomainError::InvalidPassword(msg) => write!(f, "Invalid password: {msg}"),
            DomainError::InvalidUserId(msg) => write!(f, "Invalid user ID: {msg}"),
            DomainError::UserAlreadyExists => write!(f, "User already exists"),
            DomainError::UserNotFound => write!(f, "User not found"),
            DomainError::InvalidSession => write!(f, "Invalid session"),
            DomainError::SessionExpired => write!(f, "Session expired"),
            DomainError::InvalidRole(msg) => write!(f, "Invalid role: {msg}"),
            DomainError::AuthenticationFailed => write!(f, "Authentication failed"),
            DomainError::AuthorizationFailed => write!(f, "Authorization failed"),
            DomainError::InvalidInput(msg) => write!(f, "Invalid input: {msg}"),
            DomainError::InvalidId(msg) => write!(f, "Invalid ID: {msg}"),
            DomainError::CryptoError(msg) => write!(f, "Cryptographic error: {msg}"),
            DomainError::Infrastructure(msg) => write!(f, "Infrastructure error: {msg}"),
            DomainError::Configuration(msg) => write!(f, "Configuration error: {msg}"),
            DomainError::External(msg) => write!(f, "External service error: {msg}"),
            DomainError::Database { source, context } => {
                write!(f, "Database error in {context}: {source}")
            }
            DomainError::Cache { operation, reason } => {
                write!(f, "Cache error during {operation}: {reason}")
            }
        }
    }
}

impl std::error::Error for DomainError {}

impl std::fmt::Display for UserError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UserError::AlreadyVerified => write!(f, "User is already verified"),
            UserError::AlreadyActive => write!(f, "User is already active"),
            UserError::AlreadyInactive => write!(f, "User is already inactive"),
            UserError::CannotChangeVerifiedEmail => {
                write!(f, "Cannot change email for verified user")
            }
            UserError::PasswordRecentlyUsed => write!(f, "Password was recently used"),
            UserError::RestrictedEmailDomain => write!(f, "Email domain is restricted"),
            UserError::InappropriateUsername => {
                write!(f, "Username contains inappropriate content")
            }
            UserError::AccountLocked => {
                write!(f, "Account is locked due to too many failed attempts")
            }
        }
    }
}

impl std::error::Error for UserError {}

impl From<UserError> for DomainError {
    fn from(err: UserError) -> Self {
        match err {
            UserError::RestrictedEmailDomain => {
                DomainError::InvalidEmail("Email domain is restricted".to_string())
            }
            UserError::InappropriateUsername => {
                DomainError::InvalidInput("Username contains inappropriate content".to_string())
            }
            UserError::AccountLocked => DomainError::AuthenticationFailed,
            _ => DomainError::InvalidInput(err.to_string()),
        }
    }
}
