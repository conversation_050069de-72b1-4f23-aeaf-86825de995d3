// User registration completed event
// Fired when a new user successfully registers

use super::DomainEvent;
use crate::value_objects::{DateTime, Email, UserId};

#[derive(Debug, Clone)]
pub struct UserRegistered {
    pub user_id: UserId,
    pub email: Email,
    pub occurred_at: std::time::SystemTime,
    pub registration_ip: Option<String>,
    pub user_agent: Option<String>,
}

impl UserRegistered {
    pub fn new(
        user_id: UserId,
        email: Email,
        registration_ip: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        UserRegistered {
            user_id,
            email,
            occurred_at: std::time::SystemTime::now(),
            registration_ip,
            user_agent,
        }
    }
}

impl From<UserRegistered> for DomainEvent {
    fn from(event: UserRegistered) -> Self {
        DomainEvent::UserRegistered {
            user_id: event.user_id,
            email: event.email,
            occurred_at: DateTime::from_system_time(event.occurred_at),
            registration_ip: event.registration_ip,
            user_agent: event.user_agent,
        }
    }
}

/* impl DomainEvent for UserRegistered {
    fn event_type(&self) -> &'static str {
        "user.registered"
    }

    fn occurred_at(&self) -> std::time::SystemTime {
        self.occurred_at
    }

    fn aggregate_id(&self) -> String {
        self.user_id.as_str().to_string()
    }
} */
