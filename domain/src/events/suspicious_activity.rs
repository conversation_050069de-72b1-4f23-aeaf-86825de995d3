// Suspicious activity detected event
// Fired when potential security threats are detected

// use super::DomainEvent;
use crate::value_objects::UserId;

#[derive(Debug, Clone)]
pub struct SuspiciousActivity {
    pub user_id: Option<UserId>, // May not have a user ID for anonymous attempts
    pub activity_type: SuspiciousActivityType,
    pub occurred_at: std::time::SystemTime,
    pub source_ip: Option<String>,
    pub user_agent: Option<String>,
    pub details: String,
    pub severity: SeverityLevel,
}

#[derive(Debug, Clone, PartialEq)]
pub enum SuspiciousActivityType {
    BruteForceAttempt,
    SuspiciousLogin,
    UnknownDevice,
    UnusualLocation,
    RateLimitExceeded,
    InvalidTokenUsage,
    AccountEnumeration,
    PasswordSpray,
    SuspiciousRegistration,
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum SeverityLevel {
    Low,
    Medium,
    High,
    Critical,
}

impl SuspiciousActivity {
    pub fn new(
        user_id: Option<UserId>,
        activity_type: SuspiciousActivityType,
        source_ip: Option<String>,
        user_agent: Option<String>,
        details: String,
        severity: SeverityLevel,
    ) -> Self {
        SuspiciousActivity {
            user_id,
            activity_type,
            occurred_at: std::time::SystemTime::now(),
            source_ip,
            user_agent,
            details,
            severity,
        }
    }

    pub fn brute_force_attempt(
        user_id: Option<UserId>,
        source_ip: Option<String>,
        attempt_count: u32,
    ) -> Self {
        Self::new(
            user_id,
            SuspiciousActivityType::BruteForceAttempt,
            source_ip,
            None,
            format!("Brute force detected: {attempt_count} attempts"),
            if attempt_count > 10 {
                SeverityLevel::High
            } else {
                SeverityLevel::Medium
            },
        )
    }

    pub fn suspicious_login(user_id: UserId, source_ip: Option<String>, reason: String) -> Self {
        Self::new(
            Some(user_id),
            SuspiciousActivityType::SuspiciousLogin,
            source_ip,
            None,
            format!("Suspicious login: {reason}"),
            SeverityLevel::Medium,
        )
    }

    pub fn rate_limit_exceeded(
        source_ip: Option<String>,
        endpoint: String,
        request_count: u32,
    ) -> Self {
        Self::new(
            None,
            SuspiciousActivityType::RateLimitExceeded,
            source_ip,
            None,
            format!("Rate limit exceeded on {endpoint}: {request_count} requests"),
            SeverityLevel::Low,
        )
    }

    pub fn unknown_device(
        user_id: UserId,
        source_ip: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        Self::new(
            Some(user_id),
            SuspiciousActivityType::UnknownDevice,
            source_ip,
            user_agent,
            "Login from unknown device".to_string(),
            SeverityLevel::Medium,
        )
    }

    pub fn unusual_location(user_id: UserId, source_ip: Option<String>, location: String) -> Self {
        Self::new(
            Some(user_id),
            SuspiciousActivityType::UnusualLocation,
            source_ip,
            None,
            format!("Login from unusual location: {location}"),
            SeverityLevel::Medium,
        )
    }

    pub fn should_block_request(&self) -> bool {
        match self.severity {
            SeverityLevel::Critical => true,
            SeverityLevel::High => matches!(
                self.activity_type,
                SuspiciousActivityType::BruteForceAttempt | SuspiciousActivityType::PasswordSpray
            ),
            _ => false,
        }
    }

    pub fn should_alert_admin(&self) -> bool {
        self.severity >= SeverityLevel::High
    }

    pub fn should_challenge_user(&self) -> bool {
        matches!(
            self.activity_type,
            SuspiciousActivityType::UnknownDevice
                | SuspiciousActivityType::UnusualLocation
                | SuspiciousActivityType::SuspiciousLogin
        )
    }
}

/* impl DomainEvent for SuspiciousActivity {
    fn event_type(&self) -> &'static str {
        "security.suspicious_activity"
    }

    fn occurred_at(&self) -> std::time::SystemTime {
        self.occurred_at
    }

    fn aggregate_id(&self) -> String {
        self.user_id
            .as_ref()
            .map(|id| id.as_str().to_string())
            .unwrap_or_else(|| {
                format!(
                    "anonymous_{}",
                    self.source_ip.as_deref().unwrap_or("unknown")
                )
            })
    }
} */

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_brute_force_severity() {
        let low_attempts = SuspiciousActivity::brute_force_attempt(None, None, 5);
        assert_eq!(low_attempts.severity, SeverityLevel::Medium);

        let high_attempts = SuspiciousActivity::brute_force_attempt(None, None, 15);
        assert_eq!(high_attempts.severity, SeverityLevel::High);
    }

    #[test]
    fn test_should_block_request() {
        let brute_force = SuspiciousActivity::brute_force_attempt(None, None, 15);
        assert!(brute_force.should_block_request());

        let unknown_device = SuspiciousActivity::unknown_device(
            UserId::new(),
            Some("***********".to_string()),
            None,
        );
        assert!(!unknown_device.should_block_request());
    }

    #[test]
    fn test_should_challenge_user() {
        let unknown_device = SuspiciousActivity::unknown_device(
            UserId::new(),
            Some("***********".to_string()),
            None,
        );
        assert!(unknown_device.should_challenge_user());

        let rate_limit = SuspiciousActivity::rate_limit_exceeded(
            Some("***********".to_string()),
            "/api/login".to_string(),
            100,
        );
        assert!(!rate_limit.should_challenge_user());
    }

    #[test]
    fn test_should_alert_admin() {
        let high_severity = SuspiciousActivity::brute_force_attempt(None, None, 15);
        assert!(high_severity.should_alert_admin());

        let low_severity =
            SuspiciousActivity::rate_limit_exceeded(None, "/api/test".to_string(), 10);
        assert!(!low_severity.should_alert_admin());
    }
}
