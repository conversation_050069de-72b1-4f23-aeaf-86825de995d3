// Core domain events that represent important business moments
// These events are used for event sourcing, auditing, and integration

use crate::entities::organization::OrganizationRole;
use crate::entities::role::Permission;
use crate::events::user_logged_in::AuthenticationMethod;
use crate::value_objects::{DateTime, Email, OrganizationId, RoleId, SessionId, UserId};

#[derive(Debug, Clone, PartialEq)]
pub enum DomainEvent {
    // User events
    UserRegistered {
        user_id: UserId,
        email: Email,
        occurred_at: DateTime,
        registration_ip: Option<String>,
        user_agent: Option<String>,
    },
    UserLoggedIn {
        user_id: UserId,
        session_id: SessionId,
        occurred_at: DateTime,
        login_ip: Option<String>,
        user_agent: Option<String>,
        authentication_method: AuthenticationMethod,
    },
    UserVerified {
        user_id: UserId,
        verified_at: DateTime,
    },
    EmailChanged {
        user_id: UserId,
        old_email: Email,
        new_email: Email,
    },
    PasswordChanged {
        user_id: UserId,
    },
    UserDeactivated {
        user_id: UserId,
    },
    UserActivated {
        user_id: UserId,
    },

    // Session events
    SessionRefreshed {
        session_id: SessionId,
        user_id: UserId,
    },
    SessionExpired {
        session_id: SessionId,
        user_id: UserId,
    },

    // Role events
    PermissionAdded {
        role_id: RoleId,
        permission: Permission,
    },
    PermissionRemoved {
        role_id: RoleId,
        permission: Permission,
    },

    // Organization events
    MemberAdded {
        org_id: OrganizationId,
        user_id: UserId,
        role: OrganizationRole,
        invited_by: UserId,
    },
    MemberRemoved {
        org_id: OrganizationId,
        user_id: UserId,
        removed_by: UserId,
    },
    OwnershipTransferred {
        org_id: OrganizationId,
        old_owner: UserId,
        new_owner: UserId,
    },
}

impl DomainEvent {
    pub fn occurred_at(&self) -> DateTime {
        // In a real implementation, each event would store its own timestamp
        // For now, return current time
        DateTime::now()
    }

    pub fn event_type(&self) -> &'static str {
        match self {
            DomainEvent::UserRegistered { .. } => "UserRegistered",
            DomainEvent::UserLoggedIn { .. } => "UserLoggedIn",
            DomainEvent::UserVerified { .. } => "UserVerified",
            DomainEvent::EmailChanged { .. } => "EmailChanged",
            DomainEvent::PasswordChanged { .. } => "PasswordChanged",
            DomainEvent::UserDeactivated { .. } => "UserDeactivated",
            DomainEvent::UserActivated { .. } => "UserActivated",
            DomainEvent::SessionRefreshed { .. } => "SessionRefreshed",
            DomainEvent::SessionExpired { .. } => "SessionExpired",
            DomainEvent::PermissionAdded { .. } => "PermissionAdded",
            DomainEvent::PermissionRemoved { .. } => "PermissionRemoved",
            DomainEvent::MemberAdded { .. } => "MemberAdded",
            DomainEvent::MemberRemoved { .. } => "MemberRemoved",
            DomainEvent::OwnershipTransferred { .. } => "OwnershipTransferred",
        }
    }
}
