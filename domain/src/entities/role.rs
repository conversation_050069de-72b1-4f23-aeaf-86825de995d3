// Role entity with hierarchy support
// Represents user roles and permissions in the system

use crate::errors::DomainError;
use crate::value_objects::RoleId;

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum Permission {
    Read,
    Write,
    Delete,
    Admin,
    Custom(String),
}

impl Permission {
    pub fn from_string(permission: String) -> Self {
        match permission.to_lowercase().as_str() {
            "read" => Permission::Read,
            "write" => Permission::Write,
            "delete" => Permission::Delete,
            "admin" => Permission::Admin,
            _ => Permission::Custom(permission),
        }
    }

    pub fn as_str(&self) -> &str {
        match self {
            Permission::Read => "read",
            Permission::Write => "write",
            Permission::Delete => "delete",
            Permission::Admin => "admin",
            Permission::Custom(s) => s,
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
pub struct Role {
    id: RoleId,
    name: String,
    description: Option<String>,
    permissions: Vec<Permission>,
    parent_role: Option<RoleId>,
    is_active: bool,
    created_at: std::time::SystemTime,
    updated_at: std::time::SystemTime,
}

impl Role {
    pub fn new(
        id: RoleId,
        name: String,
        description: Option<String>,
        permissions: Vec<Permission>,
    ) -> Result<Self, DomainError> {
        if name.is_empty() {
            return Err(DomainError::InvalidRole(
                "Role name cannot be empty".to_string(),
            ));
        }

        // Check for reserved role names
        let reserved_names = ["system", "root", "superuser", "administrator"];
        if reserved_names.contains(&name.to_lowercase().as_str()) {
            return Err(DomainError::InvalidRole("Reserved role name".to_string()));
        }

        // Check for malicious permission names
        for permission in &permissions {
            if let Permission::Custom(custom_name) = permission {
                if custom_name.contains("DROP TABLE")
                    || custom_name.contains("<script>")
                    || custom_name.contains('\0')
                    || custom_name.contains('\n')
                {
                    return Err(DomainError::InvalidRole(
                        "Malicious permission name".to_string(),
                    ));
                }
            }
        }

        let now = std::time::SystemTime::now();

        Ok(Role {
            id,
            name,
            description,
            permissions,
            parent_role: None,
            is_active: true,
            created_at: now,
            updated_at: now,
        })
    }

    // Getters
    pub fn id(&self) -> &RoleId {
        &self.id
    }

    pub fn name(&self) -> &str {
        &self.name
    }

    pub fn description(&self) -> Option<&str> {
        self.description.as_deref()
    }

    pub fn permissions(&self) -> &[Permission] {
        &self.permissions
    }

    pub fn parent_role(&self) -> Option<&RoleId> {
        self.parent_role.as_ref()
    }

    pub fn is_active(&self) -> bool {
        self.is_active
    }

    pub fn created_at(&self) -> std::time::SystemTime {
        self.created_at
    }

    pub fn updated_at(&self) -> std::time::SystemTime {
        self.updated_at
    }

    // Business operations
    pub fn has_permission(&self, permission: &Permission) -> bool {
        self.permissions.contains(permission) || self.permissions.contains(&Permission::Admin)
    }

    pub fn add_permission(&mut self, permission: Permission) -> Result<(), DomainError> {
        if self.permissions.contains(&permission) {
            return Err(DomainError::InvalidRole(
                "Permission already exists".to_string(),
            ));
        }

        // Limit the number of permissions per role
        if self.permissions.len() >= 100 {
            return Err(DomainError::InvalidRole("Too many permissions".to_string()));
        }

        self.permissions.push(permission);
        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }

    pub fn remove_permission(&mut self, permission: &Permission) -> Result<(), DomainError> {
        let initial_len = self.permissions.len();
        self.permissions.retain(|p| p != permission);

        if self.permissions.len() == initial_len {
            return Err(DomainError::InvalidRole("Permission not found".to_string()));
        }

        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }

    pub fn set_parent_role(&mut self, parent_role: RoleId) -> Result<(), DomainError> {
        if parent_role == self.id {
            return Err(DomainError::InvalidRole(
                "Role cannot be its own parent".to_string(),
            ));
        }

        self.parent_role = Some(parent_role);
        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }

    pub fn remove_parent_role(&mut self) {
        self.parent_role = None;
        self.updated_at = std::time::SystemTime::now();
    }

    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = std::time::SystemTime::now();
    }

    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = std::time::SystemTime::now();
    }

    pub fn inherits_from(&self, all_roles: &[Role]) -> Vec<Permission> {
        let mut permissions = std::collections::HashSet::new();
        let mut visited = std::collections::HashSet::new();

        // Add own permissions
        for perm in &self.permissions {
            permissions.insert(perm.clone());
        }

        // If has admin permission, has all permissions
        if self.has_permission(&Permission::Admin) {
            permissions.insert(Permission::Read);
            permissions.insert(Permission::Write);
            permissions.insert(Permission::Delete);
            // Note: Custom permissions would need to be added explicitly
        }

        // Traverse hierarchy
        self.collect_inherited_permissions(&mut permissions, &mut visited, all_roles);

        permissions.into_iter().collect()
    }

    fn collect_inherited_permissions(
        &self,
        permissions: &mut std::collections::HashSet<Permission>,
        visited: &mut std::collections::HashSet<RoleId>,
        all_roles: &[Role],
    ) {
        // Prevent circular references
        if !visited.insert(self.id.clone()) {
            return;
        }

        // Find parent role
        if let Some(ref parent_id) = self.parent_role {
            if let Some(parent) = all_roles.iter().find(|r| &r.id == parent_id) {
                // Add parent permissions
                for perm in &parent.permissions {
                    permissions.insert(perm.clone());
                }

                // If parent has admin, grant all permissions
                if parent.has_permission(&Permission::Admin) {
                    permissions.insert(Permission::Read);
                    permissions.insert(Permission::Write);
                    permissions.insert(Permission::Delete);
                    permissions.insert(Permission::Admin);
                }

                // Recursively collect from parent's ancestors
                parent.collect_inherited_permissions(permissions, visited, all_roles);
            }
        }
    }

    pub fn add_permission_with_audit(
        &mut self,
        permission: Permission,
    ) -> Result<Vec<crate::events::DomainEvent>, DomainError> {
        self.add_permission(permission.clone())?;

        Ok(vec![crate::events::DomainEvent::PermissionAdded {
            role_id: self.id.clone(),
            permission,
        }])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::events::DomainEvent;

    #[test]
    fn should_create_role_with_valid_data() {
        // Arrange
        let role_id = RoleId::builtin("admin").unwrap();
        let name = "Admin".to_string();
        let description = Some("Full system access".to_string());
        let permissions = vec![Permission::Admin];

        // Act
        let result = Role::new(
            role_id.clone(),
            name.clone(),
            description.clone(),
            permissions.clone(),
        );

        // Assert
        assert!(result.is_ok());
        let role = result.unwrap();
        assert_eq!(role.id(), &role_id);
        assert_eq!(role.name(), &name);
        assert_eq!(role.description(), Some("Full system access"));
        assert_eq!(role.permissions(), &permissions);
        assert!(role.is_active());
        assert!(role.parent_role().is_none());
    }

    #[test]
    fn should_fail_to_create_role_with_empty_name() {
        // Arrange
        let role_id = RoleId::generate();
        let empty_name = String::new();

        // Act
        let result = Role::new(role_id, empty_name, None, vec![]);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidRole("Role name cannot be empty".to_string())
        );
    }

    #[test]
    fn should_create_role_id_with_valid_format() {
        // Arrange - Test builtin roles
        let valid_builtin_names = vec!["admin", "user", "moderator", "admin_user"];

        for name in valid_builtin_names {
            // Act
            let result = RoleId::builtin(name);

            // Assert
            assert!(result.is_ok(), "Should accept valid builtin name: {}", name);
            assert_eq!(result.unwrap().as_str(), &format!("role_builtin_{}", name));
        }

        // Test generated role ID with proper hex format
        let generated_role = RoleId::generate();
        assert!(generated_role.as_str().starts_with("role_"));
        assert!(generated_role.as_str().len() >= 37); // role_ + 32 hex chars
    }

    #[test]
    fn should_fail_to_create_role_id_with_invalid_format() {
        // Arrange
        let invalid_ids = vec![
            "",
            "invalid role",  // space
            "invalid@role",  // special char
            "invalid.role",  // period
            "invalid/role",  // slash
            "invalid\\role", // backslash
        ];

        for invalid_id in invalid_ids {
            // Act
            let result = RoleId::from_string(invalid_id.to_string());

            // Assert
            assert!(result.is_err(), "Should reject invalid ID: {}", invalid_id);
        }
    }

    #[test]
    fn should_check_permission_correctly() {
        // Arrange
        let role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read, Permission::Write],
        )
        .unwrap();

        // Act & Assert
        assert!(role.has_permission(&Permission::Read));
        assert!(role.has_permission(&Permission::Write));
        assert!(!role.has_permission(&Permission::Delete));
        assert!(!role.has_permission(&Permission::Admin));
    }

    #[test]
    fn should_grant_all_permissions_to_admin_role() {
        // Arrange
        let role = Role::new(
            RoleId::builtin("admin").unwrap(),
            "Admin".to_string(),
            None,
            vec![Permission::Admin],
        )
        .unwrap();

        // Act & Assert - Admin permission should grant access to everything
        assert!(role.has_permission(&Permission::Read));
        assert!(role.has_permission(&Permission::Write));
        assert!(role.has_permission(&Permission::Delete));
        assert!(role.has_permission(&Permission::Admin));
        assert!(role.has_permission(&Permission::Custom("any_custom_permission".to_string())));
    }

    #[test]
    fn should_add_permission_to_role() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        // Act
        let result = role.add_permission(Permission::Write);

        // Assert
        assert!(result.is_ok());
        assert!(role.has_permission(&Permission::Write));
        assert!(role.permissions().contains(&Permission::Write));
    }

    #[test]
    fn should_fail_to_add_duplicate_permission() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        // Act
        let result = role.add_permission(Permission::Read);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidRole("Permission already exists".to_string())
        );
    }

    #[test]
    fn should_remove_permission_from_role() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read, Permission::Write],
        )
        .unwrap();

        // Act
        let result = role.remove_permission(&Permission::Write);

        // Assert
        assert!(result.is_ok());
        assert!(!role.has_permission(&Permission::Write));
        assert!(!role.permissions().contains(&Permission::Write));
        assert!(role.has_permission(&Permission::Read)); // Should still have Read
    }

    #[test]
    fn should_fail_to_remove_nonexistent_permission() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        // Act
        let result = role.remove_permission(&Permission::Write);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidRole("Permission not found".to_string())
        );
    }

    #[test]
    fn should_set_parent_role() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();
        let parent_id = RoleId::builtin("moderator").unwrap();

        // Act
        let result = role.set_parent_role(parent_id.clone());

        // Assert
        assert!(result.is_ok());
        assert_eq!(role.parent_role(), Some(&parent_id));
    }

    #[test]
    fn should_fail_to_set_self_as_parent() {
        // Arrange
        let role_id = RoleId::builtin("user").unwrap();
        let mut role = Role::new(
            role_id.clone(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        // Act
        let result = role.set_parent_role(role_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidRole("Role cannot be its own parent".to_string())
        );
    }

    #[test]
    fn should_remove_parent_role() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();
        let parent_id = RoleId::builtin("moderator").unwrap();
        role.set_parent_role(parent_id).unwrap();

        // Act
        role.remove_parent_role();

        // Assert
        assert!(role.parent_role().is_none());
    }

    #[test]
    fn should_inherit_permissions_from_parent() {
        // Arrange
        let parent_role = Role::new(
            RoleId::builtin("moderator").unwrap(),
            "Moderator".to_string(),
            None,
            vec![Permission::Read, Permission::Write, Permission::Delete],
        )
        .unwrap();

        let mut child_role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();
        child_role
            .set_parent_role(RoleId::builtin("moderator").unwrap())
            .unwrap();

        let roles = vec![parent_role, child_role.clone()];

        // Act
        let inherited_permissions = child_role.inherits_from(&roles);

        // Assert
        assert!(inherited_permissions.contains(&Permission::Read));
        assert!(inherited_permissions.contains(&Permission::Write));
        assert!(inherited_permissions.contains(&Permission::Delete));
        assert_eq!(inherited_permissions.len(), 3); // Should have all parent permissions plus own
    }

    #[test]
    fn should_inherit_permissions_from_multiple_levels() {
        // Arrange - Create a 3-level hierarchy: admin -> moderator -> user
        let admin_role = Role::new(
            RoleId::builtin("admin").unwrap(),
            "Admin".to_string(),
            None,
            vec![Permission::Admin],
        )
        .unwrap();

        let mut moderator_role = Role::new(
            RoleId::builtin("moderator").unwrap(),
            "Moderator".to_string(),
            None,
            vec![Permission::Delete],
        )
        .unwrap();
        moderator_role
            .set_parent_role(RoleId::builtin("admin").unwrap())
            .unwrap();

        let mut user_role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();
        user_role
            .set_parent_role(RoleId::builtin("moderator").unwrap())
            .unwrap();

        let roles = vec![admin_role, moderator_role, user_role.clone()];

        // Act
        let inherited_permissions = user_role.inherits_from(&roles);

        // Assert
        assert!(inherited_permissions.contains(&Permission::Read)); // Own permission
        assert!(inherited_permissions.contains(&Permission::Delete)); // From moderator
        assert!(inherited_permissions.contains(&Permission::Admin)); // From admin
    }

    #[test]
    fn should_handle_circular_hierarchy_gracefully() {
        // Arrange - This shouldn't be possible in practice, but test defensive code
        let role_a_id = RoleId::generate();
        let role_b_id = RoleId::generate();

        let mut role_a = Role::new(
            role_a_id.clone(),
            "Role A".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        let mut role_b = Role::new(
            role_b_id.clone(),
            "Role B".to_string(),
            None,
            vec![Permission::Write],
        )
        .unwrap();

        // Create circular reference
        role_a.set_parent_role(role_b_id.clone()).unwrap();
        role_b.set_parent_role(role_a_id.clone()).unwrap();

        let roles = vec![role_a.clone(), role_b];

        // Act - Should not infinite loop
        let inherited_permissions = role_a.inherits_from(&roles);

        // Assert - Should at least contain own permissions
        assert!(inherited_permissions.contains(&Permission::Read));
    }

    #[test]
    fn should_deactivate_role() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();
        assert!(role.is_active());

        // Act
        role.deactivate();

        // Assert
        assert!(!role.is_active());
    }

    #[test]
    fn should_activate_role() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();
        role.deactivate();
        assert!(!role.is_active());

        // Act
        role.activate();

        // Assert
        assert!(role.is_active());
    }

    #[test]
    fn should_handle_custom_permissions() {
        // Arrange
        let custom_permission = Permission::Custom("manage_billing".to_string());
        let role = Role::new(
            RoleId::builtin("billing_admin").unwrap(),
            "Billing Admin".to_string(),
            None,
            vec![custom_permission.clone()],
        )
        .unwrap();

        // Act & Assert
        assert!(role.has_permission(&custom_permission));
        assert!(!role.has_permission(&Permission::Custom("different_permission".to_string())));
    }

    #[test]
    fn should_create_permission_from_string() {
        // Arrange & Act
        let read_perm = Permission::from_string("read".to_string());
        let write_perm = Permission::from_string("WRITE".to_string()); // Case insensitive
        let delete_perm = Permission::from_string("delete".to_string());
        let admin_perm = Permission::from_string("admin".to_string());
        let custom_perm = Permission::from_string("custom_permission".to_string());

        // Assert
        assert_eq!(read_perm, Permission::Read);
        assert_eq!(write_perm, Permission::Write);
        assert_eq!(delete_perm, Permission::Delete);
        assert_eq!(admin_perm, Permission::Admin);
        assert_eq!(
            custom_perm,
            Permission::Custom("custom_permission".to_string())
        );
    }

    #[test]
    fn should_convert_permission_to_string() {
        // Arrange
        let permissions = [
            Permission::Read,
            Permission::Write,
            Permission::Delete,
            Permission::Admin,
            Permission::Custom("billing".to_string()),
        ];

        // Act & Assert
        assert_eq!(permissions[0].as_str(), "read");
        assert_eq!(permissions[1].as_str(), "write");
        assert_eq!(permissions[2].as_str(), "delete");
        assert_eq!(permissions[3].as_str(), "admin");
        assert_eq!(permissions[4].as_str(), "billing");
    }

    #[test]
    fn should_enforce_maximum_permissions_per_role() {
        // This test specifies that roles should have a reasonable limit on permissions
        // to prevent abuse and maintain performance
        // Arrange
        let mut role = Role::new(RoleId::generate(), "Test".to_string(), None, vec![]).unwrap();

        // Act - Try to add many permissions
        for i in 0..100 {
            let result = role.add_permission(Permission::Custom(format!("perm_{}", i)));
            assert!(result.is_ok());
        }

        // Try to add one more (should fail if limit is 100)
        let result = role.add_permission(Permission::Custom("perm_100".to_string()));

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidRole("Too many permissions".to_string())
        );
    }

    // Security-focused tests
    #[test]
    fn should_prevent_privilege_escalation() {
        // Arrange - A user role should not be able to gain admin privileges through manipulation
        let mut user_role = Role::new(
            RoleId::builtin("user").unwrap(),
            "User".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        // Act - Try to add admin permission (this should be controlled by business logic)
        let result = user_role.add_permission(Permission::Admin);

        // Assert - The role entity itself allows this, but business rules should prevent it
        assert!(result.is_ok()); // The entity allows it

        // However, in practice, there should be authorization checks before calling this method
        // This test documents that the entity itself doesn't prevent privilege escalation
        // Business logic in the application layer should handle this
    }

    #[test]
    fn should_validate_role_name_against_reserved_words() {
        // Arrange
        let reserved_names = vec!["system", "root", "superuser", "administrator"];

        for reserved_name in reserved_names {
            // Act
            let result = Role::new(RoleId::generate(), reserved_name.to_string(), None, vec![]);

            // Assert - Should fail for reserved names
            assert!(
                result.is_err(),
                "Should reject reserved name: {}",
                reserved_name
            );
            assert_eq!(
                result.unwrap_err(),
                DomainError::InvalidRole("Reserved role name".to_string())
            );
        }
    }

    #[test]
    fn should_prevent_permission_injection_attacks() {
        // Arrange
        let malicious_permissions = vec![
            "read'; DROP TABLE roles; --",
            "admin<script>alert('xss')</script>",
            "permission\0with\0nulls",
            "permission\nwith\nnewlines",
        ];

        for malicious_perm in malicious_permissions {
            // Act
            let custom_permission = Permission::Custom(malicious_perm.to_string());
            let result = Role::new(
                RoleId::generate(),
                "Test".to_string(),
                None,
                vec![custom_permission],
            );

            // Assert - Should sanitize or reject malicious permission names
            if result.is_ok() {
                let role = result.unwrap();
                // Permission should be sanitized
                assert!(!role.permissions()[0].as_str().contains("DROP TABLE"));
                assert!(!role.permissions()[0].as_str().contains("<script>"));
                assert!(!role.permissions()[0].as_str().contains('\0'));
                assert!(!role.permissions()[0].as_str().contains('\n'));
            } else {
                // Or it should be rejected entirely
                assert_eq!(
                    result.unwrap_err(),
                    DomainError::InvalidRole("Malicious permission name".to_string())
                );
            }
        }
    }

    #[test]
    fn should_audit_permission_changes() {
        // Arrange
        let mut role = Role::new(
            RoleId::builtin("auditor").unwrap(),
            "Auditor".to_string(),
            None,
            vec![Permission::Read],
        )
        .unwrap();

        // Act
        let result = role.add_permission_with_audit(Permission::Write);

        // Assert - Should produce audit events
        assert!(result.is_ok());
        let events = result.unwrap();
        assert_eq!(events.len(), 1);
        match &events[0] {
            DomainEvent::PermissionAdded {
                role_id,
                permission,
            } => {
                assert_eq!(role_id, role.id());
                assert_eq!(permission, &Permission::Write);
            }
            _ => panic!("Expected PermissionAdded event"),
        }
    }

    #[test]
    fn should_enforce_role_hierarchy_depth_limit() {
        // Arrange - Prevent excessively deep hierarchies that could cause performance issues
        let mut roles = Vec::new();
        let mut current_parent: Option<RoleId> = None;

        // Create a deep hierarchy
        for i in 0..20 {
            let role_id = RoleId::generate();
            let mut role = Role::new(
                role_id.clone(),
                format!("Role {}", i),
                None,
                vec![Permission::Read],
            )
            .unwrap();

            if let Some(parent) = current_parent {
                role.set_parent_role(parent).unwrap();
            }

            current_parent = Some(role_id);
            roles.push(role);
        }

        // Act - Try to calculate inherited permissions on deep hierarchy
        let deepest_role = &roles[19];
        let inherited_permissions = deepest_role.inherits_from(&roles);

        // Assert - Should either limit depth or handle gracefully
        assert!(!inherited_permissions.is_empty());
        // In practice, there should be a depth limit to prevent performance issues
    }
}
