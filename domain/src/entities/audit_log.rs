// Audit log entity for security and compliance tracking
// Contains immutable records of all significant system actions

use crate::value_objects::{DateTime, UserId};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Types of entities that can be audited
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EntityType {
    User,
    Role,
    Permission,
    Session,
    Organization,
    OrganizationMember,
    ApiKey,
    Settings,
    SecurityPolicy,
}

impl EntityType {
    pub fn as_str(&self) -> &'static str {
        match self {
            EntityType::User => "User",
            EntityType::Role => "Role",
            EntityType::Permission => "Permission",
            EntityType::Session => "Session",
            EntityType::Organization => "Organization",
            EntityType::OrganizationMember => "OrganizationMember",
            EntityType::ApiKey => "ApiKey",
            EntityType::Settings => "Settings",
            EntityType::SecurityPolicy => "SecurityPolicy",
        }
    }

    pub fn parse(s: &str) -> Option<Self> {
        match s {
            "User" => Some(EntityType::User),
            "Role" => Some(EntityType::Role),
            "Permission" => Some(EntityType::Permission),
            "Session" => Some(EntityType::Session),
            "Organization" => Some(EntityType::Organization),
            "OrganizationMember" => Some(EntityType::OrganizationMember),
            "ApiKey" => Some(EntityType::ApiKey),
            "Settings" => Some(EntityType::Settings),
            "SecurityPolicy" => Some(EntityType::SecurityPolicy),
            _ => None,
        }
    }
}

/// Types of actions that can be performed on entities
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AuditAction {
    // Create operations
    Create,
    Register,
    Add,

    // Read operations
    Read,
    View,
    Search,
    Export,

    // Update operations
    Update,
    Modify,
    Change,
    Enable,
    Disable,
    Activate,
    Deactivate,

    // Delete operations
    Delete,
    Remove,
    Revoke,

    // Authentication operations
    Login,
    Logout,
    LoginAttempt,
    PasswordReset,
    TwoFactorEnable,
    TwoFactorDisable,

    // Authorization operations
    AssignRole,
    RevokeRole,
    GrantPermission,
    DenyPermission,

    // Security operations
    SecurityViolation,
    RateLimitExceeded,
    SuspiciousActivity,
    AccountLockout,
    UnauthorizedAccess,

    // Administrative operations
    SystemConfiguration,
    MaintenanceMode,
    DataExport,
    DataImport,
    BackupCreated,
    BackupRestored,
}

impl AuditAction {
    pub fn as_str(&self) -> &'static str {
        match self {
            AuditAction::Create => "Create",
            AuditAction::Register => "Register",
            AuditAction::Add => "Add",
            AuditAction::Read => "Read",
            AuditAction::View => "View",
            AuditAction::Search => "Search",
            AuditAction::Export => "Export",
            AuditAction::Update => "Update",
            AuditAction::Modify => "Modify",
            AuditAction::Change => "Change",
            AuditAction::Enable => "Enable",
            AuditAction::Disable => "Disable",
            AuditAction::Activate => "Activate",
            AuditAction::Deactivate => "Deactivate",
            AuditAction::Delete => "Delete",
            AuditAction::Remove => "Remove",
            AuditAction::Revoke => "Revoke",
            AuditAction::Login => "Login",
            AuditAction::Logout => "Logout",
            AuditAction::LoginAttempt => "LoginAttempt",
            AuditAction::PasswordReset => "PasswordReset",
            AuditAction::TwoFactorEnable => "TwoFactorEnable",
            AuditAction::TwoFactorDisable => "TwoFactorDisable",
            AuditAction::AssignRole => "AssignRole",
            AuditAction::RevokeRole => "RevokeRole",
            AuditAction::GrantPermission => "GrantPermission",
            AuditAction::DenyPermission => "DenyPermission",
            AuditAction::SecurityViolation => "SecurityViolation",
            AuditAction::RateLimitExceeded => "RateLimitExceeded",
            AuditAction::SuspiciousActivity => "SuspiciousActivity",
            AuditAction::AccountLockout => "AccountLockout",
            AuditAction::UnauthorizedAccess => "UnauthorizedAccess",
            AuditAction::SystemConfiguration => "SystemConfiguration",
            AuditAction::MaintenanceMode => "MaintenanceMode",
            AuditAction::DataExport => "DataExport",
            AuditAction::DataImport => "DataImport",
            AuditAction::BackupCreated => "BackupCreated",
            AuditAction::BackupRestored => "BackupRestored",
        }
    }

    pub fn parse(s: &str) -> Option<Self> {
        match s {
            "Create" => Some(AuditAction::Create),
            "Register" => Some(AuditAction::Register),
            "Add" => Some(AuditAction::Add),
            "Read" => Some(AuditAction::Read),
            "View" => Some(AuditAction::View),
            "Search" => Some(AuditAction::Search),
            "Export" => Some(AuditAction::Export),
            "Update" => Some(AuditAction::Update),
            "Modify" => Some(AuditAction::Modify),
            "Change" => Some(AuditAction::Change),
            "Enable" => Some(AuditAction::Enable),
            "Disable" => Some(AuditAction::Disable),
            "Activate" => Some(AuditAction::Activate),
            "Deactivate" => Some(AuditAction::Deactivate),
            "Delete" => Some(AuditAction::Delete),
            "Remove" => Some(AuditAction::Remove),
            "Revoke" => Some(AuditAction::Revoke),
            "Login" => Some(AuditAction::Login),
            "Logout" => Some(AuditAction::Logout),
            "LoginAttempt" => Some(AuditAction::LoginAttempt),
            "PasswordReset" => Some(AuditAction::PasswordReset),
            "TwoFactorEnable" => Some(AuditAction::TwoFactorEnable),
            "TwoFactorDisable" => Some(AuditAction::TwoFactorDisable),
            "AssignRole" => Some(AuditAction::AssignRole),
            "RevokeRole" => Some(AuditAction::RevokeRole),
            "GrantPermission" => Some(AuditAction::GrantPermission),
            "DenyPermission" => Some(AuditAction::DenyPermission),
            "SecurityViolation" => Some(AuditAction::SecurityViolation),
            "RateLimitExceeded" => Some(AuditAction::RateLimitExceeded),
            "SuspiciousActivity" => Some(AuditAction::SuspiciousActivity),
            "AccountLockout" => Some(AuditAction::AccountLockout),
            "UnauthorizedAccess" => Some(AuditAction::UnauthorizedAccess),
            "SystemConfiguration" => Some(AuditAction::SystemConfiguration),
            "MaintenanceMode" => Some(AuditAction::MaintenanceMode),
            "DataExport" => Some(AuditAction::DataExport),
            "DataImport" => Some(AuditAction::DataImport),
            "BackupCreated" => Some(AuditAction::BackupCreated),
            "BackupRestored" => Some(AuditAction::BackupRestored),
            _ => None,
        }
    }

    /// Returns true if this action represents a security-sensitive operation
    pub fn is_security_sensitive(&self) -> bool {
        matches!(
            self,
            AuditAction::Login
                | AuditAction::LoginAttempt
                | AuditAction::PasswordReset
                | AuditAction::TwoFactorEnable
                | AuditAction::TwoFactorDisable
                | AuditAction::AssignRole
                | AuditAction::RevokeRole
                | AuditAction::GrantPermission
                | AuditAction::DenyPermission
                | AuditAction::SecurityViolation
                | AuditAction::RateLimitExceeded
                | AuditAction::SuspiciousActivity
                | AuditAction::AccountLockout
                | AuditAction::UnauthorizedAccess
                | AuditAction::SystemConfiguration
        )
    }

    /// Returns true if this action represents a read operation
    pub fn is_read_operation(&self) -> bool {
        matches!(
            self,
            AuditAction::Read | AuditAction::View | AuditAction::Search | AuditAction::Export
        )
    }

    /// Returns true if this action represents a write operation  
    pub fn is_write_operation(&self) -> bool {
        !self.is_read_operation()
    }
}

/// Immutable audit log entry representing a single system action
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AuditLogEntry {
    /// Unique identifier for this audit entry
    pub id: String,

    /// When the action occurred
    pub timestamp: DateTime,

    /// User who performed the action (None for system actions)
    pub user_id: Option<UserId>,

    /// Type of action performed
    pub action: AuditAction,

    /// Type of entity affected
    pub entity_type: EntityType,

    /// ID of the specific entity affected
    pub entity_id: String,

    /// JSON representation of changes made (before/after for updates)
    pub changes: Option<serde_json::Value>,

    /// IP address where the action originated
    pub ip_address: String,

    /// User agent string from the request
    pub user_agent: String,

    /// Whether the action completed successfully
    pub success: bool,

    /// Error message if action failed
    pub error_message: Option<String>,

    /// Additional metadata specific to the action
    pub metadata: HashMap<String, String>,
}

impl AuditLogEntry {
    /// Create a new audit log entry for a successful action
    pub fn new(
        id: String,
        user_id: Option<UserId>,
        action: AuditAction,
        entity_type: EntityType,
        entity_id: String,
        ip_address: String,
        user_agent: String,
    ) -> Self {
        Self {
            id,
            timestamp: DateTime::now(),
            user_id,
            action,
            entity_type,
            entity_id,
            changes: None,
            ip_address,
            user_agent,
            success: true,
            error_message: None,
            metadata: HashMap::new(),
        }
    }

    /// Create a new audit log entry for a failed action
    #[allow(clippy::too_many_arguments)]
    pub fn new_failed(
        id: String,
        user_id: Option<UserId>,
        action: AuditAction,
        entity_type: EntityType,
        entity_id: String,
        ip_address: String,
        user_agent: String,
        error_message: String,
    ) -> Self {
        Self {
            id,
            timestamp: DateTime::now(),
            user_id,
            action,
            entity_type,
            entity_id,
            changes: None,
            ip_address,
            user_agent,
            success: false,
            error_message: Some(error_message),
            metadata: HashMap::new(),
        }
    }

    /// Add change information to the audit entry
    pub fn with_changes(mut self, changes: serde_json::Value) -> Self {
        self.changes = Some(changes);
        self
    }

    /// Add metadata to the audit entry
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// Check if this entry represents a security-sensitive action
    pub fn is_security_sensitive(&self) -> bool {
        self.action.is_security_sensitive()
    }

    /// Check if this entry represents a failed action
    pub fn is_failure(&self) -> bool {
        !self.success
    }

    /// Get a summary description of this audit entry
    pub fn summary(&self) -> String {
        let user_part = if let Some(ref user_id) = self.user_id {
            format!("User {}", user_id.as_str())
        } else {
            "System".to_string()
        };

        let success_part = if self.success {
            "successfully"
        } else {
            "failed to"
        };

        format!(
            "{} {} {} {} {}",
            user_part,
            success_part,
            self.action.as_str().to_lowercase(),
            self.entity_type.as_str().to_lowercase(),
            self.entity_id
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_entity_type_string_conversion() {
        assert_eq!(EntityType::User.as_str(), "User");
        assert_eq!(EntityType::Organization.as_str(), "Organization");

        assert_eq!(EntityType::parse("User"), Some(EntityType::User));
        assert_eq!(EntityType::parse("Invalid"), None);
    }

    #[test]
    fn test_audit_action_string_conversion() {
        assert_eq!(AuditAction::Login.as_str(), "Login");
        assert_eq!(AuditAction::SecurityViolation.as_str(), "SecurityViolation");

        assert_eq!(AuditAction::parse("Login"), Some(AuditAction::Login));
        assert_eq!(AuditAction::parse("Invalid"), None);
    }

    #[test]
    fn test_audit_action_classifications() {
        assert!(AuditAction::Login.is_security_sensitive());
        assert!(AuditAction::AssignRole.is_security_sensitive());
        assert!(!AuditAction::Read.is_security_sensitive());

        assert!(AuditAction::Read.is_read_operation());
        assert!(AuditAction::View.is_read_operation());
        assert!(!AuditAction::Create.is_read_operation());

        assert!(AuditAction::Create.is_write_operation());
        assert!(!AuditAction::Read.is_write_operation());
    }

    #[test]
    fn test_audit_log_entry_creation() {
        let user_id = UserId::from_string("user-123".to_string()).unwrap();

        let entry = AuditLogEntry::new(
            "audit-1".to_string(),
            Some(user_id.clone()),
            AuditAction::Login,
            EntityType::User,
            "user-123".to_string(),
            "***********".to_string(),
            "Mozilla/5.0".to_string(),
        );

        assert_eq!(entry.id, "audit-1");
        assert_eq!(entry.user_id, Some(user_id));
        assert_eq!(entry.action, AuditAction::Login);
        assert_eq!(entry.entity_type, EntityType::User);
        assert_eq!(entry.entity_id, "user-123");
        assert_eq!(entry.ip_address, "***********");
        assert!(entry.success);
        assert!(entry.error_message.is_none());
        assert!(entry.is_security_sensitive());
        assert!(!entry.is_failure());
    }

    #[test]
    fn test_audit_log_entry_failed() {
        let entry = AuditLogEntry::new_failed(
            "audit-2".to_string(),
            None,
            AuditAction::UnauthorizedAccess,
            EntityType::User,
            "user-456".to_string(),
            "********".to_string(),
            "Bot/1.0".to_string(),
            "Access denied".to_string(),
        );

        assert!(!entry.success);
        assert_eq!(entry.error_message, Some("Access denied".to_string()));
        assert!(entry.is_failure());
        assert!(entry.is_security_sensitive());
    }

    #[test]
    fn test_audit_log_entry_with_changes() {
        let changes = serde_json::json!({
            "before": {"active": true},
            "after": {"active": false}
        });

        let entry = AuditLogEntry::new(
            "audit-3".to_string(),
            None,
            AuditAction::Deactivate,
            EntityType::User,
            "user-789".to_string(),
            "127.0.0.1".to_string(),
            "Admin/1.0".to_string(),
        )
        .with_changes(changes.clone());

        assert_eq!(entry.changes, Some(changes));
    }

    #[test]
    fn test_audit_log_entry_with_metadata() {
        let entry = AuditLogEntry::new(
            "audit-4".to_string(),
            None,
            AuditAction::SystemConfiguration,
            EntityType::Settings,
            "config-1".to_string(),
            "127.0.0.1".to_string(),
            "Admin/1.0".to_string(),
        )
        .with_metadata("reason".to_string(), "Security update".to_string())
        .with_metadata("version".to_string(), "1.2.3".to_string());

        assert_eq!(
            entry.metadata.get("reason"),
            Some(&"Security update".to_string())
        );
        assert_eq!(entry.metadata.get("version"), Some(&"1.2.3".to_string()));
    }

    #[test]
    fn test_audit_log_entry_summary() {
        let user_id = UserId::from_string("user-123".to_string()).unwrap();

        let entry = AuditLogEntry::new(
            "audit-5".to_string(),
            Some(user_id),
            AuditAction::Login,
            EntityType::User,
            "user-123".to_string(),
            "***********".to_string(),
            "Mozilla/5.0".to_string(),
        );

        let summary = entry.summary();
        assert!(summary.contains("User user-123"));
        assert!(summary.contains("successfully"));
        assert!(summary.contains("login"));
    }

    #[test]
    fn test_system_audit_entry_summary() {
        let entry = AuditLogEntry::new_failed(
            "audit-6".to_string(),
            None,
            AuditAction::BackupCreated,
            EntityType::Settings,
            "backup-1".to_string(),
            "127.0.0.1".to_string(),
            "System/1.0".to_string(),
            "Disk full".to_string(),
        );

        let summary = entry.summary();
        assert!(summary.contains("System"));
        assert!(summary.contains("failed to"));
        assert!(summary.contains("backupcreated"));
    }
}
