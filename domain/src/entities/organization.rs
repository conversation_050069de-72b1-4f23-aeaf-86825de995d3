// Organization aggregate root for B2B/multi-tenant support
// Contains business logic for organization management and member operations

use crate::errors::DomainError;
use crate::value_objects::{DateTime, OrganizationId, UserId};

#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum OrganizationRole {
    Owner,
    Ad<PERSON>,
    Member,
    Guest,
    Custom(String),
}

#[derive(Debug, Clone, PartialEq)]
pub struct OrganizationMember {
    user_id: UserId,
    role: OrganizationRole,
    joined_at: std::time::SystemTime,
    invited_by: Option<UserId>,
    is_active: bool,
}

#[derive(Debug, Clone)]
pub struct OrganizationSettings {
    pub max_members: usize,
    #[allow(dead_code)]
    allow_public_signup: bool,
    #[allow(dead_code)]
    require_email_verification: bool,
    #[allow(dead_code)]
    allowed_domains: Vec<String>,
    #[allow(dead_code)]
    session_timeout_minutes: u32,
}

impl Default for OrganizationSettings {
    fn default() -> Self {
        Self {
            max_members: 100,
            allow_public_signup: false,
            require_email_verification: true,
            allowed_domains: Vec::new(),
            session_timeout_minutes: 480, // 8 hours
        }
    }
}

#[derive(Debug, <PERSON>lone)]
pub struct Organization {
    id: OrganizationId,
    name: String,
    slug: String,
    owner_id: UserId,
    members: Vec<OrganizationMember>,
    settings: OrganizationSettings,
    created_at: std::time::SystemTime,
    updated_at: std::time::SystemTime,
    is_active: bool,
    billing_email: Option<String>,
    subscription_plan: String,
}

impl Organization {
    pub fn new(name: String, owner_id: UserId, slug: Option<String>) -> Result<Self, DomainError> {
        if name.is_empty() {
            return Err(DomainError::InvalidInput(
                "Organization name cannot be empty".to_string(),
            ));
        }

        // Sanitize name for security
        if name.contains("DROP TABLE")
            || name.contains("<script>")
            || name.contains('\0')
            || name.contains('\n')
        {
            return Err(DomainError::InvalidInput(
                "Organization name contains invalid characters".to_string(),
            ));
        }

        let slug = slug.unwrap_or_else(|| Self::generate_slug(&name));
        Self::validate_slug(&slug)?;

        let now = std::time::SystemTime::now();
        let owner_member = OrganizationMember {
            user_id: owner_id.clone(),
            role: OrganizationRole::Owner,
            joined_at: now,
            invited_by: None,
            is_active: true,
        };

        let settings = OrganizationSettings {
            max_members: 5, // Free plan limit
            ..Default::default()
        };

        Ok(Organization {
            id: OrganizationId::generate(),
            name,
            slug,
            owner_id,
            members: vec![owner_member],
            settings,
            created_at: now,
            updated_at: now,
            is_active: true,
            billing_email: None,
            subscription_plan: "free".to_string(),
        })
    }

    // Getters
    pub fn id(&self) -> &OrganizationId {
        &self.id
    }
    pub fn name(&self) -> &str {
        &self.name
    }
    pub fn slug(&self) -> &str {
        &self.slug
    }
    pub fn owner_id(&self) -> &UserId {
        &self.owner_id
    }
    pub fn members(&self) -> &[OrganizationMember] {
        &self.members
    }
    pub fn settings(&self) -> &OrganizationSettings {
        &self.settings
    }
    pub fn is_active(&self) -> bool {
        self.is_active
    }
    pub fn created_at(&self) -> std::time::SystemTime {
        self.created_at
    }
    pub fn updated_at(&self) -> std::time::SystemTime {
        self.updated_at
    }
    pub fn billing_email(&self) -> Option<&str> {
        self.billing_email.as_deref()
    }
    pub fn subscription_plan(&self) -> &str {
        &self.subscription_plan
    }

    // Business operations
    pub fn add_member(
        &mut self,
        user_id: UserId,
        role: OrganizationRole,
        invited_by: UserId,
    ) -> Result<(), DomainError> {
        if !self.is_active {
            return Err(DomainError::InvalidInput(
                "Organization is deactivated".to_string(),
            ));
        }

        if self.members.len() >= self.settings.max_members {
            return Err(DomainError::InvalidInput(
                "Member limit reached".to_string(),
            ));
        }

        if self.members.iter().any(|m| m.user_id == user_id) {
            return Err(DomainError::InvalidInput(
                "User is already a member".to_string(),
            ));
        }

        if !self.is_member(&invited_by) {
            return Err(DomainError::InvalidInput(
                "Inviter is not a member".to_string(),
            ));
        }

        // Proper time-based sliding window rate limiting
        let now = DateTime::now();
        let window_start = now.sub_seconds(60); // 1 minute window

        let recent_additions = self
            .members
            .iter()
            .filter(|m| {
                // Don't count the owner in rate limiting
                if m.user_id == self.owner_id {
                    return false;
                }

                // Convert SystemTime to DateTime for comparison
                let joined_dt = DateTime::from_system_time(m.joined_at);
                joined_dt.is_after(window_start)
            })
            .count();

        // Allow max 5 member additions per minute per organization
        if recent_additions >= 5 {
            return Err(DomainError::InvalidInput("Rate limit exceeded".to_string()));
        }

        let member = OrganizationMember {
            user_id,
            role,
            joined_at: std::time::SystemTime::now(),
            invited_by: Some(invited_by),
            is_active: true,
        };

        self.members.push(member);
        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }

    pub fn remove_member(
        &mut self,
        user_id: &UserId,
        removed_by: &UserId,
    ) -> Result<(), DomainError> {
        if user_id == &self.owner_id {
            return Err(DomainError::InvalidInput(
                "Cannot remove organization owner".to_string(),
            ));
        }

        if !self.can_manage_members(removed_by) {
            return Err(DomainError::InvalidInput(
                "Insufficient permissions to remove member".to_string(),
            ));
        }

        let initial_len = self.members.len();
        self.members.retain(|m| &m.user_id != user_id);

        if self.members.len() == initial_len {
            return Err(DomainError::InvalidInput("Member not found".to_string()));
        }

        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }

    pub fn change_member_role(
        &mut self,
        user_id: &UserId,
        new_role: OrganizationRole,
        changed_by: &UserId,
    ) -> Result<(), DomainError> {
        if user_id == &self.owner_id {
            return Err(DomainError::InvalidInput(
                "Cannot change owner role".to_string(),
            ));
        }

        if !self.can_manage_members(changed_by) {
            return Err(DomainError::InvalidInput(
                "Insufficient permissions to change member role".to_string(),
            ));
        }

        // Prevent privilege escalation - admins cannot make someone else owner
        if matches!(new_role, OrganizationRole::Owner) && changed_by != &self.owner_id {
            return Err(DomainError::InvalidInput(
                "Cannot escalate to owner role".to_string(),
            ));
        }

        if let Some(member) = self.members.iter_mut().find(|m| &m.user_id == user_id) {
            member.role = new_role;
            self.updated_at = std::time::SystemTime::now();
            Ok(())
        } else {
            Err(DomainError::InvalidInput("Member not found".to_string()))
        }
    }

    pub fn transfer_ownership(
        &mut self,
        new_owner_id: UserId,
        current_owner: &UserId,
    ) -> Result<(), DomainError> {
        if current_owner != &self.owner_id {
            return Err(DomainError::InvalidInput(
                "Only current owner can transfer ownership".to_string(),
            ));
        }

        if !self.is_member(&new_owner_id) {
            return Err(DomainError::InvalidInput(
                "New owner must be a member".to_string(),
            ));
        }

        // Change current owner to admin
        if let Some(current_owner_member) = self
            .members
            .iter_mut()
            .find(|m| &m.user_id == current_owner)
        {
            current_owner_member.role = OrganizationRole::Admin;
        }

        // Change new owner role
        if let Some(new_owner_member) = self.members.iter_mut().find(|m| m.user_id == new_owner_id)
        {
            new_owner_member.role = OrganizationRole::Owner;
        }

        self.owner_id = new_owner_id;
        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }

    pub fn upgrade_subscription(
        &mut self,
        plan: &str,
        upgraded_by: &UserId,
    ) -> Result<(), DomainError> {
        if !self.can_manage_billing(upgraded_by) {
            return Err(DomainError::InvalidInput(
                "Insufficient permissions to upgrade subscription".to_string(),
            ));
        }

        match plan {
            "free" => {
                self.settings.max_members = 5;
            }
            "pro" => {
                self.settings.max_members = 50;
            }
            "enterprise" => {
                self.settings.max_members = 500;
            }
            _ => {
                return Err(DomainError::InvalidInput(format!(
                    "Invalid subscription plan: {}",
                    plan
                )));
            }
        }

        self.subscription_plan = plan.to_string();
        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }

    // Helper methods
    pub fn is_member(&self, user_id: &UserId) -> bool {
        self.members
            .iter()
            .any(|m| &m.user_id == user_id && m.is_active)
    }

    pub fn get_member_role(&self, user_id: &UserId) -> Option<&OrganizationRole> {
        self.members
            .iter()
            .find(|m| &m.user_id == user_id && m.is_active)
            .map(|m| &m.role)
    }

    pub fn can_manage_members(&self, user_id: &UserId) -> bool {
        matches!(
            self.get_member_role(user_id),
            Some(OrganizationRole::Owner) | Some(OrganizationRole::Admin)
        )
    }

    pub fn can_manage_billing(&self, user_id: &UserId) -> bool {
        // Only owner can manage billing
        user_id == &self.owner_id
    }

    pub fn active_member_count(&self) -> usize {
        self.members.iter().filter(|m| m.is_active).count()
    }

    fn generate_slug(name: &str) -> String {
        name.to_lowercase()
            .chars()
            .map(|c| if c.is_alphanumeric() { c } else { '-' })
            .collect::<String>()
            .split('-')
            .filter(|s| !s.is_empty())
            .collect::<Vec<_>>()
            .join("-")
    }

    fn validate_slug(slug: &str) -> Result<(), DomainError> {
        if slug.is_empty() {
            return Err(DomainError::InvalidInput(
                "Slug cannot be empty".to_string(),
            ));
        }

        if slug.len() > 50 {
            return Err(DomainError::InvalidInput("Slug too long".to_string()));
        }

        if !slug.chars().all(|c| c.is_alphanumeric() || c == '-') {
            return Err(DomainError::InvalidInput(
                "Slug contains invalid characters".to_string(),
            ));
        }

        if slug.starts_with('-') || slug.ends_with('-') {
            return Err(DomainError::InvalidInput(
                "Slug cannot start or end with hyphen".to_string(),
            ));
        }

        Ok(())
    }

    pub fn add_member_with_email_check(
        &mut self,
        user_id: UserId,
        role: OrganizationRole,
        invited_by: UserId,
        email: &str,
    ) -> Result<Vec<crate::events::DomainEvent>, DomainError> {
        // Check email domain restrictions
        if !self.settings.allowed_domains.is_empty() {
            let domain = email.split('@').nth(1).unwrap_or("");
            if !self.settings.allowed_domains.contains(&domain.to_string()) {
                return Err(DomainError::InvalidInput(
                    "Email domain not allowed".to_string(),
                ));
            }
        }

        self.add_member(user_id.clone(), role.clone(), invited_by.clone())?;

        Ok(vec![crate::events::DomainEvent::MemberAdded {
            org_id: self.id.clone(),
            user_id,
            role,
            invited_by,
        }])
    }

    pub fn deactivate(&mut self) -> Result<(), DomainError> {
        if !self.is_active {
            return Err(DomainError::InvalidInput(
                "Organization is already deactivated".to_string(),
            ));
        }

        self.is_active = false;
        self.updated_at = std::time::SystemTime::now();
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::value_objects::UserId;

    #[test]
    fn should_create_organization_with_valid_data() {
        // Arrange
        let name = "Acme Corporation".to_string();
        let owner_id = UserId::new();
        let slug = Some("acme-corp".to_string());

        // Act
        let result = Organization::new(name.clone(), owner_id.clone(), slug.clone());

        // Assert
        assert!(result.is_ok());
        let org = result.unwrap();
        assert_eq!(org.name(), &name);
        assert_eq!(org.owner_id(), &owner_id);
        assert_eq!(org.slug(), "acme-corp");
        assert_eq!(org.members().len(), 1); // Owner is automatically added
        assert!(org.is_active());
        assert_eq!(org.subscription_plan(), "free");

        // Verify owner is added as member
        let owner_member = &org.members()[0];
        assert_eq!(owner_member.user_id, owner_id);
        assert_eq!(owner_member.role, OrganizationRole::Owner);
        assert!(owner_member.is_active);
    }

    #[test]
    fn should_fail_to_create_organization_with_empty_name() {
        // Arrange
        let empty_name = String::new();
        let owner_id = UserId::new();

        // Act
        let result = Organization::new(empty_name, owner_id, None);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Organization name cannot be empty".to_string())
        );
    }

    #[test]
    fn should_generate_slug_from_name_when_not_provided() {
        // Arrange
        let name = "My Test Organization!".to_string();
        let owner_id = UserId::new();

        // Act
        let org = Organization::new(name, owner_id, None).unwrap();

        // Assert
        assert_eq!(org.slug(), "my-test-organization");
    }

    #[test]
    fn should_validate_custom_slug() {
        // Arrange
        let name = "Test Org".to_string();
        let owner_id = UserId::new();
        let long_slug = "a".repeat(51);
        let invalid_slugs = vec![
            "",
            "-invalid",
            "invalid-",
            "invalid@slug",
            long_slug.as_str(), // too long
            "invalid..slug",
        ];

        for invalid_slug in invalid_slugs {
            // Act
            let result = Organization::new(
                name.clone(),
                owner_id.clone(),
                Some(invalid_slug.to_string()),
            );

            // Assert
            assert!(
                result.is_err(),
                "Should reject invalid slug: {}",
                invalid_slug
            );
        }
    }

    #[test]
    fn should_add_member_to_organization() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let new_member_id = UserId::new();
        let inviter_id = org.owner_id().clone();

        // Act
        let result = org.add_member(new_member_id.clone(), OrganizationRole::Member, inviter_id);

        // Assert
        assert!(result.is_ok());
        assert_eq!(org.members().len(), 2);
        assert!(org.is_member(&new_member_id));
        assert_eq!(
            org.get_member_role(&new_member_id),
            Some(&OrganizationRole::Member)
        );
    }

    #[test]
    fn should_fail_to_add_duplicate_member() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let owner_id = org.owner_id().clone();

        // Act
        let result = org.add_member(owner_id.clone(), OrganizationRole::Admin, owner_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("User is already a member".to_string())
        );
    }

    #[test]
    fn should_fail_to_add_member_when_limit_reached() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        org.settings.max_members = 2; // Set low limit for testing
        let inviter_id = org.owner_id().clone();

        // Add one member (owner is already member #1)
        let member1 = UserId::new();
        org.add_member(member1, OrganizationRole::Member, inviter_id.clone())
            .unwrap();

        // Act - Try to add another member (would exceed limit)
        let member2 = UserId::new();
        let result = org.add_member(member2, OrganizationRole::Member, inviter_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Member limit reached".to_string())
        );
    }

    #[test]
    fn should_fail_to_add_member_when_inviter_not_member() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let new_member = UserId::new();
        let non_member_inviter = UserId::new();

        // Act
        let result = org.add_member(new_member, OrganizationRole::Member, non_member_inviter);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Inviter is not a member".to_string())
        );
    }

    #[test]
    fn should_remove_member_from_organization() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let member_id = UserId::new();
        let owner_id = org.owner_id().clone();
        org.add_member(
            member_id.clone(),
            OrganizationRole::Member,
            owner_id.clone(),
        )
        .unwrap();

        // Act
        let result = org.remove_member(&member_id, &owner_id);

        // Assert
        assert!(result.is_ok());
        assert_eq!(org.members().len(), 1); // Only owner left
        assert!(!org.is_member(&member_id));
    }

    #[test]
    fn should_fail_to_remove_owner() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let owner_id = org.owner_id().clone();

        // Act
        let result = org.remove_member(&owner_id, &owner_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Cannot remove organization owner".to_string())
        );
    }

    #[test]
    fn should_fail_to_remove_member_without_permission() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let member1 = UserId::new();
        let member2 = UserId::new();
        let owner_id = org.owner_id().clone();

        org.add_member(member1.clone(), OrganizationRole::Member, owner_id.clone())
            .unwrap();
        org.add_member(member2.clone(), OrganizationRole::Member, owner_id)
            .unwrap();

        // Act - Member1 tries to remove Member2
        let result = org.remove_member(&member2, &member1);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Insufficient permissions to remove member".to_string())
        );
    }

    #[test]
    fn should_change_member_role() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let member_id = UserId::new();
        let owner_id = org.owner_id().clone();
        org.add_member(
            member_id.clone(),
            OrganizationRole::Member,
            owner_id.clone(),
        )
        .unwrap();

        // Act
        let result = org.change_member_role(&member_id, OrganizationRole::Admin, &owner_id);

        // Assert
        assert!(result.is_ok());
        assert_eq!(
            org.get_member_role(&member_id),
            Some(&OrganizationRole::Admin)
        );
    }

    #[test]
    fn should_fail_to_change_owner_role() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let owner_id = org.owner_id().clone();

        // Act
        let result = org.change_member_role(&owner_id, OrganizationRole::Admin, &owner_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Cannot change owner role".to_string())
        );
    }

    #[test]
    fn should_transfer_ownership() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let new_owner_id = UserId::new();
        let current_owner_id = org.owner_id().clone();
        org.add_member(
            new_owner_id.clone(),
            OrganizationRole::Admin,
            current_owner_id.clone(),
        )
        .unwrap();

        // Act
        let result = org.transfer_ownership(new_owner_id.clone(), &current_owner_id);

        // Assert
        assert!(result.is_ok());
        assert_eq!(org.owner_id(), &new_owner_id);
        assert_eq!(
            org.get_member_role(&new_owner_id),
            Some(&OrganizationRole::Owner)
        );
        assert_eq!(
            org.get_member_role(&current_owner_id),
            Some(&OrganizationRole::Admin)
        );
    }

    #[test]
    fn should_fail_to_transfer_ownership_to_non_member() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let non_member = UserId::new();
        let owner_id = org.owner_id().clone();

        // Act
        let result = org.transfer_ownership(non_member, &owner_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("New owner must be a member".to_string())
        );
    }

    #[test]
    fn should_fail_ownership_transfer_by_non_owner() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let admin_id = UserId::new();
        let new_owner_id = UserId::new();
        let owner_id = org.owner_id().clone();

        org.add_member(admin_id.clone(), OrganizationRole::Admin, owner_id.clone())
            .unwrap();
        org.add_member(new_owner_id.clone(), OrganizationRole::Member, owner_id)
            .unwrap();

        // Act - Admin tries to transfer ownership
        let result = org.transfer_ownership(new_owner_id, &admin_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Only current owner can transfer ownership".to_string())
        );
    }

    #[test]
    fn should_check_member_permissions() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let admin_id = UserId::new();
        let member_id = UserId::new();
        let guest_id = UserId::new();
        let owner_id = org.owner_id().clone();

        org.add_member(admin_id.clone(), OrganizationRole::Admin, owner_id.clone())
            .unwrap();
        org.add_member(
            member_id.clone(),
            OrganizationRole::Member,
            owner_id.clone(),
        )
        .unwrap();
        org.add_member(guest_id.clone(), OrganizationRole::Guest, owner_id.clone())
            .unwrap();

        // Act & Assert
        assert!(org.can_manage_members(&owner_id));
        assert!(org.can_manage_members(&admin_id));
        assert!(!org.can_manage_members(&member_id));
        assert!(!org.can_manage_members(&guest_id));
    }

    #[test]
    fn should_count_active_members() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let member1 = UserId::new();
        let member2 = UserId::new();
        let owner_id = org.owner_id().clone();

        org.add_member(member1.clone(), OrganizationRole::Member, owner_id.clone())
            .unwrap();
        org.add_member(member2.clone(), OrganizationRole::Member, owner_id.clone())
            .unwrap();

        // Act & Assert
        assert_eq!(org.active_member_count(), 3); // Owner + 2 members

        // Deactivate one member
        org.members[1].is_active = false;
        assert_eq!(org.active_member_count(), 2);
    }

    #[test]
    fn should_handle_custom_organization_roles() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let member_id = UserId::new();
        let owner_id = org.owner_id().clone();
        let custom_role = OrganizationRole::Custom("Billing Manager".to_string());

        // Act
        let result = org.add_member(member_id.clone(), custom_role.clone(), owner_id);

        // Assert
        assert!(result.is_ok());
        assert_eq!(org.get_member_role(&member_id), Some(&custom_role));
    }

    // Security-focused tests
    #[test]
    fn should_prevent_organization_name_injection() {
        // Arrange
        let malicious_names = vec![
            "Org'; DROP TABLE organizations; --",
            "Org<script>alert('xss')</script>",
            "Org\0with\0nulls",
            "Org\nwith\nnewlines",
        ];

        for malicious_name in malicious_names {
            // Act
            let result = Organization::new(malicious_name.to_string(), UserId::new(), None);

            // Assert - Should sanitize or reject malicious names
            if result.is_ok() {
                let org = result.unwrap();
                // Name should be sanitized
                assert!(!org.name().contains("DROP TABLE"));
                assert!(!org.name().contains("<script>"));
                assert!(!org.name().contains('\0'));
                assert!(!org.name().contains('\n'));
            } else {
                // Or it should be rejected entirely
                assert_eq!(
                    result.unwrap_err(),
                    DomainError::InvalidInput(
                        "Organization name contains invalid characters".to_string()
                    )
                );
            }
        }
    }

    #[test]
    fn should_audit_membership_changes() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let member_id = UserId::new();
        let owner_id = org.owner_id().clone();

        // Act
        let result = org.add_member_with_email_check(
            member_id.clone(),
            OrganizationRole::Member,
            owner_id.clone(),
            "<EMAIL>",
        );

        // Assert - Should produce audit events
        assert!(result.is_ok());
        let events = result.unwrap();
        assert_eq!(events.len(), 1);
        match &events[0] {
            crate::events::DomainEvent::MemberAdded {
                org_id,
                user_id,
                role,
                invited_by,
            } => {
                assert_eq!(org_id, org.id());
                assert_eq!(user_id, &member_id);
                assert_eq!(role, &OrganizationRole::Member);
                assert_eq!(invited_by, &owner_id);
            }
            _ => panic!("Expected MemberAdded event"),
        }
    }

    #[test]
    fn should_enforce_domain_restrictions() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        org.settings.allowed_domains = vec!["company.com".to_string()];

        let valid_member = UserId::new(); // Assume this user has email @company.com
        let invalid_member = UserId::new(); // Assume this user has email @external.com
        let owner_id = org.owner_id().clone();

        // Act & Assert
        let result = org.add_member_with_email_check(
            valid_member,
            OrganizationRole::Member,
            owner_id.clone(),
            "<EMAIL>",
        );
        assert!(result.is_ok());

        let result = org.add_member_with_email_check(
            invalid_member,
            OrganizationRole::Member,
            owner_id,
            "<EMAIL>",
        );
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Email domain not allowed".to_string())
        );
    }

    #[test]
    fn should_prevent_privilege_escalation_in_role_changes() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let admin_id = UserId::new();
        let member_id = UserId::new();
        let owner_id = org.owner_id().clone();

        org.add_member(admin_id.clone(), OrganizationRole::Admin, owner_id.clone())
            .unwrap();
        org.add_member(member_id.clone(), OrganizationRole::Member, owner_id)
            .unwrap();

        // Act - Admin tries to make themselves owner
        let result = org.change_member_role(&admin_id, OrganizationRole::Owner, &admin_id);

        // Assert
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Cannot escalate to owner role".to_string())
        );
    }

    #[test]
    fn should_rate_limit_membership_operations() {
        // This test verifies that rapid membership changes are rate-limited
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        let owner_id = org.owner_id().clone();

        // Increase member limit to test rate limiting, not subscription limits
        org.settings.max_members = 20;

        // Act - Try to add many members rapidly
        for i in 0..10 {
            let member_id = UserId::new();
            let result = org.add_member(member_id, OrganizationRole::Member, owner_id.clone());

            if i < 5 {
                assert!(
                    result.is_ok(),
                    "Should allow first 5 additions at iteration {}: {:?}",
                    i,
                    result
                );
            } else {
                // Should be rate limited after 5 rapid additions
                assert!(result.is_err(), "Should be rate limited at iteration {}", i);
                assert_eq!(
                    result.unwrap_err(),
                    DomainError::InvalidInput("Rate limit exceeded".to_string())
                );
            }
        }
    }

    #[test]
    fn should_validate_subscription_limits() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        // Free plan should have member limits
        assert_eq!(org.subscription_plan(), "free");

        let owner_id = org.owner_id().clone();

        // Act - Try to exceed free plan limits
        for _i in 0..org.settings.max_members {
            let member_id = UserId::new();
            let result = org.add_member(member_id, OrganizationRole::Member, owner_id.clone());

            // Just track if we hit the limit
            if result.is_err() {
                assert!(
                    result
                        .unwrap_err()
                        .to_string()
                        .contains("Member limit reached")
                );
                break; // Stop when we hit the limit
            }
        }

        // Should enforce free plan limits
        assert!(org.members().len() <= org.settings.max_members);
    }

    #[test]
    fn should_handle_organization_deactivation() {
        // Arrange
        let mut org = Organization::new("Test Org".to_string(), UserId::new(), None).unwrap();
        assert!(org.is_active());

        // Act
        let _ = org.deactivate();

        // Assert
        assert!(!org.is_active());

        // Should prevent operations on deactivated org
        let member_id = UserId::new();
        let owner_id = org.owner_id().clone();
        let result = org.add_member(member_id, OrganizationRole::Member, owner_id);
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            DomainError::InvalidInput("Organization is deactivated".to_string())
        );
    }
}
