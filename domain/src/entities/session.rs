// Session entity
// Represents an authenticated user session

use crate::crypto::{ConstantTimeComparison, ConstantTimeService};
use crate::errors::DomainError;
use crate::events::DomainEvent;
use crate::value_objects::{DateTime, SessionId, UserId};
use std::time::Duration;

const MAX_SESSION_DURATION: u64 = 24 * 3600; // 24 hours max
const MAX_IDLE_TIME: u64 = 24 * 3600; // 24 hours idle timeout

#[derive(Debug, Clone, PartialEq)]
pub struct Session {
    id: SessionId,
    user_id: UserId,
    token_hash: Option<String>,
    created_at: DateTime,
    expires_at: DateTime,
    last_accessed: DateTime,
    absolute_expiry: DateTime, // For absolute timeout
    ip_address: Option<String>,
    user_agent: Option<String>,
    is_active: bool,
}

impl Session {
    pub fn new(
        user_id: UserId,
        duration_seconds: u64,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        let now = DateTime::now();
        let capped_duration = duration_seconds.min(MAX_SESSION_DURATION);
        let expires_at = now.add_seconds(capped_duration);
        let absolute_expiry = now.add_seconds(MAX_SESSION_DURATION);

        Session {
            id: SessionId::generate(),
            user_id,
            token_hash: None,
            created_at: now,
            expires_at,
            last_accessed: now,
            absolute_expiry,
            ip_address,
            user_agent,
            is_active: true,
        }
    }

    pub fn with_token(
        user_id: UserId,
        token: &str,
        duration_seconds: u64,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        let mut session = Self::new(user_id, duration_seconds, ip_address, user_agent);
        session.token_hash = Some(hash_token(token));
        session
    }

    pub fn with_timeouts(
        user_id: UserId,
        idle_timeout_seconds: u64,
        absolute_timeout_seconds: u64,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        let now = DateTime::now();
        let expires_at = now.add_seconds(idle_timeout_seconds);
        let absolute_expiry = now.add_seconds(absolute_timeout_seconds);

        Session {
            id: SessionId::generate(),
            user_id,
            token_hash: None,
            created_at: now,
            expires_at,
            last_accessed: now,
            absolute_expiry,
            ip_address,
            user_agent,
            is_active: true,
        }
    }

    // Getters
    pub fn id(&self) -> &SessionId {
        &self.id
    }

    pub fn user_id(&self) -> &UserId {
        &self.user_id
    }

    pub fn created_at(&self) -> DateTime {
        self.created_at
    }

    pub fn expires_at(&self) -> DateTime {
        self.expires_at
    }

    pub fn last_accessed(&self) -> DateTime {
        self.last_accessed
    }

    pub fn ip_address(&self) -> Option<&str> {
        self.ip_address.as_deref()
    }

    pub fn user_agent(&self) -> Option<&str> {
        self.user_agent.as_deref()
    }

    pub fn is_active(&self) -> bool {
        self.is_active
    }

    // Business operations
    pub fn is_expired(&self) -> bool {
        let now = DateTime::now();
        now.is_after(self.expires_at)
    }

    pub fn is_absolutely_expired(&self) -> bool {
        let now = DateTime::now();
        now.is_after(self.absolute_expiry)
    }

    pub fn is_inactive_too_long(&self) -> bool {
        self.last_accessed
            .is_older_than(Duration::from_secs(MAX_IDLE_TIME))
    }

    pub fn is_valid(&self) -> bool {
        self.is_active
            && !self.is_expired()
            && !self.is_absolutely_expired()
            && !self.is_inactive_too_long()
    }

    pub fn touch(&mut self) -> Result<(), DomainError> {
        if !self.is_active {
            return Err(DomainError::InvalidSession);
        }

        if self.is_expired() || self.is_absolutely_expired() {
            return Err(DomainError::SessionExpired);
        }

        let now = DateTime::now();
        self.last_accessed = now;

        // Implement sliding window - extend expiry on activity
        let new_expiry = now.add_seconds(MAX_IDLE_TIME);
        if new_expiry.is_before(self.absolute_expiry) {
            self.expires_at = new_expiry;
        }

        Ok(())
    }

    pub fn invalidate(&mut self) {
        self.is_active = false;
    }

    pub fn extend(&mut self, additional_seconds: u64) -> Result<(), DomainError> {
        if !self.is_active {
            return Err(DomainError::InvalidSession);
        }

        let now = DateTime::now();
        self.expires_at = self.expires_at.add_seconds(additional_seconds);

        // Don't exceed absolute expiry
        if self.expires_at.is_after(self.absolute_expiry) {
            self.expires_at = self.absolute_expiry;
        }

        self.last_accessed = now;
        Ok(())
    }

    pub fn verify_token(&self, token: &str) -> bool {
        if let Some(ref stored_hash) = self.token_hash {
            ConstantTimeService::new().constant_time_compare_str(stored_hash, &hash_token(token))
        } else {
            false
        }
    }

    pub fn refresh_token(&mut self, new_token: &str) -> Result<Vec<DomainEvent>, DomainError> {
        if !self.is_active {
            return Err(DomainError::InvalidSession);
        }

        self.token_hash = Some(hash_token(new_token));
        self.last_accessed = DateTime::now();

        Ok(vec![DomainEvent::SessionRefreshed {
            session_id: self.id.clone(),
            user_id: self.user_id.clone(),
        }])
    }

    pub fn regenerate_id(&mut self) -> Result<(), DomainError> {
        if !self.is_active {
            return Err(DomainError::InvalidSession);
        }

        self.id = SessionId::generate();
        Ok(())
    }

    pub fn is_suspicious_ip(&self, current_ip: &str) -> bool {
        if let Some(ref original_ip) = self.ip_address {
            original_ip != current_ip
        } else {
            false
        }
    }

    pub fn is_suspicious_user_agent(&self, current_ua: &str) -> bool {
        if let Some(ref original_ua) = self.user_agent {
            original_ua != current_ua
        } else {
            false
        }
    }

    pub fn detect_concurrent_use(
        &self,
        current_ip: &str,
        current_ua: &str,
        access_time: DateTime,
    ) -> bool {
        // If accessed from different location within a short time
        if self.is_suspicious_ip(current_ip) || self.is_suspicious_user_agent(current_ua) {
            // Check if last access was very recent (within 1 minute)
            if let Ok(duration) = access_time.duration_since(self.last_accessed) {
                return duration.as_secs() < 60;
            }
        }
        false
    }

    pub fn detect_potential_hijacking(&self, current_ip: &str, current_ua: &str) -> bool {
        self.is_suspicious_ip(current_ip) || self.is_suspicious_user_agent(current_ua)
    }
}

// Helper functions
fn hash_token(token: &str) -> String {
    // For now, we'll use a simple placeholder hash
    // In production, this should use a proper hash from infrastructure layer
    // The domain layer should not directly hash tokens for storage
    format!("hashed_{}", token)
}

// Note: constant_time_compare is now imported from crypto module

#[cfg(test)]
mod tests {
    use super::*;
    use crate::events::DomainEvent;
    use crate::value_objects::UserId;

    #[test]
    fn should_create_session_with_expiry() {
        // Arrange
        let user_id = UserId::new();
        let ip_address = Some("***********".to_string());
        let user_agent = Some("Mozilla/5.0".to_string());
        let duration_seconds = 3600; // 1 hour

        // Act
        let session = Session::new(
            user_id.clone(),
            duration_seconds,
            ip_address.clone(),
            user_agent.clone(),
        );

        // Assert
        assert!(!session.is_expired());
        assert!(session.is_active());
        assert!(session.is_valid());
        assert_eq!(session.user_id(), &user_id);
        assert_eq!(session.ip_address(), Some("***********"));
        assert_eq!(session.user_agent(), Some("Mozilla/5.0"));
        assert!(session.created_at() <= session.last_accessed());
        assert!(session.expires_at() > session.created_at());
    }

    #[test]
    fn should_create_session_without_optional_fields() {
        // Arrange
        let user_id = UserId::new();
        let duration_seconds = 3600;

        // Act
        let session = Session::new(user_id.clone(), duration_seconds, None, None);

        // Assert
        assert!(session.is_valid());
        assert_eq!(session.user_id(), &user_id);
        assert_eq!(session.ip_address(), None);
        assert_eq!(session.user_agent(), None);
    }

    #[test]
    fn should_expire_after_timeout() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);

        // Act - Simulate expiry by manipulating the expires_at time
        session.expires_at = DateTime::now().sub_seconds(1);

        // Assert
        assert!(session.is_expired());
        assert!(!session.is_valid());
    }

    #[test]
    fn should_expire_after_inactivity_timeout() {
        // Arrange
        let mut session = Session::new(UserId::new(), 86400, None, None); // 24 hours

        // Act - Simulate long inactivity (more than 24 hours since last access)
        session.last_accessed = DateTime::now().sub_hours(25);

        // Assert
        assert!(session.is_inactive_too_long());
        assert!(!session.is_valid());
    }

    #[test]
    fn should_touch_active_session() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        let initial_last_accessed = session.last_accessed();

        // Act
        std::thread::sleep(std::time::Duration::from_millis(10)); // Ensure time difference
        let result = session.touch();

        // Assert
        assert!(result.is_ok());
        assert!(session.last_accessed() > initial_last_accessed);
        assert!(session.is_valid());
    }

    #[test]
    fn should_fail_to_touch_inactive_session() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        session.invalidate();

        // Act
        let result = session.touch();

        // Assert
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), DomainError::InvalidSession);
    }

    #[test]
    fn should_fail_to_touch_expired_session() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        session.expires_at = DateTime::now().sub_seconds(1);

        // Act
        let result = session.touch();

        // Assert
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), DomainError::SessionExpired);
    }

    #[test]
    fn should_invalidate_session() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        assert!(session.is_active());

        // Act
        session.invalidate();

        // Assert
        assert!(!session.is_active());
        assert!(!session.is_valid());
    }

    #[test]
    fn should_extend_active_session() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        let initial_expires_at = session.expires_at();
        let initial_last_accessed = session.last_accessed();

        // Act
        std::thread::sleep(std::time::Duration::from_millis(10)); // Ensure time difference
        let result = session.extend(1800); // Extend by 30 minutes

        // Assert
        assert!(result.is_ok());
        assert!(session.expires_at() > initial_expires_at);
        assert!(session.last_accessed() > initial_last_accessed);
        assert!(session.is_valid());
    }

    #[test]
    fn should_fail_to_extend_inactive_session() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        session.invalidate();

        // Act
        let result = session.extend(1800);

        // Assert
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), DomainError::InvalidSession);
    }

    #[test]
    fn should_create_session_with_token_hash() {
        // Arrange
        let user_id = UserId::new();
        let token = "secure_session_token_12345";

        // Act
        let session = Session::with_token(user_id.clone(), token, 3600, None, None);

        // Assert
        assert!(session.is_valid());
        assert_eq!(session.user_id(), &user_id);
        assert!(session.verify_token(token));
        assert!(!session.verify_token("wrong_token"));
    }

    #[test]
    fn should_refresh_session_token() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        let new_token = "new_secure_token_67890";

        // Act
        let result = session.refresh_token(new_token);

        // Assert
        assert!(result.is_ok());
        let events = result.unwrap();
        assert_eq!(events.len(), 1);
        assert!(session.verify_token(new_token));
        match &events[0] {
            DomainEvent::SessionRefreshed {
                session_id,
                user_id,
            } => {
                assert_eq!(session_id, session.id());
                assert_eq!(user_id, session.user_id());
            }
            _ => panic!("Expected SessionRefreshed event"),
        }
    }

    #[test]
    fn should_detect_suspicious_activity() {
        // Arrange
        let session = Session::new(
            UserId::new(),
            3600,
            Some("***********".to_string()),
            Some("Mozilla/5.0".to_string()),
        );

        // Act & Assert - Different IP should be flagged
        assert!(session.is_suspicious_ip("********"));
        assert!(!session.is_suspicious_ip("***********"));

        // Act & Assert - Different User-Agent should be flagged
        assert!(session.is_suspicious_user_agent("Chrome/91.0"));
        assert!(!session.is_suspicious_user_agent("Mozilla/5.0"));
    }

    #[test]
    fn should_enforce_maximum_session_duration() {
        // Arrange
        let max_duration = 24 * 3600; // 24 hours
        let excessive_duration = 48 * 3600; // 48 hours

        // Act
        let session = Session::new(UserId::new(), excessive_duration, None, None);

        // Assert - Should cap at maximum duration
        let max_allowed_expiry = DateTime::now().add_seconds(max_duration + 1); // Add 1s for tolerance
        assert!(
            session.expires_at().is_before(max_allowed_expiry)
                || session.expires_at() == max_allowed_expiry
        );
    }

    #[test]
    fn should_track_concurrent_sessions() {
        // Arrange
        let user_id = UserId::new();

        // Act
        let session1 = Session::new(user_id.clone(), 3600, Some("***********".to_string()), None);
        let session2 = Session::new(user_id.clone(), 3600, Some("********".to_string()), None);

        // Assert - Sessions should have different IDs but same user
        assert_ne!(session1.id(), session2.id());
        assert_eq!(session1.user_id(), session2.user_id());
        assert_ne!(session1.ip_address(), session2.ip_address());
    }

    // Security-focused tests
    #[test]
    fn should_prevent_session_fixation() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        let original_id = session.id().clone();

        // Act - Simulate authentication event that should regenerate session ID
        let result = session.regenerate_id();

        // Assert
        assert!(result.is_ok());
        assert_ne!(session.id(), &original_id);
        assert!(session.is_valid());
    }

    #[test]
    fn should_detect_concurrent_use() {
        // Arrange
        let session = Session::new(
            UserId::new(),
            3600,
            Some("***********".to_string()),
            Some("Mozilla/5.0".to_string()),
        );

        // Act & Assert - Using same session from different location should be flagged
        assert!(session.detect_concurrent_use("********", "Chrome/91.0", DateTime::now()));
    }

    #[test]
    fn should_enforce_secure_session_creation() {
        // Arrange
        let user_id = UserId::new();

        // Act
        let session = Session::new(user_id, 3600, None, None);

        // Assert - Session ID should be cryptographically secure
        // Session ID should be a secure base64 token
        assert!(session.id().as_str().len() >= 43); // Base64 of 32 bytes
        // Base64 URL-safe characters
        assert!(
            session
                .id()
                .as_str()
                .chars()
                .all(|c| c.is_alphanumeric() || c == '-' || c == '_')
        );
    }

    #[test]
    fn should_validate_session_token_format() {
        // Arrange
        let invalid_tokens = vec![
            "",
            "short",
            "contains spaces",
            "contains@special!chars",
            "null\0byte",
        ];

        for invalid_token in invalid_tokens {
            // Act
            let result = SessionId::from_string(invalid_token.to_string());

            // Assert
            assert!(
                result.is_err(),
                "Should reject invalid token: {}",
                invalid_token
            );
        }
    }

    #[test]
    fn should_implement_session_timeout_sliding_window() {
        // Arrange
        let mut session = Session::new(UserId::new(), 3600, None, None);
        let initial_expires_at = session.expires_at();

        // Act - Touch should extend expiry (sliding window)
        std::thread::sleep(std::time::Duration::from_millis(10));
        session.touch().unwrap();

        // Assert
        // Note: This test assumes sliding window behavior
        // The actual implementation should extend expiry on activity
        assert!(session.expires_at() >= initial_expires_at);
    }

    #[test]
    fn should_prevent_timing_attacks_on_token_verification() {
        // Arrange
        let session = Session::with_token(UserId::new(), "correct_token_123456", 3600, None, None);
        let wrong_tokens = vec![
            "wrong_token_123456",
            "wrong",
            "very_long_wrong_token_that_differs_significantly_from_correct",
            "",
        ];

        // Act & Assert - All verifications should take similar time
        let start = std::time::Instant::now();
        let _ = session.verify_token("correct_token_123456");
        let correct_duration = start.elapsed();

        for wrong_token in wrong_tokens {
            let start = std::time::Instant::now();
            let _ = session.verify_token(wrong_token);
            let wrong_duration = start.elapsed();

            // Timing should be within reasonable bounds
            let ratio = if correct_duration > wrong_duration {
                correct_duration.as_nanos() as f64 / wrong_duration.as_nanos() as f64
            } else {
                wrong_duration.as_nanos() as f64 / correct_duration.as_nanos() as f64
            };

            assert!(
                ratio < 10.0, // More lenient for CI environments and varying system load
                "Timing difference too large: correct={:?}, wrong={:?}",
                correct_duration,
                wrong_duration
            );
        }
    }

    #[test]
    fn should_enforce_session_hijacking_prevention() {
        // Arrange
        let session = Session::new(
            UserId::new(),
            3600,
            Some("***********".to_string()),
            Some("Mozilla/5.0 (Windows NT 10.0; Win64; x64)".to_string()),
        );

        // Act & Assert - Should detect potential hijacking attempts
        assert!(session.detect_potential_hijacking(
            "********",                                        // Different IP
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)", // Different User-Agent
        ));

        assert!(!session.detect_potential_hijacking(
            "***********",                               // Same IP
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64)", // Same User-Agent
        ));
    }

    #[test]
    fn should_implement_absolute_session_timeout() {
        // Arrange - Create session with both idle and absolute timeouts
        let mut session = Session::with_timeouts(
            UserId::new(),
            1800, // 30 min idle timeout
            7200, // 2 hour absolute timeout
            None,
            None,
        );

        // Act - Even if we keep touching the session, it should expire after absolute timeout
        session.absolute_expiry = DateTime::now().sub_seconds(1);

        // Assert - Should be expired due to absolute timeout regardless of activity
        assert!(session.is_absolutely_expired());
        assert!(!session.is_valid());
    }
}
