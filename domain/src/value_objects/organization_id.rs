//! Organization ID Value Object
//!
//! Provides a strongly-typed identifier for organizations to prevent ID confusion
//! and improve type safety across the multi-tenant domain.

use crate::crypto::{ChaChaRandomService, SecureRandomService};
use crate::errors::DomainError;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtResult};

/// Strongly-typed organization identifier
///
/// Organizations are represented by unique identifiers that distinguish between
/// different tenant organizations in a multi-tenant system. This type ensures
/// organization IDs cannot be confused with other ID types.
#[derive(Debug, Clone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct OrganizationId(String);

impl OrganizationId {
    /// Generate a new organization ID
    ///
    /// Creates a new organization ID with secure random generation to ensure
    /// uniqueness across all tenants in the system.
    pub fn generate() -> Self {
        let random_service = ChaChaRandomService::new();
        let random_bytes = random_service.generate_secure_bytes(16);
        let hex_id = random_bytes
            .iter()
            .map(|b| format!("{:02x}", b))
            .collect::<String>();
        OrganizationId(format!("org_{}", hex_id))
    }

    /// Create an OrganizationId from a string (used when loading from storage)
    ///
    /// # Arguments
    ///
    /// * `id` - The organization ID string from storage
    ///
    /// # Returns
    ///
    /// * `Ok(OrganizationId)` if the ID format is valid
    /// * `Err(DomainError)` if the format is invalid
    pub fn from_string(id: String) -> Result<Self, DomainError> {
        if id.is_empty() {
            return Err(DomainError::InvalidId(
                "OrganizationId cannot be empty".to_string(),
            ));
        }

        // Basic format validation
        if !id.starts_with("org_") {
            return Err(DomainError::InvalidId(
                "OrganizationId must start with 'org_'".to_string(),
            ));
        }

        // Check length - should have prefix + at least 32 hex chars
        if id.len() < 36 {
            return Err(DomainError::InvalidId(
                "OrganizationId is too short".to_string(),
            ));
        }

        // Validate hex characters after prefix
        let hex_part = &id[4..]; // Skip "org_"
        if !hex_part.chars().all(|c| c.is_ascii_hexdigit()) {
            return Err(DomainError::InvalidId(
                "OrganizationId contains invalid characters".to_string(),
            ));
        }

        Ok(OrganizationId(id))
    }

    /// Create an OrganizationId from a slug (used for vanity URLs)
    ///
    /// Creates an organization ID based on a human-readable slug,
    /// suitable for use in URLs and display purposes.
    ///
    /// # Arguments
    ///
    /// * `slug` - The organization slug (alphanumeric and hyphens only)
    ///
    /// # Returns
    ///
    /// * `Ok(OrganizationId)` if the slug format is valid
    /// * `Err(DomainError)` if the format is invalid
    pub fn from_slug(slug: &str) -> Result<Self, DomainError> {
        if slug.is_empty() {
            return Err(DomainError::InvalidId(
                "Organization slug cannot be empty".to_string(),
            ));
        }

        if slug.len() < 3 {
            return Err(DomainError::InvalidId(
                "Organization slug must be at least 3 characters".to_string(),
            ));
        }

        if slug.len() > 50 {
            return Err(DomainError::InvalidId(
                "Organization slug cannot exceed 50 characters".to_string(),
            ));
        }

        // Validate slug format: alphanumeric, hyphens, no consecutive hyphens
        let is_valid_char = |c: char| c.is_alphanumeric() || c == '-';

        if !slug.chars().all(is_valid_char) {
            return Err(DomainError::InvalidId(
                "Organization slug can only contain alphanumeric characters and hyphens"
                    .to_string(),
            ));
        }

        // Cannot start or end with hyphen
        if slug.starts_with('-') || slug.ends_with('-') {
            return Err(DomainError::InvalidId(
                "Organization slug cannot start or end with hyphen".to_string(),
            ));
        }

        // Cannot have consecutive hyphens
        if slug.contains("--") {
            return Err(DomainError::InvalidId(
                "Organization slug cannot contain consecutive hyphens".to_string(),
            ));
        }

        Ok(OrganizationId(format!("org_slug_{}", slug.to_lowercase())))
    }

    /// Get the organization ID as a string
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Check if this is a slug-based organization ID
    pub fn is_slug_based(&self) -> bool {
        self.0.starts_with("org_slug_")
    }

    /// Get the slug for slug-based organization IDs
    ///
    /// Returns None for generated organization IDs
    pub fn slug(&self) -> Option<&str> {
        if self.is_slug_based() {
            Some(&self.0[9..]) // Skip "org_slug_"
        } else {
            None
        }
    }

    /// Check if this is a generated (non-slug) organization ID
    pub fn is_generated(&self) -> bool {
        !self.is_slug_based()
    }
}

impl Display for OrganizationId {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_organization_id() {
        let id1 = OrganizationId::generate();
        let id2 = OrganizationId::generate();

        // IDs should be different
        assert_ne!(id1, id2);

        // Should have correct prefix
        assert!(id1.as_str().starts_with("org_"));
        assert!(id2.as_str().starts_with("org_"));

        // Should be generated (not slug-based)
        assert!(id1.is_generated());
        assert!(id2.is_generated());
        assert!(!id1.is_slug_based());
        assert!(!id2.is_slug_based());

        // Should have sufficient length
        assert!(id1.as_str().len() >= 36);
        assert!(id2.as_str().len() >= 36);
    }

    #[test]
    fn test_from_string_valid() {
        let id_str = "org_abcdef1234567890abcdef1234567890abcdef12".to_string();
        let org_id = OrganizationId::from_string(id_str.clone()).unwrap();
        assert_eq!(org_id.as_str(), id_str);
        assert!(org_id.is_generated());
    }

    #[test]
    fn test_from_string_invalid() {
        // Empty string
        assert!(OrganizationId::from_string("".to_string()).is_err());

        // Wrong prefix
        assert!(OrganizationId::from_string("user_123456".to_string()).is_err());

        // Too short
        assert!(OrganizationId::from_string("org_123".to_string()).is_err());

        // Invalid hex characters
        assert!(
            OrganizationId::from_string("org_gggggggggggggggggggggggggggggggg".to_string())
                .is_err()
        );
    }

    #[test]
    fn test_from_slug_valid() {
        let org_id = OrganizationId::from_slug("acme-corp").unwrap();
        assert_eq!(org_id.as_str(), "org_slug_acme-corp");
        assert!(org_id.is_slug_based());
        assert!(!org_id.is_generated());
        assert_eq!(org_id.slug(), Some("acme-corp"));

        // Test uppercase conversion
        let org_id2 = OrganizationId::from_slug("ACME-CORP").unwrap();
        assert_eq!(org_id2.as_str(), "org_slug_acme-corp");
        assert_eq!(org_id2.slug(), Some("acme-corp"));
    }

    #[test]
    fn test_from_slug_invalid() {
        // Empty slug
        assert!(OrganizationId::from_slug("").is_err());

        // Too short
        assert!(OrganizationId::from_slug("ab").is_err());

        // Too long
        let long_slug = "a".repeat(51);
        assert!(OrganizationId::from_slug(&long_slug).is_err());

        // Invalid characters
        assert!(OrganizationId::from_slug("acme@corp").is_err());
        assert!(OrganizationId::from_slug("acme_corp").is_err());
        assert!(OrganizationId::from_slug("acme corp").is_err());

        // Starts/ends with hyphen
        assert!(OrganizationId::from_slug("-acme").is_err());
        assert!(OrganizationId::from_slug("acme-").is_err());

        // Consecutive hyphens
        assert!(OrganizationId::from_slug("acme--corp").is_err());
    }

    #[test]
    fn test_generated_org_not_slug_based() {
        let generated = OrganizationId::generate();
        assert!(!generated.is_slug_based());
        assert!(generated.is_generated());
        assert!(generated.slug().is_none());
    }

    #[test]
    fn test_display() {
        let id = OrganizationId::generate();
        let display_str = format!("{}", id);
        assert_eq!(display_str, id.as_str());
    }

    #[test]
    fn test_ordering() {
        let id1 =
            OrganizationId::from_string("org_aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa".to_string())
                .unwrap();
        let id2 =
            OrganizationId::from_string("org_bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb".to_string())
                .unwrap();

        assert!(id1 < id2);
    }

    #[test]
    fn test_mixed_ordering() {
        let generated = OrganizationId::generate();
        let slug_based = OrganizationId::from_slug("acme-corp").unwrap();

        // Should be able to compare different types
        assert!(generated != slug_based);
    }
}
