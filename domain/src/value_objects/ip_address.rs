//! IP Address Value Object
//!
//! Provides secure handling of IP addresses for security logging and
//! access control. Supports both IPv4 and IPv6 addresses with validation
//! and normalization.

use crate::errors::DomainError;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtR<PERSON><PERSON>};
use std::net::{Ipv4Addr, Ipv6Addr};

/// IP Address value object for security-sensitive operations
///
/// Handles both IPv4 and IPv6 addresses with proper validation,
/// normalization, and security considerations for logging and
/// access control systems.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum IpAddress {
    /// IPv4 address
    V4(Ipv4Addr),
    /// IPv6 address
    V6(Ipv6Addr),
}

impl IpAddress {
    /// Parse an IP address from a string
    ///
    /// Accepts both IPv4 and IPv6 address formats. IPv6 addresses
    /// are normalized to canonical representation.
    ///
    /// # Arguments
    ///
    /// * `ip_str` - The IP address string to parse
    ///
    /// # Returns
    ///
    /// * `Ok(IpAddress)` if the address is valid
    /// * `Err(DomainError)` if the format is invalid
    ///
    /// # Security Notes
    ///
    /// - Rejects private/localhost addresses in production contexts
    /// - Normalizes IPv6 addresses to prevent bypass attempts
    /// - Validates against malformed input that could cause parsing issues
    pub fn parse(ip_str: &str) -> Result<Self, DomainError> {
        if ip_str.is_empty() {
            return Err(DomainError::InvalidInput(
                "IP address cannot be empty".to_string(),
            ));
        }

        // Security: Check for null bytes or control characters
        if ip_str.contains('\0') || ip_str.chars().any(|c| c.is_control()) {
            return Err(DomainError::InvalidInput(
                "IP address contains invalid characters".to_string(),
            ));
        }

        // Trim whitespace that could cause parsing issues
        let trimmed = ip_str.trim();

        // Try IPv4 first
        if let Ok(ipv4) = trimmed.parse::<Ipv4Addr>() {
            return Ok(IpAddress::V4(ipv4));
        }

        // Try IPv6
        if let Ok(ipv6) = trimmed.parse::<Ipv6Addr>() {
            return Ok(IpAddress::V6(ipv6));
        }

        Err(DomainError::InvalidInput(
            "Invalid IP address format".to_string(),
        ))
    }

    /// Create an IpAddress from IPv4 octets
    pub fn from_ipv4(a: u8, b: u8, c: u8, d: u8) -> Self {
        IpAddress::V4(Ipv4Addr::new(a, b, c, d))
    }

    /// Create an IpAddress from IPv6 segments
    pub fn from_ipv6(segments: [u16; 8]) -> Self {
        IpAddress::V6(Ipv6Addr::new(
            segments[0],
            segments[1],
            segments[2],
            segments[3],
            segments[4],
            segments[5],
            segments[6],
            segments[7],
        ))
    }

    /// Check if this is an IPv4 address
    pub fn is_ipv4(&self) -> bool {
        matches!(self, IpAddress::V4(_))
    }

    /// Check if this is an IPv6 address
    pub fn is_ipv6(&self) -> bool {
        matches!(self, IpAddress::V6(_))
    }

    /// Check if this is a loopback address (localhost)
    pub fn is_loopback(&self) -> bool {
        match self {
            IpAddress::V4(addr) => addr.is_loopback(),
            IpAddress::V6(addr) => addr.is_loopback(),
        }
    }

    /// Check if this is a private/internal address
    pub fn is_private(&self) -> bool {
        match self {
            IpAddress::V4(addr) => addr.is_private(),
            IpAddress::V6(addr) => {
                // IPv6 private address ranges
                addr.is_loopback() ||
                addr.segments()[0] & 0xffc0 == 0xfc00 || // Unique local addresses (fc00::/7)
                addr.segments()[0] & 0xfe80 == 0xfe80 // Link-local addresses (fe80::/10)
            }
        }
    }

    /// Check if this is a multicast address
    pub fn is_multicast(&self) -> bool {
        match self {
            IpAddress::V4(addr) => addr.is_multicast(),
            IpAddress::V6(addr) => addr.is_multicast(),
        }
    }

    /// Check if this is an unspecified address (0.0.0.0 or ::)
    pub fn is_unspecified(&self) -> bool {
        match self {
            IpAddress::V4(addr) => addr.is_unspecified(),
            IpAddress::V6(addr) => addr.is_unspecified(),
        }
    }

    /// Check if this address should be blocked for security reasons
    ///
    /// Returns true for addresses that should not be allowed in
    /// production authentication systems (localhost, private ranges, etc.)
    pub fn is_blocked_for_production(&self) -> bool {
        self.is_loopback()
            || self.is_private()
            || self.is_multicast()
            || self.is_unspecified()
            || self.is_documentation()
            || self.is_reserved()
    }

    /// Check if this is a documentation/test address range
    pub fn is_documentation(&self) -> bool {
        match self {
            IpAddress::V4(addr) => {
                let octets = addr.octets();
                // TEST-NET-1 (*********/24)
                (octets[0] == 192 && octets[1] == 0 && octets[2] == 2) ||
                // TEST-NET-2 (************/24) 
                (octets[0] == 198 && octets[1] == 51 && octets[2] == 100) ||
                // TEST-NET-3 (***********/24)
                (octets[0] == 203 && octets[1] == 0 && octets[2] == 113)
            }
            IpAddress::V6(addr) => {
                // Documentation prefix 2001:db8::/32
                addr.segments()[0] == 0x2001 && addr.segments()[1] == 0x0db8
            }
        }
    }

    /// Check if this is a reserved address range
    pub fn is_reserved(&self) -> bool {
        match self {
            IpAddress::V4(addr) => {
                let octets = addr.octets();
                // Reserved ranges beyond what's covered by other methods
                octets[0] == 240 // Class E (240.0.0.0/4)
            }
            IpAddress::V6(_) => {
                // Most IPv6 reserved ranges are covered by other methods
                false
            }
        }
    }

    /// Get a sanitized string representation suitable for logging
    ///
    /// For security reasons, this may mask or truncate sensitive
    /// information in certain contexts.
    pub fn to_log_string(&self) -> String {
        match self {
            IpAddress::V4(addr) => {
                if self.is_private() || self.is_loopback() {
                    // Don't log full private/localhost addresses
                    format!("{}.xxx.xxx.xxx", addr.octets()[0])
                } else {
                    addr.to_string()
                }
            }
            IpAddress::V6(addr) => {
                if self.is_private() || self.is_loopback() {
                    // Mask most of the address for privacy
                    let segments = addr.segments();
                    format!(
                        "{}:{}:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx",
                        segments[0], segments[1]
                    )
                } else {
                    addr.to_string()
                }
            }
        }
    }

    /// Get the canonical string representation
    pub fn to_canonical_string(&self) -> String {
        match self {
            IpAddress::V4(addr) => addr.to_string(),
            IpAddress::V6(addr) => addr.to_string(),
        }
    }
}

impl Display for IpAddress {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.to_canonical_string())
    }
}

impl PartialOrd for IpAddress {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for IpAddress {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        match (self, other) {
            (IpAddress::V4(a), IpAddress::V4(b)) => a.cmp(b),
            (IpAddress::V6(a), IpAddress::V6(b)) => a.cmp(b),
            (IpAddress::V4(_), IpAddress::V6(_)) => std::cmp::Ordering::Less,
            (IpAddress::V6(_), IpAddress::V4(_)) => std::cmp::Ordering::Greater,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_ipv4() {
        let ip = IpAddress::parse("***********").unwrap();
        assert!(ip.is_ipv4());
        assert!(!ip.is_ipv6());
        assert_eq!(ip.to_string(), "***********");
    }

    #[test]
    fn test_parse_ipv6() {
        let ip = IpAddress::parse("2001:db8::1").unwrap();
        assert!(ip.is_ipv6());
        assert!(!ip.is_ipv4());
        assert_eq!(ip.to_string(), "2001:db8::1");
    }

    #[test]
    fn test_parse_invalid() {
        assert!(IpAddress::parse("").is_err());
        assert!(IpAddress::parse("invalid").is_err());
        assert!(IpAddress::parse("999.999.999.999").is_err());
        assert!(IpAddress::parse("192.168.1").is_err());
    }

    #[test]
    fn test_security_validation() {
        // Null byte injection
        assert!(IpAddress::parse("***********\0").is_err());

        // Control characters
        assert!(IpAddress::parse("***********\n").is_err());
        assert!(IpAddress::parse("***********\r").is_err());
        assert!(IpAddress::parse("***********\t").is_err());
    }

    #[test]
    fn test_address_types() {
        // Loopback
        let localhost_v4 = IpAddress::parse("127.0.0.1").unwrap();
        assert!(localhost_v4.is_loopback());

        let localhost_v6 = IpAddress::parse("::1").unwrap();
        assert!(localhost_v6.is_loopback());

        // Private
        let private_v4 = IpAddress::parse("***********").unwrap();
        assert!(private_v4.is_private());

        let private_v6 = IpAddress::parse("fc00::1").unwrap();
        assert!(private_v6.is_private());

        // Public
        let public_v4 = IpAddress::parse("*******").unwrap();
        assert!(!public_v4.is_private());
        assert!(!public_v4.is_loopback());
    }

    #[test]
    fn test_production_blocking() {
        // These should be blocked in production
        assert!(
            IpAddress::parse("127.0.0.1")
                .unwrap()
                .is_blocked_for_production()
        );
        assert!(
            IpAddress::parse("***********")
                .unwrap()
                .is_blocked_for_production()
        );
        assert!(IpAddress::parse("::1").unwrap().is_blocked_for_production());
        assert!(
            IpAddress::parse("0.0.0.0")
                .unwrap()
                .is_blocked_for_production()
        );

        // Documentation addresses
        assert!(
            IpAddress::parse("*********")
                .unwrap()
                .is_blocked_for_production()
        );
        assert!(
            IpAddress::parse("2001:db8::1")
                .unwrap()
                .is_blocked_for_production()
        );

        // These should be allowed
        assert!(
            !IpAddress::parse("*******")
                .unwrap()
                .is_blocked_for_production()
        );
        assert!(
            !IpAddress::parse("*******")
                .unwrap()
                .is_blocked_for_production()
        );
    }

    #[test]
    fn test_log_string_masking() {
        // Private addresses should be masked
        let private = IpAddress::parse("***********00").unwrap();
        assert_eq!(private.to_log_string(), "192.xxx.xxx.xxx");

        let localhost = IpAddress::parse("127.0.0.1").unwrap();
        assert_eq!(localhost.to_log_string(), "127.xxx.xxx.xxx");

        // Public addresses should not be masked
        let public = IpAddress::parse("*******").unwrap();
        assert_eq!(public.to_log_string(), "*******");

        // IPv6 masking
        let private_v6 = IpAddress::parse("fc00::1234:5678").unwrap();
        assert!(private_v6.to_log_string().contains("xxxx"));
    }

    #[test]
    fn test_from_constructors() {
        let ipv4 = IpAddress::from_ipv4(192, 168, 1, 1);
        assert_eq!(ipv4.to_string(), "***********");

        let ipv6 = IpAddress::from_ipv6([0x2001, 0x0db8, 0, 0, 0, 0, 0, 1]);
        assert_eq!(ipv6.to_string(), "2001:db8::1");
    }

    #[test]
    fn test_ordering() {
        let ip1 = IpAddress::parse("***********").unwrap();
        let ip2 = IpAddress::parse("***********").unwrap();
        let ip6 = IpAddress::parse("2001:db8::1").unwrap();

        assert!(ip1 < ip2);
        assert!(ip1 < ip6); // IPv4 < IPv6
    }

    #[test]
    fn test_whitespace_handling() {
        let ip = IpAddress::parse("  ***********  ").unwrap();
        assert_eq!(ip.to_string(), "***********");
    }
}
