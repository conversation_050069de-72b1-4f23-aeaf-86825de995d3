// Domain DateTime value object
// Pure std library implementation for domain isolation

use crate::errors::DomainError;
use std::fmt;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

#[derive(
    <PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, <PERSON>ialOrd, Ord, serde::Serialize, serde::Deserialize,
)]
pub struct DateTime(SystemTime);

impl DateTime {
    pub fn now() -> Self {
        Self(SystemTime::now())
    }

    pub fn from_timestamp(secs: u64) -> Result<Self, DomainError> {
        let system_time = UNIX_EPOCH + Duration::from_secs(secs);
        Ok(Self(system_time))
    }

    pub fn from_timestamp_millis(millis: u64) -> Result<Self, DomainError> {
        let system_time = UNIX_EPOCH + Duration::from_millis(millis);
        Ok(Self(system_time))
    }

    pub fn timestamp(&self) -> u64 {
        self.0
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::ZERO)
            .as_secs()
    }

    pub fn timestamp_millis(&self) -> u64 {
        self.0
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::ZERO)
            .as_millis() as u64
    }

    pub fn add_seconds(&self, secs: u64) -> Self {
        Self(self.0 + Duration::from_secs(secs))
    }

    pub fn add_minutes(&self, minutes: u64) -> Self {
        self.add_seconds(minutes * 60)
    }

    pub fn add_hours(&self, hours: u64) -> Self {
        self.add_seconds(hours * 3600)
    }

    pub fn add_days(&self, days: u64) -> Self {
        self.add_seconds(days * 86400)
    }

    pub fn sub_seconds(&self, secs: u64) -> Self {
        Self(self.0 - Duration::from_secs(secs))
    }

    pub fn sub_minutes(&self, minutes: u64) -> Self {
        self.sub_seconds(minutes * 60)
    }

    pub fn sub_hours(&self, hours: u64) -> Self {
        self.sub_seconds(hours * 3600)
    }

    pub fn sub_days(&self, days: u64) -> Self {
        self.sub_seconds(days * 86400)
    }

    pub fn duration_since(&self, other: DateTime) -> Result<Duration, DomainError> {
        self.0
            .duration_since(other.0)
            .map_err(|_| DomainError::InvalidInput("Invalid duration calculation".to_string()))
    }

    pub fn elapsed(&self) -> Duration {
        self.0.elapsed().unwrap_or(Duration::ZERO)
    }

    pub fn is_before(&self, other: DateTime) -> bool {
        self.0 < other.0
    }

    pub fn is_after(&self, other: DateTime) -> bool {
        self.0 > other.0
    }

    pub fn is_within_last(&self, duration: Duration) -> bool {
        let now = SystemTime::now();
        match now.duration_since(self.0) {
            Ok(elapsed) => elapsed <= duration,
            Err(_) => false, // DateTime is in the future
        }
    }

    pub fn is_older_than(&self, duration: Duration) -> bool {
        !self.is_within_last(duration)
    }

    pub fn format_iso8601(&self) -> String {
        // Simple ISO 8601 format using timestamp
        let timestamp = self.timestamp();
        let secs = timestamp % 60;
        let mins = (timestamp / 60) % 60;
        let hours = (timestamp / 3600) % 24;
        let days = timestamp / 86400;

        // This is a simplified implementation
        // In practice, you'd want proper date calculation
        format!(
            "{:04}-{:02}-{:02}T{:02}:{:02}:{:02}Z",
            1970 + days / 365, // Simplified year calculation
            1,                 // Simplified month
            1 + days % 365,    // Simplified day
            hours,
            mins,
            secs
        )
    }

    pub fn to_system_time(&self) -> SystemTime {
        self.0
    }

    pub fn from_system_time(system_time: SystemTime) -> Self {
        Self(system_time)
    }
}

impl fmt::Display for DateTime {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "DateTime({})", self.timestamp())
    }
}

impl From<SystemTime> for DateTime {
    fn from(system_time: SystemTime) -> Self {
        Self(system_time)
    }
}

impl From<DateTime> for SystemTime {
    fn from(datetime: DateTime) -> Self {
        datetime.0
    }
}

// Mathematical operations
impl std::ops::Add<Duration> for DateTime {
    type Output = DateTime;

    fn add(self, duration: Duration) -> Self::Output {
        DateTime(self.0 + duration)
    }
}

impl std::ops::Sub<Duration> for DateTime {
    type Output = DateTime;

    fn sub(self, duration: Duration) -> Self::Output {
        DateTime(self.0 - duration)
    }
}

impl std::ops::Sub<DateTime> for DateTime {
    type Output = Result<Duration, DomainError>;

    fn sub(self, other: DateTime) -> Self::Output {
        self.duration_since(other)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn should_create_datetime_now() {
        // Arrange & Act
        let dt1 = DateTime::now();
        std::thread::sleep(Duration::from_millis(1));
        let dt2 = DateTime::now();

        // Assert
        assert!(dt2.is_after(dt1));
        assert!(dt1.is_before(dt2));
    }

    #[test]
    fn should_create_datetime_from_timestamp() {
        // Arrange
        let timestamp = 1_640_995_200u64; // 2022-01-01 00:00:00 UTC

        // Act
        let result = DateTime::from_timestamp(timestamp);

        // Assert
        assert!(result.is_ok());
        let dt = result.unwrap();
        assert_eq!(dt.timestamp(), timestamp);
    }

    #[test]
    fn should_create_datetime_from_timestamp_millis() {
        // Arrange
        let timestamp_millis = 1_640_995_200_000u64; // 2022-01-01 00:00:00.000 UTC

        // Act
        let result = DateTime::from_timestamp_millis(timestamp_millis);

        // Assert
        assert!(result.is_ok());
        let dt = result.unwrap();
        assert_eq!(dt.timestamp_millis(), timestamp_millis);
        assert_eq!(dt.timestamp(), 1_640_995_200);
    }

    #[test]
    fn should_convert_to_timestamps() {
        // Arrange
        let dt = DateTime::from_timestamp(1_640_995_200).unwrap();

        // Act & Assert
        assert_eq!(dt.timestamp(), 1_640_995_200);
        assert_eq!(dt.timestamp_millis(), 1_640_995_200_000);
    }

    #[test]
    fn should_add_time_units() {
        // Arrange
        let base_dt = DateTime::from_timestamp(1_640_995_200).unwrap(); // 2022-01-01 00:00:00

        // Act
        let plus_30_secs = base_dt.add_seconds(30);
        let plus_5_mins = base_dt.add_minutes(5);
        let plus_2_hours = base_dt.add_hours(2);
        let plus_1_day = base_dt.add_days(1);

        // Assert
        assert_eq!(plus_30_secs.timestamp(), 1_640_995_230);
        assert_eq!(plus_5_mins.timestamp(), 1_640_995_500);
        assert_eq!(plus_2_hours.timestamp(), 1_641_002_400);
        assert_eq!(plus_1_day.timestamp(), 1_641_081_600);
    }

    #[test]
    fn should_subtract_time_units() {
        // Arrange
        let base_dt = DateTime::from_timestamp(1_641_081_600).unwrap(); // 2022-01-02 00:00:00

        // Act
        let minus_30_secs = base_dt.sub_seconds(30);
        let minus_5_mins = base_dt.sub_minutes(5);
        let minus_2_hours = base_dt.sub_hours(2);
        let minus_1_day = base_dt.sub_days(1);

        // Assert
        assert_eq!(minus_30_secs.timestamp(), 1_641_081_570);
        assert_eq!(minus_5_mins.timestamp(), 1_641_081_300);
        assert_eq!(minus_2_hours.timestamp(), 1_641_074_400);
        assert_eq!(minus_1_day.timestamp(), 1_640_995_200);
    }

    #[test]
    fn should_calculate_duration_between_datetimes() {
        // Arrange
        let dt1 = DateTime::from_timestamp(1_640_995_200).unwrap();
        let dt2 = DateTime::from_timestamp(1_641_081_600).unwrap(); // 1 day later

        // Act
        let duration = dt2.duration_since(dt1).unwrap();

        // Assert
        assert_eq!(duration.as_secs(), 86400); // 1 day in seconds
    }

    #[test]
    fn should_fail_duration_calculation_for_reverse_order() {
        // Arrange
        let dt1 = DateTime::from_timestamp(1_641_081_600).unwrap();
        let dt2 = DateTime::from_timestamp(1_640_995_200).unwrap(); // Earlier time

        // Act
        let result = dt2.duration_since(dt1);

        // Assert
        assert!(result.is_err());
    }

    #[test]
    fn should_check_temporal_relationships() {
        // Arrange
        let earlier = DateTime::from_timestamp(1_640_995_200).unwrap();
        let later = DateTime::from_timestamp(1_641_081_600).unwrap();

        // Act & Assert
        assert!(earlier.is_before(later));
        assert!(!earlier.is_after(later));
        assert!(later.is_after(earlier));
        assert!(!later.is_before(earlier));
        assert!(!earlier.is_before(earlier)); // Not before itself
        assert!(!earlier.is_after(earlier)); // Not after itself
    }

    #[test]
    fn should_check_if_within_duration() {
        // Arrange
        let now = DateTime::now();
        let recent = now.sub_minutes(5);
        let old = now.sub_hours(2);

        // Act & Assert
        assert!(recent.is_within_last(Duration::from_secs(600))); // 10 minutes
        assert!(!recent.is_within_last(Duration::from_secs(60))); // 1 minute
        assert!(!old.is_within_last(Duration::from_secs(3600))); // 1 hour
        assert!(old.is_within_last(Duration::from_secs(7300))); // Just over 2 hours
    }

    #[test]
    fn should_check_if_older_than_duration() {
        // Arrange
        let now = DateTime::now();
        let recent = now.sub_minutes(5);
        let old = now.sub_hours(2);

        // Act & Assert
        assert!(!recent.is_older_than(Duration::from_secs(600))); // 10 minutes
        assert!(recent.is_older_than(Duration::from_secs(60))); // 1 minute
        assert!(old.is_older_than(Duration::from_secs(3600))); // 1 hour
        assert!(!old.is_older_than(Duration::from_secs(7300))); // Slightly more than 2 hours for tolerance
    }

    #[test]
    fn should_calculate_elapsed_time() {
        // Arrange
        let dt = DateTime::now().sub_seconds(30);

        // Act
        let elapsed = dt.elapsed();

        // Assert
        assert!(elapsed.as_secs() >= 30);
        assert!(elapsed.as_secs() < 35); // Allow some tolerance
    }

    #[test]
    fn should_format_iso8601_string() {
        // Arrange
        let dt = DateTime::from_timestamp(1_640_995_200).unwrap();

        // Act
        let formatted = dt.format_iso8601();

        // Assert
        assert!(formatted.contains("T"));
        assert!(formatted.ends_with("Z"));
        assert!(formatted.len() > 15); // Basic format check
    }

    #[test]
    fn should_display_datetime() {
        // Arrange
        let dt = DateTime::from_timestamp(1_640_995_200).unwrap();

        // Act
        let display_string = format!("{}", dt);

        // Assert
        assert_eq!(display_string, "DateTime(1640995200)");
    }

    #[test]
    fn should_convert_to_system_time() {
        // Arrange
        let dt = DateTime::from_timestamp(1_640_995_200).unwrap();

        // Act
        let system_time = dt.to_system_time();

        // Assert
        let expected = UNIX_EPOCH + Duration::from_secs(1_640_995_200);
        assert_eq!(system_time, expected);
    }

    #[test]
    fn should_convert_from_system_time() {
        // Arrange
        let system_time = UNIX_EPOCH + Duration::from_secs(1_640_995_200);

        // Act
        let dt = DateTime::from(system_time);

        // Assert
        assert_eq!(dt.timestamp(), 1_640_995_200);
    }

    #[test]
    fn should_support_mathematical_operations() {
        // Arrange
        let dt = DateTime::from_timestamp(1_640_995_200).unwrap();
        let duration = Duration::from_secs(3600); // 1 hour

        // Act
        let plus_duration = dt + duration;
        let minus_duration = dt - duration;
        let diff = plus_duration - dt;

        // Assert
        assert_eq!(plus_duration.timestamp(), 1_640_998_800);
        assert_eq!(minus_duration.timestamp(), 1_640_991_600);
        assert!(diff.is_ok());
        assert_eq!(diff.unwrap(), duration);
    }

    #[test]
    fn should_be_ordered_correctly() {
        // Arrange
        let dt1 = DateTime::from_timestamp(1_640_995_200).unwrap();
        let dt2 = DateTime::from_timestamp(1_641_081_600).unwrap();
        let dt3 = DateTime::from_timestamp(1_641_168_000).unwrap();

        let mut dates = vec![dt3, dt1, dt2];

        // Act
        dates.sort();

        // Assert
        assert_eq!(dates, vec![dt1, dt2, dt3]);
    }

    #[test]
    fn should_be_comparable() {
        // Arrange
        let dt1 = DateTime::from_timestamp(1_640_995_200).unwrap();
        let dt2 = DateTime::from_timestamp(1_640_995_200).unwrap();
        let dt3 = DateTime::from_timestamp(1_641_081_600).unwrap();

        // Act & Assert
        assert_eq!(dt1, dt2);
        assert_ne!(dt1, dt3);
        assert!(dt1 < dt3);
        assert!(dt3 > dt1);
        assert!(dt1 <= dt2);
        assert!(dt1 >= dt2);
    }

    // Security and edge case tests
    #[test]
    fn should_handle_unix_epoch() {
        // Arrange & Act
        let epoch = DateTime::from_timestamp(0).unwrap();

        // Assert
        assert_eq!(epoch.timestamp(), 0);
        assert_eq!(epoch.timestamp_millis(), 0);
    }

    #[test]
    fn should_handle_year_2038_problem() {
        // Arrange
        let timestamp_2038 = 2_147_483_648u64; // Beyond 32-bit signed int limit

        // Act
        let result = DateTime::from_timestamp(timestamp_2038);

        // Assert
        assert!(result.is_ok());
        let dt = result.unwrap();
        assert_eq!(dt.timestamp(), timestamp_2038);
    }

    #[test]
    fn should_handle_large_timestamps() {
        // Arrange
        let large_timestamp = u64::MAX / 2; // Very large but valid timestamp

        // Act
        let result = DateTime::from_timestamp(large_timestamp);

        // Assert
        assert!(result.is_ok());
        let dt = result.unwrap();
        assert_eq!(dt.timestamp(), large_timestamp);
    }

    #[test]
    fn should_handle_time_arithmetic_overflow_gracefully() {
        // Arrange
        let dt = DateTime::from_timestamp(1_640_995_200).unwrap(); // A reasonable timestamp

        // Act - Add a large but safe amount
        let result = dt.add_days(365); // Add 1 year

        // Assert - Should not panic
        assert!(result.timestamp() > dt.timestamp()); // Basic sanity check
    }

    #[test]
    fn should_handle_negative_duration_subtraction() {
        // Arrange
        let dt = DateTime::from_timestamp(100).unwrap();
        let large_duration = Duration::from_secs(200);

        // Act - This would go before Unix epoch
        let result = dt - large_duration;

        // Assert - Should handle gracefully (implementation-dependent)
        // This test documents the behavior rather than enforcing it
        assert!(result.timestamp() < 100);
    }

    #[test]
    fn should_maintain_precision_in_conversions() {
        // Arrange
        let original_millis = 1_640_995_200_123u64;

        // Act
        let dt = DateTime::from_timestamp_millis(original_millis).unwrap();
        let converted_millis = dt.timestamp_millis();

        // Assert
        assert_eq!(converted_millis, original_millis);
    }

    #[test]
    fn should_be_deterministic_for_same_inputs() {
        // Arrange
        let timestamp = 1_640_995_200u64;

        // Act
        let dt1 = DateTime::from_timestamp(timestamp).unwrap();
        let dt2 = DateTime::from_timestamp(timestamp).unwrap();

        // Assert
        assert_eq!(dt1, dt2);
        assert_eq!(dt1.timestamp(), dt2.timestamp());
        assert_eq!(format!("{}", dt1), format!("{}", dt2));
    }

    #[test]
    fn should_handle_concurrent_now_calls() {
        // This test verifies that multiple threads calling DateTime::now()
        // don't interfere with each other
        use std::sync::{Arc, Mutex};
        use std::thread;

        // Arrange
        let results = Arc::new(Mutex::new(Vec::new()));
        let mut handles = vec![];

        // Act - Spawn multiple threads calling now()
        for _ in 0..10 {
            let results_clone = Arc::clone(&results);
            let handle = thread::spawn(move || {
                let dt = DateTime::now();
                results_clone.lock().unwrap().push(dt);
            });
            handles.push(handle);
        }

        // Wait for all threads
        for handle in handles {
            handle.join().unwrap();
        }

        // Assert
        let results = results.lock().unwrap();
        assert_eq!(results.len(), 10);

        // All results should be reasonable (within last few seconds)
        let _now = DateTime::now();
        for dt in results.iter() {
            assert!(dt.is_within_last(Duration::from_secs(5)));
        }
    }

    #[test]
    fn should_maintain_monotonic_ordering() {
        // Arrange & Act
        let mut timestamps = Vec::new();

        for _ in 0..100 {
            timestamps.push(DateTime::now());
            // Small delay to ensure different timestamps
            std::thread::sleep(Duration::from_nanos(1));
        }

        // Assert - Should be mostly ordered (allowing for some clock variance)
        let mut ordered_count = 0;
        for i in 1..timestamps.len() {
            if timestamps[i].is_after(timestamps[i - 1]) || timestamps[i] == timestamps[i - 1] {
                ordered_count += 1;
            }
        }

        // Should be mostly ordered (allow some tolerance for clock precision)
        assert!(ordered_count as f64 / (timestamps.len() - 1) as f64 > 0.9);
    }
}
