// UserId value object
// Strongly-typed user identifier with validation

use crate::errors::DomainError;

#[derive(
    <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, PartialOrd, Ord, Hash, serde::Serialize, serde::Deserialize,
)]
pub struct UserId(String);

impl UserId {
    pub fn new() -> Self {
        // Generate a new user ID using a simple counter for now
        // In a real implementation, this would use UUID v4 or similar
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        let id = COUNTER.fetch_add(1, Ordering::SeqCst);
        UserId(format!("user_{id:08}"))
    }

    pub fn from_string(id: String) -> Result<Self, DomainError> {
        if id.is_empty() {
            return Err(DomainError::InvalidUserId(
                "User ID cannot be empty".to_string(),
            ));
        }

        if id.len() > 50 {
            return Err(DomainError::InvalidUserId(
                "User ID cannot exceed 50 characters".to_string(),
            ));
        }

        // Validate format - should contain only alphanumeric characters, hyphens, and underscores
        if !id
            .chars()
            .all(|c| c.is_alphanumeric() || c == '-' || c == '_')
        {
            return Err(DomainError::InvalidUserId(
                "User ID can only contain alphanumeric characters, hyphens, and underscores"
                    .to_string(),
            ));
        }

        Ok(UserId(id))
    }

    pub fn as_str(&self) -> &str {
        &self.0
    }

    pub fn into_string(self) -> String {
        self.0
    }
}

impl Default for UserId {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Display for UserId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new_user_id() {
        let id1 = UserId::new();
        let id2 = UserId::new();

        // IDs should be different
        assert_ne!(id1, id2);

        // IDs should have expected format
        assert!(id1.as_str().starts_with("user_"));
        assert!(id2.as_str().starts_with("user_"));
    }

    #[test]
    fn test_from_valid_string() {
        let id = UserId::from_string("user_12345".to_string()).unwrap();
        assert_eq!(id.as_str(), "user_12345");
    }

    #[test]
    fn test_from_invalid_strings() {
        // Empty string
        assert!(UserId::from_string("".to_string()).is_err());

        // Too long
        let long_id = "a".repeat(51);
        assert!(UserId::from_string(long_id).is_err());

        // Invalid characters
        assert!(UserId::from_string("user@123".to_string()).is_err());
        assert!(UserId::from_string("user 123".to_string()).is_err());
        assert!(UserId::from_string("user.123".to_string()).is_err());
    }

    #[test]
    fn test_valid_characters() {
        // Valid characters should work
        assert!(UserId::from_string("user_123".to_string()).is_ok());
        assert!(UserId::from_string("user-123".to_string()).is_ok());
        assert!(UserId::from_string("User123".to_string()).is_ok());
        assert!(UserId::from_string("123user".to_string()).is_ok());
    }

    #[test]
    fn test_display() {
        let id = UserId::from_string("test_user_123".to_string()).unwrap();
        assert_eq!(format!("{id}"), "test_user_123");
    }

    #[test]
    fn test_into_string() {
        let id = UserId::from_string("test_user".to_string()).unwrap();
        let string_id = id.into_string();
        assert_eq!(string_id, "test_user");
    }
}
