//! Phone Number Value Object
//!
//! Provides secure handling of phone numbers for SMS-based MFA and
//! user verification. Includes validation, normalization, and
//! security checks for international phone numbers.

use crate::errors::DomainError;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as Fmt<PERSON><PERSON><PERSON>};

/// Phone Number value object for MFA and verification
///
/// Handles international phone numbers with proper validation,
/// normalization to E.164 format, and security considerations
/// for SMS-based authentication systems.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct PhoneNumber {
    /// The phone number in E.164 format (e.g., +1234567890)
    value: String,
    /// The country code (e.g., "US", "GB", "DE")
    country_code: String,
    /// The national number without country code
    national_number: String,
}

impl PhoneNumber {
    /// Parse and validate a phone number string
    ///
    /// Accepts various formats and normalizes to E.164 format.
    /// Supports international numbers with country codes.
    ///
    /// # Arguments
    ///
    /// * `phone_str` - The phone number string to parse
    ///
    /// # Returns
    ///
    /// * `Ok(PhoneNumber)` if the number is valid
    /// * `Err(DomainError)` if the format is invalid
    ///
    /// # Security Notes
    ///
    /// - Validates against premium rate numbers
    /// - Checks for known spam/fraud patterns
    /// - Normalizes format to prevent bypass attempts
    pub fn parse(phone_str: &str) -> Result<Self, DomainError> {
        if phone_str.is_empty() {
            return Err(DomainError::InvalidInput(
                "Phone number cannot be empty".to_string(),
            ));
        }

        // Security: Check for null bytes or control characters
        if phone_str.contains('\0') || phone_str.chars().any(|c| c.is_control()) {
            return Err(DomainError::InvalidInput(
                "Phone number contains invalid characters".to_string(),
            ));
        }

        // Clean and normalize the input
        let normalized = Self::normalize_input(phone_str)?;

        // Extract country code and national number
        let (country_code, national_number) = Self::parse_components(&normalized)?;

        // Validate the components
        Self::validate_country_code(&country_code)?;
        Self::validate_national_number(&national_number, &country_code)?;

        // Security checks
        Self::security_validation(&normalized, &country_code)?;

        Ok(PhoneNumber {
            value: normalized,
            country_code,
            national_number,
        })
    }

    /// Parse with explicit country code
    ///
    /// Useful when you know the country context and want to parse
    /// national format numbers (without international prefix).
    pub fn parse_with_country(phone_str: &str, default_country: &str) -> Result<Self, DomainError> {
        if phone_str.starts_with('+') {
            // Already has country code, parse normally
            Self::parse(phone_str)
        } else {
            // Add country code based on default
            let country_prefix = Self::get_country_prefix(default_country)?;
            let full_number = format!("+{}{}", country_prefix, phone_str);
            Self::parse(&full_number)
        }
    }

    /// Get the phone number in E.164 format
    pub fn e164(&self) -> &str {
        &self.value
    }

    /// Get the country code (ISO 3166-1 alpha-2)
    pub fn country(&self) -> &str {
        &self.country_code
    }

    /// Get the national number (without country code)
    pub fn national_number(&self) -> &str {
        &self.national_number
    }

    /// Format for display in international format
    pub fn international_format(&self) -> String {
        // Simple formatting - in real implementation might use more sophisticated formatting
        if self.country_code == "US" || self.country_code == "CA" {
            // North American format: +1 (xxx) xxx-xxxx
            if self.national_number.len() == 10 {
                format!(
                    "+1 ({}) {}-{}",
                    &self.national_number[0..3],
                    &self.national_number[3..6],
                    &self.national_number[6..10]
                )
            } else {
                self.value.clone()
            }
        } else {
            // International format: +xx xxx xxx xxxx
            let prefix = self
                .value
                .chars()
                .take_while(|c| c.is_ascii_digit() || *c == '+')
                .collect::<String>();
            if prefix.len() > 3 {
                format!("{} {}", &prefix[..4], &prefix[4..])
            } else {
                self.value.clone()
            }
        }
    }

    /// Check if this number is likely for SMS delivery
    pub fn supports_sms(&self) -> bool {
        // Basic check - mobile prefixes vary by country
        match self.country_code.as_str() {
            "US" | "CA" => {
                // In North America, most numbers support SMS
                self.national_number.len() == 10
            }
            _ => {
                // Conservative approach - assume mobile
                true
            }
        }
    }

    /// Check if this number should be blocked for security reasons
    pub fn is_blocked(&self) -> bool {
        self.is_premium_rate() || self.is_known_spam_pattern() || self.is_test_number()
    }

    /// Check if this is a premium rate number
    pub fn is_premium_rate(&self) -> bool {
        match self.country_code.as_str() {
            "US" | "CA" => {
                // Premium rate prefixes in North America
                self.national_number.starts_with("900") || self.national_number.starts_with("976")
            }
            "GB" => {
                // UK premium rate
                self.national_number.starts_with("09")
            }
            "DE" => {
                // German premium rate
                self.national_number.starts_with("190") || self.national_number.starts_with("900")
            }
            _ => false, // Conservative approach
        }
    }

    /// Check for known spam/fraud patterns
    fn is_known_spam_pattern(&self) -> bool {
        // Common spam patterns
        let repeating_digits = self
            .national_number
            .chars()
            .all(|c| c == self.national_number.chars().next().unwrap_or('0'));
        let sequential = self.is_sequential_digits();

        repeating_digits || sequential
    }

    /// Check if digits are sequential (123456789, 987654321, etc.)
    fn is_sequential_digits(&self) -> bool {
        let digits: Vec<u32> = self
            .national_number
            .chars()
            .filter_map(|c| c.to_digit(10))
            .collect();

        if digits.len() < 3 {
            return false;
        }

        // Check ascending sequence
        let ascending = digits.windows(2).all(|w| w[1] == w[0] + 1);

        // Check descending sequence
        let descending = digits.windows(2).all(|w| w[1] == w[0] - 1);

        ascending || descending
    }

    /// Check if this is a test/development number
    fn is_test_number(&self) -> bool {
        // Common test number patterns
        self.national_number == "5550100" || // US test number
        self.national_number.starts_with("555") && self.country_code == "US"
    }

    /// Normalize input string to E.164 format
    fn normalize_input(input: &str) -> Result<String, DomainError> {
        // Remove all non-digit characters except +
        let mut cleaned = String::new();
        let mut has_plus = false;

        for ch in input.chars() {
            if ch == '+' && cleaned.is_empty() {
                has_plus = true;
                cleaned.push(ch);
            } else if ch.is_ascii_digit() {
                cleaned.push(ch);
            } else if ch.is_whitespace() || ch == '-' || ch == '(' || ch == ')' || ch == '.' {
                // Skip formatting characters
                continue;
            } else {
                return Err(DomainError::InvalidInput(format!(
                    "Invalid character in phone number: {}",
                    ch
                )));
            }
        }

        if cleaned.is_empty() {
            return Err(DomainError::InvalidInput(
                "Phone number contains no digits".to_string(),
            ));
        }

        // Must start with + for international format
        if !has_plus {
            return Err(DomainError::InvalidInput(
                "Phone number must include country code (start with +)".to_string(),
            ));
        }

        // Check length constraints
        if cleaned.len() < 8 || cleaned.len() > 16 {
            return Err(DomainError::InvalidInput(
                "Phone number length invalid (must be 8-15 digits plus +)".to_string(),
            ));
        }

        Ok(cleaned)
    }

    /// Parse country code and national number from E.164 format
    fn parse_components(e164: &str) -> Result<(String, String), DomainError> {
        if !e164.starts_with('+') {
            return Err(DomainError::InvalidInput(
                "E.164 format must start with +".to_string(),
            ));
        }

        let digits = &e164[1..]; // Remove +

        // Try to identify country code (1-3 digits)
        for len in 1..=3 {
            if len <= digits.len() {
                let potential_code = &digits[..len];
                if let Some(country) = Self::country_code_to_iso(potential_code) {
                    let national = digits[len..].to_string();
                    if !national.is_empty() {
                        return Ok((country, national));
                    }
                }
            }
        }

        Err(DomainError::InvalidInput(
            "Could not identify country code".to_string(),
        ))
    }

    /// Map numeric country code to ISO country code
    fn country_code_to_iso(numeric_code: &str) -> Option<String> {
        match numeric_code {
            "1" => Some("US".to_string()), // Also CA, but defaulting to US
            "44" => Some("GB".to_string()),
            "49" => Some("DE".to_string()),
            "33" => Some("FR".to_string()),
            "39" => Some("IT".to_string()),
            "34" => Some("ES".to_string()),
            "81" => Some("JP".to_string()),
            "86" => Some("CN".to_string()),
            "91" => Some("IN".to_string()),
            "55" => Some("BR".to_string()),
            "7" => Some("RU".to_string()),
            _ => None, // Unknown country code
        }
    }

    /// Get numeric country prefix for ISO country code
    fn get_country_prefix(iso_code: &str) -> Result<String, DomainError> {
        match iso_code {
            "US" | "CA" => Ok("1".to_string()),
            "GB" => Ok("44".to_string()),
            "DE" => Ok("49".to_string()),
            "FR" => Ok("33".to_string()),
            "IT" => Ok("39".to_string()),
            "ES" => Ok("34".to_string()),
            "JP" => Ok("81".to_string()),
            "CN" => Ok("86".to_string()),
            "IN" => Ok("91".to_string()),
            "BR" => Ok("55".to_string()),
            "RU" => Ok("7".to_string()),
            _ => Err(DomainError::InvalidInput(format!(
                "Unsupported country code: {}",
                iso_code
            ))),
        }
    }

    /// Validate country code
    fn validate_country_code(country_code: &str) -> Result<(), DomainError> {
        if country_code.len() != 2 {
            return Err(DomainError::InvalidInput(
                "Country code must be 2 characters".to_string(),
            ));
        }

        if !country_code.chars().all(|c| c.is_ascii_uppercase()) {
            return Err(DomainError::InvalidInput(
                "Country code must be uppercase ASCII".to_string(),
            ));
        }

        Ok(())
    }

    /// Validate national number based on country
    fn validate_national_number(national: &str, country: &str) -> Result<(), DomainError> {
        if national.is_empty() {
            return Err(DomainError::InvalidInput(
                "National number cannot be empty".to_string(),
            ));
        }

        // Country-specific validation
        match country {
            "US" | "CA" => {
                if national.len() != 10 {
                    return Err(DomainError::InvalidInput(
                        "US/Canada numbers must be 10 digits".to_string(),
                    ));
                }

                // Area code cannot start with 0 or 1
                if national.starts_with('0') || national.starts_with('1') {
                    return Err(DomainError::InvalidInput(
                        "Invalid US/Canada area code".to_string(),
                    ));
                }
            }
            _ => {
                // General validation for other countries
                if national.len() < 4 || national.len() > 12 {
                    return Err(DomainError::InvalidInput(
                        "National number length invalid".to_string(),
                    ));
                }
            }
        }

        Ok(())
    }

    /// Perform security validation
    fn security_validation(e164: &str, country: &str) -> Result<(), DomainError> {
        // Check for suspicious patterns
        // Take the last min(10, available) digits for analysis
        let start_pos = if e164.len() > 10 { e164.len() - 10 } else { 1 }; // Skip the '+'
        let national_part = &e164[start_pos..];

        // Reject numbers with too many repeated digits
        let mut digit_counts = [0; 10];
        for ch in national_part.chars() {
            if let Some(digit) = ch.to_digit(10) {
                digit_counts[digit as usize] += 1;
            }
        }

        // If any digit appears more than 60% of the time, it's suspicious
        let max_count = digit_counts.iter().max().unwrap_or(&0);
        if *max_count > (national_part.len() * 6 / 10) {
            return Err(DomainError::InvalidInput(
                "Phone number appears to be invalid (too many repeated digits)".to_string(),
            ));
        }

        // Country-specific security checks
        match country {
            "US" | "CA" => {
                // Reject common invalid patterns
                if national_part.starts_with("000")
                    || national_part.starts_with("111")
                    || national_part.starts_with("555")
                {
                    return Err(DomainError::InvalidInput(
                        "Phone number appears to be invalid or test number".to_string(),
                    ));
                }
            }
            _ => {
                // Generic validation for other countries
            }
        }

        Ok(())
    }
}

impl Display for PhoneNumber {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.international_format())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_us_number() {
        let phone = PhoneNumber::parse("+12345678901").unwrap();
        assert_eq!(phone.e164(), "+12345678901");
        assert_eq!(phone.country(), "US");
        assert_eq!(phone.national_number(), "2345678901");
    }

    #[test]
    fn test_parse_with_formatting() {
        let phone = PhoneNumber::parse("+****************").unwrap();
        assert_eq!(phone.e164(), "+12345678901");

        let phone2 = PhoneNumber::parse("+44 20 7946 0958").unwrap();
        assert_eq!(phone2.e164(), "+442079460958");
        assert_eq!(phone2.country(), "GB");
    }

    #[test]
    fn test_parse_invalid() {
        assert!(PhoneNumber::parse("").is_err());
        assert!(PhoneNumber::parse("invalid").is_err());
        assert!(PhoneNumber::parse("1234567890").is_err()); // Missing +
        assert!(PhoneNumber::parse("+").is_err());
        assert!(PhoneNumber::parse("+999999999999999999").is_err()); // Too long
    }

    #[test]
    fn test_security_validation() {
        // Null byte injection
        assert!(PhoneNumber::parse("+1234567890\0").is_err());

        // Control characters
        assert!(PhoneNumber::parse("+1234567890\n").is_err());

        // Too many repeated digits
        assert!(PhoneNumber::parse("+1111111111").is_err());

        // Test numbers should be rejected
        assert!(PhoneNumber::parse("+15550100").is_err());
    }

    #[test]
    fn test_premium_rate_detection() {
        let premium_us = PhoneNumber::parse("+19001234567").unwrap();
        assert!(premium_us.is_premium_rate());
        assert!(premium_us.is_blocked());

        let normal_us = PhoneNumber::parse("+12345678901").unwrap();
        assert!(!normal_us.is_premium_rate());
    }

    #[test]
    fn test_spam_pattern_detection() {
        // Sequential digits (using a different sequential pattern that's clearer)
        let phone = PhoneNumber::parse("+19876543210").unwrap();
        assert!(phone.is_known_spam_pattern());
        assert!(phone.is_blocked());

        // Normal number
        let normal = PhoneNumber::parse("+12135551234").unwrap();
        assert!(!normal.is_known_spam_pattern());
    }

    #[test]
    fn test_international_formatting() {
        let us_phone = PhoneNumber::parse("+12345678901").unwrap();
        let formatted = us_phone.international_format();
        assert!(formatted.contains("(234)"));

        let uk_phone = PhoneNumber::parse("+442079460958").unwrap();
        let formatted_uk = uk_phone.international_format();
        assert!(formatted_uk.starts_with("+44"));
    }

    #[test]
    fn test_parse_with_country() {
        let phone = PhoneNumber::parse_with_country("2345678901", "US").unwrap();
        assert_eq!(phone.e164(), "+12345678901");
        assert_eq!(phone.country(), "US");
    }

    #[test]
    fn test_sms_support() {
        let mobile = PhoneNumber::parse("+12345678901").unwrap();
        assert!(mobile.supports_sms());
    }

    #[test]
    fn test_various_countries() {
        // Germany
        let de_phone = PhoneNumber::parse("+4930123456").unwrap();
        assert_eq!(de_phone.country(), "DE");

        // Japan
        let jp_phone = PhoneNumber::parse("+81312345678").unwrap();
        assert_eq!(jp_phone.country(), "JP");

        // Brazil
        let br_phone = PhoneNumber::parse("+5511987654321").unwrap();
        assert_eq!(br_phone.country(), "BR");
    }

    #[test]
    fn test_edge_cases() {
        // Minimum length (using a country code that allows shorter numbers)
        let short = PhoneNumber::parse("+33123456").unwrap(); // France with short number
        assert_eq!(short.e164(), "+33123456");

        // Different formatting styles
        let dotted = PhoneNumber::parse("******.567.8901").unwrap();
        assert_eq!(dotted.e164(), "+12345678901");
    }
}
