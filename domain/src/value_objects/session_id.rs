//! Session ID Value Object
//!
//! Provides a strongly-typed identifier for sessions to prevent ID confusion
//! and improve type safety across the authentication domain.

use crate::crypto::{ChaChaRandomService, SecureRandomService};
use crate::errors::DomainError;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtR<PERSON><PERSON>};

/// Strongly-typed session identifier
///
/// Sessions are represented by cryptographically secure random identifiers
/// that are generated when a new session is created. This type ensures
/// session IDs cannot be confused with other ID types.
#[derive(Debug, Clone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct SessionId(String);

impl SessionId {
    /// Generate a new cryptographically secure session ID
    ///
    /// Creates a new session ID using secure random generation suitable
    /// for authentication sessions. The ID is URL-safe and has sufficient
    /// entropy to prevent guessing attacks.
    pub fn generate() -> Self {
        let random_service = ChaChaRandomService::new();
        let token = random_service.generate_session_token();
        // Token already has sess_ prefix from the service
        SessionId(token)
    }

    /// Create a SessionId from a string (used when loading from storage)
    ///
    /// # Arguments
    ///
    /// * `id` - The session ID string from storage
    ///
    /// # Returns
    ///
    /// * `Ok(SessionId)` if the ID format is valid
    /// * `Err(DomainError)` if the format is invalid
    pub fn from_string(id: String) -> Result<Self, DomainError> {
        if id.is_empty() {
            return Err(DomainError::InvalidId(
                "SessionId cannot be empty".to_string(),
            ));
        }

        // Basic format validation
        if !id.starts_with("sess_") {
            return Err(DomainError::InvalidId(
                "SessionId must start with 'sess_'".to_string(),
            ));
        }

        // Check length - should have prefix + at least 32 chars of token
        if id.len() < 37 {
            return Err(DomainError::InvalidId("SessionId is too short".to_string()));
        }

        Ok(SessionId(id))
    }

    /// Get the session ID as a string
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

impl Display for SessionId {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_session_id() {
        let id1 = SessionId::generate();
        let id2 = SessionId::generate();

        // IDs should be different
        assert_ne!(id1, id2);

        // Should have correct prefix
        assert!(id1.as_str().starts_with("sess_"));
        assert!(id2.as_str().starts_with("sess_"));

        // Should have sufficient length
        assert!(id1.as_str().len() > 37);
        assert!(id2.as_str().len() > 37);
    }

    #[test]
    fn test_from_string_valid() {
        let id_str = "sess_abcdef1234567890abcdef1234567890abcdef12".to_string();
        let session_id = SessionId::from_string(id_str.clone()).unwrap();
        assert_eq!(session_id.as_str(), id_str);
    }

    #[test]
    fn test_from_string_invalid() {
        // Empty string
        assert!(SessionId::from_string("".to_string()).is_err());

        // Wrong prefix
        assert!(SessionId::from_string("user_123456".to_string()).is_err());

        // Too short
        assert!(SessionId::from_string("sess_123".to_string()).is_err());
    }

    #[test]
    fn test_display() {
        let id = SessionId::generate();
        let display_str = format!("{}", id);
        assert_eq!(display_str, id.as_str());
    }

    #[test]
    fn test_ordering() {
        let id1 = SessionId::from_string("sess_aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa".to_string())
            .unwrap();
        let id2 = SessionId::from_string("sess_bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb".to_string())
            .unwrap();

        assert!(id1 < id2);
    }
}
