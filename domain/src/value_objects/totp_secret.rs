//! TOTP Secret Value Object
//!
//! Provides secure handling of TOTP (Time-based One-Time Password) secrets
//! for multi-factor authentication. Includes secure memory management,
//! validation, and encoding/decoding capabilities.

use crate::crypto::{ChaChaRandomService, SecureRandomService};
use crate::errors::DomainError;
use std::fmt::{<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, Result as FmtResult};

/// TOTP Secret value object with secure memory handling
///
/// Stores TOTP secrets with automatic memory zeroing on drop.
/// Provides base32 encoding/decoding and validation for RFC 6238
/// compliance. Secrets are kept in memory only as long as necessary.
pub struct TotpSecret {
    /// The raw secret bytes (will be zeroed on drop)
    secret_bytes: Vec<u8>,
}

impl TotpSecret {
    /// Generate a new cryptographically secure TOTP secret
    ///
    /// Creates a 160-bit (20 byte) secret suitable for TOTP authentication.
    /// This meets RFC 6238 recommendations for secret entropy.
    ///
    /// # Returns
    ///
    /// A new TotpSecret with cryptographically secure random bytes
    pub fn generate() -> Self {
        // RFC 6238 recommends at least 128 bits, we use 160 bits (20 bytes)
        let random_service = ChaChaRandomService::new();
        let secret_bytes = random_service.generate_secure_bytes(20);

        TotpSecret { secret_bytes }
    }

    /// Create a TotpSecret from raw bytes
    ///
    /// # Arguments
    ///
    /// * `bytes` - The secret bytes (should be at least 16 bytes)
    ///
    /// # Returns
    ///
    /// * `Ok(TotpSecret)` if the bytes are valid
    /// * `Err(DomainError)` if validation fails
    pub fn from_bytes(bytes: Vec<u8>) -> Result<Self, DomainError> {
        if bytes.is_empty() {
            return Err(DomainError::InvalidInput(
                "TOTP secret cannot be empty".to_string(),
            ));
        }

        if bytes.len() < 16 {
            return Err(DomainError::InvalidInput(
                "TOTP secret must be at least 16 bytes (128 bits)".to_string(),
            ));
        }

        if bytes.len() > 64 {
            return Err(DomainError::InvalidInput(
                "TOTP secret cannot exceed 64 bytes (512 bits)".to_string(),
            ));
        }

        Ok(TotpSecret {
            secret_bytes: bytes,
        })
    }

    /// Create a TotpSecret from a base32 encoded string
    ///
    /// # Arguments
    ///
    /// * `base32_str` - The base32 encoded secret string
    ///
    /// # Returns
    ///
    /// * `Ok(TotpSecret)` if the encoding is valid
    /// * `Err(DomainError)` if the format is invalid
    pub fn from_base32(base32_str: &str) -> Result<Self, DomainError> {
        if base32_str.is_empty() {
            return Err(DomainError::InvalidInput(
                "Base32 string cannot be empty".to_string(),
            ));
        }

        // Clean whitespace first
        let cleaned = base32_str.replace([' ', '\n', '\r', '\t'], "");

        // Security: Check for null bytes or other dangerous control characters
        if cleaned.contains('\0') || cleaned.chars().any(|c| c.is_control()) {
            return Err(DomainError::InvalidInput(
                "Base32 string contains invalid characters".to_string(),
            ));
        }
        let decoded = Self::decode_base32(&cleaned)?;

        Self::from_bytes(decoded)
    }

    /// Get the secret as base32 encoded string
    ///
    /// Returns the secret in RFC 4648 base32 encoding, which is
    /// the standard format for TOTP secrets in QR codes and
    /// authenticator apps.
    pub fn to_base32(&self) -> String {
        Self::encode_base32(&self.secret_bytes)
    }

    /// Get the raw secret bytes
    ///
    /// # Security Warning
    ///
    /// This exposes the raw secret bytes. Use with caution and
    /// ensure the returned slice is not stored long-term.
    pub fn as_bytes(&self) -> &[u8] {
        &self.secret_bytes
    }

    /// Get the length of the secret in bytes
    pub fn len(&self) -> usize {
        self.secret_bytes.len()
    }

    /// Check if the secret is empty (should never happen after validation)
    pub fn is_empty(&self) -> bool {
        self.secret_bytes.is_empty()
    }

    /// Generate a TOTP URL for QR code generation
    ///
    /// Creates an otpauth:// URL that can be encoded in a QR code
    /// for easy setup in authenticator apps.
    ///
    /// # Arguments
    ///
    /// * `account_name` - The account identifier (usually email)
    /// * `issuer` - The service name
    ///
    /// # Returns
    ///
    /// A properly formatted otpauth:// URL
    pub fn to_qr_url(&self, account_name: &str, issuer: &str) -> Result<String, DomainError> {
        if account_name.is_empty() {
            return Err(DomainError::InvalidInput(
                "Account name cannot be empty".to_string(),
            ));
        }

        if issuer.is_empty() {
            return Err(DomainError::InvalidInput(
                "Issuer cannot be empty".to_string(),
            ));
        }

        // URL encode the components to prevent injection
        let encoded_account = urlencoding::encode(account_name);
        let encoded_issuer = urlencoding::encode(issuer);

        Ok(format!(
            "otpauth://totp/{}:{}?secret={}&issuer={}",
            encoded_issuer,
            encoded_account,
            self.to_base32(),
            encoded_issuer
        ))
    }

    /// Validate that this secret can generate valid TOTP codes
    ///
    /// Performs basic validation to ensure the secret is suitable
    /// for TOTP generation.
    pub fn validate(&self) -> Result<(), DomainError> {
        if self.secret_bytes.len() < 16 {
            return Err(DomainError::InvalidInput(
                "TOTP secret too short for secure operation".to_string(),
            ));
        }

        // Check for obviously weak secrets (all zeros, all same byte, etc.)
        let first_byte = self.secret_bytes[0];
        let all_same = self.secret_bytes.iter().all(|&b| b == first_byte);

        if all_same {
            return Err(DomainError::InvalidInput(
                "TOTP secret appears to be weak (all bytes identical)".to_string(),
            ));
        }

        // Check for sufficient entropy (very basic check)
        let unique_bytes: std::collections::HashSet<_> = self.secret_bytes.iter().collect();
        if unique_bytes.len() < 4 {
            return Err(DomainError::InvalidInput(
                "TOTP secret appears to have insufficient entropy".to_string(),
            ));
        }

        Ok(())
    }

    /// Encode bytes to base32
    fn encode_base32(data: &[u8]) -> String {
        const ALPHABET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        let mut result = String::new();

        let mut bits = 0u64;
        let mut bit_count = 0;

        for &byte in data {
            bits = (bits << 8) | (byte as u64);
            bit_count += 8;

            while bit_count >= 5 {
                let index = ((bits >> (bit_count - 5)) & 0x1F) as usize;
                result.push(ALPHABET[index] as char);
                bit_count -= 5;
            }
        }

        if bit_count > 0 {
            let index = ((bits << (5 - bit_count)) & 0x1F) as usize;
            result.push(ALPHABET[index] as char);
        }

        result
    }

    /// Decode base32 to bytes
    fn decode_base32(encoded: &str) -> Result<Vec<u8>, DomainError> {
        let mut result = Vec::new();
        let mut bits = 0u64;
        let mut bit_count = 0;

        for ch in encoded.chars() {
            let value = match ch {
                'A'..='Z' => (ch as u8) - b'A',
                '2'..='7' => (ch as u8) - b'2' + 26,
                _ => {
                    return Err(DomainError::InvalidInput(format!(
                        "Invalid base32 character: {}",
                        ch
                    )));
                }
            } as u64;

            bits = (bits << 5) | value;
            bit_count += 5;

            if bit_count >= 8 {
                result.push(((bits >> (bit_count - 8)) & 0xFF) as u8);
                bit_count -= 8;
            }
        }

        Ok(result)
    }
}

impl Drop for TotpSecret {
    /// Securely zero the secret bytes when dropped
    fn drop(&mut self) {
        // Zero out the secret bytes for security
        for byte in &mut self.secret_bytes {
            *byte = 0;
        }
    }
}

impl Debug for TotpSecret {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        f.debug_struct("TotpSecret")
            .field("length", &self.secret_bytes.len())
            .field("secret", &"[REDACTED]")
            .finish()
    }
}

impl Clone for TotpSecret {
    fn clone(&self) -> Self {
        TotpSecret {
            secret_bytes: self.secret_bytes.clone(),
        }
    }
}

impl PartialEq for TotpSecret {
    fn eq(&self, other: &Self) -> bool {
        use crate::crypto::{ConstantTimeComparison, ConstantTimeService};
        let ct_service = ConstantTimeService::new();
        ct_service.constant_time_compare_bytes(&self.secret_bytes, &other.secret_bytes)
    }
}

impl Eq for TotpSecret {}

// Simple URL encoding for QR code generation
mod urlencoding {
    pub fn encode(input: &str) -> String {
        input
            .chars()
            .map(|c| match c {
                'A'..='Z' | 'a'..='z' | '0'..='9' | '-' | '_' | '.' | '~' => c.to_string(),
                _ => format!("%{:02X}", c as u8),
            })
            .collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_secret() {
        let secret1 = TotpSecret::generate();
        let secret2 = TotpSecret::generate();

        // Secrets should be different
        assert_ne!(secret1, secret2);

        // Should have correct length (20 bytes)
        assert_eq!(secret1.len(), 20);
        assert_eq!(secret2.len(), 20);

        // Should validate successfully
        assert!(secret1.validate().is_ok());
        assert!(secret2.validate().is_ok());
    }

    #[test]
    fn test_from_bytes_valid() {
        let bytes = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];
        let secret = TotpSecret::from_bytes(bytes.clone()).unwrap();

        assert_eq!(secret.as_bytes(), &bytes);
        assert_eq!(secret.len(), 16);
    }

    #[test]
    fn test_from_bytes_invalid() {
        // Empty bytes
        assert!(TotpSecret::from_bytes(vec![]).is_err());

        // Too short
        assert!(TotpSecret::from_bytes(vec![1, 2, 3]).is_err());

        // Too long
        let long_bytes = vec![0u8; 65];
        assert!(TotpSecret::from_bytes(long_bytes).is_err());
    }

    #[test]
    fn test_base32_encoding_decoding() {
        let secret = TotpSecret::generate();
        let base32 = secret.to_base32();

        // Should be valid base32
        assert!(!base32.is_empty());
        assert!(
            base32
                .chars()
                .all(|c| "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567".contains(c))
        );

        // Should round-trip correctly
        let decoded = TotpSecret::from_base32(&base32).unwrap();
        assert_eq!(secret, decoded);
    }

    #[test]
    fn test_base32_with_whitespace() {
        let secret = TotpSecret::generate();
        let base32 = secret.to_base32();

        // Add various whitespace
        let with_spaces = format!("{} {} ", &base32[..8], &base32[8..]);
        let with_newlines = format!("{}\n{}", &base32[..8], &base32[8..]);

        // Should still decode correctly
        let decoded1 = TotpSecret::from_base32(&with_spaces).unwrap();
        let decoded2 = TotpSecret::from_base32(&with_newlines).unwrap();

        assert_eq!(secret, decoded1);
        assert_eq!(secret, decoded2);
    }

    #[test]
    fn test_base32_invalid() {
        // Invalid characters
        assert!(TotpSecret::from_base32("INVALID8").is_err());
        assert!(TotpSecret::from_base32("ABC@DEF").is_err());

        // Control characters
        assert!(TotpSecret::from_base32("ABCD\0EFG").is_err());
        assert!(TotpSecret::from_base32("ABCD\nEFG\r").is_err());

        // Empty string
        assert!(TotpSecret::from_base32("").is_err());
    }

    #[test]
    fn test_qr_url_generation() {
        let secret = TotpSecret::generate();
        let url = secret.to_qr_url("<EMAIL>", "TestApp").unwrap();

        assert!(url.starts_with("otpauth://totp/"));
        assert!(url.contains("user%40example.com")); // URL encoded @
        assert!(url.contains("TestApp"));
        assert!(url.contains("secret="));
        assert!(url.contains("issuer=TestApp"));
    }

    #[test]
    fn test_qr_url_validation() {
        let secret = TotpSecret::generate();

        // Empty account name
        assert!(secret.to_qr_url("", "TestApp").is_err());

        // Empty issuer
        assert!(secret.to_qr_url("<EMAIL>", "").is_err());
    }

    #[test]
    fn test_qr_url_encoding() {
        let secret = TotpSecret::generate();

        // Test special characters that need encoding
        let url = secret
            .to_qr_url("<EMAIL>", "Test App")
            .unwrap();
        assert!(url.contains("user%2Btest%40example.com"));
        assert!(url.contains("Test%20App"));
    }

    #[test]
    fn test_validation() {
        let secret = TotpSecret::generate();
        assert!(secret.validate().is_ok());

        // Weak secret (all zeros)
        let weak_bytes = vec![0u8; 20];
        let weak_secret = TotpSecret::from_bytes(weak_bytes).unwrap();
        assert!(weak_secret.validate().is_err());

        // Low entropy secret
        let low_entropy = vec![1, 1, 1, 1, 2, 2, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2];
        let low_entropy_secret = TotpSecret::from_bytes(low_entropy).unwrap();
        assert!(low_entropy_secret.validate().is_err());
    }

    #[test]
    fn test_secure_memory_handling() {
        let _secret = {
            let mut bytes = vec![0xAAu8; 20];
            let secret = TotpSecret::from_bytes(bytes.clone()).unwrap();

            // Verify the secret contains the expected bytes
            assert_eq!(secret.as_bytes(), &bytes);

            // Modify original bytes to verify independence
            bytes.fill(0xFF);
            assert_ne!(secret.as_bytes(), &bytes);

            secret
        }; // secret goes out of scope here, triggering Drop

        // Note: We can't directly test that memory was zeroed since
        // the secret is dropped, but the Drop implementation ensures it
    }

    #[test]
    fn test_debug_output() {
        let secret = TotpSecret::generate();
        let debug_str = format!("{:?}", secret);

        // Should not contain the actual secret
        assert!(debug_str.contains("REDACTED"));
        assert!(debug_str.contains("TotpSecret"));
        assert!(!debug_str.contains(&secret.to_base32()));
    }

    #[test]
    fn test_constant_time_comparison() {
        let secret1 = TotpSecret::generate();
        let secret2 = secret1.clone();
        let secret3 = TotpSecret::generate();

        // Same secrets should be equal
        assert_eq!(secret1, secret2);

        // Different secrets should not be equal
        assert_ne!(secret1, secret3);

        // Comparison should be constant-time (we can't easily test timing,
        // but the implementation uses constant_time_compare_bytes)
    }

    #[test]
    fn test_known_base32_vectors() {
        // Test with known vectors
        let test_cases = vec![(
            vec![
                0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x21, 0xde, 0xad, 0xbe, 0xef, 0x48, 0x65, 0x6c, 0x6c,
                0x6f, 0x21,
            ],
            "JBSWY3DPEHPK3PXP",
        )];

        for (bytes, expected_base32) in test_cases {
            let secret = TotpSecret::from_bytes(bytes).unwrap();
            let base32 = secret.to_base32();
            assert!(base32.starts_with(expected_base32));

            // Should round-trip
            let decoded = TotpSecret::from_base32(&base32).unwrap();
            assert_eq!(secret, decoded);
        }
    }
}
