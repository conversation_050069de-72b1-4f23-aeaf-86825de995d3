// Repository traits module
// Defines persistence interfaces (ports) for domain entities

pub mod audit_log_repository;
pub mod role_repository;
pub mod session_repository;
pub mod user_repository;

pub use audit_log_repository::{
    AuditFailureStats, AuditLogRepository, AuditLogSearchCriteria, AuditLogSearchResult,
    AuditLogSortField, SortDirection as AuditSortDirection,
};
pub use role_repository::RoleRepository;
pub use session_repository::SessionRepository;
pub use user_repository::{
    SortDirection, UserRepository, UserSearchCriteria, UserSearchResult, UserSortField,
};
