// User repository trait
// Defines the persistence interface for User entities

use crate::entities::User;
use crate::errors::DomainError;
use crate::value_objects::{Email, UserId};

/// Repository trait for User aggregate
/// This is a port in hexagonal architecture - implementations are adapters
#[async_trait::async_trait]
pub trait UserRepository: Send + Sync {
    /// Save a new user or update an existing one
    async fn save(&self, user: &User) -> Result<(), DomainError>;

    /// Find a user by their ID
    async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError>;

    /// Find a user by their email address
    async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError>;

    /// Find a user by their username
    async fn find_by_username(&self, username: &str) -> Result<Option<User>, DomainError>;

    /// Check if a user exists with the given email
    async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError>;

    /// Delete a user by their ID
    async fn delete(&self, id: &UserId) -> Result<(), DomainError>;

    /// Get all users (paginated)
    async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError>;

    /// Count total number of users
    async fn count(&self) -> Result<usize, DomainError>;

    /// Find users created within a date range
    async fn find_by_created_date_range(
        &self,
        start: std::time::SystemTime,
        end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError>;

    /// Find users by verification status
    async fn find_by_verification_status(
        &self,
        is_verified: bool,
    ) -> Result<Vec<User>, DomainError>;

    /// Find users by active status
    async fn find_by_active_status(&self, is_active: bool) -> Result<Vec<User>, DomainError>;

    /// Search users with complex filtering and pagination
    /// For admin use cases requiring comprehensive user search
    async fn search_users(
        &self,
        criteria: &UserSearchCriteria,
    ) -> Result<UserSearchResult, DomainError>;
}

/// Comprehensive search criteria for user queries
#[derive(Debug, Clone)]
pub struct UserSearchCriteria {
    /// Email filter (partial match)
    pub email_filter: Option<String>,
    /// Name filter (partial match on first or last name)
    pub name_filter: Option<String>,
    /// Username filter (partial match)
    pub username_filter: Option<String>,
    /// Role filter (exact match)
    pub role_filter: Option<String>,
    /// Active status filter
    pub status_filter: Option<bool>,
    /// Verification status filter
    pub verification_filter: Option<bool>,
    /// Created date range filter
    pub created_after: Option<std::time::SystemTime>,
    pub created_before: Option<std::time::SystemTime>,
    /// Last login date range filter
    pub last_login_after: Option<std::time::SystemTime>,
    pub last_login_before: Option<std::time::SystemTime>,
    /// Pagination parameters
    pub page: usize,
    pub page_size: usize,
    /// Sort field and direction
    pub sort_by: Option<UserSortField>,
    pub sort_direction: Option<SortDirection>,
}

/// Fields available for sorting user search results
#[derive(Debug, Clone, Copy)]
pub enum UserSortField {
    Email,
    Username,
    FirstName,
    LastName,
    CreatedAt,
    LastLoginAt,
}

/// Sort direction for user search results
#[derive(Debug, Clone, Copy)]
pub enum SortDirection {
    Ascending,
    Descending,
}

/// Search result with pagination metadata
#[derive(Debug, Clone)]
pub struct UserSearchResult {
    pub users: Vec<User>,
    pub total_count: usize,
    pub page: usize,
    pub page_size: usize,
    pub has_next: bool,
    pub has_previous: bool,
}

impl UserSearchResult {
    pub fn new(users: Vec<User>, total_count: usize, page: usize, page_size: usize) -> Self {
        let has_next = (page + 1) * page_size < total_count;
        let has_previous = page > 0;

        Self {
            users,
            total_count,
            page,
            page_size,
            has_next,
            has_previous,
        }
    }
}
