// Repository trait for audit log access
// Provides methods for querying audit logs with performance optimization

use crate::entities::audit_log::{AuditAction, AuditLogEntry, EntityType};
use crate::errors::DomainError;
use crate::value_objects::UserId;
use async_trait::async_trait;
use std::time::SystemTime;

/// Search criteria for audit log queries
/// Mandatory time range for performance optimization
#[derive(Debug, Clone)]
pub struct AuditLogSearchCriteria {
    /// Mandatory start time (for performance - must be indexed)
    pub start_time: SystemTime,

    /// Mandatory end time (for performance - must be indexed)  
    pub end_time: SystemTime,

    /// Filter by entity type
    pub entity_type: Option<EntityType>,

    /// Filter by specific entity ID
    pub entity_id: Option<String>,

    /// Filter by user who performed action
    pub user_id: Option<UserId>,

    /// Filter by specific audit actions
    pub actions: Vec<AuditAction>,

    /// Filter by success/failure status
    pub success_only: Option<bool>,

    /// Filter by security-sensitive actions only
    pub security_sensitive_only: bool,

    /// Pagination offset
    pub page: usize,

    /// Number of items per page (max 100)
    pub page_size: usize,

    /// Sort field
    pub sort_by: AuditLogSortField,

    /// Sort direction
    pub sort_direction: SortDirection,
}

impl AuditLogSearchCriteria {
    /// Create new search criteria with mandatory time range
    pub fn new(start_time: SystemTime, end_time: SystemTime) -> Self {
        Self {
            start_time,
            end_time,
            entity_type: None,
            entity_id: None,
            user_id: None,
            actions: Vec::new(),
            success_only: None,
            security_sensitive_only: false,
            page: 0,
            page_size: 20,
            sort_by: AuditLogSortField::Timestamp,
            sort_direction: SortDirection::Descending,
        }
    }

    /// Validate time range is reasonable for performance
    pub fn validate_time_range(&self) -> Result<(), DomainError> {
        if self.end_time <= self.start_time {
            return Err(DomainError::InvalidInput(
                "End time must be after start time".to_string(),
            ));
        }

        let duration = self
            .end_time
            .duration_since(self.start_time)
            .map_err(|_| DomainError::InvalidInput("Invalid time range".to_string()))?;

        // Maximum 90 days for performance
        if duration.as_secs() > 90 * 24 * 60 * 60 {
            return Err(DomainError::InvalidInput(
                "Time range cannot exceed 90 days".to_string(),
            ));
        }

        Ok(())
    }

    /// Get time range duration in seconds
    pub fn duration_seconds(&self) -> Result<u64, DomainError> {
        self.end_time
            .duration_since(self.start_time)
            .map(|d| d.as_secs())
            .map_err(|_| DomainError::InvalidInput("Invalid time range".to_string()))
    }
}

/// Fields available for sorting audit log results
#[derive(Debug, Clone, PartialEq)]
pub enum AuditLogSortField {
    Timestamp,
    UserId,
    Action,
    EntityType,
    EntityId,
    Success,
}

/// Sort direction for queries
#[derive(Debug, Clone, PartialEq)]
pub enum SortDirection {
    Ascending,
    Descending,
}

/// Result of audit log search with pagination metadata
#[derive(Debug, Clone)]
pub struct AuditLogSearchResult {
    /// Matching audit log entries
    pub entries: Vec<AuditLogEntry>,

    /// Total number of matching entries (for pagination)
    pub total_count: usize,

    /// Current page number (0-based)
    pub page: usize,

    /// Number of items per page
    pub page_size: usize,

    /// Whether there are more pages after this one
    pub has_next: bool,

    /// Whether there are previous pages before this one
    pub has_previous: bool,
}

impl AuditLogSearchResult {
    pub fn new(
        entries: Vec<AuditLogEntry>,
        total_count: usize,
        page: usize,
        page_size: usize,
    ) -> Self {
        let has_next = (page + 1) * page_size < total_count;
        let has_previous = page > 0;

        Self {
            entries,
            total_count,
            page,
            page_size,
            has_next,
            has_previous,
        }
    }
}

/// Repository trait for audit log persistence and querying
#[async_trait]
pub trait AuditLogRepository: Send + Sync {
    /// Store a new audit log entry
    async fn save(&self, entry: &AuditLogEntry) -> Result<(), DomainError>;

    /// Search audit logs with comprehensive filtering and pagination
    /// Performance target: <40ms for indexed queries
    async fn search(
        &self,
        criteria: &AuditLogSearchCriteria,
    ) -> Result<AuditLogSearchResult, DomainError>;

    /// Get a specific audit log entry by ID
    async fn find_by_id(&self, id: &str) -> Result<Option<AuditLogEntry>, DomainError>;

    /// Get recent audit entries for a specific user
    /// Limited to last 30 days for performance
    async fn find_recent_by_user(
        &self,
        user_id: &UserId,
        limit: usize,
    ) -> Result<Vec<AuditLogEntry>, DomainError>;

    /// Get recent security-sensitive entries
    /// Limited to last 7 days for performance
    async fn find_recent_security_events(
        &self,
        limit: usize,
    ) -> Result<Vec<AuditLogEntry>, DomainError>;

    /// Get entries for a specific entity within time range
    async fn find_by_entity(
        &self,
        entity_type: EntityType,
        entity_id: &str,
        start_time: SystemTime,
        end_time: SystemTime,
    ) -> Result<Vec<AuditLogEntry>, DomainError>;

    /// Count total audit entries within time range (for analytics)
    async fn count_by_time_range(
        &self,
        start_time: SystemTime,
        end_time: SystemTime,
    ) -> Result<usize, DomainError>;

    /// Get failure statistics for monitoring
    async fn get_failure_stats(
        &self,
        start_time: SystemTime,
        end_time: SystemTime,
    ) -> Result<AuditFailureStats, DomainError>;

    /// Clean up old audit entries (for data retention)
    /// Should be used with caution and proper authorization
    async fn delete_older_than(&self, cutoff_time: SystemTime) -> Result<usize, DomainError>;
}

/// Statistics about audit failures for monitoring
#[derive(Debug, Clone)]
pub struct AuditFailureStats {
    pub total_failures: usize,
    pub security_failures: usize,
    pub authentication_failures: usize,
    pub authorization_failures: usize,
    pub most_common_failure_action: Option<AuditAction>,
    pub most_common_failure_entity: Option<EntityType>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_audit_log_search_criteria_creation() {
        let start = SystemTime::now() - Duration::from_secs(3600);
        let end = SystemTime::now();

        let criteria = AuditLogSearchCriteria::new(start, end);

        assert_eq!(criteria.start_time, start);
        assert_eq!(criteria.end_time, end);
        assert_eq!(criteria.page, 0);
        assert_eq!(criteria.page_size, 20);
        assert_eq!(criteria.sort_by, AuditLogSortField::Timestamp);
        assert_eq!(criteria.sort_direction, SortDirection::Descending);
        assert!(!criteria.security_sensitive_only);
    }

    #[test]
    fn test_time_range_validation_valid() {
        let start = SystemTime::now() - Duration::from_secs(3600);
        let end = SystemTime::now();

        let criteria = AuditLogSearchCriteria::new(start, end);
        assert!(criteria.validate_time_range().is_ok());
    }

    #[test]
    fn test_time_range_validation_invalid_order() {
        let start = SystemTime::now();
        let end = SystemTime::now() - Duration::from_secs(3600);

        let criteria = AuditLogSearchCriteria::new(start, end);
        assert!(criteria.validate_time_range().is_err());
    }

    #[test]
    fn test_time_range_validation_too_long() {
        let start = SystemTime::now() - Duration::from_secs(100 * 24 * 60 * 60); // 100 days
        let end = SystemTime::now();

        let criteria = AuditLogSearchCriteria::new(start, end);
        assert!(criteria.validate_time_range().is_err());
    }

    #[test]
    fn test_time_range_validation_90_days_boundary() {
        let start = SystemTime::now() - Duration::from_secs(90 * 24 * 60 * 60); // Exactly 90 days
        let end = SystemTime::now();

        let criteria = AuditLogSearchCriteria::new(start, end);
        assert!(criteria.validate_time_range().is_ok());
    }

    #[test]
    fn test_duration_seconds() {
        let start = SystemTime::now() - Duration::from_secs(7200);
        let end = SystemTime::now();

        let criteria = AuditLogSearchCriteria::new(start, end);
        let duration = criteria.duration_seconds().unwrap();

        // Should be approximately 7200 seconds (allow some variance for test execution time)
        assert!((7199..=7201).contains(&duration));
    }

    #[test]
    fn test_audit_log_search_result_pagination() {
        let entries = vec![];
        let result = AuditLogSearchResult::new(entries, 50, 2, 10);

        assert_eq!(result.total_count, 50);
        assert_eq!(result.page, 2);
        assert_eq!(result.page_size, 10);
        assert!(result.has_next); // (2+1)*10 = 30 < 50
        assert!(result.has_previous); // page 2 > 0
    }

    #[test]
    fn test_audit_log_search_result_first_page() {
        let entries = vec![];
        let result = AuditLogSearchResult::new(entries, 25, 0, 10);

        assert!(!result.has_previous);
        assert!(result.has_next); // (0+1)*10 = 10 < 25
    }

    #[test]
    fn test_audit_log_search_result_last_page() {
        let entries = vec![];
        let result = AuditLogSearchResult::new(entries, 25, 2, 10);

        assert!(result.has_previous);
        assert!(!result.has_next); // (2+1)*10 = 30 >= 25
    }
}
