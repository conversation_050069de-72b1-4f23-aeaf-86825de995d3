// Role repository trait
// Defines the persistence interface for Role entities

use crate::entities::Role;
use crate::errors::DomainError;
use crate::value_objects::{RoleId, UserId};

/// Repository trait for Role entity
/// This is a port in hexagonal architecture - implementations are adapters
#[async_trait::async_trait]
pub trait RoleRepository: Send + Sync {
    /// Save a new role or update an existing one
    async fn save(&self, role: &Role) -> Result<(), DomainError>;

    /// Find a role by its ID
    async fn find_by_id(&self, id: &RoleId) -> Result<Option<Role>, DomainError>;

    /// Find a role by its name
    async fn find_by_name(&self, name: &str) -> Result<Option<Role>, DomainError>;

    /// Check if a role exists with the given name
    async fn exists_by_name(&self, name: &str) -> Result<bool, DomainError>;

    /// Delete a role by its ID
    async fn delete(&self, id: &RoleId) -> Result<(), DomainError>;

    /// Get all roles (paginated)
    async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<Role>, DomainError>;

    /// Count total number of roles
    async fn count(&self) -> Result<usize, DomainError>;

    /// Find all active roles
    async fn find_active(&self) -> Result<Vec<Role>, DomainError>;

    /// Find roles assigned to a specific user
    async fn find_by_user(&self, user_id: &UserId) -> Result<Vec<Role>, DomainError>;

    /// Find roles that have a specific permission
    async fn find_by_permission(&self, permission: &str) -> Result<Vec<Role>, DomainError>;

    /// Find child roles of a parent role
    async fn find_children(&self, parent_id: &RoleId) -> Result<Vec<Role>, DomainError>;

    /// Get the complete role hierarchy for permission inheritance
    async fn get_hierarchy(&self) -> Result<Vec<Role>, DomainError>;
}
