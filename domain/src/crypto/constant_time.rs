//! Constant-time comparison operations for timing attack prevention
//!
//! This module provides constant-time comparison functions using the
//! subtle crate to prevent timing-based side-channel attacks.

use super::traits::ConstantTimeComparison;

/// Constant-time comparison service
///
/// Provides timing-attack resistant comparison operations using
/// the audited subtle crate.
pub struct ConstantTimeService;

impl ConstantTimeService {
    /// Create a new constant-time comparison service
    pub fn new() -> Self {
        Self
    }
}

impl Default for ConstantTimeService {
    fn default() -> Self {
        Self::new()
    }
}

impl ConstantTimeComparison for ConstantTimeService {
    fn constant_time_compare_bytes(&self, a: &[u8], b: &[u8]) -> bool {
        // SECURITY CRITICAL: This implementation MUST be constant-time to prevent
        // timing attacks. The subtle crate provides audited constant-time operations.
        //
        // Strategy:
        // 1. Use subtle's ct_eq for same-length comparisons (already constant-time)
        // 2. For different lengths, we must avoid early returns and branches
        // 3. Always perform a fixed amount of work based on a reasonable maximum

        use subtle::ConstantTimeEq;

        // To avoid timing leaks from different-length inputs, we'll:
        // 1. Pad both inputs to a fixed maximum length
        // 2. Compare the padded versions
        // 3. Also compare the lengths separately

        // Define a reasonable maximum length for constant-time operations
        // This should be larger than typical tokens/passwords but not excessive
        const MAX_COMPARE_LEN: usize = 256;

        // If either input exceeds our maximum, we need a different approach
        if a.len() > MAX_COMPARE_LEN || b.len() > MAX_COMPARE_LEN {
            // For very long inputs, fall back to the subtle crate's implementation
            // with length check first (still constant-time for same lengths)
            return a.ct_eq(b).into();
        }

        // Create fixed-size buffers for constant-time comparison
        let mut buf_a = [0u8; MAX_COMPARE_LEN];
        let mut buf_b = [0u8; MAX_COMPARE_LEN];

        // Copy the input data to our buffers
        // This is safe because we checked the length above
        buf_a[..a.len()].copy_from_slice(a);
        buf_b[..b.len()].copy_from_slice(b);

        // Store lengths for comparison (also needs to be constant-time)
        let len_a = a.len();
        let len_b = b.len();

        // Perform constant-time comparison on the full buffers
        // This always compares exactly MAX_COMPARE_LEN bytes
        let buffers_match = buf_a.ct_eq(&buf_b);

        // Also check that lengths match (constant-time)
        let lengths_match = len_a.ct_eq(&len_b);

        // Both conditions must be true (constant-time AND)
        let result = buffers_match & lengths_match;

        // Prevent compiler optimizations with black_box
        std::hint::black_box(&buf_a);
        std::hint::black_box(&buf_b);

        // Convert Choice to bool
        result.into()
    }

    fn constant_time_compare_str(&self, a: &str, b: &str) -> bool {
        self.constant_time_compare_bytes(a.as_bytes(), b.as_bytes())
    }
}

/// Extended constant-time operations
pub struct ExtendedConstantTimeService {
    base: ConstantTimeService,
}

impl ExtendedConstantTimeService {
    /// Create a new extended constant-time service
    pub fn new() -> Self {
        Self {
            base: ConstantTimeService::new(),
        }
    }

    /// Select between two values in constant time
    ///
    /// Returns `a` if `choice` is true, `b` otherwise.
    /// The selection is performed without branching.
    pub fn constant_time_select<T: Copy>(&self, choice: bool, a: T, b: T) -> T {
        // SECURITY: This must be implemented without branches to prevent timing attacks
        // We use black_box to prevent compiler optimizations that might introduce branches

        // Process both values to ensure constant time
        let val_a = std::hint::black_box(a);
        let val_b = std::hint::black_box(b);

        // For primitive types, we can't use subtle's ConditionallySelectable directly
        // So we ensure both values are processed and rely on black_box to prevent
        // optimization. In production, you'd want type-specific implementations.

        // This still has a branch, but the black_box operations above ensure
        // consistent timing by preventing optimization
        if std::hint::black_box(choice) {
            val_a
        } else {
            val_b
        }
    }

    /// Compare with a minimum time guarantee
    ///
    /// Ensures comparison takes at least the specified duration
    /// to further obscure timing information.
    pub fn compare_with_min_time(
        &self,
        a: &[u8],
        b: &[u8],
        min_duration: std::time::Duration,
    ) -> bool {
        let start = std::time::Instant::now();

        let result = self.base.constant_time_compare_bytes(a, b);

        // Ensure minimum time elapsed
        while start.elapsed() < min_duration {
            std::hint::black_box(&result);
        }

        result
    }
}

impl Default for ExtendedConstantTimeService {
    fn default() -> Self {
        Self::new()
    }
}

impl ConstantTimeComparison for ExtendedConstantTimeService {
    fn constant_time_compare_bytes(&self, a: &[u8], b: &[u8]) -> bool {
        self.base.constant_time_compare_bytes(a, b)
    }

    fn constant_time_compare_str(&self, a: &str, b: &str) -> bool {
        self.base.constant_time_compare_str(a, b)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Instant;

    #[test]
    fn test_constant_time_bytes_comparison() {
        let service = ConstantTimeService::new();

        // Equal bytes
        let a = b"hello world";
        let b = b"hello world";
        assert!(service.constant_time_compare_bytes(a, b));

        // Different bytes
        let a = b"hello world";
        let b = b"hello worle";
        assert!(!service.constant_time_compare_bytes(a, b));

        // Different lengths
        let a = b"hello";
        let b = b"hello world";
        assert!(!service.constant_time_compare_bytes(a, b));

        // Empty slices
        let a = b"";
        let b = b"";
        assert!(service.constant_time_compare_bytes(a, b));
    }

    #[test]
    fn test_constant_time_string_comparison() {
        let service = ConstantTimeService::new();

        // Equal strings
        assert!(service.constant_time_compare_str("test", "test"));

        // Different strings
        assert!(!service.constant_time_compare_str("test", "fail"));

        // Different lengths
        assert!(!service.constant_time_compare_str("test", "testing"));

        // Unicode strings
        assert!(service.constant_time_compare_str("🔐", "🔐"));
        assert!(!service.constant_time_compare_str("🔐", "🔑"));
    }

    #[test]
    fn test_timing_consistency() {
        let service = ConstantTimeService::new();

        // Test data with varying similarity
        let test_cases: &[(&[u8], &[u8])] = &[
            (b"aaaaaaaaaaaaaaaaaaaa", b"aaaaaaaaaaaaaaaaaaaa"), // Equal
            (b"aaaaaaaaaaaaaaaaaaaa", b"baaaaaaaaaaaaaaaaaaa"), // First byte different
            (b"aaaaaaaaaaaaaaaaaaaa", b"aaaaaaaaaaaaaaaaaaba"), // Last byte different
            (b"aaaaaaaaaaaaaaaaaaaa", b"bbbbbbbbbbbbbbbbbbbb"), // All different
            (b"aaaaaaaaaaaaaaaaaaaa", b"aaaa"),                 // Different length
        ];

        // Warm up to reduce variance from cold CPU/cache
        for _ in 0..100 {
            for (a, b) in test_cases {
                let _ = service.constant_time_compare_bytes(a, b);
            }
        }

        // Collect multiple samples for each test case to get stable measurements
        let mut all_timings = Vec::new();

        for _ in 0..10 {
            // Multiple rounds
            for (a, b) in test_cases {
                let start = Instant::now();
                let _ = service.constant_time_compare_bytes(a, b);
                all_timings.push(start.elapsed().as_nanos() as f64);
            }
        }

        // Sort and use percentiles to remove outliers
        all_timings.sort_by(|a, b| a.partial_cmp(b).unwrap());

        // Use 10th and 90th percentiles to avoid outliers
        let p10_idx = all_timings.len() / 10;
        let p90_idx = (all_timings.len() * 9) / 10;

        let p10_time = all_timings[p10_idx];
        let p90_time = all_timings[p90_idx];

        // Timing should be consistent (more lenient for CI environments)
        let ratio = if p10_time > 0.0 {
            p90_time / p10_time
        } else {
            1.0
        };

        // More lenient threshold for CI environments and system noise
        assert!(
            ratio < 10.0,
            "Timing variance too high: ratio = {:.2} (p10={:.0}ns, p90={:.0}ns)",
            ratio,
            p10_time,
            p90_time
        );
    }

    #[test]
    fn test_extended_service_min_time() {
        let service = ExtendedConstantTimeService::new();
        let min_duration = std::time::Duration::from_micros(100);

        let a = b"test data";
        let b = b"test data";

        let start = Instant::now();
        let result = service.compare_with_min_time(a, b, min_duration);
        let elapsed = start.elapsed();

        assert!(result);
        assert!(
            elapsed >= min_duration,
            "Operation completed too quickly: {:?} < {:?}",
            elapsed,
            min_duration
        );
    }

    #[test]
    fn test_large_data_comparison() {
        let service = ConstantTimeService::new();

        // Test with large data
        let a = vec![0xAA; 10000];
        let b = vec![0xAA; 10000];
        assert!(service.constant_time_compare_bytes(&a, &b));

        // Test with large different data
        let mut c = vec![0xAA; 10000];
        c[9999] = 0xBB;
        assert!(!service.constant_time_compare_bytes(&a, &c));
    }

    #[test]
    fn test_edge_cases() {
        let service = ConstantTimeService::new();

        // Null bytes
        let a = [0u8; 32];
        let b = [0u8; 32];
        assert!(service.constant_time_compare_bytes(&a, &b));

        // Max value bytes
        let a = [0xFF; 32];
        let b = [0xFF; 32];
        assert!(service.constant_time_compare_bytes(&a, &b));

        // Single byte
        let a = [42];
        let b = [42];
        assert!(service.constant_time_compare_bytes(&a, &b));

        let a = [42];
        let b = [43];
        assert!(!service.constant_time_compare_bytes(&a, &b));
    }

    #[test]
    fn test_timing_attack_resistance() {
        let service = ConstantTimeService::new();

        // Create test data where early bytes differ
        let reference = b"secret_password_12345";
        let wrong_early = b"wrong__password_12345"; // Different at start
        let wrong_late = b"secret_password_12346"; // Different at end

        let mut early_timings = Vec::new();
        let mut late_timings = Vec::new();

        // Collect timing samples
        for _ in 0..100 {
            let start = Instant::now();
            let _ = service.constant_time_compare_bytes(reference, wrong_early);
            early_timings.push(start.elapsed());

            let start = Instant::now();
            let _ = service.constant_time_compare_bytes(reference, wrong_late);
            late_timings.push(start.elapsed());
        }

        // Calculate average timings
        let early_avg =
            early_timings.iter().map(|d| d.as_nanos()).sum::<u128>() / early_timings.len() as u128;

        let late_avg =
            late_timings.iter().map(|d| d.as_nanos()).sum::<u128>() / late_timings.len() as u128;

        // Timings should be very similar regardless of where the difference occurs
        let ratio = if early_avg > late_avg {
            early_avg as f64 / late_avg as f64
        } else {
            late_avg as f64 / early_avg as f64
        };

        assert!(
            ratio < 3.0, // More lenient for CI environments
            "Timing difference detected: early={} ns, late={} ns, ratio={}",
            early_avg,
            late_avg,
            ratio
        );
    }
}
