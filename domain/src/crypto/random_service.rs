//! Cryptographically secure random number generation service
//!
//! This module provides secure random number generation using the audited
//! rand and rand_chacha crates with OS entropy sources.

use base64::{Engine, engine::general_purpose::URL_SAFE_NO_PAD};
use rand::{RngCore, SeedableRng};
use rand_chacha::ChaCha20Rng;
use zeroize::Zeroize;

use super::traits::SecureRandomService;

/// ChaCha20-based secure random service
///
/// Uses ChaCha20 CSPRNG seeded from OS entropy for all random operations.
/// Provides high-performance, cryptographically secure random generation.
pub struct ChaChaRandomService {
    rng: std::sync::Mutex<ChaCha20Rng>,
}

impl ChaChaRandomService {
    /// Create a new random service with OS entropy
    pub fn new() -> Self {
        // Seed from OS entropy (uses getrandom internally)
        let rng = ChaCha20Rng::from_entropy();
        Self {
            rng: std::sync::Mutex::new(rng),
        }
    }

    /// Create a new random service with a specific seed (for testing only)
    #[cfg(test)]
    pub fn from_seed(seed: [u8; 32]) -> Self {
        let rng = ChaCha20Rng::from_seed(seed);
        Self {
            rng: std::sync::Mutex::new(rng),
        }
    }

    /// Fill a buffer with secure random bytes
    fn fill_bytes(&self, dest: &mut [u8]) {
        let mut rng = self.rng.lock().unwrap();
        rng.fill_bytes(dest);
    }
}

impl Default for ChaChaRandomService {
    fn default() -> Self {
        Self::new()
    }
}

impl SecureRandomService for ChaChaRandomService {
    fn generate_salt(&self) -> [u8; 32] {
        let mut salt = [0u8; 32];
        self.fill_bytes(&mut salt);
        salt
    }

    fn generate_session_token(&self) -> String {
        // Generate 32 bytes (256 bits) of entropy
        let mut token_bytes = [0u8; 32];
        self.fill_bytes(&mut token_bytes);

        // Add a timestamp component for uniqueness
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_micros();

        // Combine random bytes with timestamp
        let mut combined = Vec::with_capacity(40);
        combined.extend_from_slice(&token_bytes);
        combined.extend_from_slice(&timestamp.to_le_bytes()[..8]);

        // Encode as URL-safe base64
        format!("sess_{}", URL_SAFE_NO_PAD.encode(&combined))
    }

    fn generate_secure_bytes(&self, len: usize) -> Vec<u8> {
        let mut bytes = vec![0u8; len];
        self.fill_bytes(&mut bytes);
        bytes
    }

    fn generate_api_key(&self) -> String {
        // Generate 32 bytes of entropy
        let mut key_bytes = [0u8; 32];
        self.fill_bytes(&mut key_bytes);

        // Add a checksum byte for verification
        let checksum = key_bytes.iter().fold(0u8, |acc, &b| acc.wrapping_add(b));

        let mut full_key = Vec::with_capacity(33);
        full_key.extend_from_slice(&key_bytes);
        full_key.push(checksum);

        format!("ak_{}", URL_SAFE_NO_PAD.encode(&full_key))
    }

    fn generate_otp(&self, length: usize) -> String {
        let mut otp = String::with_capacity(length);
        let mut bytes = vec![0u8; length];
        self.fill_bytes(&mut bytes);

        for byte in bytes {
            // Use modulo to get digits 0-9
            otp.push_str(&(byte % 10).to_string());
        }

        otp
    }
}

/// Thread-safe OS random service
///
/// Uses the operating system's secure random number generator directly.
/// Suitable for high-security operations where OS entropy is preferred.
pub struct OsRandomService;

impl OsRandomService {
    /// Create a new OS random service
    pub fn new() -> Self {
        Self
    }
}

impl Default for OsRandomService {
    fn default() -> Self {
        Self::new()
    }
}

impl SecureRandomService for OsRandomService {
    fn generate_salt(&self) -> [u8; 32] {
        let mut salt = [0u8; 32];
        rand::thread_rng().fill_bytes(&mut salt);
        salt
    }

    fn generate_session_token(&self) -> String {
        let mut token_bytes = [0u8; 32];
        rand::thread_rng().fill_bytes(&mut token_bytes);

        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_micros();

        let mut combined = Vec::with_capacity(40);
        combined.extend_from_slice(&token_bytes);
        combined.extend_from_slice(&timestamp.to_le_bytes()[..8]);

        format!("sess_{}", URL_SAFE_NO_PAD.encode(&combined))
    }

    fn generate_secure_bytes(&self, len: usize) -> Vec<u8> {
        let mut bytes = vec![0u8; len];
        rand::thread_rng().fill_bytes(&mut bytes);
        bytes
    }
}

/// Secure buffer that automatically zeroes memory on drop
#[derive(Zeroize)]
#[zeroize(drop)]
pub struct SecureBuffer {
    data: Vec<u8>,
}

impl SecureBuffer {
    /// Create a new secure buffer with random data
    pub fn random(service: &dyn SecureRandomService, len: usize) -> Self {
        Self {
            data: service.generate_secure_bytes(len),
        }
    }

    /// Get the buffer data
    pub fn as_bytes(&self) -> &[u8] {
        &self.data
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashSet;

    #[test]
    fn test_salt_generation() {
        let service = ChaChaRandomService::new();

        let salt1 = service.generate_salt();
        let salt2 = service.generate_salt();

        // Salts should be different
        assert_ne!(salt1, salt2);

        // Should be 32 bytes
        assert_eq!(salt1.len(), 32);
        assert_eq!(salt2.len(), 32);
    }

    #[test]
    fn test_salt_uniqueness() {
        let service = ChaChaRandomService::new();
        let mut salts = HashSet::new();

        // Generate 1000 salts
        for _ in 0..1000 {
            let salt = service.generate_salt();
            assert!(salts.insert(salt), "Duplicate salt generated");
        }
    }

    #[test]
    fn test_session_token_generation() {
        let service = ChaChaRandomService::new();

        let token1 = service.generate_session_token();
        let token2 = service.generate_session_token();

        // Tokens should be different
        assert_ne!(token1, token2);

        // Should have the correct prefix
        assert!(token1.starts_with("sess_"));
        assert!(token2.starts_with("sess_"));

        // Should have sufficient length (base64 encoded 40 bytes)
        assert!(token1.len() >= 59); // "sess_" + base64(40 bytes)
        assert!(token2.len() >= 59);
    }

    #[test]
    fn test_secure_bytes_generation() {
        let service = ChaChaRandomService::new();

        // Test various lengths
        let bytes_10 = service.generate_secure_bytes(10);
        let bytes_100 = service.generate_secure_bytes(100);
        let bytes_1000 = service.generate_secure_bytes(1000);

        assert_eq!(bytes_10.len(), 10);
        assert_eq!(bytes_100.len(), 100);
        assert_eq!(bytes_1000.len(), 1000);

        // Should generate different bytes
        let bytes_10_2 = service.generate_secure_bytes(10);
        assert_ne!(bytes_10, bytes_10_2);
    }

    #[test]
    fn test_api_key_generation() {
        let service = ChaChaRandomService::new();

        let key1 = service.generate_api_key();
        let key2 = service.generate_api_key();

        // Keys should be different
        assert_ne!(key1, key2);

        // Should have the correct prefix
        assert!(key1.starts_with("ak_"));
        assert!(key2.starts_with("ak_"));

        // Should have sufficient length
        assert!(key1.len() >= 47); // "ak_" + base64(33 bytes)
        assert!(key2.len() >= 47);
    }

    #[test]
    fn test_otp_generation() {
        let service = ChaChaRandomService::new();

        // Test 6-digit OTP
        let otp6 = service.generate_otp(6);
        assert_eq!(otp6.len(), 6);
        assert!(otp6.chars().all(|c| c.is_ascii_digit()));

        // Test 8-digit OTP
        let otp8 = service.generate_otp(8);
        assert_eq!(otp8.len(), 8);
        assert!(otp8.chars().all(|c| c.is_ascii_digit()));

        // OTPs should be different
        let otp6_2 = service.generate_otp(6);
        assert_ne!(otp6, otp6_2);
    }

    #[test]
    fn test_entropy_quality() {
        let service = ChaChaRandomService::new();
        let bytes = service.generate_secure_bytes(1000);

        // Simple entropy test: count unique bytes
        let mut unique_bytes = HashSet::new();
        for &byte in &bytes {
            unique_bytes.insert(byte);
        }

        // Should have good distribution (at least 200 unique values out of 256)
        assert!(
            unique_bytes.len() >= 200,
            "Poor entropy: only {} unique bytes",
            unique_bytes.len()
        );

        // Check for obvious patterns
        let mut consecutive_same = 0;
        let mut max_consecutive = 0;
        let mut prev_byte = bytes[0];

        for &byte in &bytes[1..] {
            if byte == prev_byte {
                consecutive_same += 1;
                max_consecutive = max_consecutive.max(consecutive_same);
            } else {
                consecutive_same = 0;
            }
            prev_byte = byte;
        }

        // Should not have long runs of the same byte
        assert!(
            max_consecutive < 10,
            "Found {} consecutive same bytes",
            max_consecutive
        );
    }

    #[test]
    fn test_os_random_service() {
        let service = OsRandomService::new();

        // Test basic functionality
        let salt = service.generate_salt();
        assert_eq!(salt.len(), 32);

        let token = service.generate_session_token();
        assert!(token.starts_with("sess_"));

        let bytes = service.generate_secure_bytes(100);
        assert_eq!(bytes.len(), 100);
    }

    #[test]
    fn test_secure_buffer_zeroization() {
        let service = ChaChaRandomService::new();

        {
            let buffer = SecureBuffer::random(&service, 100);
            assert_eq!(buffer.as_bytes().len(), 100);

            // Verify it contains random data (not all zeros)
            let non_zero = buffer.as_bytes().iter().any(|&b| b != 0);
            assert!(non_zero, "Buffer should contain random data");
        } // buffer is dropped and zeroized here
    }

    #[test]
    fn test_thread_safety() {
        use std::sync::Arc;
        use std::thread;

        let service = Arc::new(ChaChaRandomService::new());
        let mut handles = vec![];

        // Spawn multiple threads generating random data
        for _ in 0..10 {
            let service_clone = Arc::clone(&service);
            let handle = thread::spawn(move || {
                let mut salts = HashSet::new();
                for _ in 0..100 {
                    let salt = service_clone.generate_salt();
                    salts.insert(salt);
                }
                salts.len()
            });
            handles.push(handle);
        }

        // All threads should generate unique salts
        for handle in handles {
            let unique_count = handle.join().unwrap();
            assert_eq!(unique_count, 100, "Thread generated duplicate salts");
        }
    }

    #[test]
    fn test_deterministic_with_seed() {
        let seed = [42u8; 32];

        let service1 = ChaChaRandomService::from_seed(seed);
        let salt1 = service1.generate_salt();
        let bytes1 = service1.generate_secure_bytes(32);

        let service2 = ChaChaRandomService::from_seed(seed);
        let salt2 = service2.generate_salt();
        let bytes2 = service2.generate_secure_bytes(32);

        // With same seed, should generate same values
        assert_eq!(salt1, salt2);
        assert_eq!(bytes1, bytes2);

        // Tokens will differ due to timestamp, so test secure bytes instead
        // This tests the deterministic property of the RNG correctly
    }
}
