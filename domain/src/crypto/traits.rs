//! Cryptographic service traits for domain layer
//!
//! These traits define the security-critical cryptographic operations required
//! by the domain layer. All implementations MUST use approved, audited libraries
//! as specified in CRYPTOGRAPHIC-POLICY.md.

use base64::{Engine, engine::general_purpose::URL_SAFE_NO_PAD};
use std::error::Error;
use std::fmt;

/// Error type for cryptographic operations
#[derive(Debug)]
pub enum CryptoError {
    /// Password hashing or verification failed
    PasswordError(String),
    /// Random number generation failed
    RandomError(String),
    /// Invalid input provided
    InvalidInput(String),
    /// Constant-time comparison failed
    ComparisonError(String),
}

impl fmt::Display for CryptoError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CryptoError::PasswordError(e) => write!(f, "Password error: {}", e),
            CryptoError::RandomError(e) => write!(f, "Random generation error: {}", e),
            CryptoError::InvalidInput(e) => write!(f, "Invalid input: {}", e),
            CryptoError::ComparisonError(e) => write!(f, "Comparison error: {}", e),
        }
    }
}

impl Error for CryptoError {}

/// Trait for password hashing and verification operations
///
/// Implementations MUST:
/// - Use Argon2id with OWASP 2025 parameters (19MiB, 2 iterations, 1 parallelism)
/// - Generate cryptographically secure salts
/// - Provide constant-time verification
/// - Complete operations within 200ms SLA
pub trait PasswordHashingService: Send + Sync {
    /// Hash a password using Argon2id with secure parameters
    ///
    /// # Arguments
    /// * `password` - The plaintext password to hash
    ///
    /// # Returns
    /// * `Ok(String)` - PHC format hash string
    /// * `Err(CryptoError)` - If hashing fails
    ///
    /// # Security Requirements
    /// - MUST use Argon2id algorithm
    /// - MUST use 19MiB memory, 2 iterations, 1 parallelism (OWASP 2025)
    /// - MUST generate unique salt for each password
    /// - MUST complete within 200ms
    fn hash_password(&self, password: &str) -> Result<String, CryptoError>;

    /// Verify a password against a stored hash
    ///
    /// # Arguments
    /// * `password` - The plaintext password to verify
    /// * `hash` - The stored PHC format hash
    ///
    /// # Returns
    /// * `Ok(true)` - Password matches
    /// * `Ok(false)` - Password does not match
    /// * `Err(CryptoError)` - If verification fails
    ///
    /// # Security Requirements
    /// - MUST use constant-time comparison
    /// - MUST complete within 200ms
    /// - MUST NOT leak timing information
    fn verify_password(&self, password: &str, hash: &str) -> Result<bool, CryptoError>;

    /// Validate password strength before hashing
    ///
    /// # Arguments
    /// * `password` - The password to validate
    ///
    /// # Returns
    /// * `Ok(())` - Password meets requirements
    /// * `Err(CryptoError)` - Password does not meet requirements
    ///
    /// # Requirements (OWASP 2025)
    /// - Minimum 12 characters
    /// - At least one uppercase letter
    /// - At least one lowercase letter
    /// - At least one digit
    /// - At least one special character
    fn validate_password_strength(&self, password: &str) -> Result<(), CryptoError> {
        if password.len() < 12 {
            return Err(CryptoError::InvalidInput(
                "Password must be at least 12 characters".to_string(),
            ));
        }

        let has_uppercase = password.chars().any(|c| c.is_uppercase());
        let has_lowercase = password.chars().any(|c| c.is_lowercase());
        let has_digit = password.chars().any(|c| c.is_ascii_digit());
        let has_special = password.chars().any(|c| !c.is_alphanumeric());

        if !has_uppercase || !has_lowercase || !has_digit || !has_special {
            return Err(CryptoError::InvalidInput(
                "Password must contain uppercase, lowercase, digit, and special character"
                    .to_string(),
            ));
        }

        Ok(())
    }
}

/// Trait for cryptographically secure random number generation
///
/// Implementations MUST:
/// - Use OS entropy sources
/// - Provide unpredictable output
/// - Be suitable for security-sensitive operations
pub trait SecureRandomService: Send + Sync {
    /// Generate a cryptographically secure salt
    ///
    /// # Returns
    /// * 32-byte array suitable for cryptographic operations
    ///
    /// # Security Requirements
    /// - MUST use OS entropy
    /// - MUST be unpredictable
    /// - MUST NOT reuse values
    fn generate_salt(&self) -> [u8; 32];

    /// Generate a secure session token
    ///
    /// # Returns
    /// * URL-safe base64 encoded token string
    ///
    /// # Security Requirements
    /// - MUST contain at least 256 bits of entropy
    /// - MUST be globally unique
    /// - MUST be unpredictable
    fn generate_session_token(&self) -> String;

    /// Generate secure random bytes of specified length
    ///
    /// # Arguments
    /// * `len` - Number of bytes to generate
    ///
    /// # Returns
    /// * Vector of cryptographically secure random bytes
    ///
    /// # Security Requirements
    /// - MUST use OS entropy
    /// - MUST handle large lengths efficiently
    fn generate_secure_bytes(&self, len: usize) -> Vec<u8>;

    /// Generate a secure API key
    ///
    /// # Returns
    /// * API key string with prefix for identification
    ///
    /// # Security Requirements
    /// - MUST contain at least 256 bits of entropy
    /// - MUST be globally unique
    /// - MUST include checksum or verification component
    fn generate_api_key(&self) -> String {
        let random_bytes = self.generate_secure_bytes(32);
        format!("ak_{}", URL_SAFE_NO_PAD.encode(random_bytes))
    }

    /// Generate a secure one-time password (OTP)
    ///
    /// # Arguments
    /// * `length` - Length of the OTP (default 6)
    ///
    /// # Returns
    /// * Numeric string OTP
    ///
    /// # Security Requirements
    /// - MUST use cryptographically secure randomness
    /// - MUST provide uniform distribution
    fn generate_otp(&self, length: usize) -> String {
        let bytes = self.generate_secure_bytes(length);
        bytes
            .iter()
            .map(|b| (b % 10).to_string())
            .collect::<Vec<_>>()
            .join("")
    }
}

/// Trait for constant-time comparison operations
///
/// Implementations MUST:
/// - Prevent timing attacks
/// - Take consistent time regardless of input
/// - Handle variable-length inputs securely
pub trait ConstantTimeComparison: Send + Sync {
    /// Compare two byte slices in constant time
    ///
    /// # Arguments
    /// * `a` - First byte slice
    /// * `b` - Second byte slice
    ///
    /// # Returns
    /// * `true` if slices are equal, `false` otherwise
    ///
    /// # Security Requirements
    /// - MUST take same time for equal and unequal inputs
    /// - MUST compare all bytes regardless of mismatches
    /// - MUST handle different lengths securely
    fn constant_time_compare_bytes(&self, a: &[u8], b: &[u8]) -> bool;

    /// Compare two strings in constant time
    ///
    /// # Arguments
    /// * `a` - First string
    /// * `b` - Second string
    ///
    /// # Returns
    /// * `true` if strings are equal, `false` otherwise
    ///
    /// # Security Requirements
    /// - MUST take same time for equal and unequal inputs
    /// - MUST compare all characters regardless of mismatches
    /// - MUST handle different lengths securely
    fn constant_time_compare_str(&self, a: &str, b: &str) -> bool {
        self.constant_time_compare_bytes(a.as_bytes(), b.as_bytes())
    }
}

/// Combined cryptographic service trait
///
/// This trait combines all cryptographic operations needed by the domain layer.
/// Implementations should use composition to provide all required functionality.
pub trait CryptoService:
    PasswordHashingService + SecureRandomService + ConstantTimeComparison + Send + Sync
{
    /// Securely wipe sensitive data from memory
    ///
    /// # Arguments
    /// * `data` - Mutable reference to data to be wiped
    ///
    /// # Security Requirements
    /// - MUST overwrite memory with zeros
    /// - MUST prevent compiler optimization
    /// - MUST handle all data types securely
    fn secure_wipe(&self, data: &mut [u8]) {
        use zeroize::Zeroize;
        data.zeroize();
    }

    /// Generate a secure CSRF token
    ///
    /// # Returns
    /// * CSRF token suitable for web forms
    ///
    /// # Security Requirements
    /// - MUST be unpredictable
    /// - MUST be unique per session
    /// - MUST contain sufficient entropy
    fn generate_csrf_token(&self) -> String {
        let bytes = self.generate_secure_bytes(32);
        URL_SAFE_NO_PAD.encode(bytes)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_strength_validation() {
        // This will be tested with actual implementations
        // Placeholder to ensure trait compiles correctly
    }

    #[test]
    fn test_crypto_error_display() {
        let error = CryptoError::PasswordError("test error".to_string());
        assert_eq!(error.to_string(), "Password error: test error");

        let error = CryptoError::RandomError("random fail".to_string());
        assert_eq!(error.to_string(), "Random generation error: random fail");

        let error = CryptoError::InvalidInput("bad input".to_string());
        assert_eq!(error.to_string(), "Invalid input: bad input");

        let error = CryptoError::ComparisonError("compare fail".to_string());
        assert_eq!(error.to_string(), "Comparison error: compare fail");
    }
}
