//! Combined cryptographic service implementation
//!
//! This module provides a unified cryptographic service that combines
//! password hashing, random generation, and constant-time operations.

use base64::{Engine, engine::general_purpose::URL_SAFE_NO_PAD};
use zeroize::Zeroize;

use super::{
    constant_time::ConstantTimeService,
    password_service::Argon2PasswordService,
    random_service::ChaChaRandomService,
    traits::{
        ConstantTimeComparison, CryptoError, CryptoService, PasswordHashingService,
        SecureRandomService,
    },
};

/// Unified cryptographic service
///
/// Combines all cryptographic operations into a single service
/// using composition of specialized services.
pub struct UnifiedCryptoService {
    password_service: Argon2PasswordService,
    random_service: ChaChaRandomService,
    constant_time_service: ConstantTimeService,
}

impl UnifiedCryptoService {
    /// Create a new unified crypto service with default configurations
    pub fn new() -> Result<Self, CryptoError> {
        Ok(Self {
            password_service: Argon2PasswordService::new()?,
            random_service: ChaChaRandomService::new(),
            constant_time_service: ConstantTimeService::new(),
        })
    }
}

impl Default for UnifiedCryptoService {
    fn default() -> Self {
        Self::new().expect("Failed to create unified crypto service")
    }
}

// Implement all required traits by delegating to specialized services

impl PasswordHashingService for UnifiedCryptoService {
    fn hash_password(&self, password: &str) -> Result<String, CryptoError> {
        self.password_service.hash_password(password)
    }

    fn verify_password(&self, password: &str, hash: &str) -> Result<bool, CryptoError> {
        self.password_service.verify_password(password, hash)
    }

    fn validate_password_strength(&self, password: &str) -> Result<(), CryptoError> {
        self.password_service.validate_password_strength(password)
    }
}

impl SecureRandomService for UnifiedCryptoService {
    fn generate_salt(&self) -> [u8; 32] {
        self.random_service.generate_salt()
    }

    fn generate_session_token(&self) -> String {
        self.random_service.generate_session_token()
    }

    fn generate_secure_bytes(&self, len: usize) -> Vec<u8> {
        self.random_service.generate_secure_bytes(len)
    }

    fn generate_api_key(&self) -> String {
        self.random_service.generate_api_key()
    }

    fn generate_otp(&self, length: usize) -> String {
        self.random_service.generate_otp(length)
    }
}

impl ConstantTimeComparison for UnifiedCryptoService {
    fn constant_time_compare_bytes(&self, a: &[u8], b: &[u8]) -> bool {
        self.constant_time_service.constant_time_compare_bytes(a, b)
    }

    fn constant_time_compare_str(&self, a: &str, b: &str) -> bool {
        self.constant_time_service.constant_time_compare_str(a, b)
    }
}

impl CryptoService for UnifiedCryptoService {
    fn secure_wipe(&self, data: &mut [u8]) {
        data.zeroize();
    }

    fn generate_csrf_token(&self) -> String {
        let bytes = self.generate_secure_bytes(32);
        URL_SAFE_NO_PAD.encode(bytes)
    }
}

/// Factory for creating crypto services
pub struct CryptoServiceFactory;

impl CryptoServiceFactory {
    /// Create the default production crypto service
    pub fn create_default() -> Result<UnifiedCryptoService, CryptoError> {
        UnifiedCryptoService::new()
    }

    /// Create a crypto service with custom configuration (for testing)
    #[cfg(test)]
    pub fn create_test() -> Result<UnifiedCryptoService, CryptoError> {
        // Could use reduced parameters for faster testing
        UnifiedCryptoService::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_unified_service_creation() {
        let service = UnifiedCryptoService::new().unwrap();
        assert!(service.generate_salt().len() == 32);
    }

    #[test]
    fn test_password_operations() {
        let service = UnifiedCryptoService::new().unwrap();
        let password = "TestPassword123!@#";

        let hash = service.hash_password(password).unwrap();
        assert!(service.verify_password(password, &hash).unwrap());
        assert!(
            !service
                .verify_password("WrongPassword123!@#", &hash)
                .unwrap()
        );
    }

    #[test]
    fn test_random_operations() {
        let service = UnifiedCryptoService::new().unwrap();

        let salt = service.generate_salt();
        assert_eq!(salt.len(), 32);

        let token = service.generate_session_token();
        assert!(token.starts_with("sess_"));

        let api_key = service.generate_api_key();
        assert!(api_key.starts_with("ak_"));

        let otp = service.generate_otp(6);
        assert_eq!(otp.len(), 6);
        assert!(otp.chars().all(|c| c.is_ascii_digit()));
    }

    #[test]
    fn test_constant_time_operations() {
        let service = UnifiedCryptoService::new().unwrap();

        assert!(service.constant_time_compare_str("test", "test"));
        assert!(!service.constant_time_compare_str("test", "fail"));

        let a = b"secure data";
        let b = b"secure data";
        assert!(service.constant_time_compare_bytes(a, b));
    }

    #[test]
    fn test_crypto_service_trait_methods() {
        let service = UnifiedCryptoService::new().unwrap();

        // Test CSRF token generation
        let csrf1 = service.generate_csrf_token();
        let csrf2 = service.generate_csrf_token();
        assert_ne!(csrf1, csrf2);
        assert!(csrf1.len() >= 43); // Base64 of 32 bytes

        // Test secure wipe
        let mut sensitive_data = b"sensitive".to_vec();
        service.secure_wipe(&mut sensitive_data);
        assert_eq!(sensitive_data, vec![0u8; 9]);
    }

    #[test]
    fn test_factory_creation() {
        let service = CryptoServiceFactory::create_default().unwrap();
        assert!(service.generate_salt().len() == 32);

        #[cfg(test)]
        {
            let test_service = CryptoServiceFactory::create_test().unwrap();
            assert!(test_service.generate_salt().len() == 32);
        }
    }

    #[test]
    fn test_comprehensive_workflow() {
        let service = UnifiedCryptoService::new().unwrap();

        // User registration workflow
        let password = "SecurePassword123!@#";

        // Validate password strength
        service.validate_password_strength(password).unwrap();

        // Hash password
        let password_hash = service.hash_password(password).unwrap();

        // Generate session token
        let session_token = service.generate_session_token();

        // Generate CSRF token
        let csrf_token = service.generate_csrf_token();

        // User login workflow
        assert!(service.verify_password(password, &password_hash).unwrap());

        // Token comparison (constant time)
        assert!(service.constant_time_compare_str(&session_token, &session_token));
        assert!(service.constant_time_compare_str(&csrf_token, &csrf_token));

        // Generate API key for user
        let api_key = service.generate_api_key();
        assert!(api_key.starts_with("ak_"));

        // Generate OTP for 2FA
        let otp = service.generate_otp(6);
        assert_eq!(otp.len(), 6);
    }

    #[test]
    fn test_thread_safety() {
        use std::sync::Arc;
        use std::thread;

        let service = Arc::new(UnifiedCryptoService::new().unwrap());
        let mut handles = vec![];

        for i in 0..10 {
            let service_clone = Arc::clone(&service);
            let handle = thread::spawn(move || {
                let password = format!("Password{}!@#", i);
                let hash = service_clone.hash_password(&password).unwrap();
                let token = service_clone.generate_session_token();
                let salt = service_clone.generate_salt();

                (hash, token, salt)
            });
            handles.push(handle);
        }

        let mut results = vec![];
        for handle in handles {
            results.push(handle.join().unwrap());
        }

        // Verify all results are unique
        let mut hashes = std::collections::HashSet::new();
        let mut tokens = std::collections::HashSet::new();
        let mut salts = std::collections::HashSet::new();

        for (hash, token, salt) in results {
            assert!(hashes.insert(hash));
            assert!(tokens.insert(token));
            assert!(salts.insert(salt));
        }
    }
}
