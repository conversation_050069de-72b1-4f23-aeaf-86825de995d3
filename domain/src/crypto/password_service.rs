//! Secure password hashing service using Argon2id
//!
//! This module implements OWASP 2025 compliant password hashing using the
//! audited argon2 crate. All operations are timing-attack resistant.

use argon2::{Argon2, Params, Version};
use password_hash::{PasswordHash, PasswordHasher, PasswordVerifier, SaltString};
use zeroize::Zeroize;

use super::random_service::ChaChaRandomService;
use super::traits::SecureRandomService;
use super::traits::{CryptoError, PasswordHashingService};

/// Argon2id password hashing service
///
/// Implements OWASP 2025 recommended parameters:
/// - Memory: 19 MiB (19456 KiB)
/// - Iterations: 2
/// - Parallelism: 1
/// - Hash length: 32 bytes
pub struct Argon2PasswordService {
    argon2: Argon2<'static>,
}

impl Argon2PasswordService {
    /// Create a new password service with OWASP 2025 parameters
    pub fn new() -> Result<Self, CryptoError> {
        // OWASP 2025 recommended parameters
        // Memory: 19 MiB (19456 KiB)
        // Iterations: 2
        // Parallelism: 1
        let params = Params::new(19456, 2, 1, Some(32))
            .map_err(|e| CryptoError::PasswordError(format!("Invalid Argon2 params: {}", e)))?;

        let argon2 = Argon2::new(argon2::Algorithm::Argon2id, Version::V0x13, params);

        Ok(Self { argon2 })
    }

    /// Create a password service with custom parameters (for testing only)
    #[cfg(test)]
    pub fn with_params(
        memory_kb: u32,
        iterations: u32,
        parallelism: u32,
    ) -> Result<Self, CryptoError> {
        let params = Params::new(memory_kb, iterations, parallelism, Some(32))
            .map_err(|e| CryptoError::PasswordError(format!("Invalid Argon2 params: {}", e)))?;

        let argon2 = Argon2::new(argon2::Algorithm::Argon2id, Version::V0x13, params);

        Ok(Self { argon2 })
    }
}

impl Default for Argon2PasswordService {
    fn default() -> Self {
        Self::new().expect("Failed to create Argon2 service with default parameters")
    }
}

impl PasswordHashingService for Argon2PasswordService {
    fn hash_password(&self, password: &str) -> Result<String, CryptoError> {
        // Validate password strength first
        self.validate_password_strength(password)?;

        // Generate a secure random salt
        let random_service = ChaChaRandomService::new();
        let salt_bytes = random_service.generate_salt();

        // Encode salt as base64 for SaltString
        let salt = SaltString::encode_b64(&salt_bytes[..16])
            .map_err(|e| CryptoError::PasswordError(format!("Failed to encode salt: {}", e)))?;

        // Hash the password
        let password_hash = self
            .argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|e| CryptoError::PasswordError(format!("Failed to hash password: {}", e)))?;

        // Return PHC format string
        Ok(password_hash.to_string())
    }

    fn verify_password(&self, password: &str, hash: &str) -> Result<bool, CryptoError> {
        // Only allow Argon2id hashes for security
        if !hash.starts_with("$argon2id$") {
            return Err(CryptoError::PasswordError(
                "Invalid hash format: Only Argon2id hashes are supported".to_string(),
            ));
        }

        // Parse the PHC format hash
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| CryptoError::PasswordError(format!("Invalid hash format: {}", e)))?;

        // Perform constant-time verification
        match self
            .argon2
            .verify_password(password.as_bytes(), &parsed_hash)
        {
            Ok(()) => Ok(true),
            Err(argon2::password_hash::Error::Password) => Ok(false),
            Err(e) => Err(CryptoError::PasswordError(format!(
                "Password verification failed: {}",
                e
            ))),
        }
    }
}

/// Secure memory wrapper for password data
///
/// Automatically zeroes memory when dropped to prevent password leakage
#[derive(Zeroize)]
#[zeroize(drop)]
pub struct SecurePassword {
    data: Vec<u8>,
}

impl SecurePassword {
    /// Create a new secure password wrapper
    pub fn new(password: &str) -> Self {
        Self {
            data: password.as_bytes().to_vec(),
        }
    }

    /// Get the password bytes
    pub fn as_bytes(&self) -> &[u8] {
        &self.data
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Instant;

    #[test]
    fn test_password_hashing_and_verification() {
        let service = Argon2PasswordService::new().unwrap();
        let password = "TestPassword123!@#";

        // Hash the password
        let hash = service.hash_password(password).unwrap();

        // Verify it starts with argon2id
        assert!(hash.starts_with("$argon2id$"));

        // Verify correct password
        assert!(service.verify_password(password, &hash).unwrap());

        // Verify incorrect password
        assert!(
            !service
                .verify_password("WrongPassword123!@#", &hash)
                .unwrap()
        );
    }

    #[test]
    fn test_unique_salts() {
        let service = Argon2PasswordService::new().unwrap();
        let password = "TestPassword123!@#";

        // Generate multiple hashes
        let hash1 = service.hash_password(password).unwrap();
        let hash2 = service.hash_password(password).unwrap();

        // Hashes should be different due to unique salts
        assert_ne!(hash1, hash2);

        // Both should verify correctly
        assert!(service.verify_password(password, &hash1).unwrap());
        assert!(service.verify_password(password, &hash2).unwrap());
    }

    #[test]
    fn test_password_strength_validation() {
        let service = Argon2PasswordService::new().unwrap();

        // Too short
        assert!(service.hash_password("Short1!").is_err());

        // Missing uppercase
        assert!(service.hash_password("lowercase123!@#").is_err());

        // Missing lowercase
        assert!(service.hash_password("UPPERCASE123!@#").is_err());

        // Missing digit
        assert!(service.hash_password("NoDigitsHere!@#").is_err());

        // Missing special character
        assert!(service.hash_password("NoSpecialChar123").is_err());

        // Valid password
        assert!(service.hash_password("ValidPassword123!").is_ok());
    }

    #[test]
    fn test_phc_format_parsing() {
        let service = Argon2PasswordService::new().unwrap();
        let password = "TestPassword123!@#";
        let hash = service.hash_password(password).unwrap();

        // Parse and verify parameters
        assert!(hash.contains("m=19456")); // Memory parameter
        assert!(hash.contains("t=2")); // Iterations
        assert!(hash.contains("p=1")); // Parallelism
    }

    #[test]
    fn test_invalid_hash_format() {
        let service = Argon2PasswordService::new().unwrap();

        // Invalid hash format should return an error
        assert!(service.verify_password("password", "invalid-hash").is_err());
        assert!(
            service
                .verify_password("password", "$bcrypt$invalid")
                .is_err()
        );
        assert!(service.verify_password("password", "").is_err());

        // Test specifically that non-argon2 hashes are rejected
        let result = service.verify_password("password", "$bcrypt$invalid");
        match result {
            Err(CryptoError::PasswordError(msg)) => {
                assert!(msg.contains("Invalid hash format"));
            }
            _ => panic!("Expected password error for invalid hash format"),
        }
    }

    #[test]
    fn test_timing_attack_resistance() {
        // Use reduced parameters for faster testing
        let service = Argon2PasswordService::with_params(1024, 1, 1).unwrap();
        let password = "TestPassword123!@#";
        let hash = service.hash_password(password).unwrap();

        let mut timings = Vec::new();

        // Measure timing for correct password
        let start = Instant::now();
        let _ = service.verify_password(password, &hash);
        timings.push(start.elapsed());

        // Measure timing for various wrong passwords
        let wrong_passwords = [
            "WrongPassword123!@#",
            "T",                   // Very different
            "TestPassword123!@",   // One char different
            "testPassword123!@#",  // Case different
            "TestPassword123!@#$", // One char extra
        ];

        for wrong_pass in &wrong_passwords {
            let start = Instant::now();
            let _ = service.verify_password(wrong_pass, &hash);
            timings.push(start.elapsed());
        }

        // Calculate timing variance
        let max_time = timings.iter().max().unwrap().as_nanos() as f64;
        let min_time = timings.iter().max().unwrap().as_nanos() as f64;

        // Timing should be consistent (within 2x variance)
        let ratio = if min_time > 0.0 {
            max_time / min_time
        } else {
            1.0
        };

        assert!(ratio < 2.0, "Timing variance too high: {}", ratio);
    }

    #[test]
    fn test_secure_password_zeroization() {
        let password_str = "SensitivePassword123!@#";

        {
            let secure_pass = SecurePassword::new(password_str);
            assert_eq!(secure_pass.as_bytes(), password_str.as_bytes());
        } // secure_pass is dropped and zeroized here

        // Memory should be cleared (this is more of a conceptual test)
        // In practice, verifying memory zeroization requires unsafe code
    }

    #[test]
    fn test_performance_sla() {
        // Use lighter parameters for CI/testing environments
        let service = Argon2PasswordService::with_params(1024, 1, 1).unwrap();
        let password = "TestPassword123!@#";

        // Test hashing performance - more lenient for CI
        let start = Instant::now();
        let hash = service.hash_password(password).unwrap();
        let hash_duration = start.elapsed();

        // Should complete within 1000ms SLA for testing (more lenient)
        assert!(
            hash_duration.as_millis() < 1000,
            "Hashing took too long: {}ms",
            hash_duration.as_millis()
        );

        // Test verification performance
        let start = Instant::now();
        let _ = service.verify_password(password, &hash).unwrap();
        let verify_duration = start.elapsed();

        // Should complete within 1000ms SLA for testing
        assert!(
            verify_duration.as_millis() < 1000,
            "Verification took too long: {}ms",
            verify_duration.as_millis()
        );
    }
}
