// Password policy domain service
// Contains password strength policies and validation rules

use crate::errors::DomainError;
use crate::value_objects::Password;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct PasswordPolicy {
    min_length: usize,
    max_length: usize,
    require_lowercase: bool,
    require_uppercase: bool,
    require_digits: bool,
    require_special_chars: bool,
    min_strength_score: u8,
    blacklisted_patterns: Vec<String>,
}

impl Default for PasswordPolicy {
    fn default() -> Self {
        PasswordPolicy {
            min_length: 8,
            max_length: 128,
            require_lowercase: true,
            require_uppercase: true,
            require_digits: true,
            require_special_chars: false,
            min_strength_score: 60,
            blacklisted_patterns: vec![
                "password".to_string(),
                "123456".to_string(),
                "qwerty".to_string(),
                "admin".to_string(),
                "root".to_string(),
            ],
        }
    }
}

impl PasswordPolicy {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn with_min_length(mut self, min_length: usize) -> Self {
        self.min_length = min_length;
        self
    }

    pub fn with_max_length(mut self, max_length: usize) -> Self {
        self.max_length = max_length;
        self
    }

    pub fn with_min_strength_score(mut self, min_score: u8) -> Self {
        self.min_strength_score = min_score;
        self
    }

    pub fn require_lowercase(mut self, require: bool) -> Self {
        self.require_lowercase = require;
        self
    }

    pub fn require_uppercase(mut self, require: bool) -> Self {
        self.require_uppercase = require;
        self
    }

    pub fn require_digits(mut self, require: bool) -> Self {
        self.require_digits = require;
        self
    }

    pub fn require_special_chars(mut self, require: bool) -> Self {
        self.require_special_chars = require;
        self
    }

    pub fn add_blacklisted_pattern(mut self, pattern: String) -> Self {
        self.blacklisted_patterns.push(pattern);
        self
    }

    /// Validate a password string against this policy
    pub fn validate(&self, password: &str) -> Result<(), DomainError> {
        // Length validation
        if password.len() < self.min_length {
            return Err(DomainError::InvalidPassword(format!(
                "Password must be at least {} characters long",
                self.min_length
            )));
        }

        if password.len() > self.max_length {
            return Err(DomainError::InvalidPassword(format!(
                "Password cannot exceed {} characters",
                self.max_length
            )));
        }

        // Character requirement validation
        if self.require_lowercase && !password.chars().any(|c| c.is_lowercase()) {
            return Err(DomainError::InvalidPassword(
                "Password must contain at least one lowercase letter".to_string(),
            ));
        }

        if self.require_uppercase && !password.chars().any(|c| c.is_uppercase()) {
            return Err(DomainError::InvalidPassword(
                "Password must contain at least one uppercase letter".to_string(),
            ));
        }

        if self.require_digits && !password.chars().any(|c| c.is_ascii_digit()) {
            return Err(DomainError::InvalidPassword(
                "Password must contain at least one digit".to_string(),
            ));
        }

        if self.require_special_chars
            && !password
                .chars()
                .any(|c| "!@#$%^&*()_+-=[]{}|;':\",./<>?".contains(c))
        {
            return Err(DomainError::InvalidPassword(
                "Password must contain at least one special character".to_string(),
            ));
        }

        // Blacklist validation
        let lower_password = password.to_lowercase();
        for pattern in &self.blacklisted_patterns {
            if lower_password.contains(&pattern.to_lowercase()) {
                return Err(DomainError::InvalidPassword(
                    "Password contains forbidden patterns".to_string(),
                ));
            }
        }

        // Create a Password object to check strength
        let password_obj = Password::new(password)?;
        if password_obj.strength_score() < self.min_strength_score {
            return Err(DomainError::InvalidPassword(format!(
                "Password strength is too low (minimum score: {})",
                self.min_strength_score
            )));
        }

        Ok(())
    }

    /// Check if a password needs to be changed based on age
    pub fn should_change_password(
        password_created_at: std::time::SystemTime,
        max_age_days: Option<u64>,
    ) -> bool {
        if let Some(max_days) = max_age_days {
            let max_age = std::time::Duration::from_secs(max_days * 24 * 60 * 60);
            match std::time::SystemTime::now().duration_since(password_created_at) {
                Ok(age) => age > max_age,
                Err(_) => false, // If there's a clock issue, don't force change
            }
        } else {
            false // No expiration policy
        }
    }

    /// Check if a password is being reused (simple history check)
    pub fn is_password_reused(
        new_password: &Password,
        password_history: &[String], // Hashed passwords
    ) -> bool {
        // Check against each hash in history
        for old_hash in password_history {
            if new_password.verify_against_hash(old_hash) {
                return true;
            }
        }
        false
    }

    /// Generate password strength feedback
    pub fn get_strength_feedback(&self, password: &str) -> PasswordStrengthFeedback {
        let mut feedback = PasswordStrengthFeedback {
            score: 0,
            suggestions: Vec::new(),
            is_valid: false,
        };

        // Try to create a password object to get the score
        if let Ok(password_obj) = Password::new(password) {
            feedback.score = password_obj.strength_score();
            feedback.is_valid = self.validate(password).is_ok();
        }

        // Generate suggestions based on what's missing
        if password.len() < self.min_length {
            feedback
                .suggestions
                .push(format!("Use at least {} characters", self.min_length));
        }

        if self.require_lowercase && !password.chars().any(|c| c.is_lowercase()) {
            feedback
                .suggestions
                .push("Add lowercase letters".to_string());
        }

        if self.require_uppercase && !password.chars().any(|c| c.is_uppercase()) {
            feedback
                .suggestions
                .push("Add uppercase letters".to_string());
        }

        if self.require_digits && !password.chars().any(|c| c.is_ascii_digit()) {
            feedback.suggestions.push("Add numbers".to_string());
        }

        if self.require_special_chars
            && !password
                .chars()
                .any(|c| "!@#$%^&*()_+-=[]{}|;':\",./<>?".contains(c))
        {
            feedback
                .suggestions
                .push("Add special characters (!@#$%^&*...)".to_string());
        }

        let lower_password = password.to_lowercase();
        for pattern in &self.blacklisted_patterns {
            if lower_password.contains(&pattern.to_lowercase()) {
                feedback
                    .suggestions
                    .push("Avoid common patterns".to_string());
                break;
            }
        }

        feedback
    }
}

#[derive(Debug, Clone)]
pub struct PasswordStrengthFeedback {
    pub score: u8,
    pub suggestions: Vec<String>,
    pub is_valid: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_policy() {
        let policy = PasswordPolicy::default();

        // Valid password should pass
        assert!(policy.validate("StrongSecret123!").is_ok());

        // Too short should fail
        assert!(policy.validate("Short1").is_err());

        // Missing uppercase should fail
        assert!(policy.validate("lowercase123").is_err());

        // Missing lowercase should fail
        assert!(policy.validate("UPPERCASE123").is_err());

        // Missing digits should fail
        assert!(policy.validate("OnlyLetters").is_err());
    }

    #[test]
    fn test_custom_policy() {
        let policy = PasswordPolicy::new()
            .with_min_length(12)
            .require_special_chars(true)
            .with_min_strength_score(80);

        // Should require special chars and higher strength
        assert!(policy.validate("StrongPassword123").is_err());
        assert!(policy.validate("StrongSecret123!").is_ok());
    }

    #[test]
    fn test_blacklisted_patterns() {
        let policy = PasswordPolicy::default().add_blacklisted_pattern("company".to_string());

        assert!(policy.validate("CompanySecure123!").is_err()); // Contains "company" which is blacklisted
        assert!(policy.validate("SecureAccess123!").is_ok());
    }

    #[test]
    fn test_password_age() {
        let now = std::time::SystemTime::now();
        let old_password = now - std::time::Duration::from_secs(31 * 24 * 60 * 60); // 31 days ago
        let new_password = now - std::time::Duration::from_secs(24 * 60 * 60); // 1 day ago

        // 30-day expiration policy
        assert!(PasswordPolicy::should_change_password(
            old_password,
            Some(30)
        ));
        assert!(!PasswordPolicy::should_change_password(
            new_password,
            Some(30)
        ));
        assert!(!PasswordPolicy::should_change_password(old_password, None));
    }

    #[test]
    fn test_password_reuse() {
        let password = Password::new("TestPassword123!").unwrap();
        let password_hash = password.clone().into_hash().unwrap();
        let history = vec![password_hash];

        assert!(PasswordPolicy::is_password_reused(&password, &history));

        let different_password = Password::new("DifferentPassword123!").unwrap();
        assert!(!PasswordPolicy::is_password_reused(
            &different_password,
            &history
        ));
    }

    #[test]
    fn test_strength_feedback() {
        let policy = PasswordPolicy::default();

        let feedback = policy.get_strength_feedback("weak");
        assert!(!feedback.is_valid);
        assert!(!feedback.suggestions.is_empty());

        let feedback = policy.get_strength_feedback("StrongSecret123!");
        assert!(feedback.is_valid);
        assert!(feedback.score > 60);
    }
}
