// Authentication domain service
// Contains authentication business rules and logic

use crate::entities::{Session, User};
use crate::errors::DomainError;
use crate::value_objects::{Password, UserId};

pub struct AuthService;

impl AuthService {
    /// Authenticate a user with email and password
    /// Returns the user if authentication is successful
    pub fn authenticate_user(user: &User, provided_password: &Password) -> Result<(), DomainError> {
        if !user.is_active() {
            return Err(DomainError::AuthenticationFailed);
        }

        match user.verify_password(provided_password.as_str()) {
            Ok(true) => {}
            Ok(false) | Err(_) => return Err(DomainError::AuthenticationFailed),
        }

        Ok(())
    }

    /// Create a new session for an authenticated user
    pub fn create_session(
        user_id: UserId,
        duration_seconds: u64,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Session {
        Session::new(user_id, duration_seconds, ip_address, user_agent)
    }

    /// Validate a session and check if it's still valid
    pub fn validate_session(session: &Session) -> Result<(), DomainError> {
        if !session.is_valid() {
            if session.is_expired() {
                return Err(DomainError::SessionExpired);
            } else {
                return Err(DomainError::InvalidSession);
            }
        }

        Ok(())
    }

    /// Check if a user can be authenticated based on account status
    pub fn can_authenticate(user: &User) -> Result<(), DomainError> {
        if !user.is_active() {
            return Err(DomainError::AuthenticationFailed);
        }

        // In a real implementation, you might check for:
        // - Account lockout due to failed attempts
        // - Password expiration
        // - Account verification status (depending on requirements)

        Ok(())
    }

    /// Determine session duration based on user preferences or security policies
    pub fn determine_session_duration(remember_me: bool) -> u64 {
        if remember_me {
            // 30 days for "remember me" sessions
            30 * 24 * 60 * 60
        } else {
            // 8 hours for regular sessions
            8 * 60 * 60
        }
    }

    /// Check if authentication attempt should be rate limited
    pub fn should_rate_limit(failed_attempts: u32, last_attempt: std::time::SystemTime) -> bool {
        // Simple rate limiting logic
        const MAX_ATTEMPTS: u32 = 5;
        const LOCKOUT_DURATION: std::time::Duration = std::time::Duration::from_secs(15 * 60); // 15 minutes

        if failed_attempts >= MAX_ATTEMPTS {
            match std::time::SystemTime::now().duration_since(last_attempt) {
                Ok(elapsed) => elapsed < LOCKOUT_DURATION,
                Err(_) => false, // If there's a clock issue, don't rate limit
            }
        } else {
            false
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_authenticate_valid_user() {
        let email = crate::value_objects::Email::new("<EMAIL>").unwrap();
        let password = crate::value_objects::Password::new("ValidPass123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.change_password("ValidPassword123!").unwrap();
        let password = Password::new("ValidPassword123!").unwrap();

        assert!(AuthService::authenticate_user(&user, &password).is_ok());
    }

    #[test]
    fn test_authenticate_invalid_password() {
        let email = crate::value_objects::Email::new("<EMAIL>").unwrap();
        let password = crate::value_objects::Password::new("ValidPass123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.change_password("ValidPassword123!").unwrap();
        let wrong_password = Password::new("WrongPassword123!").unwrap();

        assert!(AuthService::authenticate_user(&user, &wrong_password).is_err());
    }

    #[test]
    fn test_create_session() {
        let user_id = UserId::new();
        let session = AuthService::create_session(
            user_id.clone(),
            3600,
            Some("127.0.0.1".to_string()),
            Some("Test User Agent".to_string()),
        );

        assert_eq!(session.user_id(), &user_id);
        assert_eq!(session.ip_address(), Some("127.0.0.1"));
        assert_eq!(session.user_agent(), Some("Test User Agent"));
        assert!(session.is_valid());
    }

    #[test]
    fn test_session_duration() {
        assert_eq!(AuthService::determine_session_duration(false), 8 * 60 * 60);
        assert_eq!(
            AuthService::determine_session_duration(true),
            30 * 24 * 60 * 60
        );
    }

    #[test]
    fn test_rate_limiting() {
        let now = std::time::SystemTime::now();

        // Should not rate limit with few attempts
        assert!(!AuthService::should_rate_limit(3, now));

        // Should rate limit with many recent attempts
        assert!(AuthService::should_rate_limit(5, now));

        // Should not rate limit with many old attempts
        let old_time = now - std::time::Duration::from_secs(20 * 60); // 20 minutes ago
        assert!(!AuthService::should_rate_limit(5, old_time));
    }
}
