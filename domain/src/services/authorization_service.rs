// Authorization domain service
// Contains authorization business rules and policy evaluation

use crate::entities::{Permission, Role};
use crate::errors::DomainError;
use crate::value_objects::UserId;

pub struct AuthorizationService;

impl AuthorizationService {
    /// Check if a user has a specific permission
    pub fn has_permission(user_roles: &[Role], required_permission: &Permission) -> bool {
        user_roles
            .iter()
            .any(|role| role.is_active() && role.has_permission(required_permission))
    }

    /// Check if a user has any of the required permissions
    pub fn has_any_permission(user_roles: &[Role], required_permissions: &[Permission]) -> bool {
        required_permissions
            .iter()
            .any(|permission| Self::has_permission(user_roles, permission))
    }

    /// Check if a user has all of the required permissions
    pub fn has_all_permissions(user_roles: &[Role], required_permissions: &[Permission]) -> bool {
        required_permissions
            .iter()
            .all(|permission| Self::has_permission(user_roles, permission))
    }

    /// Check if a user can access a resource based on ownership
    pub fn can_access_own_resource(
        requesting_user_id: &UserId,
        resource_owner_id: &UserId,
        user_roles: &[Role],
    ) -> bool {
        // User can access their own resources
        if requesting_user_id == resource_owner_id {
            return true;
        }

        // Or if they have admin permission
        Self::has_permission(user_roles, &Permission::Admin)
    }

    /// Check if a user is an administrator
    pub fn is_admin(user_roles: &[Role]) -> bool {
        Self::has_permission(user_roles, &Permission::Admin)
    }

    /// Get effective permissions for a user (including inherited permissions)
    pub fn get_effective_permissions(user_roles: &[Role]) -> Vec<Permission> {
        let mut permissions = Vec::new();

        for role in user_roles {
            if role.is_active() {
                for permission in role.permissions() {
                    if !permissions.contains(permission) {
                        permissions.push(permission.clone());
                    }
                }
            }
        }

        permissions
    }

    /// Check role hierarchy - if a role inherits from another
    pub fn inherits_from_role(
        child_role: &Role,
        parent_role_id: &crate::value_objects::RoleId,
        all_roles: &[Role],
    ) -> bool {
        if let Some(parent_id) = child_role.parent_role() {
            if parent_id == parent_role_id {
                return true;
            }

            // Check if any parent role inherits from the target role (recursive)
            if let Some(parent_role) = all_roles.iter().find(|r| r.id() == parent_id) {
                return Self::inherits_from_role(parent_role, parent_role_id, all_roles);
            }
        }

        false
    }

    /// Validate that a role assignment is allowed
    pub fn can_assign_role(
        assigner_roles: &[Role],
        role_to_assign: &Role,
    ) -> Result<(), DomainError> {
        // Only admins can assign roles
        if !Self::is_admin(assigner_roles) {
            return Err(DomainError::AuthorizationFailed);
        }

        // Can't assign inactive roles
        if !role_to_assign.is_active() {
            return Err(DomainError::InvalidRole(
                "Cannot assign inactive role".to_string(),
            ));
        }

        Ok(())
    }

    /// Check if a user can modify another user's data
    pub fn can_modify_user(
        modifier_user_id: &UserId,
        target_user_id: &UserId,
        modifier_roles: &[Role],
    ) -> bool {
        // Users can modify their own data
        if modifier_user_id == target_user_id {
            return true;
        }

        // Admins can modify any user's data
        Self::is_admin(modifier_roles)
    }

    /// Generate an authorization context for logging and auditing
    pub fn create_authorization_context(
        user_id: &UserId,
        action: &str,
        resource: &str,
        granted: bool,
    ) -> AuthorizationContext {
        AuthorizationContext {
            user_id: user_id.clone(),
            action: action.to_string(),
            resource: resource.to_string(),
            granted,
            timestamp: std::time::SystemTime::now(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct AuthorizationContext {
    pub user_id: UserId,
    pub action: String,
    pub resource: String,
    pub granted: bool,
    pub timestamp: std::time::SystemTime,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::value_objects::RoleId;

    fn create_test_role(id: &str, permissions: Vec<Permission>) -> Role {
        let role_id = RoleId::from_string(format!("role_{}", "a".repeat(32))).unwrap();
        Role::new(role_id, format!("{id} Role"), None, permissions).unwrap()
    }

    #[test]
    fn test_has_permission() {
        let roles = vec![
            create_test_role("read_role", vec![Permission::Read]),
            create_test_role("write_role", vec![Permission::Write]),
        ];

        assert!(AuthorizationService::has_permission(
            &roles,
            &Permission::Read
        ));
        assert!(AuthorizationService::has_permission(
            &roles,
            &Permission::Write
        ));
        assert!(!AuthorizationService::has_permission(
            &roles,
            &Permission::Delete
        ));
    }

    #[test]
    fn test_admin_permission() {
        let admin_roles = vec![create_test_role("admin", vec![Permission::Admin])];

        // Admin should have all permissions
        assert!(AuthorizationService::has_permission(
            &admin_roles,
            &Permission::Read
        ));
        assert!(AuthorizationService::has_permission(
            &admin_roles,
            &Permission::Write
        ));
        assert!(AuthorizationService::has_permission(
            &admin_roles,
            &Permission::Delete
        ));
        assert!(AuthorizationService::is_admin(&admin_roles));
    }

    #[test]
    fn test_has_any_permission() {
        let roles = vec![create_test_role("read_role", vec![Permission::Read])];

        let required_permissions = vec![Permission::Read, Permission::Write];
        assert!(AuthorizationService::has_any_permission(
            &roles,
            &required_permissions
        ));

        let other_permissions = vec![Permission::Write, Permission::Delete];
        assert!(!AuthorizationService::has_any_permission(
            &roles,
            &other_permissions
        ));
    }

    #[test]
    fn test_has_all_permissions() {
        let roles = vec![create_test_role(
            "multi_role",
            vec![Permission::Read, Permission::Write],
        )];

        let required_permissions = vec![Permission::Read, Permission::Write];
        assert!(AuthorizationService::has_all_permissions(
            &roles,
            &required_permissions
        ));

        let more_permissions = vec![Permission::Read, Permission::Write, Permission::Delete];
        assert!(!AuthorizationService::has_all_permissions(
            &roles,
            &more_permissions
        ));
    }

    #[test]
    fn test_can_access_own_resource() {
        let user_id = UserId::new();
        let other_user_id = UserId::new();
        let roles = vec![create_test_role("user", vec![Permission::Read])];

        // User can access their own resources
        assert!(AuthorizationService::can_access_own_resource(
            &user_id, &user_id, &roles
        ));

        // User cannot access other's resources without admin
        assert!(!AuthorizationService::can_access_own_resource(
            &user_id,
            &other_user_id,
            &roles
        ));

        // Admin can access any resource
        let admin_roles = vec![create_test_role("admin", vec![Permission::Admin])];
        assert!(AuthorizationService::can_access_own_resource(
            &user_id,
            &other_user_id,
            &admin_roles
        ));
    }

    #[test]
    fn test_get_effective_permissions() {
        let roles = vec![
            create_test_role("role1", vec![Permission::Read, Permission::Write]),
            create_test_role("role2", vec![Permission::Write, Permission::Delete]),
        ];

        let permissions = AuthorizationService::get_effective_permissions(&roles);

        assert!(permissions.contains(&Permission::Read));
        assert!(permissions.contains(&Permission::Write));
        assert!(permissions.contains(&Permission::Delete));

        // Should not have duplicates
        assert_eq!(permissions.len(), 3);
    }

    #[test]
    fn test_can_assign_role() {
        let admin_roles = vec![create_test_role("admin", vec![Permission::Admin])];
        let user_roles = vec![create_test_role("user", vec![Permission::Read])];
        let target_role = create_test_role("target", vec![Permission::Write]);

        // Admin can assign roles
        assert!(AuthorizationService::can_assign_role(&admin_roles, &target_role).is_ok());

        // Regular user cannot assign roles
        assert!(AuthorizationService::can_assign_role(&user_roles, &target_role).is_err());
    }

    #[test]
    fn test_can_modify_user() {
        let user_id = UserId::new();
        let other_user_id = UserId::new();
        let user_roles = vec![create_test_role("user", vec![Permission::Read])];
        let admin_roles = vec![create_test_role("admin", vec![Permission::Admin])];

        // Users can modify themselves
        assert!(AuthorizationService::can_modify_user(
            &user_id,
            &user_id,
            &user_roles
        ));

        // Users cannot modify others without admin
        assert!(!AuthorizationService::can_modify_user(
            &user_id,
            &other_user_id,
            &user_roles
        ));

        // Admins can modify anyone
        assert!(AuthorizationService::can_modify_user(
            &user_id,
            &other_user_id,
            &admin_roles
        ));
    }
}
