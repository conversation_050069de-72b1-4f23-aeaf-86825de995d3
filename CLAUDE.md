# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Aethelus AuthService - a Rust-based authentication and authorization service following Domain-Driven Design (DDD) principles with hexagonal architecture. The project is currently in the planning phase with comprehensive documentation but no implementation yet.

## Critical Requirements

### Rust Configuration
- **Rust Edition**: 2024 (MANDATORY - do not change)
- **Workspace Resolver**: 3 (MANDATORY - do not change)
- **Dependency Versions**: NEVER change dependency versions in Cargo.toml (MANDATORY - if API issues arise, check documentation instead of changing versions)
- **No Prometheus**: This project does not use Prometheus

### Architecture Rules
- Follow Domain-Driven Design with hexagonal architecture
- Domain crate cannot depend on OTHER PROJECT LAYERS (application, infrastructure, server)
- Domain crate may use external crates from crates.io as needed for business logic
- Use static dispatch via generics instead of dyn traits
- Separate domain models from persistence records
- All code must be developed using Test-Driven Development (TDD)

### Development Workflow
1. **Requirements Audit**: Extract all mandates from docs before coding
2. **User Validation**: Present understanding for approval before implementation
3. **TDD Cycle**: Write failing tests first → implement → refactor
4. **Quality Gates**: Run clippy, fmt, and audit before considering complete
5. **Code Review**: Submit to code-review agent → iterate on feedback → achieve approval
6. **Task Handoff**: Document review scores and approval in handoff notes

### Code Review Integration

After completing implementation and passing local quality checks, all code must be reviewed by the code-review agent before task completion. The review process follows these steps:

1. **Submit for Review**: After committing changes, invoke the code-review agent with context:
   ```
   "Review implementation of [task description] focusing on [specific concerns]"
   ```

2. **Review Scoring**: The code-review agent evaluates based on weighted criteria:
   - Security: 40% (must be ≥90%)
   - Testing: 25%
   - Performance: 20%
   - Quality: 10%
   - Documentation: 5%
   - Overall score must be ≥95% for approval

3. **Iteration Process**:
   - If not approved: Address all CRITICAL and HIGH PRIORITY feedback
   - Re-run quality checks after changes
   - Re-submit for review with note about addressed feedback
   - Continue until approval achieved

4. **Handoff Documentation**: Include final review scores in task completion:
   ```markdown
   ## Task Completion
   - Implementation: ✓ Complete
   - Tests: ✓ 98% coverage
   - Code Review: ✓ Approved
     - Security: 95/100
     - Testing: 96/100
     - Performance: 100/100
     - Quality: 98/100
     - Overall: 96.35%
   ```

## Build and Development Commands

```bash
# Build the workspace
cargo build --workspace

# Run all tests
cargo test --workspace

# Run specific test categories
cargo test --lib                    # Unit tests only
cargo test tests/integration        # Mock integration tests
cargo test tests/live_integration   # Live integration tests with real DB
cargo test tests/e2e               # End-to-end tests

# Code quality checks
cargo fmt --all -- --check         # Check formatting
cargo clippy --all-targets --all-features -- -D warnings
cargo audit --ignore RUSTSEC-2023-0071  # Security audit (ignore known MySQL/RSA issue)

# Coverage
cargo llvm-cov --workspace --html  # Generate coverage report

# Run a single test
cargo test test_name -- --exact

# Run tests in a specific module
cargo test module::path::to::tests

# Benchmarks
cargo bench

# Database migrations (when implemented)
sqlx migrate run
```

## Project Structure

The project uses a Cargo workspace with clear separation of concerns:

- `domain/` - Pure business logic, independent of other project layers
- `application/` - Use cases and command/query handlers
- `infrastructure/` - External adapters (DB, cache, email, etc.)
- `server/` - API layers (REST/gRPC) and main entry point
- `tests/` - Integration and E2E tests organized by type

## Key Development Principles

1. **Safety First**: No unwrap/expect in production code, explicit error handling
2. **Performance**: Use arrays over Vec for known sizes, static dispatch
3. **Testing**: 100% unit test coverage for functions, comprehensive integration tests
4. **Security**: Include security tests in every category, constant-time operations for crypto
5. **Idiomatic Rust**: Leverage ownership/borrowing, prefer iterators, minimize unsafe

## Testing Standards

- **Unit Tests**: In-file under `#[cfg(test)]`, 100% function coverage
- **Mock Integration**: Fast tests with mockall for component interactions
- **Live Integration**: Real DB/server tests using testcontainers
- **E2E Tests**: Browser automation for user flows (OAuth, SAML)

## Dependencies

All dependencies are pinned in `Docs/NewPlans/Pinned-Versions.md`. Key crates:
- sqlx 0.8.6 for database
- tokio 1.47.0 for async runtime
- figment 0.10.19 for configuration
- argon2 0.6.0-rc.0 for password hashing
- tonic 0.14.0 for gRPC

## Security Considerations

- Authentication service requires exceptional security focus
- All auth operations must complete in <100ms
- Include timing attack prevention
- Implement rate limiting and threat detection
- Use WebAuthn, MFA (TOTP/SMS/email), and passwordless flows

## Common Tasks

When implementing features:
1. Review relevant documentation in `Docs/NewPlans/`
2. Follow the architecture in `Architectural-Standards.md`
3. Apply rules from `AgentRules.md` and `SecurityRules.md`
4. Use TDD workflow from `TestingStandards.md`
5. Implement features from `Auth-Features-List.md` in priority order

## Rust Development Rules (Lessons Learned from Phase D)

### CRITICAL: Continuous Compilation Validation
**NEVER accumulate non-compiling code. Each increment must compile.**

1. **Maximum Non-Compiling Lines**: Never exceed 10-20 lines without running `cargo check`
2. **Commit Requirements**: All commits must compile and pass basic tests
3. **Quality Gates**: Run after every change:
   ```bash
   cargo check --workspace        # Basic compilation check
   cargo test --workspace         # All tests must pass
   cargo clippy --workspace       # No warnings allowed
   cargo fmt --all               # Code formatting
   ```

### Async Development Patterns
**Rust's ownership system requires careful async design**

1. **Interior Mutability for Monitoring**: Use `Arc<Mutex<>>` for shared mutable state in async contexts
2. **Repository Traits**: If trait requires `&self` but implementation needs mutation, use interior mutability
3. **Lifetime Management**: Plan async lifetimes carefully - borrowing across await points is complex
4. **Test Async Patterns**: Validate async patterns in isolation before integration

### Domain Error Architecture
**Infrastructure errors must map cleanly to domain concepts**

1. **Domain-First Design**: Design domain errors based on business use cases first
2. **Layer Boundaries**: Infrastructure errors must be transformed at boundaries
3. **Required Domain Error Variants**: Ensure domain includes Infrastructure, Configuration, External variants
4. **Error Mapping Documentation**: Document transformation strategy between layers

### Dependency and Feature Management
**Validate dependencies work before complex implementation**

1. **Feature Flag Validation**: Test with minimal examples before full implementation
2. **Build-Time Macros**: Verify macros work (e.g., `sqlx::migrate!`) with simple usage
3. **Path Resolution**: Use workspace root paths for migrations, not relative crate paths
4. **Dependency Testing**: Create proof-of-concept for new dependencies

### Trait Bound Management
**Generic constraints must propagate through the type system**

1. **Upfront Specification**: Declare all required bounds (Serialize, Clone, etc.) immediately
2. **Propagation Testing**: Test generic code with realistic type parameters
3. **Constraint Documentation**: Document why specific bounds are required
4. **Cascading Fa1ilure Prevention**: Missing bounds cause widespread compilation errors

### TDD with Compilation Gates
**Test-driven development must enforce compilable increments**

1. **Failing Test First**: Write tests that require compilation before implementing
2. **Minimal Implementation**: Implement just enough to make tests pass and compile
3. **Incremental Refactoring**: Refactor while maintaining compilation and passing tests
4. **Architecture Validation**: If it doesn't compile, the design needs revision

### Infrastructure Development Protocol
**Special rules for infrastructure crate due to complexity**

1. **Trait Implementation Order**: Basic trait skeleton first, then one method at a time
2. **Async Repository Pattern**: Use interior mutability for performance monitoring in `&self` methods
3. **Database Transaction Handling**: Plan transaction boundaries and error propagation upfront
4. **Cache Implementation**: Validate serialization bounds before implementing cache logic

### Pre-Commit Quality Gates
**MANDATORY checks before any commit**

1. **Compilation**: `cargo build --workspace` must succeed
2. **Testing**: `cargo test --workspace` must pass
3. **Linting**: `cargo clippy --workspace` must pass with no warnings
4. **Formatting**: `cargo fmt --all` must be applied
5. **Security**: `cargo audit` must pass (known exceptions documented)

### Emergency Recovery Protocol
**When compilation errors accumulate**

1. **Stop Adding Features**: Fix compilation before adding functionality
2. **Isolate Errors**: Fix one compilation error at a time
3. **Revert Strategy**: Be prepared to revert to last compiling state
4. **Root Cause Analysis**: Document what caused the accumulation
5. **Process Review**: Update development rules to prevent recurrence