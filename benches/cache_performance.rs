// Cache performance benchmarks
// Verifies that caching meets SLA requirements

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use auth_infrastructure::{
    adapters::cache::{RedisCache, RedisCacheQueryAdapter, CacheKeyGenerator},
    CacheConfiguration,
};
use auth_application::queries::{
    base::{Query, QueryCache, QueryContext, AsyncQueryHandler},
    get_user_profile::{GetUserProfileQuery, UserProfileResult, GetUserProfileHandler},
    check_user_permissions::{CheckUserPermissionsQuery, PermissionCheckResult, CheckUserPermissionsHandler},
};
use auth_domain::{
    entities::User,
    repositories::{UserRepository, RoleRepository},
    value_objects::{Email, Password, UserId, SessionId},
    errors::DomainError,
};
use std::time::Duration;
use tokio::runtime::Runtime;

// Mock repositories for benchmarking
struct MockUserRepository;

#[async_trait::async_trait]
impl UserRepository for MockUserRepository {
    async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
        // Simulate database latency
        tokio::time::sleep(Duration::from_millis(20)).await;
        
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("BenchPassword123!").unwrap();
        let user = User::new(email, password).unwrap();
        Ok(Some(user))
    }

    async fn save(&self, _user: &User) -> Result<(), DomainError> {
        Ok(())
    }

    async fn find_by_email(&self, _email: &Email) -> Result<Option<User>, DomainError> {
        Ok(None)
    }

    async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
        Ok(None)
    }

    async fn exists_by_email(&self, _email: &Email) -> Result<bool, DomainError> {
        Ok(false)
    }

    async fn delete(&self, _id: &UserId) -> Result<(), DomainError> {
        Ok(())
    }

    async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
        Ok(Vec::new())
    }

    async fn count(&self) -> Result<usize, DomainError> {
        Ok(0)
    }

    async fn find_by_created_date_range(
        &self,
        _start: std::time::SystemTime,
        _end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError> {
        Ok(Vec::new())
    }

    async fn find_by_verification_status(
        &self,
        _is_verified: bool,
    ) -> Result<Vec<User>, DomainError> {
        Ok(Vec::new())
    }

    async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
        Ok(Vec::new())
    }

    async fn search_users(
        &self,
        _criteria: &auth_domain::repositories::UserSearchCriteria,
    ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
        Ok(auth_domain::repositories::UserSearchResult {
            users: Vec::new(),
            total: 0,
        })
    }
}

fn bench_cache_hit_performance(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    // Setup cache and handler
    let redis_cache = rt.block_on(async {
        RedisCache::new("redis://localhost:6379/2").expect("Redis connection required for benchmarks")
    });
    
    let key_generator = CacheKeyGenerator::new("bench".to_string());
    let cache_adapter = RedisCacheQueryAdapter::<GetUserProfileQuery>::new(
        redis_cache,
        key_generator,
        Duration::from_secs(300),
    );
    
    let user_repo = MockUserRepository;
    let handler = GetUserProfileHandler::new(user_repo, cache_adapter);
    
    let query = GetUserProfileQuery {
        user_id: "bench_user_123".to_string(),
        include_roles: false,
        include_sessions: false,
        include_mfa_status: false,
    };
    
    let context = QueryContext::for_user(
        "bench-req".to_string(),
        UserId::from_string("bench_user_123".to_string()).unwrap(),
        SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
        vec!["user:read".to_string()],
    );
    
    // Pre-populate cache
    rt.block_on(async {
        let _ = handler.handle_with_context(query.clone(), context.clone()).await;
    });
    
    // Benchmark cache hits
    c.bench_function("cache_hit_user_profile", |b| {
        b.to_async(&rt).iter(|| async {
            let result = handler.handle_with_context(
                black_box(query.clone()),
                black_box(context.clone())
            ).await;
            black_box(result)
        })
    });
}

fn bench_cache_miss_performance(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    // Setup cache and handler
    let redis_cache = rt.block_on(async {
        RedisCache::new("redis://localhost:6379/2").expect("Redis connection required for benchmarks")
    });
    
    let key_generator = CacheKeyGenerator::new("bench".to_string());
    let cache_adapter = RedisCacheQueryAdapter::<GetUserProfileQuery>::new(
        redis_cache,
        key_generator,
        Duration::from_secs(300),
    );
    
    let user_repo = MockUserRepository;
    let handler = GetUserProfileHandler::new(user_repo, cache_adapter);
    
    let mut counter = 0;
    
    c.bench_function("cache_miss_user_profile", |b| {
        b.to_async(&rt).iter(|| {
            counter += 1;
            let query = GetUserProfileQuery {
                user_id: format!("bench_user_{}", counter),
                include_roles: false,
                include_sessions: false,
                include_mfa_status: false,
            };
            
            let context = QueryContext::for_user(
                "bench-req".to_string(),
                UserId::from_string(format!("bench_user_{}", counter)).unwrap(),
                SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
                vec!["user:read".to_string()],
            );
            
            async {
                let result = handler.handle_with_context(
                    black_box(query),
                    black_box(context)
                ).await;
                black_box(result)
            }
        })
    });
}

fn bench_permission_check_performance(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("permission_checks");
    
    // Different permission check scenarios
    let scenarios = vec![
        ("single_permission", vec!["read:documents".to_string()]),
        ("multiple_permissions", vec![
            "read:documents".to_string(),
            "write:documents".to_string(),
            "delete:documents".to_string(),
        ]),
        ("complex_permissions", vec![
            "admin:users:read".to_string(),
            "admin:users:write".to_string(),
            "admin:roles:read".to_string(),
            "admin:roles:write".to_string(),
            "security:audit:read".to_string(),
        ]),
    ];
    
    for (scenario_name, permissions) in scenarios {
        group.bench_with_input(
            BenchmarkId::new("cached", scenario_name),
            &permissions,
            |b, perms| {
                let redis_cache = rt.block_on(async {
                    RedisCache::new("redis://localhost:6379/2").unwrap()
                });
                
                let key_generator = CacheKeyGenerator::new("bench".to_string());
                let cache_adapter = RedisCacheQueryAdapter::<CheckUserPermissionsQuery>::new(
                    redis_cache,
                    key_generator,
                    Duration::from_secs(300),
                );
                
                let role_repo = MockRoleRepository;
                let handler = CheckUserPermissionsHandler::new(role_repo, cache_adapter);
                
                let query = CheckUserPermissionsQuery {
                    user_id: "bench_user".to_string(),
                    permissions: perms.clone(),
                    resource_id: Some("doc:123".to_string()),
                    check_all: true,
                };
                
                let context = QueryContext::for_user(
                    "bench-req".to_string(),
                    UserId::from_string("bench_user".to_string()).unwrap(),
                    SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
                    vec!["user:read".to_string()],
                );
                
                // Pre-populate cache
                rt.block_on(async {
                    let _ = handler.handle_with_context(query.clone(), context.clone()).await;
                });
                
                b.to_async(&rt).iter(|| async {
                    let result = handler.handle_with_context(
                        black_box(query.clone()),
                        black_box(context.clone())
                    ).await;
                    black_box(result)
                })
            },
        );
    }
    
    group.finish();
}

fn bench_concurrent_cache_access(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("concurrent_access");
    group.sample_size(10);
    
    for num_concurrent in [10, 50, 100, 500].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_requests", num_concurrent),
            num_concurrent,
            |b, &num| {
                let redis_cache = rt.block_on(async {
                    RedisCache::new("redis://localhost:6379/2").unwrap()
                });
                
                let key_generator = CacheKeyGenerator::new("bench".to_string());
                let cache_adapter = RedisCacheQueryAdapter::<GetUserProfileQuery>::new(
                    redis_cache,
                    key_generator,
                    Duration::from_secs(300),
                );
                
                let user_repo = MockUserRepository;
                let handler = std::sync::Arc::new(GetUserProfileHandler::new(user_repo, cache_adapter));
                
                let query = GetUserProfileQuery {
                    user_id: "concurrent_user".to_string(),
                    include_roles: false,
                    include_sessions: false,
                    include_mfa_status: false,
                };
                
                let context = QueryContext::for_user(
                    "bench-req".to_string(),
                    UserId::from_string("concurrent_user".to_string()).unwrap(),
                    SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
                    vec!["user:read".to_string()],
                );
                
                // Pre-populate cache
                rt.block_on(async {
                    let _ = handler.handle_with_context(query.clone(), context.clone()).await;
                });
                
                b.to_async(&rt).iter(|| {
                    let handler = handler.clone();
                    let query = query.clone();
                    let context = context.clone();
                    
                    async move {
                        let mut handles = Vec::new();
                        
                        for _ in 0..num {
                            let h = handler.clone();
                            let q = query.clone();
                            let c = context.clone();
                            
                            let handle = tokio::spawn(async move {
                                h.handle_with_context(q, c).await
                            });
                            
                            handles.push(handle);
                        }
                        
                        for handle in handles {
                            let _ = handle.await;
                        }
                    }
                })
            },
        );
    }
    
    group.finish();
}

// Mock role repository
struct MockRoleRepository;

#[async_trait::async_trait]
impl RoleRepository for MockRoleRepository {
    async fn save(&self, _role: &auth_domain::entities::Role) -> Result<(), DomainError> {
        Ok(())
    }

    async fn find_by_id(&self, _id: &auth_domain::value_objects::RoleId) -> Result<Option<auth_domain::entities::Role>, DomainError> {
        // Simulate database latency
        tokio::time::sleep(Duration::from_millis(15)).await;
        Ok(None)
    }

    async fn find_by_name(&self, _name: &str) -> Result<Option<auth_domain::entities::Role>, DomainError> {
        Ok(None)
    }

    async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<auth_domain::entities::Role>, DomainError> {
        Ok(Vec::new())
    }

    async fn delete(&self, _id: &auth_domain::value_objects::RoleId) -> Result<(), DomainError> {
        Ok(())
    }

    async fn find_by_user_id(&self, _user_id: &UserId) -> Result<Vec<auth_domain::entities::Role>, DomainError> {
        // Simulate database latency
        tokio::time::sleep(Duration::from_millis(15)).await;
        Ok(Vec::new())
    }

    async fn assign_to_user(&self, _role_id: &auth_domain::value_objects::RoleId, _user_id: &UserId) -> Result<(), DomainError> {
        Ok(())
    }

    async fn remove_from_user(&self, _role_id: &auth_domain::value_objects::RoleId, _user_id: &UserId) -> Result<(), DomainError> {
        Ok(())
    }

    async fn count(&self) -> Result<usize, DomainError> {
        Ok(0)
    }
}

criterion_group!(
    benches,
    bench_cache_hit_performance,
    bench_cache_miss_performance,
    bench_permission_check_performance,
    bench_concurrent_cache_access
);
criterion_main!(benches);