// Integration tests for cache infrastructure
// Tests end-to-end caching functionality with query handlers

use auth_application::queries::{
    base::{Query, QueryCache, QueryContext, AsyncQueryHandler},
    get_user_profile::{GetUserProfileQuery, UserProfileResult, GetUserProfileHandler},
    check_user_permissions::{CheckUserPermissionsQuery, PermissionCheckResult, CheckUserPermissionsHandler},
    list_user_roles::{ListUserRolesQuery, UserRolesResult, ListUserRolesHandler},
    search_users::{SearchUsersQuery, SearchUsersHandler},
};
use auth_domain::{
    entities::User,
    repositories::{UserRepository, RoleRepository},
    value_objects::{Email, Password, UserId, SessionId, RoleId},
    errors::DomainError,
};
use auth_infrastructure::adapters::cache::{
    RedisCacheQueryAdapter, CacheKeyGenerator, RedisCache,
    RedisCacheInvalidator, CacheInvalidator,
};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::sleep;

/// Test fixture for cache integration tests
struct CacheTestFixture {
    redis_cache: Arc<RedisCache>,
    key_generator: CacheKeyGenerator,
}

impl CacheTestFixture {
    async fn new() -> Self {
        let redis_cache = Arc::new(
            RedisCache::new("redis://localhost:6379/1") // Use database 1 for tests
                .expect("Failed to connect to Redis")
        );
        
        let key_generator = CacheKeyGenerator::new("test".to_string());
        
        Self {
            redis_cache,
            key_generator,
        }
    }

    fn create_cache_adapter<Q>(&self) -> RedisCacheQueryAdapter<Q>
    where
        Q: Query,
        Q::Result: serde::Serialize + for<'de> serde::Deserialize<'de>,
    {
        RedisCacheQueryAdapter::new(
            self.redis_cache.as_ref().clone(),
            self.key_generator.clone(),
            Duration::from_secs(60), // 1 minute TTL for tests
        )
    }
}

#[tokio::test]
async fn test_user_profile_caching_end_to_end() {
    let fixture = CacheTestFixture::new().await;
    let cache_adapter = fixture.create_cache_adapter::<GetUserProfileQuery>();
    
    // Create mock repositories
    let mut user_repo = MockUserRepository::new();
    let test_user = create_test_user();
    user_repo.add_user(test_user.clone());
    
    // Create handler with cache
    let handler = GetUserProfileHandler::new(user_repo, cache_adapter);
    
    let query = GetUserProfileQuery {
        user_id: test_user.id().as_str().to_string(),
        include_roles: true,
        include_sessions: true,
        include_mfa_status: true,
    };
    
    let context = QueryContext::for_user(
        "req-123".to_string(),
        test_user.id().clone(),
        SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
        vec!["user:read".to_string()],
    );
    
    // First call - cache miss
    let start = Instant::now();
    let result1 = handler.handle_with_context(query.clone(), context.clone()).await.unwrap();
    let first_call_duration = start.elapsed();
    
    // Second call - cache hit
    let start = Instant::now();
    let result2 = handler.handle_with_context(query.clone(), context.clone()).await.unwrap();
    let second_call_duration = start.elapsed();
    
    // Assertions
    assert_eq!(result1.user_id, result2.user_id);
    assert_eq!(result1.email, result2.email);
    
    // Cache hit should be significantly faster
    assert!(
        second_call_duration < first_call_duration / 2,
        "Cache hit ({:?}) should be faster than cache miss ({:?})",
        second_call_duration,
        first_call_duration
    );
    
    // Verify SLA compliance
    assert!(
        second_call_duration < Duration::from_millis(5),
        "Cached response exceeded 5ms SLA: {:?}",
        second_call_duration
    );
}

#[tokio::test]
async fn test_permission_check_caching() {
    let fixture = CacheTestFixture::new().await;
    let cache_adapter = fixture.create_cache_adapter::<CheckUserPermissionsQuery>();
    
    // Create mock repositories
    let role_repo = MockRoleRepository::new();
    
    // Create handler with cache
    let handler = CheckUserPermissionsHandler::new(role_repo, cache_adapter);
    
    let query = CheckUserPermissionsQuery {
        user_id: "user123".to_string(),
        permissions: vec!["read:documents".to_string(), "write:documents".to_string()],
        resource_id: Some("doc:456".to_string()),
        check_all: true,
    };
    
    let context = QueryContext::for_user(
        "req-456".to_string(),
        UserId::from_string("user123".to_string()).unwrap(),
        SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
        vec!["user:read".to_string()],
    );
    
    // Multiple calls to test caching
    let mut durations = Vec::new();
    for i in 0..5 {
        let start = Instant::now();
        let _ = handler.handle_with_context(query.clone(), context.clone()).await;
        durations.push(start.elapsed());
    }
    
    // First call should be slowest (cache miss)
    let first_duration = durations[0];
    let avg_cached_duration = durations[1..].iter().sum::<Duration>() / 4;
    
    assert!(
        avg_cached_duration < first_duration / 2,
        "Average cached response time ({:?}) should be much faster than initial call ({:?})",
        avg_cached_duration,
        first_duration
    );
}

#[tokio::test]
async fn test_cache_invalidation_on_user_update() {
    let fixture = CacheTestFixture::new().await;
    let cache_adapter = fixture.create_cache_adapter::<GetUserProfileQuery>();
    let invalidator = RedisCacheInvalidator::new(
        fixture.redis_cache.clone(),
        fixture.key_generator.clone(),
    );
    
    // Setup
    let mut user_repo = MockUserRepository::new();
    let test_user = create_test_user();
    user_repo.add_user(test_user.clone());
    
    let handler = GetUserProfileHandler::new(user_repo, cache_adapter);
    
    let query = GetUserProfileQuery {
        user_id: test_user.id().as_str().to_string(),
        include_roles: false,
        include_sessions: false,
        include_mfa_status: false,
    };
    
    let context = QueryContext::for_user(
        "req-789".to_string(),
        test_user.id().clone(),
        SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
        vec!["user:read".to_string()],
    );
    
    // First call to populate cache
    let result1 = handler.handle_with_context(query.clone(), context.clone()).await.unwrap();
    
    // Simulate user update event
    let update_event = auth_domain::events::DomainEvent::EmailChanged {
        user_id: test_user.id().clone(),
        old_email: test_user.email().clone(),
        new_email: Email::new("<EMAIL>").unwrap(),
    };
    
    // Invalidate cache
    invalidator.invalidate_for_event(&update_event).await;
    
    // Small delay to ensure invalidation completes
    sleep(Duration::from_millis(10)).await;
    
    // Next call should be a cache miss
    let start = Instant::now();
    let result2 = handler.handle_with_context(query.clone(), context.clone()).await.unwrap();
    let duration = start.elapsed();
    
    // Should take longer due to cache miss
    assert!(
        duration > Duration::from_millis(5),
        "Expected cache miss after invalidation, but got fast response: {:?}",
        duration
    );
}

#[tokio::test]
async fn test_search_query_caching_with_different_parameters() {
    let fixture = CacheTestFixture::new().await;
    let cache_adapter = fixture.create_cache_adapter::<SearchUsersQuery>();
    
    let user_repo = MockUserRepository::new();
    let handler = SearchUsersHandler::new(user_repo, cache_adapter);
    
    // Two different search queries
    let query1 = SearchUsersQuery {
        email: Some("<EMAIL>".to_string()),
        username: None,
        is_active: Some(true),
        is_verified: None,
        created_after: None,
        created_before: None,
        page: 0,
        page_size: 20,
        sort_by: None,
        sort_direction: None,
    };
    
    let query2 = SearchUsersQuery {
        email: None,
        username: Some("testuser".to_string()),
        is_active: Some(true),
        is_verified: None,
        created_after: None,
        created_before: None,
        page: 0,
        page_size: 20,
        sort_by: None,
        sort_direction: None,
    };
    
    let context = QueryContext::for_user(
        "req-search".to_string(),
        UserId::from_string("admin".to_string()).unwrap(),
        SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
        vec!["admin:users:read".to_string()],
    );
    
    // Execute both queries twice
    let start1 = Instant::now();
    let _ = handler.handle_with_context(query1.clone(), context.clone()).await;
    let first_query1_duration = start1.elapsed();
    
    let start2 = Instant::now();
    let _ = handler.handle_with_context(query2.clone(), context.clone()).await;
    let first_query2_duration = start2.elapsed();
    
    // Second calls should be cached
    let start1 = Instant::now();
    let _ = handler.handle_with_context(query1.clone(), context.clone()).await;
    let cached_query1_duration = start1.elapsed();
    
    let start2 = Instant::now();
    let _ = handler.handle_with_context(query2.clone(), context.clone()).await;
    let cached_query2_duration = start2.elapsed();
    
    // Both should have separate cache entries
    assert!(cached_query1_duration < first_query1_duration / 2);
    assert!(cached_query2_duration < first_query2_duration / 2);
}

#[tokio::test]
async fn test_cache_ttl_expiration() {
    let fixture = CacheTestFixture::new().await;
    
    // Create adapter with very short TTL
    let cache_adapter = RedisCacheQueryAdapter::<GetUserProfileQuery>::new(
        fixture.redis_cache.as_ref().clone(),
        fixture.key_generator.clone(),
        Duration::from_secs(1), // 1 second TTL
    );
    
    let mut user_repo = MockUserRepository::new();
    let test_user = create_test_user();
    user_repo.add_user(test_user.clone());
    
    let handler = GetUserProfileHandler::new(user_repo, cache_adapter);
    
    let query = GetUserProfileQuery {
        user_id: test_user.id().as_str().to_string(),
        include_roles: false,
        include_sessions: false,
        include_mfa_status: false,
    };
    
    let context = QueryContext::for_user(
        "req-ttl".to_string(),
        test_user.id().clone(),
        SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
        vec!["user:read".to_string()],
    );
    
    // First call to populate cache
    let _ = handler.handle_with_context(query.clone(), context.clone()).await.unwrap();
    
    // Immediate call should hit cache
    let start = Instant::now();
    let _ = handler.handle_with_context(query.clone(), context.clone()).await.unwrap();
    let cached_duration = start.elapsed();
    assert!(cached_duration < Duration::from_millis(5));
    
    // Wait for TTL to expire
    sleep(Duration::from_secs(2)).await;
    
    // Call after TTL should miss cache
    let start = Instant::now();
    let _ = handler.handle_with_context(query.clone(), context.clone()).await.unwrap();
    let expired_duration = start.elapsed();
    
    assert!(
        expired_duration > Duration::from_millis(5),
        "Expected cache miss after TTL expiration, but got fast response: {:?}",
        expired_duration
    );
}

#[tokio::test]
async fn test_concurrent_cache_access() {
    let fixture = CacheTestFixture::new().await;
    let cache_adapter = Arc::new(fixture.create_cache_adapter::<GetUserProfileQuery>());
    
    let user_repo = Arc::new(MockUserRepository::new());
    let test_user = create_test_user();
    user_repo.add_user(test_user.clone());
    
    let handler = Arc::new(GetUserProfileHandler::new(
        user_repo.as_ref().clone(),
        cache_adapter.as_ref().clone(),
    ));
    
    let query = GetUserProfileQuery {
        user_id: test_user.id().as_str().to_string(),
        include_roles: false,
        include_sessions: false,
        include_mfa_status: false,
    };
    
    let context = QueryContext::for_user(
        "req-concurrent".to_string(),
        test_user.id().clone(),
        SessionId::from_string("sess_abcdef123456789012345678901234567890".to_string()).unwrap(),
        vec!["user:read".to_string()],
    );
    
    // Spawn multiple concurrent requests
    let mut handles = Vec::new();
    for i in 0..10 {
        let handler_clone = handler.clone();
        let query_clone = query.clone();
        let context_clone = context.clone();
        
        let handle = tokio::spawn(async move {
            let start = Instant::now();
            let _ = handler_clone.handle_with_context(query_clone, context_clone).await;
            start.elapsed()
        });
        
        handles.push(handle);
    }
    
    // Wait for all requests to complete
    let mut durations = Vec::new();
    for handle in handles {
        durations.push(handle.await.unwrap());
    }
    
    // Most requests should benefit from caching
    let fast_responses = durations.iter().filter(|d| **d < Duration::from_millis(5)).count();
    assert!(
        fast_responses >= 8,
        "Expected at least 8 fast responses from cache, got {}",
        fast_responses
    );
}

// Helper functions and mock implementations

fn create_test_user() -> User {
    let email = Email::new("<EMAIL>").unwrap();
    let password = Password::new("TestPassword123!").unwrap();
    User::new(email, password).unwrap()
}

// Mock repositories for testing
#[derive(Clone)]
struct MockUserRepository {
    users: Arc<tokio::sync::Mutex<std::collections::HashMap<String, User>>>,
}

impl MockUserRepository {
    fn new() -> Self {
        Self {
            users: Arc::new(tokio::sync::Mutex::new(std::collections::HashMap::new())),
        }
    }

    async fn add_user(&self, user: User) {
        let mut users = self.users.lock().await;
        users.insert(user.id().as_str().to_string(), user);
    }
}

#[async_trait::async_trait]
impl UserRepository for MockUserRepository {
    async fn save(&self, user: &User) -> Result<(), DomainError> {
        let mut users = self.users.lock().await;
        users.insert(user.id().as_str().to_string(), user.clone());
        Ok(())
    }

    async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
        let users = self.users.lock().await;
        Ok(users.get(id.as_str()).cloned())
    }

    async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
        let users = self.users.lock().await;
        Ok(users.values().find(|u| u.email() == email).cloned())
    }

    async fn find_by_username(&self, _username: &str) -> Result<Option<User>, DomainError> {
        Ok(None)
    }

    async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
        let users = self.users.lock().await;
        Ok(users.values().any(|u| u.email() == email))
    }

    async fn delete(&self, id: &UserId) -> Result<(), DomainError> {
        let mut users = self.users.lock().await;
        users.remove(id.as_str());
        Ok(())
    }

    async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError> {
        let users = self.users.lock().await;
        Ok(users.values().skip(offset).take(limit).cloned().collect())
    }

    async fn count(&self) -> Result<usize, DomainError> {
        let users = self.users.lock().await;
        Ok(users.len())
    }

    async fn find_by_created_date_range(
        &self,
        _start: std::time::SystemTime,
        _end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError> {
        Ok(Vec::new())
    }

    async fn find_by_verification_status(
        &self,
        _is_verified: bool,
    ) -> Result<Vec<User>, DomainError> {
        Ok(Vec::new())
    }

    async fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
        Ok(Vec::new())
    }

    async fn search_users(
        &self,
        _criteria: &auth_domain::repositories::UserSearchCriteria,
    ) -> Result<auth_domain::repositories::UserSearchResult, DomainError> {
        Ok(auth_domain::repositories::UserSearchResult {
            users: Vec::new(),
            total: 0,
        })
    }
}

#[derive(Clone)]
struct MockRoleRepository;

impl MockRoleRepository {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl RoleRepository for MockRoleRepository {
    async fn save(&self, _role: &auth_domain::entities::Role) -> Result<(), DomainError> {
        Ok(())
    }

    async fn find_by_id(&self, _id: &RoleId) -> Result<Option<auth_domain::entities::Role>, DomainError> {
        Ok(None)
    }

    async fn find_by_name(&self, _name: &str) -> Result<Option<auth_domain::entities::Role>, DomainError> {
        Ok(None)
    }

    async fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<auth_domain::entities::Role>, DomainError> {
        Ok(Vec::new())
    }

    async fn delete(&self, _id: &RoleId) -> Result<(), DomainError> {
        Ok(())
    }

    async fn find_by_user_id(&self, _user_id: &UserId) -> Result<Vec<auth_domain::entities::Role>, DomainError> {
        Ok(Vec::new())
    }

    async fn assign_to_user(&self, _role_id: &RoleId, _user_id: &UserId) -> Result<(), DomainError> {
        Ok(())
    }

    async fn remove_from_user(&self, _role_id: &RoleId, _user_id: &UserId) -> Result<(), DomainError> {
        Ok(())
    }

    async fn count(&self) -> Result<usize, DomainError> {
        Ok(0)
    }
}