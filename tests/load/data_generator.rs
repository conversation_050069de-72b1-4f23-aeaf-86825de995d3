//! Test Data Generator
//! 
//! Generates realistic test data for load testing scenarios including users,
//! roles, permissions, and sessions with proper distributions and relationships.

use std::collections::HashMap;
use rand::{Rng, thread_rng};
use rand::seq::SliceRandom;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// Configuration for test data generation
#[derive(Debug, Clone)]
pub struct TestDataConfig {
    pub user_count: u32,
    pub role_count: u32,
    pub permission_count: u32,
    pub session_count: u32,
    pub organization_count: u32,
    pub audit_log_entries: u32,
    pub data_seed: Option<u64>,
}

impl Default for TestDataConfig {
    fn default() -> Self {
        Self {
            user_count: 10_000,
            role_count: 50,
            permission_count: 200,
            session_count: 5_000,
            organization_count: 10,
            audit_log_entries: 50_000,
            data_seed: None,
        }
    }
}

/// Test user data
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TestUser {
    pub id: Uuid,
    pub email: String,
    pub username: String,
    pub password_hash: String,
    pub is_active: bool,
    pub is_mfa_enabled: bool,
    pub role_ids: Vec<Uuid>,
    pub organization_id: Uuid,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_login_at: Option<chrono::DateTime<chrono::Utc>>,
    pub failed_login_attempts: u32,
}

/// Test role data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestRole {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub permission_ids: Vec<Uuid>,
    pub is_system_role: bool,
    pub organization_id: Option<Uuid>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// Test permission data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestPermission {
    pub id: Uuid,
    pub resource: String,
    pub action: String,
    pub description: String,
    pub is_system_permission: bool,
}

/// Test session data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestSession {
    pub id: Uuid,
    pub user_id: Uuid,
    pub token: String,
    pub refresh_token: String,
    pub expires_at: chrono::DateTime<chrono::Utc>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_accessed_at: chrono::DateTime<chrono::Utc>,
    pub ip_address: String,
    pub user_agent: String,
    pub is_active: bool,
}

/// Test organization data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestOrganization {
    pub id: Uuid,
    pub name: String,
    pub domain: String,
    pub is_active: bool,
    pub settings: serde_json::Value,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// Generated test data set
#[derive(Debug)]
pub struct TestDataSet {
    pub users: Vec<TestUser>,
    pub roles: Vec<TestRole>,
    pub permissions: Vec<TestPermission>,
    pub sessions: Vec<TestSession>,
    pub organizations: Vec<TestOrganization>,
    pub user_credentials: HashMap<String, String>, // email -> password
}

impl TestDataSet {
    /// Get a random active user
    pub fn get_random_active_user(&self) -> Option<&TestUser> {
        let active_users: Vec<_> = self.users.iter().filter(|u| u.is_active).collect();
        active_users.choose(&mut thread_rng()).copied()
    }
    
    /// Get credentials for a user by email
    pub fn get_user_credentials(&self, email: &str) -> Option<&str> {
        self.user_credentials.get(email).map(|s| s.as_str())
    }
    
    /// Get users by role name
    pub fn get_users_by_role(&self, role_name: &str) -> Vec<&TestUser> {
        let role = self.roles.iter().find(|r| r.name == role_name);
        if let Some(role) = role {
            self.users.iter()
                .filter(|u| u.role_ids.contains(&role.id))
                .collect()
        } else {
            Vec::new()
        }
    }
    
    /// Get admin users
    pub fn get_admin_users(&self) -> Vec<&TestUser> {
        self.get_users_by_role("Admin")
    }
    
    /// Get regular users
    pub fn get_regular_users(&self) -> Vec<&TestUser> {
        self.get_users_by_role("User")
    }
    
    /// Get service account users
    pub fn get_service_users(&self) -> Vec<&TestUser> {
        self.get_users_by_role("Service")
    }
}

/// Main test data generator
pub struct TestDataGenerator {
    config: TestDataConfig,
    rng: rand::rngs::ThreadRng,
}

impl TestDataGenerator {
    pub fn new(config: TestDataConfig) -> Self {
        Self {
            config,
            rng: thread_rng(),
        }
    }
    
    /// Generate complete test data set
    pub fn generate(&mut self) -> TestDataSet {
        tracing::info!("Generating test data with config: {:?}", self.config);
        
        // Generate organizations first
        let organizations = self.generate_organizations();
        
        // Generate permissions
        let permissions = self.generate_permissions();
        
        // Generate roles with permission assignments
        let roles = self.generate_roles(&permissions, &organizations);
        
        // Generate users with role assignments
        let (users, user_credentials) = self.generate_users(&roles, &organizations);
        
        // Generate sessions for users
        let sessions = self.generate_sessions(&users);
        
        TestDataSet {
            users,
            roles,
            permissions,
            sessions,
            organizations,
            user_credentials,
        }
    }
    
    fn generate_organizations(&mut self) -> Vec<TestOrganization> {
        let company_names = [
            "Acme Corp", "TechStart Inc", "Global Solutions", "Innovation Labs",
            "Digital Dynamics", "Future Systems", "Cloud Ventures", "Data Insights",
            "Secure Solutions", "Enterprise Tech",
        ];
        
        (0..self.config.organization_count)
            .map(|i| {
                let name = if i < company_names.len() as u32 {
                    company_names[i as usize].to_string()
                } else {
                    format!("Organization {}", i + 1)
                };
                
                let domain = name.to_lowercase()
                    .replace(" ", "")
                    .replace("corp", "")
                    .replace("inc", "")
                    + ".com";
                
                TestOrganization {
                    id: Uuid::new_v4(),
                    name,
                    domain,
                    is_active: self.rng.gen_bool(0.9), // 90% active
                    settings: serde_json::json!({
                        "mfa_required": self.rng.gen_bool(0.6),
                        "password_policy": {
                            "min_length": self.rng.gen_range(8..=12),
                            "require_special_chars": self.rng.gen_bool(0.8)
                        }
                    }),
                    created_at: self.random_past_datetime(365),
                }
            })
            .collect()
    }
    
    fn generate_permissions(&mut self) -> Vec<TestPermission> {
        let resources = [
            "users", "roles", "permissions", "organizations", "sessions",
            "audit_logs", "settings", "billing", "reports", "integrations",
            "webhooks", "api_keys", "tokens", "files", "notifications",
        ];
        
        let actions = [
            "create", "read", "update", "delete", "list", "search",
            "export", "import", "manage", "configure", "execute",
        ];
        
        let mut permissions = Vec::new();
        let mut permission_count = 0;
        
        // Generate system permissions
        for resource in &resources {
            for action in &actions {
                if permission_count >= self.config.permission_count {
                    break;
                }
                
                permissions.push(TestPermission {
                    id: Uuid::new_v4(),
                    resource: resource.to_string(),
                    action: action.to_string(),
                    description: format!("Allow {} on {}", action, resource),
                    is_system_permission: true,
                });
                
                permission_count += 1;
            }
            
            if permission_count >= self.config.permission_count {
                break;
            }
        }
        
        permissions
    }
    
    fn generate_roles(&mut self, permissions: &[TestPermission], organizations: &[TestOrganization]) -> Vec<TestRole> {
        let mut roles = Vec::new();
        
        // Create system roles first
        let system_roles = [
            ("SuperAdmin", "Full system access", 1.0),
            ("Admin", "Organization administration", 0.8),
            ("Manager", "Team management", 0.4),
            ("User", "Basic user permissions", 0.2),
            ("Service", "Service account permissions", 0.3),
            ("Auditor", "Read-only audit access", 0.1),
            ("Support", "Customer support access", 0.3),
        ];
        
        for (name, description, permission_ratio) in &system_roles {
            let permission_count = (permissions.len() as f64 * permission_ratio) as usize;
            let selected_permissions: Vec<_> = permissions
                .choose_multiple(&mut self.rng, permission_count)
                .map(|p| p.id)
                .collect();
            
            roles.push(TestRole {
                id: Uuid::new_v4(),
                name: name.to_string(),
                description: description.to_string(),
                permission_ids: selected_permissions,
                is_system_role: true,
                organization_id: None,
                created_at: self.random_past_datetime(300),
            });
        }
        
        // Create organization-specific roles
        let remaining_roles = self.config.role_count.saturating_sub(system_roles.len() as u32);
        for i in 0..remaining_roles {
            let org = organizations.choose(&mut self.rng).unwrap();
            let permission_count = self.rng.gen_range(1..=10);
            let selected_permissions: Vec<_> = permissions
                .choose_multiple(&mut self.rng, permission_count)
                .map(|p| p.id)
                .collect();
            
            roles.push(TestRole {
                id: Uuid::new_v4(),
                name: format!("Custom Role {}", i + 1),
                description: format!("Custom role for {}", org.name),
                permission_ids: selected_permissions,
                is_system_role: false,
                organization_id: Some(org.id),
                created_at: self.random_past_datetime(200),
            });
        }
        
        roles
    }
    
    fn generate_users(&mut self, roles: &[TestRole], organizations: &[TestOrganization]) -> (Vec<TestUser>, HashMap<String, String>) {
        let mut users = Vec::new();
        let mut user_credentials = HashMap::new();
        
        // Role distribution for load testing
        let admin_role = roles.iter().find(|r| r.name == "Admin").unwrap();
        let user_role = roles.iter().find(|r| r.name == "User").unwrap();
        let service_role = roles.iter().find(|r| r.name == "Service").unwrap();
        
        let admin_count = (self.config.user_count as f64 * 0.20) as u32; // 20% admins
        let service_count = (self.config.user_count as f64 * 0.10) as u32; // 10% service accounts
        let regular_count = self.config.user_count - admin_count - service_count; // 70% regular users
        
        // Generate admin users
        for i in 0..admin_count {
            let org = organizations.choose(&mut self.rng).unwrap();
            let email = format!("admin{}@{}", i, org.domain);
            let password = "AdminPass123!";
            let username = format!("admin{}", i);
            
            users.push(TestUser {
                id: Uuid::new_v4(),
                email: email.clone(),
                username,
                password_hash: self.hash_password(password),
                is_active: self.rng.gen_bool(0.95), // 95% active
                is_mfa_enabled: self.rng.gen_bool(0.8), // 80% have MFA
                role_ids: vec![admin_role.id],
                organization_id: org.id,
                created_at: self.random_past_datetime(300),
                last_login_at: if self.rng.gen_bool(0.8) {
                    Some(self.random_past_datetime(30))
                } else {
                    None
                },
                failed_login_attempts: if self.rng.gen_bool(0.1) {
                    self.rng.gen_range(1..=3)
                } else {
                    0
                },
            });
            
            user_credentials.insert(email, password.to_string());
        }
        
        // Generate regular users
        for i in 0..regular_count {
            let org = organizations.choose(&mut self.rng).unwrap();
            let email = format!("user{}@{}", i, org.domain);
            let password = "UserPass123!";
            let username = format!("user{}", i);
            
            users.push(TestUser {
                id: Uuid::new_v4(),
                email: email.clone(),
                username,
                password_hash: self.hash_password(password),
                is_active: self.rng.gen_bool(0.92), // 92% active
                is_mfa_enabled: self.rng.gen_bool(0.4), // 40% have MFA
                role_ids: vec![user_role.id],
                organization_id: org.id,
                created_at: self.random_past_datetime(300),
                last_login_at: if self.rng.gen_bool(0.9) {
                    Some(self.random_past_datetime(7))
                } else {
                    None
                },
                failed_login_attempts: if self.rng.gen_bool(0.05) {
                    self.rng.gen_range(1..=2)
                } else {
                    0
                },
            });
            
            user_credentials.insert(email, password.to_string());
        }
        
        // Generate service accounts
        for i in 0..service_count {
            let org = organizations.choose(&mut self.rng).unwrap();
            let email = format!("service{}@{}", i, org.domain);
            let password = "ServicePass123!";
            let username = format!("service-{}", i);
            
            users.push(TestUser {
                id: Uuid::new_v4(),
                email: email.clone(),
                username,
                password_hash: self.hash_password(password),
                is_active: self.rng.gen_bool(0.98), // 98% active
                is_mfa_enabled: false, // Service accounts typically don't use MFA
                role_ids: vec![service_role.id],
                organization_id: org.id,
                created_at: self.random_past_datetime(180),
                last_login_at: Some(self.random_past_datetime(1)), // Recently active
                failed_login_attempts: 0,
            });
            
            user_credentials.insert(email, password.to_string());
        }
        
        (users, user_credentials)
    }
    
    fn generate_sessions(&mut self, users: &[TestUser]) -> Vec<TestSession> {
        let active_users: Vec<_> = users.iter().filter(|u| u.is_active).collect();
        
        (0..self.config.session_count)
            .map(|_| {
                let user = active_users.choose(&mut self.rng).unwrap();
                let created_at = self.random_past_datetime(7); // Sessions within last week
                let expires_at = created_at + chrono::Duration::hours(24); // 24 hour sessions
                
                TestSession {
                    id: Uuid::new_v4(),
                    user_id: user.id,
                    token: format!("session_token_{}", Uuid::new_v4()),
                    refresh_token: format!("refresh_token_{}", Uuid::new_v4()),
                    expires_at,
                    created_at,
                    last_accessed_at: created_at + chrono::Duration::minutes(self.rng.gen_range(1..=60)),
                    ip_address: self.random_ip_address(),
                    user_agent: self.random_user_agent(),
                    is_active: expires_at > chrono::Utc::now(),
                }
            })
            .collect()
    }
    
    fn hash_password(&self, password: &str) -> String {
        // Simple hash for testing - in real scenarios would use proper hashing
        format!("$argon2id$v=19$m=19456,t=2,p=1${}", base64::encode(password))
    }
    
    fn random_past_datetime(&mut self, max_days_ago: u32) -> chrono::DateTime<chrono::Utc> {
        let days_ago = self.rng.gen_range(0..=max_days_ago);
        chrono::Utc::now() - chrono::Duration::days(days_ago as i64)
    }
    
    fn random_ip_address(&mut self) -> String {
        format!(
            "{}.{}.{}.{}",
            self.rng.gen_range(1..=255),
            self.rng.gen_range(0..=255),
            self.rng.gen_range(0..=255),
            self.rng.gen_range(1..=254)
        )
    }
    
    fn random_user_agent(&mut self) -> String {
        let user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101",
            "PostmanRuntime/7.29.2",
            "curl/7.68.0",
            "python-requests/2.28.1",
        ];
        
        user_agents.choose(&mut self.rng).unwrap().to_string()
    }
}

/// Utility functions for load test data setup
pub struct LoadTestDataSetup;

impl LoadTestDataSetup {
    /// Generate and insert test data into the database
    pub async fn setup_database_with_test_data(
        db_pool: &sqlx::PgPool,
        config: TestDataConfig,
    ) -> Result<TestDataSet, sqlx::Error> {
        let mut generator = TestDataGenerator::new(config);
        let data_set = generator.generate();
        
        tracing::info!("Inserting test data into database...");
        
        // Insert organizations
        for org in &data_set.organizations {
            sqlx::query!(
                "INSERT INTO organizations (id, name, domain, is_active, settings, created_at) 
                 VALUES ($1, $2, $3, $4, $5, $6)",
                org.id,
                org.name,
                org.domain,
                org.is_active,
                org.settings,
                org.created_at
            )
            .execute(db_pool)
            .await?;
        }
        
        // Insert permissions
        for permission in &data_set.permissions {
            sqlx::query!(
                "INSERT INTO permissions (id, resource, action, description, is_system_permission) 
                 VALUES ($1, $2, $3, $4, $5)",
                permission.id,
                permission.resource,
                permission.action,
                permission.description,
                permission.is_system_permission
            )
            .execute(db_pool)
            .await?;
        }
        
        // Insert roles
        for role in &data_set.roles {
            sqlx::query!(
                "INSERT INTO roles (id, name, description, is_system_role, organization_id, created_at) 
                 VALUES ($1, $2, $3, $4, $5, $6)",
                role.id,
                role.name,
                role.description,
                role.is_system_role,
                role.organization_id,
                role.created_at
            )
            .execute(db_pool)
            .await?;
            
            // Insert role permissions
            for permission_id in &role.permission_ids {
                sqlx::query!(
                    "INSERT INTO role_permissions (role_id, permission_id) VALUES ($1, $2)",
                    role.id,
                    permission_id
                )
                .execute(db_pool)
                .await?;
            }
        }
        
        // Insert users
        for user in &data_set.users {
            sqlx::query!(
                "INSERT INTO users (id, email, username, password_hash, is_active, is_mfa_enabled, 
                                   organization_id, created_at, last_login_at, failed_login_attempts) 
                 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)",
                user.id,
                user.email,
                user.username,
                user.password_hash,
                user.is_active,
                user.is_mfa_enabled,
                user.organization_id,
                user.created_at,
                user.last_login_at,
                user.failed_login_attempts as i32
            )
            .execute(db_pool)
            .await?;
            
            // Insert user roles
            for role_id in &user.role_ids {
                sqlx::query!(
                    "INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)",
                    user.id,
                    role_id
                )
                .execute(db_pool)
                .await?;
            }
        }
        
        // Insert sessions
        for session in &data_set.sessions {
            sqlx::query!(
                "INSERT INTO sessions (id, user_id, token, refresh_token, expires_at, created_at, 
                                     last_accessed_at, ip_address, user_agent, is_active) 
                 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)",
                session.id,
                session.user_id,
                session.token,
                session.refresh_token,
                session.expires_at,
                session.created_at,
                session.last_accessed_at,
                session.ip_address,
                session.user_agent,
                session.is_active
            )
            .execute(db_pool)
            .await?;
        }
        
        tracing::info!("Test data insertion completed successfully");
        Ok(data_set)
    }
    
    /// Clean up test data from database
    pub async fn cleanup_test_data(db_pool: &sqlx::PgPool) -> Result<(), sqlx::Error> {
        tracing::info!("Cleaning up test data...");
        
        // Delete in reverse dependency order
        sqlx::query!("DELETE FROM user_roles").execute(db_pool).await?;
        sqlx::query!("DELETE FROM role_permissions").execute(db_pool).await?;
        sqlx::query!("DELETE FROM sessions").execute(db_pool).await?;
        sqlx::query!("DELETE FROM users").execute(db_pool).await?;
        sqlx::query!("DELETE FROM roles").execute(db_pool).await?;
        sqlx::query!("DELETE FROM permissions").execute(db_pool).await?;
        sqlx::query!("DELETE FROM organizations").execute(db_pool).await?;
        
        tracing::info!("Test data cleanup completed");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_data_config_has_reasonable_defaults() {
        let config = TestDataConfig::default();
        assert_eq!(config.user_count, 10_000);
        assert_eq!(config.role_count, 50);
        assert_eq!(config.permission_count, 200);
        assert_eq!(config.session_count, 5_000);
    }

    #[test]
    fn test_data_generator_creates_organizations() {
        let mut generator = TestDataGenerator::new(TestDataConfig {
            organization_count: 5,
            ..Default::default()
        });
        let orgs = generator.generate_organizations();
        
        assert_eq!(orgs.len(), 5);
        assert!(orgs.iter().all(|o| !o.name.is_empty()));
        assert!(orgs.iter().all(|o| o.domain.contains(".com")));
    }

    #[test]
    fn test_data_generator_creates_permissions() {
        let mut generator = TestDataGenerator::new(TestDataConfig {
            permission_count: 10,
            ..Default::default()
        });
        let permissions = generator.generate_permissions();
        
        assert_eq!(permissions.len(), 10);
        assert!(permissions.iter().all(|p| !p.resource.is_empty()));
        assert!(permissions.iter().all(|p| !p.action.is_empty()));
        assert!(permissions.iter().all(|p| p.is_system_permission));
    }

    #[test]
    fn test_data_set_finds_users_by_role() {
        let config = TestDataConfig {
            user_count: 100,
            role_count: 10,
            permission_count: 20,
            session_count: 50,
            organization_count: 2,
            audit_log_entries: 100,
            data_seed: Some(42),
        };
        
        let mut generator = TestDataGenerator::new(config);
        let data_set = generator.generate();
        
        let admin_users = data_set.get_admin_users();
        let regular_users = data_set.get_regular_users();
        let service_users = data_set.get_service_users();
        
        // Should have users distributed according to our 70/20/10 split
        assert!(!admin_users.is_empty());
        assert!(!regular_users.is_empty());
        assert!(!service_users.is_empty());
        
        // Admin users should be roughly 20% of total
        let expected_admin_count = (100.0 * 0.20) as usize;
        assert!((admin_users.len() as i32 - expected_admin_count as i32).abs() <= 2);
    }

    #[test]
    fn test_data_set_provides_user_credentials() {
        let config = TestDataConfig {
            user_count: 10,
            ..Default::default()
        };
        
        let mut generator = TestDataGenerator::new(config);
        let data_set = generator.generate();
        
        // Should have credentials for all users
        assert_eq!(data_set.user_credentials.len(), data_set.users.len());
        
        // Should be able to get credentials for each user
        for user in &data_set.users {
            let password = data_set.get_user_credentials(&user.email);
            assert!(password.is_some());
            assert!(!password.unwrap().is_empty());
        }
    }

    #[test]
    fn test_data_set_gets_random_active_user() {
        let config = TestDataConfig {
            user_count: 50,
            ..Default::default()
        };
        
        let mut generator = TestDataGenerator::new(config);
        let data_set = generator.generate();
        
        // Should return an active user
        let user = data_set.get_random_active_user();
        assert!(user.is_some());
        assert!(user.unwrap().is_active);
        
        // Should return different users on multiple calls (with high probability)
        let users: Vec<_> = (0..10).map(|_| data_set.get_random_active_user().unwrap().id).collect();
        let unique_users: std::collections::HashSet<_> = users.into_iter().collect();
        assert!(unique_users.len() > 1); // Should have at least some variety
    }
}