//! User Simulation for Load Testing
//! 
//! Provides realistic user behavior simulation with different user types,
//! behavioral patterns, and query handler interactions for comprehensive
//! load testing of the AuthService.

use std::time::Duration;
use rand::{Rng, thread_rng};
use rand::seq::SliceRandom;
use serde::{Serialize, Deserialize};

/// Different types of users with distinct behavioral patterns
#[derive(Debu<PERSON>, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum UserType {
    /// Regular end-users: Simple queries, profile operations
    Regular,
    /// Admin users: Complex queries, user management, audit operations
    Admin,
    /// Service accounts: API calls, automated operations, high frequency
    Service,
}

/// User actions that can be performed during load testing
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum UserAction {
    /// Authenticate user (all user types)
    Login { email: String, password: String },
    
    /// Check user permissions for a resource/action (all user types)
    CheckPermissions { resource: String, action: String },
    
    /// Get current user profile (regular, admin)
    GetProfile,
    
    /// List user's roles (regular, admin)
    ListRoles,
    
    /// Search for users (admin only)
    SearchUsers { query: String },
    
    /// View audit log (admin only)
    ViewAuditLog { filters: String },
    
    /// Get role details (admin, service)
    GetRoleDetails { role_id: String },
    
    /// List user sessions (admin only)
    ListUserSessions { user_id: String },
    
    /// Get current session details (all user types)
    GetCurrentSession,
    
    /// Logout (all user types)
    Logout,
}

impl UserAction {
    /// Get the query handler endpoint for this action
    pub fn get_endpoint(&self) -> &'static str {
        match self {
            UserAction::Login { .. } => "/api/auth/login",
            UserAction::CheckPermissions { .. } => "/api/auth/permissions",
            UserAction::GetProfile => "/api/users/profile",
            UserAction::ListRoles => "/api/users/roles",
            UserAction::SearchUsers { .. } => "/api/users/search",
            UserAction::ViewAuditLog { .. } => "/api/audit",
            UserAction::GetRoleDetails { .. } => "/api/roles/details",
            UserAction::ListUserSessions { .. } => "/api/users/sessions",
            UserAction::GetCurrentSession => "/api/sessions/current",
            UserAction::Logout => "/api/auth/logout",
        }
    }
    
    /// Get the HTTP method for this action
    pub fn get_method(&self) -> &'static str {
        match self {
            UserAction::Login { .. } | UserAction::Logout => "POST",
            _ => "GET",
        }
    }
    
    /// Check if this action is allowed for the given user type
    pub fn is_allowed_for_user_type(&self, user_type: UserType) -> bool {
        match self {
            UserAction::Login { .. } | 
            UserAction::CheckPermissions { .. } |
            UserAction::GetProfile |
            UserAction::ListRoles |
            UserAction::GetCurrentSession |
            UserAction::Logout => true, // All user types
            
            UserAction::SearchUsers { .. } |
            UserAction::ViewAuditLog { .. } |
            UserAction::ListUserSessions { .. } => {
                matches!(user_type, UserType::Admin)
            }
            
            UserAction::GetRoleDetails { .. } => {
                matches!(user_type, UserType::Admin | UserType::Service)
            }
        }
    }
    
    /// Get the expected cache behavior for this action
    pub fn expected_cache_behavior(&self) -> CacheExpectation {
        match self {
            UserAction::Login { .. } | UserAction::Logout => CacheExpectation::NoCache,
            UserAction::CheckPermissions { .. } => CacheExpectation::HighHitRate(0.90),
            UserAction::GetProfile => CacheExpectation::HighHitRate(0.85),
            UserAction::ListRoles => CacheExpectation::HighHitRate(0.80),
            UserAction::GetRoleDetails { .. } => CacheExpectation::HighHitRate(0.85),
            UserAction::SearchUsers { .. } => CacheExpectation::LowHitRate(0.30),
            UserAction::ViewAuditLog { .. } => CacheExpectation::LowHitRate(0.20),
            UserAction::ListUserSessions { .. } => CacheExpectation::MediumHitRate(0.60),
            UserAction::GetCurrentSession => CacheExpectation::HighHitRate(0.90),
        }
    }
    
    /// Get expected response time range for this action
    pub fn expected_response_time_range(&self) -> (Duration, Duration) {
        match self {
            UserAction::Login { .. } => (Duration::from_millis(50), Duration::from_millis(200)),
            UserAction::CheckPermissions { .. } => (Duration::from_millis(10), Duration::from_millis(50)),
            UserAction::GetProfile => (Duration::from_millis(15), Duration::from_millis(75)),
            UserAction::ListRoles => (Duration::from_millis(20), Duration::from_millis(100)),
            UserAction::SearchUsers { .. } => (Duration::from_millis(100), Duration::from_millis(500)),
            UserAction::ViewAuditLog { .. } => (Duration::from_millis(150), Duration::from_millis(800)),
            UserAction::GetRoleDetails { .. } => (Duration::from_millis(25), Duration::from_millis(150)),
            UserAction::ListUserSessions { .. } => (Duration::from_millis(50), Duration::from_millis(300)),
            UserAction::GetCurrentSession => (Duration::from_millis(10), Duration::from_millis(40)),
            UserAction::Logout => (Duration::from_millis(20), Duration::from_millis(80)),
        }
    }
}

/// Expected cache behavior for actions
#[derive(Debug, Clone)]
pub enum CacheExpectation {
    NoCache,
    LowHitRate(f64),    // < 50%
    MediumHitRate(f64), // 50-75%
    HighHitRate(f64),   // > 75%
}

/// A complete user session with behavioral patterns
#[derive(Debug, Clone)]
pub struct UserSession {
    pub user_type: UserType,
    pub session_duration: Duration,
    pub actions: Vec<UserAction>,
    pub think_time: Duration,
    pub session_pattern: SessionPattern,
}

/// Different session patterns for realistic behavior
#[derive(Debug, Clone)]
pub enum SessionPattern {
    /// Quick actions: Login -> Quick checks -> Logout
    QuickSession,
    /// Normal workflow: Login -> Multiple operations -> Logout  
    NormalWorkflow,
    /// Admin session: Login -> Management tasks -> Audit -> Logout
    AdminWorkflow,
    /// Service session: Login -> API calls -> Service operations
    ServiceWorkflow,
    /// Browsing session: Login -> Multiple read operations
    BrowsingSession,
}

impl UserSession {
    /// Generate a realistic user session for the given user type
    pub fn generate_for_type(
        user_type: UserType, 
        think_time_range: (Duration, Duration),
        max_session_duration: Duration,
    ) -> Self {
        let mut rng = thread_rng();
        
        // Determine session pattern based on user type
        let session_pattern = match user_type {
            UserType::Regular => {
                let patterns = [
                    SessionPattern::QuickSession,
                    SessionPattern::NormalWorkflow,
                    SessionPattern::BrowsingSession,
                ];
                *patterns.choose(&mut rng).unwrap()
            }
            UserType::Admin => {
                let patterns = [
                    SessionPattern::AdminWorkflow,
                    SessionPattern::NormalWorkflow,
                ];
                *patterns.choose(&mut rng).unwrap()
            }
            UserType::Service => SessionPattern::ServiceWorkflow,
        };
        
        // Generate think time
        let think_time = Duration::from_millis(
            rng.gen_range(think_time_range.0.as_millis()..=think_time_range.1.as_millis()) as u64
        );
        
        // Generate session duration (percentage of max duration)
        let session_duration_factor = match session_pattern {
            SessionPattern::QuickSession => rng.gen_range(0.1..0.3),
            SessionPattern::NormalWorkflow => rng.gen_range(0.4..0.8),
            SessionPattern::AdminWorkflow => rng.gen_range(0.6..1.0),
            SessionPattern::ServiceWorkflow => rng.gen_range(0.8..1.0),
            SessionPattern::BrowsingSession => rng.gen_range(0.3..0.7),
        };
        
        let session_duration = Duration::from_millis(
            (max_session_duration.as_millis() as f64 * session_duration_factor) as u64
        );
        
        // Generate actions based on pattern
        let actions = Self::generate_actions_for_pattern(user_type, &session_pattern, &mut rng);
        
        UserSession {
            user_type,
            session_duration,
            actions,
            think_time,
            session_pattern,
        }
    }
    
    fn generate_actions_for_pattern(
        user_type: UserType,
        pattern: &SessionPattern,
        rng: &mut rand::rngs::ThreadRng,
    ) -> Vec<UserAction> {
        let mut actions = Vec::new();
        
        // All sessions start with login
        actions.push(Self::generate_login_action(user_type, rng));
        
        match pattern {
            SessionPattern::QuickSession => {
                // 2-4 quick actions
                let action_count = rng.gen_range(2..=4);
                for _ in 0..action_count {
                    actions.push(Self::generate_quick_action(user_type, rng));
                }
            }
            
            SessionPattern::NormalWorkflow => {
                // 5-12 varied actions
                let action_count = rng.gen_range(5..=12);
                for _ in 0..action_count {
                    actions.push(Self::generate_normal_action(user_type, rng));
                }
            }
            
            SessionPattern::AdminWorkflow => {
                // 8-20 admin-focused actions
                let action_count = rng.gen_range(8..=20);
                for _ in 0..action_count {
                    actions.push(Self::generate_admin_action(user_type, rng));
                }
            }
            
            SessionPattern::ServiceWorkflow => {
                // 10-50 service actions (high frequency)
                let action_count = rng.gen_range(10..=50);
                for _ in 0..action_count {
                    actions.push(Self::generate_service_action(user_type, rng));
                }
            }
            
            SessionPattern::BrowsingSession => {
                // 6-15 mostly read actions
                let action_count = rng.gen_range(6..=15);
                for _ in 0..action_count {
                    actions.push(Self::generate_browsing_action(user_type, rng));
                }
            }
        }
        
        // All sessions end with logout (80% of the time)
        if rng.gen_bool(0.8) {
            actions.push(UserAction::Logout);
        }
        
        actions
    }
    
    fn generate_login_action(user_type: UserType, rng: &mut rand::rngs::ThreadRng) -> UserAction {
        let email = match user_type {
            UserType::Regular => format!("user{}@example.com", rng.gen_range(1..=1000)),
            UserType::Admin => format!("admin{}@example.com", rng.gen_range(1..=100)),
            UserType::Service => format!("service{}@example.com", rng.gen_range(1..=50)),
        };
        
        let password = match user_type {
            UserType::Regular => "UserPass123!",
            UserType::Admin => "AdminPass123!",
            UserType::Service => "ServicePass123!",
        }.to_string();
        
        UserAction::Login { email, password }
    }
    
    fn generate_quick_action(user_type: UserType, rng: &mut rand::rngs::ThreadRng) -> UserAction {
        let quick_actions = match user_type {
            UserType::Regular => vec![
                UserAction::GetProfile,
                UserAction::GetCurrentSession,
                UserAction::CheckPermissions { 
                    resource: "profile".to_string(), 
                    action: "read".to_string() 
                },
            ],
            UserType::Admin => vec![
                UserAction::GetProfile,
                UserAction::ListRoles,
                UserAction::CheckPermissions { 
                    resource: "users".to_string(), 
                    action: "read".to_string() 
                },
            ],
            UserType::Service => vec![
                UserAction::CheckPermissions { 
                    resource: "api".to_string(), 
                    action: "execute".to_string() 
                },
                UserAction::GetCurrentSession,
            ],
        };
        
        quick_actions.choose(rng).unwrap().clone()
    }
    
    fn generate_normal_action(user_type: UserType, rng: &mut rand::rngs::ThreadRng) -> UserAction {
        let normal_actions = match user_type {
            UserType::Regular => vec![
                UserAction::GetProfile,
                UserAction::ListRoles,
                UserAction::GetCurrentSession,
                UserAction::CheckPermissions { 
                    resource: "documents".to_string(), 
                    action: "read".to_string() 
                },
                UserAction::CheckPermissions { 
                    resource: "profile".to_string(), 
                    action: "update".to_string() 
                },
            ],
            UserType::Admin => vec![
                UserAction::GetProfile,
                UserAction::ListRoles,
                UserAction::SearchUsers { 
                    query: format!("user{}", rng.gen_range(1..=100)) 
                },
                UserAction::ViewAuditLog { 
                    filters: "action:login".to_string() 
                },
                UserAction::GetRoleDetails { 
                    role_id: format!("role-{}", rng.gen_range(1..=10)) 
                },
                UserAction::CheckPermissions { 
                    resource: "users".to_string(), 
                    action: "manage".to_string() 
                },
            ],
            UserType::Service => vec![
                UserAction::CheckPermissions { 
                    resource: "api".to_string(), 
                    action: "execute".to_string() 
                },
                UserAction::GetRoleDetails { 
                    role_id: "service-role".to_string() 
                },
                UserAction::GetCurrentSession,
            ],
        };
        
        normal_actions.choose(rng).unwrap().clone()
    }
    
    fn generate_admin_action(user_type: UserType, rng: &mut rand::rngs::ThreadRng) -> UserAction {
        let admin_actions = vec![
            UserAction::SearchUsers { 
                query: Self::generate_search_query(rng) 
            },
            UserAction::ViewAuditLog { 
                filters: Self::generate_audit_filters(rng) 
            },
            UserAction::ListUserSessions { 
                user_id: format!("user-{}", rng.gen_range(1..=1000)) 
            },
            UserAction::GetRoleDetails { 
                role_id: format!("role-{}", rng.gen_range(1..=20)) 
            },
            UserAction::CheckPermissions { 
                resource: Self::generate_resource_name(rng), 
                action: Self::generate_action_name(rng) 
            },
            UserAction::GetProfile,
            UserAction::ListRoles,
        ];
        
        admin_actions.choose(rng).unwrap().clone()
    }
    
    fn generate_service_action(user_type: UserType, rng: &mut rand::rngs::ThreadRng) -> UserAction {
        let service_actions = vec![
            UserAction::CheckPermissions { 
                resource: "api".to_string(), 
                action: "execute".to_string() 
            },
            UserAction::CheckPermissions { 
                resource: "data".to_string(), 
                action: "read".to_string() 
            },
            UserAction::CheckPermissions { 
                resource: "files".to_string(), 
                action: "process".to_string() 
            },
            UserAction::GetRoleDetails { 
                role_id: "service-role".to_string() 
            },
            UserAction::GetCurrentSession,
        ];
        
        service_actions.choose(rng).unwrap().clone()
    }
    
    fn generate_browsing_action(user_type: UserType, rng: &mut rand::rngs::ThreadRng) -> UserAction {
        let browsing_actions = match user_type {
            UserType::Regular => vec![
                UserAction::GetProfile,
                UserAction::ListRoles,
                UserAction::GetCurrentSession,
                UserAction::CheckPermissions { 
                    resource: "documents".to_string(), 
                    action: "read".to_string() 
                },
            ],
            UserType::Admin => vec![
                UserAction::GetProfile,
                UserAction::ListRoles,
                UserAction::SearchUsers { 
                    query: Self::generate_search_query(rng) 
                },
                UserAction::ViewAuditLog { 
                    filters: "recent".to_string() 
                },
                UserAction::GetRoleDetails { 
                    role_id: format!("role-{}", rng.gen_range(1..=5)) 
                },
            ],
            UserType::Service => vec![
                UserAction::GetCurrentSession,
                UserAction::CheckPermissions { 
                    resource: "api".to_string(), 
                    action: "read".to_string() 
                },
            ],
        };
        
        browsing_actions.choose(rng).unwrap().clone()
    }
    
    fn generate_search_query(rng: &mut rand::rngs::ThreadRng) -> String {
        let search_terms = [
            "john", "admin", "test", "user", "active", "inactive",
            "developer", "manager", "guest", "service",
        ];
        search_terms.choose(rng).unwrap().to_string()
    }
    
    fn generate_audit_filters(rng: &mut rand::rngs::ThreadRng) -> String {
        let filters = [
            "action:login", "action:logout", "level:error", "level:warning",
            "user:admin", "recent", "today", "failed_attempts",
        ];
        filters.choose(rng).unwrap().to_string()
    }
    
    fn generate_resource_name(rng: &mut rand::rngs::ThreadRng) -> String {
        let resources = [
            "users", "roles", "permissions", "audit", "settings",
            "reports", "billing", "integrations", "api", "files",
        ];
        resources.choose(rng).unwrap().to_string()
    }
    
    fn generate_action_name(rng: &mut rand::rngs::ThreadRng) -> String {
        let actions = [
            "read", "write", "create", "update", "delete",
            "manage", "execute", "configure", "export", "import",
        ];
        actions.choose(rng).unwrap().to_string()
    }
}

/// User behavior patterns for different scenarios
pub struct UserBehaviorPatterns;

impl UserBehaviorPatterns {
    /// Get realistic user type distribution for normal business hours
    pub fn business_hours_distribution() -> (f64, f64, f64) {
        (0.70, 0.25, 0.05) // Regular, Admin, Service
    }
    
    /// Get realistic user type distribution for off-hours/automated systems
    pub fn off_hours_distribution() -> (f64, f64, f64) {
        (0.40, 0.20, 0.40) // More service accounts active
    }
    
    /// Get realistic user type distribution for peak usage (e.g., morning login rush)
    pub fn peak_hours_distribution() -> (f64, f64, f64) {
        (0.80, 0.15, 0.05) // Mostly regular users logging in
    }
    
    /// Get think time distribution for different user types
    pub fn think_time_for_user_type(user_type: UserType) -> (Duration, Duration) {
        match user_type {
            UserType::Regular => (Duration::from_millis(500), Duration::from_secs(5)),
            UserType::Admin => (Duration::from_secs(1), Duration::from_secs(10)),
            UserType::Service => (Duration::from_millis(50), Duration::from_millis(500)),
        }
    }
    
    /// Get session duration distribution for different user types  
    pub fn session_duration_for_user_type(user_type: UserType, base_duration: Duration) -> Duration {
        let mut rng = thread_rng();
        let factor = match user_type {
            UserType::Regular => rng.gen_range(0.3..1.0),
            UserType::Admin => rng.gen_range(0.8..1.2),
            UserType::Service => rng.gen_range(0.9..1.0),
        };
        
        Duration::from_millis((base_duration.as_millis() as f64 * factor) as u64)
    }
    
    /// Get action frequency (actions per session) for different user types
    pub fn actions_per_session_range(user_type: UserType) -> (u32, u32) {
        match user_type {
            UserType::Regular => (3, 12),
            UserType::Admin => (8, 25),
            UserType::Service => (10, 50),
        }
    }
}

/// Session statistics for analysis
#[derive(Debug, Clone)]
pub struct SessionStatistics {
    pub user_type: UserType,
    pub total_actions: u32,
    pub unique_action_types: u32,
    pub estimated_duration: Duration,
    pub cache_friendly_actions: u32,
    pub complex_actions: u32,
}

impl SessionStatistics {
    pub fn analyze_session(session: &UserSession) -> Self {
        let total_actions = session.actions.len() as u32;
        
        let mut unique_actions = std::collections::HashSet::new();
        let mut cache_friendly_actions = 0;
        let mut complex_actions = 0;
        
        for action in &session.actions {
            unique_actions.insert(std::mem::discriminant(action));
            
            match action.expected_cache_behavior() {
                CacheExpectation::HighHitRate(_) | CacheExpectation::MediumHitRate(_) => {
                    cache_friendly_actions += 1;
                }
                _ => {}
            }
            
            match action {
                UserAction::SearchUsers { .. } |
                UserAction::ViewAuditLog { .. } |
                UserAction::ListUserSessions { .. } => {
                    complex_actions += 1;
                }
                _ => {}
            }
        }
        
        let estimated_duration = Duration::from_millis(
            session.actions.len() as u64 * session.think_time.as_millis() as u64
        );
        
        SessionStatistics {
            user_type: session.user_type,
            total_actions,
            unique_action_types: unique_actions.len() as u32,
            estimated_duration,
            cache_friendly_actions,
            complex_actions,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn user_action_permissions_are_enforced_correctly() {
        // Regular users can do basic operations
        assert!(UserAction::GetProfile.is_allowed_for_user_type(UserType::Regular));
        assert!(UserAction::CheckPermissions { resource: "test".to_string(), action: "read".to_string() }.is_allowed_for_user_type(UserType::Regular));
        
        // Regular users cannot do admin operations
        assert!(!UserAction::SearchUsers { query: "test".to_string() }.is_allowed_for_user_type(UserType::Regular));
        assert!(!UserAction::ViewAuditLog { filters: "test".to_string() }.is_allowed_for_user_type(UserType::Regular));
        
        // Admin users can do everything
        assert!(UserAction::SearchUsers { query: "test".to_string() }.is_allowed_for_user_type(UserType::Admin));
        assert!(UserAction::ViewAuditLog { filters: "test".to_string() }.is_allowed_for_user_type(UserType::Admin));
        assert!(UserAction::GetProfile.is_allowed_for_user_type(UserType::Admin));
        
        // Service accounts have limited permissions
        assert!(UserAction::CheckPermissions { resource: "api".to_string(), action: "execute".to_string() }.is_allowed_for_user_type(UserType::Service));
        assert!(UserAction::GetRoleDetails { role_id: "service".to_string() }.is_allowed_for_user_type(UserType::Service));
        assert!(!UserAction::SearchUsers { query: "test".to_string() }.is_allowed_for_user_type(UserType::Service));
    }

    #[test]
    fn user_sessions_generate_appropriate_actions_for_type() {
        let think_time_range = (Duration::from_millis(100), Duration::from_millis(500));
        let max_duration = Duration::from_secs(300);
        
        // Regular user session
        let regular_session = UserSession::generate_for_type(UserType::Regular, think_time_range, max_duration);
        assert_eq!(regular_session.user_type, UserType::Regular);
        assert!(!regular_session.actions.is_empty());
        
        // First action should be login
        if let Some(first_action) = regular_session.actions.first() {
            assert!(matches!(first_action, UserAction::Login { .. }));
        }
        
        // All actions should be allowed for regular users
        for action in &regular_session.actions {
            assert!(action.is_allowed_for_user_type(UserType::Regular));
        }
        
        // Admin user session
        let admin_session = UserSession::generate_for_type(UserType::Admin, think_time_range, max_duration);
        assert_eq!(admin_session.user_type, UserType::Admin);
        assert!(!admin_session.actions.is_empty());
        
        // All actions should be allowed for admin users
        for action in &admin_session.actions {
            assert!(action.is_allowed_for_user_type(UserType::Admin));
        }
        
        // Service user session
        let service_session = UserSession::generate_for_type(UserType::Service, think_time_range, max_duration);
        assert_eq!(service_session.user_type, UserType::Service);
        assert!(!service_session.actions.is_empty());
        
        // All actions should be allowed for service users
        for action in &service_session.actions {
            assert!(action.is_allowed_for_user_type(UserType::Service));
        }
    }

    #[test]
    fn user_action_cache_expectations_are_reasonable() {
        // High cache hit rate actions
        match UserAction::GetProfile.expected_cache_behavior() {
            CacheExpectation::HighHitRate(rate) => assert!(rate > 0.75),
            _ => panic!("GetProfile should have high cache hit rate"),
        }
        
        match UserAction::CheckPermissions { resource: "test".to_string(), action: "read".to_string() }.expected_cache_behavior() {
            CacheExpectation::HighHitRate(rate) => assert!(rate > 0.85),
            _ => panic!("CheckPermissions should have high cache hit rate"),
        }
        
        // Low cache hit rate actions
        match UserAction::SearchUsers { query: "test".to_string() }.expected_cache_behavior() {
            CacheExpectation::LowHitRate(rate) => assert!(rate < 0.50),
            _ => panic!("SearchUsers should have low cache hit rate"),
        }
        
        // No cache actions
        match UserAction::Login { email: "test".to_string(), password: "test".to_string() }.expected_cache_behavior() {
            CacheExpectation::NoCache => {},
            _ => panic!("Login should not be cached"),
        }
    }

    #[test]
    fn user_action_response_times_are_reasonable() {
        let (min, max) = UserAction::GetProfile.expected_response_time_range();
        assert!(min < max);
        assert!(max < Duration::from_millis(1000)); // Should be sub-second
        
        let (min, max) = UserAction::CheckPermissions { resource: "test".to_string(), action: "read".to_string() }.expected_response_time_range();
        assert!(min < Duration::from_millis(100)); // Should be very fast
        assert!(max < Duration::from_millis(100));
        
        let (min, max) = UserAction::ViewAuditLog { filters: "test".to_string() }.expected_response_time_range();
        assert!(max > Duration::from_millis(100)); // Complex queries take longer
        assert!(max < Duration::from_secs(2)); // But not too long
    }

    #[test]
    fn behavior_patterns_provide_realistic_distributions() {
        let (regular, admin, service) = UserBehaviorPatterns::business_hours_distribution();
        assert!((regular + admin + service - 1.0).abs() < 0.01);
        assert!(regular > admin); // More regular users than admin
        assert!(admin > service); // More admin than service accounts
        
        let (regular_off, admin_off, service_off) = UserBehaviorPatterns::off_hours_distribution();
        assert!((regular_off + admin_off + service_off - 1.0).abs() < 0.01);
        assert!(service_off > service); // More service accounts off-hours
    }

    #[test]
    fn session_statistics_analyze_sessions_correctly() {
        let think_time_range = (Duration::from_millis(100), Duration::from_millis(200));
        let max_duration = Duration::from_secs(300);
        
        let session = UserSession::generate_for_type(UserType::Admin, think_time_range, max_duration);
        let stats = SessionStatistics::analyze_session(&session);
        
        assert_eq!(stats.user_type, UserType::Admin);
        assert_eq!(stats.total_actions, session.actions.len() as u32);
        assert!(stats.unique_action_types > 0);
        assert!(stats.estimated_duration > Duration::ZERO);
    }

    #[test]
    fn think_times_are_appropriate_for_user_types() {
        let (regular_min, regular_max) = UserBehaviorPatterns::think_time_for_user_type(UserType::Regular);
        let (admin_min, admin_max) = UserBehaviorPatterns::think_time_for_user_type(UserType::Admin);
        let (service_min, service_max) = UserBehaviorPatterns::think_time_for_user_type(UserType::Service);
        
        // Service accounts should have shortest think times (automated)
        assert!(service_max < regular_min);
        
        // Admin users should have longer think times (more complex decisions)
        assert!(admin_max > regular_max);
        
        // All ranges should be valid
        assert!(regular_min < regular_max);
        assert!(admin_min < admin_max);
        assert!(service_min < service_max);
    }

    #[test]
    fn actions_per_session_ranges_are_realistic() {
        let (regular_min, regular_max) = UserBehaviorPatterns::actions_per_session_range(UserType::Regular);
        let (admin_min, admin_max) = UserBehaviorPatterns::actions_per_session_range(UserType::Admin);
        let (service_min, service_max) = UserBehaviorPatterns::actions_per_session_range(UserType::Service);
        
        // Service accounts should have the most actions (automated workflows)
        assert!(service_max > admin_max);
        assert!(service_max > regular_max);
        
        // Admin users should have more actions than regular users
        assert!(admin_max > regular_max);
        
        // All ranges should be reasonable
        assert!(regular_min >= 3);
        assert!(admin_min >= 8);
        assert!(service_min >= 10);
    }
}