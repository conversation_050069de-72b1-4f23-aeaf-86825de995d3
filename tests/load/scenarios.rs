//! Load Testing Scenarios
//! 
//! Defines comprehensive load testing scenarios for AuthService query handlers
//! with realistic user patterns and performance validation.

use std::time::Duration;
use crate::load::{LoadTestConfig, Sla<PERSON>onfig, Load<PERSON>est<PERSON><PERSON><PERSON>, LoadTestResults, LoadTestResult, LoadTestError};

/// Predefined load test scenarios for AuthService
pub struct LoadTestScenarios;

impl LoadTestScenarios {
    /// Normal load scenario: 100 concurrent users for 10 minutes
    /// Target: Typical production usage patterns
    /// SLA: P95 < 100ms, Error rate < 1%, Cache hit ratio > 80%
    pub fn normal_load() -> LoadTestConfig {
        LoadTestConfig {
            scenario_name: "Normal Load Test".to_string(),
            concurrent_users: 100,
            test_duration: Duration::from_secs(600), // 10 minutes
            ramp_up_time: Duration::from_secs(60), // 1 minute gradual ramp-up
            ramp_down_time: Duration::from_secs(30),
            target_base_url: "http://localhost:8080".to_string(),
            performance_thresholds: SlaConfig {
                max_p95_response_time: Duration::from_millis(100),
                max_error_rate: 0.01, // 1%
                min_cache_hit_ratio: 0.80, // 80%
                max_memory_growth_mb: 50.0,
                max_cpu_utilization: 0.70, // 70%
            },
            user_type_distribution: crate::load::UserTypeDistribution {
                regular_users: 0.70, // 70% regular users
                admin_users: 0.20,   // 20% admin users
                service_accounts: 0.10, // 10% service accounts
            },
            think_time_range: (Duration::from_millis(500), Duration::from_secs(3)),
            max_requests_per_second: Some(200),
            enable_monitoring: true,
            warm_up_duration: Duration::from_secs(60),
        }
    }

    /// Peak load scenario: 500 concurrent users for 15 minutes
    /// Target: Black Friday / peak traffic simulation
    /// SLA: P95 < 100ms, Error rate < 1%, Cache hit ratio > 80%
    pub fn peak_load() -> LoadTestConfig {
        LoadTestConfig {
            scenario_name: "Peak Load Test".to_string(),
            concurrent_users: 500,
            test_duration: Duration::from_secs(900), // 15 minutes
            ramp_up_time: Duration::from_secs(180), // 3 minute ramp-up
            ramp_down_time: Duration::from_secs(60),
            target_base_url: "http://localhost:8080".to_string(),
            performance_thresholds: SlaConfig {
                max_p95_response_time: Duration::from_millis(100),
                max_error_rate: 0.01, // 1%
                min_cache_hit_ratio: 0.80, // 80%
                max_memory_growth_mb: 150.0,
                max_cpu_utilization: 0.85, // 85%
            },
            user_type_distribution: crate::load::UserTypeDistribution {
                regular_users: 0.75, // More regular users during peak
                admin_users: 0.15,   // Fewer admin operations
                service_accounts: 0.10,
            },
            think_time_range: (Duration::from_millis(200), Duration::from_secs(2)),
            max_requests_per_second: Some(1000),
            enable_monitoring: true,
            warm_up_duration: Duration::from_secs(120),
        }
    }

    /// Stress test scenario: Find breaking point by gradually increasing load
    /// Target: Determine system capacity limits and degradation behavior
    /// SLA: Track when response times exceed thresholds and error rates spike
    pub fn stress_test() -> LoadTestConfig {
        LoadTestConfig {
            scenario_name: "Stress Test".to_string(),
            concurrent_users: 1000, // Start high, framework will ramp up
            test_duration: Duration::from_secs(1200), // 20 minutes
            ramp_up_time: Duration::from_secs(600), // Slow 10 minute ramp-up
            ramp_down_time: Duration::from_secs(120),
            target_base_url: "http://localhost:8080".to_string(),
            performance_thresholds: SlaConfig {
                max_p95_response_time: Duration::from_millis(500), // More lenient for stress test
                max_error_rate: 0.05, // 5% error rate allowed
                min_cache_hit_ratio: 0.70, // 70% (may degrade under stress)
                max_memory_growth_mb: 300.0,
                max_cpu_utilization: 0.95, // 95%
            },
            user_type_distribution: crate::load::UserTypeDistribution {
                regular_users: 0.80, // Heavy regular user load
                admin_users: 0.15,
                service_accounts: 0.05,
            },
            think_time_range: (Duration::from_millis(100), Duration::from_millis(500)),
            max_requests_per_second: None, // No limit - find breaking point
            enable_monitoring: true,
            warm_up_duration: Duration::from_secs(180),
        }
    }

    /// Endurance test scenario: 200 users for 60 minutes
    /// Target: Detect memory leaks, connection leaks, and performance degradation over time
    /// SLA: P95 < 100ms maintained throughout, No memory growth > 100MB
    pub fn endurance_test() -> LoadTestConfig {
        LoadTestConfig {
            scenario_name: "Endurance Test".to_string(),
            concurrent_users: 200,
            test_duration: Duration::from_secs(3600), // 60 minutes
            ramp_up_time: Duration::from_secs(120), // 2 minute ramp-up
            ramp_down_time: Duration::from_secs(60),
            target_base_url: "http://localhost:8080".to_string(),
            performance_thresholds: SlaConfig {
                max_p95_response_time: Duration::from_millis(100),
                max_error_rate: 0.005, // 0.5% - very strict for endurance
                min_cache_hit_ratio: 0.85, // 85% - should improve over time
                max_memory_growth_mb: 100.0, // No significant memory growth
                max_cpu_utilization: 0.75, // 75%
            },
            user_type_distribution: crate::load::UserTypeDistribution {
                regular_users: 0.70,
                admin_users: 0.20,
                service_accounts: 0.10,
            },
            think_time_range: (Duration::from_secs(1), Duration::from_secs(5)), // Longer think times
            max_requests_per_second: Some(300),
            enable_monitoring: true,
            warm_up_duration: Duration::from_secs(300), // 5 minute warm-up
        }
    }

    /// Spike test scenario: Rapid increase from 50 to 500 users
    /// Target: Test system behavior under sudden traffic spikes
    /// SLA: System should recover within 30 seconds, no cascading failures
    pub fn spike_test() -> LoadTestConfig {
        LoadTestConfig {
            scenario_name: "Spike Test".to_string(),
            concurrent_users: 500, // Target spike level
            test_duration: Duration::from_secs(600), // 10 minutes total
            ramp_up_time: Duration::from_secs(10), // Rapid 10 second spike
            ramp_down_time: Duration::from_secs(30),
            target_base_url: "http://localhost:8080".to_string(),
            performance_thresholds: SlaConfig {
                max_p95_response_time: Duration::from_millis(200), // More lenient during spike
                max_error_rate: 0.02, // 2% error rate allowed during spike
                min_cache_hit_ratio: 0.70, // 70% (may degrade during spike)
                max_memory_growth_mb: 200.0,
                max_cpu_utilization: 0.90, // 90%
            },
            user_type_distribution: crate::load::UserTypeDistribution {
                regular_users: 0.85, // Mostly regular users during spike
                admin_users: 0.10,
                service_accounts: 0.05,
            },
            think_time_range: (Duration::from_millis(50), Duration::from_millis(300)),
            max_requests_per_second: None, // No limit during spike
            enable_monitoring: true,
            warm_up_duration: Duration::from_secs(30),
        }
    }

    /// Get all predefined scenarios
    pub fn all_scenarios() -> Vec<LoadTestConfig> {
        vec![
            Self::normal_load(),
            Self::peak_load(),
            Self::stress_test(),
            Self::endurance_test(),
            Self::spike_test(),
        ]
    }

    /// Execute a specific scenario by name
    pub async fn execute_scenario(scenario_name: &str) -> LoadTestResult<LoadTestResults> {
        let config = match scenario_name.to_lowercase().as_str() {
            "normal" | "normal_load" => Self::normal_load(),
            "peak" | "peak_load" => Self::peak_load(),
            "stress" | "stress_test" => Self::stress_test(),
            "endurance" | "endurance_test" => Self::endurance_test(),
            "spike" | "spike_test" => Self::spike_test(),
            _ => return Err(LoadTestError::SetupFailed(
                format!("Unknown scenario: {}", scenario_name)
            )),
        };

        let runner = LoadTestRunner::new(config);
        runner.execute().await
    }

    /// Execute all scenarios in sequence
    pub async fn execute_all_scenarios() -> LoadTestResult<Vec<LoadTestResults>> {
        let mut results = Vec::new();
        
        for scenario in Self::all_scenarios() {
            tracing::info!("Starting scenario: {}", scenario.scenario_name);
            
            let runner = LoadTestRunner::new(scenario);
            match runner.execute().await {
                Ok(result) => {
                    tracing::info!("Scenario completed successfully: {}", result.generate_report());
                    results.push(result);
                }
                Err(e) => {
                    tracing::error!("Scenario failed: {}", e);
                    return Err(e);
                }
            }
            
            // Brief pause between scenarios to allow system recovery
            tokio::time::sleep(Duration::from_secs(30)).await;
        }
        
        Ok(results)
    }
}

/// Specialized scenario configurations for specific testing needs
pub struct SpecializedScenarios;

impl SpecializedScenarios {
    /// Database-focused load test: Heavy query operations
    pub fn database_intensive() -> LoadTestConfig {
        let mut config = LoadTestScenarios::normal_load();
        config.scenario_name = "Database Intensive Test".to_string();
        config.user_type_distribution = crate::load::UserTypeDistribution {
            regular_users: 0.40, // Fewer regular users
            admin_users: 0.50,   // More admin users (complex queries)
            service_accounts: 0.10,
        };
        config.think_time_range = (Duration::from_millis(100), Duration::from_millis(500));
        config.performance_thresholds.min_cache_hit_ratio = 0.60; // Lower due to complex queries
        config
    }

    /// Cache-focused load test: Heavy cache utilization
    pub fn cache_intensive() -> LoadTestConfig {
        let mut config = LoadTestScenarios::peak_load();
        config.scenario_name = "Cache Intensive Test".to_string();
        config.user_type_distribution = crate::load::UserTypeDistribution {
            regular_users: 0.90, // Mostly regular users (cacheable queries)
            admin_users: 0.05,
            service_accounts: 0.05,
        };
        config.think_time_range = (Duration::from_millis(50), Duration::from_millis(200));
        config.performance_thresholds.min_cache_hit_ratio = 0.95; // Very high cache hit ratio expected
        config
    }

    /// Security-focused load test: Mixed authentication patterns
    pub fn security_intensive() -> LoadTestConfig {
        let mut config = LoadTestScenarios::normal_load();
        config.scenario_name = "Security Intensive Test".to_string();
        config.user_type_distribution = crate::load::UserTypeDistribution {
            regular_users: 0.60,
            admin_users: 0.30, // More admin operations (permission checks)
            service_accounts: 0.10,
        };
        config.think_time_range = (Duration::from_millis(200), Duration::from_secs(1));
        config.performance_thresholds.max_p95_response_time = Duration::from_millis(150); // Security checks may be slower
        config
    }

    /// Mobile/API focused test: High frequency, low complexity
    pub fn mobile_api_simulation() -> LoadTestConfig {
        let mut config = LoadTestScenarios::peak_load();
        config.scenario_name = "Mobile API Simulation".to_string();
        config.concurrent_users = 1000; // High concurrent mobile users
        config.user_type_distribution = crate::load::UserTypeDistribution {
            regular_users: 0.95, // Almost all regular users
            admin_users: 0.03,
            service_accounts: 0.02,
        };
        config.think_time_range = (Duration::from_millis(100), Duration::from_millis(500));
        config.max_requests_per_second = Some(2000); // High RPS for mobile
        config
    }
}

/// Scenario validation utilities
pub struct ScenarioValidator;

impl ScenarioValidator {
    /// Validate a scenario configuration for sanity
    pub fn validate_config(config: &LoadTestConfig) -> Result<(), String> {
        if config.concurrent_users == 0 {
            return Err("Concurrent users must be greater than 0".to_string());
        }
        
        if config.test_duration.is_zero() {
            return Err("Test duration must be greater than 0".to_string());
        }
        
        if config.ramp_up_time >= config.test_duration {
            return Err("Ramp-up time must be less than test duration".to_string());
        }
        
        if config.target_base_url.is_empty() {
            return Err("Target base URL cannot be empty".to_string());
        }
        
        let distribution = &config.user_type_distribution;
        let total = distribution.regular_users + distribution.admin_users + distribution.service_accounts;
        if (total - 1.0).abs() > 0.01 {
            return Err("User type distribution must sum to 1.0".to_string());
        }
        
        if config.think_time_range.0 > config.think_time_range.1 {
            return Err("Think time minimum must be less than maximum".to_string());
        }
        
        Ok(())
    }
    
    /// Check if scenario is suitable for the given environment
    pub fn check_environment_suitability(config: &LoadTestConfig, available_memory_gb: f64, available_cpu_cores: u32) -> Vec<String> {
        let mut warnings = Vec::new();
        
        // Memory estimation: ~2MB per concurrent user
        let estimated_memory_gb = (config.concurrent_users as f64 * 2.0) / 1024.0;
        if estimated_memory_gb > available_memory_gb * 0.8 {
            warnings.push(format!(
                "High memory usage expected: {:.1}GB estimated vs {:.1}GB available",
                estimated_memory_gb, available_memory_gb
            ));
        }
        
        // CPU estimation: ~50 users per core for load testing
        let recommended_cores = (config.concurrent_users as f64 / 50.0).ceil() as u32;
        if recommended_cores > available_cpu_cores {
            warnings.push(format!(
                "High CPU usage expected: {} cores recommended vs {} available",
                recommended_cores, available_cpu_cores
            ));
        }
        
        // Duration warnings
        if config.test_duration.as_secs() > 3600 {
            warnings.push("Long test duration may impact system stability".to_string());
        }
        
        // Concurrent user warnings
        if config.concurrent_users > 1000 {
            warnings.push("Very high concurrent user count - ensure sufficient resources".to_string());
        }
        
        warnings
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn normal_load_scenario_has_correct_configuration() {
        let config = LoadTestScenarios::normal_load();
        
        assert_eq!(config.scenario_name, "Normal Load Test");
        assert_eq!(config.concurrent_users, 100);
        assert_eq!(config.test_duration, Duration::from_secs(600));
        assert_eq!(config.performance_thresholds.max_p95_response_time, Duration::from_millis(100));
        assert_eq!(config.performance_thresholds.max_error_rate, 0.01);
        assert_eq!(config.performance_thresholds.min_cache_hit_ratio, 0.80);
    }

    #[test]
    fn peak_load_scenario_has_higher_concurrency() {
        let config = LoadTestScenarios::peak_load();
        
        assert_eq!(config.scenario_name, "Peak Load Test");
        assert_eq!(config.concurrent_users, 500);
        assert_eq!(config.test_duration, Duration::from_secs(900));
        assert!(config.concurrent_users > LoadTestScenarios::normal_load().concurrent_users);
    }

    #[test]
    fn stress_test_has_most_lenient_thresholds() {
        let stress_config = LoadTestScenarios::stress_test();
        let normal_config = LoadTestScenarios::normal_load();
        
        assert!(stress_config.performance_thresholds.max_p95_response_time > normal_config.performance_thresholds.max_p95_response_time);
        assert!(stress_config.performance_thresholds.max_error_rate > normal_config.performance_thresholds.max_error_rate);
        assert!(stress_config.concurrent_users > normal_config.concurrent_users);
    }

    #[test]
    fn endurance_test_has_longest_duration() {
        let endurance_config = LoadTestScenarios::endurance_test();
        let all_configs = LoadTestScenarios::all_scenarios();
        
        for config in all_configs {
            if config.scenario_name != endurance_config.scenario_name {
                assert!(endurance_config.test_duration >= config.test_duration);
            }
        }
    }

    #[test]
    fn spike_test_has_shortest_ramp_up() {
        let spike_config = LoadTestScenarios::spike_test();
        let all_configs = LoadTestScenarios::all_scenarios();
        
        for config in all_configs {
            if config.scenario_name != spike_config.scenario_name {
                assert!(spike_config.ramp_up_time <= config.ramp_up_time);
            }
        }
    }

    #[test]
    fn all_scenarios_have_unique_names() {
        let scenarios = LoadTestScenarios::all_scenarios();
        let mut names = std::collections::HashSet::new();
        
        for scenario in scenarios {
            assert!(names.insert(scenario.scenario_name.clone()), 
                   "Duplicate scenario name: {}", scenario.scenario_name);
        }
    }

    #[test]
    fn scenario_validator_accepts_valid_config() {
        let config = LoadTestScenarios::normal_load();
        assert!(ScenarioValidator::validate_config(&config).is_ok());
    }

    #[test]
    fn scenario_validator_rejects_zero_users() {
        let mut config = LoadTestScenarios::normal_load();
        config.concurrent_users = 0;
        
        let result = ScenarioValidator::validate_config(&config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Concurrent users must be greater than 0"));
    }

    #[test]
    fn scenario_validator_rejects_invalid_user_distribution() {
        let mut config = LoadTestScenarios::normal_load();
        config.user_type_distribution = crate::load::UserTypeDistribution {
            regular_users: 0.50,
            admin_users: 0.30,
            service_accounts: 0.30, // Total = 1.10, invalid
        };
        
        let result = ScenarioValidator::validate_config(&config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("User type distribution must sum to 1.0"));
    }

    #[test]
    fn scenario_validator_provides_resource_warnings() {
        let config = LoadTestScenarios::stress_test(); // 1000 users
        let warnings = ScenarioValidator::check_environment_suitability(&config, 4.0, 2);
        
        assert!(!warnings.is_empty());
        assert!(warnings.iter().any(|w| w.contains("CPU usage expected")));
        assert!(warnings.iter().any(|w| w.contains("memory usage expected")));
    }

    #[test]
    fn specialized_scenarios_have_distinct_characteristics() {
        let db_intensive = SpecializedScenarios::database_intensive();
        let cache_intensive = SpecializedScenarios::cache_intensive();
        let security_intensive = SpecializedScenarios::security_intensive();
        let mobile_api = SpecializedScenarios::mobile_api_simulation();
        
        // Database intensive should have more admin users
        assert!(db_intensive.user_type_distribution.admin_users > 0.4);
        
        // Cache intensive should have very high cache hit ratio requirement
        assert!(cache_intensive.performance_thresholds.min_cache_hit_ratio > 0.9);
        
        // Security intensive should have longer response time allowance
        assert!(security_intensive.performance_thresholds.max_p95_response_time > Duration::from_millis(100));
        
        // Mobile API should have high concurrent users
        assert!(mobile_api.concurrent_users > 500);
    }

    #[tokio::test]
    async fn execute_scenario_handles_unknown_scenario() {
        let result = LoadTestScenarios::execute_scenario("unknown_scenario").await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), LoadTestError::SetupFailed(_)));
    }
}