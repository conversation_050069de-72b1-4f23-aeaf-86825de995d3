//! Load Testing Framework
//! 
//! Core framework for executing load tests with configurable scenarios,
//! user simulation, and performance monitoring integration.

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::time::sleep;
use rand::{Rng, thread_rng};

use crate::load::{LoadTestResults, SlaConfig, LoadTestError, LoadTestResult};
use crate::load::user_simulation::{UserSession, UserAction, UserType};
use crate::load::monitoring_integration::LoadTestMonitor;

/// Configuration for a load test execution
#[derive(Debug, Clone)]
pub struct LoadTestConfig {
    pub scenario_name: String,
    pub concurrent_users: u32,
    pub test_duration: Duration,
    pub ramp_up_time: Duration,
    pub ramp_down_time: Duration,
    pub target_base_url: String,
    pub performance_thresholds: SlaConfig,
    pub user_type_distribution: UserTypeDistribution,
    pub think_time_range: (Duration, Duration),
    pub max_requests_per_second: Option<u32>,
    pub enable_monitoring: bool,
    pub warm_up_duration: Duration,
}

impl Default for LoadTestConfig {
    fn default() -> Self {
        Self {
            scenario_name: "default_load_test".to_string(),
            concurrent_users: 100,
            test_duration: Duration::from_secs(600), // 10 minutes
            ramp_up_time: Duration::from_secs(60), // 1 minute
            ramp_down_time: Duration::from_secs(30), // 30 seconds
            target_base_url: "http://localhost:8080".to_string(),
            performance_thresholds: SlaConfig::default(),
            user_type_distribution: UserTypeDistribution::default(),
            think_time_range: (Duration::from_millis(100), Duration::from_secs(2)),
            max_requests_per_second: None,
            enable_monitoring: true,
            warm_up_duration: Duration::from_secs(30),
        }
    }
}

/// Distribution of user types in the load test
#[derive(Debug, Clone)]
pub struct UserTypeDistribution {
    pub regular_users: f64,    // 70%
    pub admin_users: f64,      // 20%
    pub service_accounts: f64, // 10%
}

impl Default for UserTypeDistribution {
    fn default() -> Self {
        Self {
            regular_users: 0.70,
            admin_users: 0.20,
            service_accounts: 0.10,
        }
    }
}

impl UserTypeDistribution {
    pub fn get_user_type(&self) -> UserType {
        let mut rng = thread_rng();
        let random_value: f64 = rng.gen();
        
        if random_value < self.regular_users {
            UserType::Regular
        } else if random_value < self.regular_users + self.admin_users {
            UserType::Admin
        } else {
            UserType::Service
        }
    }
}

/// Main load test executor
pub struct LoadTestRunner {
    config: LoadTestConfig,
    monitor: Option<Arc<LoadTestMonitor>>,
    client: reqwest::Client,
    stats: Arc<Mutex<LoadTestStats>>,
}

#[derive(Debug, Default)]
struct LoadTestStats {
    total_requests: u64,
    successful_requests: u64,
    failed_requests: u64,
    response_times: Vec<Duration>,
    error_counts: HashMap<String, u64>,
    cache_hits: u64,
    cache_misses: u64,
    sla_violations: u64,
}

impl LoadTestRunner {
    pub fn new(config: LoadTestConfig) -> Self {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .pool_max_idle_per_host(config.concurrent_users as usize * 2)
            .build()
            .expect("Failed to create HTTP client");

        let monitor = if config.enable_monitoring {
            Some(Arc::new(LoadTestMonitor::new()))
        } else {
            None
        };

        Self {
            config,
            monitor,
            client,
            stats: Arc::new(Mutex::new(LoadTestStats::default())),
        }
    }

    pub async fn execute(&self) -> LoadTestResult<LoadTestResults> {
        tracing::info!("Starting load test: {}", self.config.scenario_name);
        
        // Warm up phase
        if self.config.warm_up_duration > Duration::ZERO {
            self.warm_up().await?;
        }

        let start_time = Instant::now();
        
        // Initialize monitoring
        if let Some(monitor) = &self.monitor {
            monitor.start_monitoring(&self.config).await?;
        }

        // Execute the main load test
        let semaphore = Arc::new(Semaphore::new(self.config.concurrent_users as usize));
        let mut tasks = Vec::new();

        let ramp_up_interval = if self.config.concurrent_users > 1 {
            self.config.ramp_up_time.as_millis() / (self.config.concurrent_users as u128)
        } else {
            0
        };

        // Spawn user sessions with ramp-up
        for user_id in 0..self.config.concurrent_users {
            let semaphore = semaphore.clone();
            let config = self.config.clone();
            let client = self.client.clone();
            let stats = self.stats.clone();
            let monitor = self.monitor.clone();

            let task = tokio::spawn(async move {
                // Ramp-up delay
                if ramp_up_interval > 0 {
                    let delay = Duration::from_millis((user_id as u128 * ramp_up_interval) as u64);
                    sleep(delay).await;
                }

                let _permit = semaphore.acquire().await.unwrap();
                
                let user_session = UserSession::generate_for_type(
                    config.user_type_distribution.get_user_type(),
                    config.think_time_range,
                    config.test_duration,
                );

                Self::execute_user_session(
                    user_id,
                    user_session,
                    &config,
                    &client,
                    stats,
                    monitor,
                ).await
            });

            tasks.push(task);
        }

        // Wait for test duration
        sleep(self.config.test_duration).await;

        // Stop monitoring
        if let Some(monitor) = &self.monitor {
            monitor.stop_monitoring().await?;
        }

        // Cancel remaining tasks and collect results
        for task in tasks {
            task.abort();
        }

        let execution_time = start_time.elapsed();
        
        // Compile results
        let results = self.compile_results(execution_time).await?;
        
        tracing::info!("Load test completed: {}", results.generate_report());
        
        // Validate SLA compliance
        if !results.meets_sla(&self.config.performance_thresholds) {
            return Err(LoadTestError::SlaViolation(format!(
                "Load test {} failed to meet SLA requirements",
                self.config.scenario_name
            )));
        }

        Ok(results)
    }

    async fn warm_up(&self) -> LoadTestResult<()> {
        tracing::info!("Starting warm-up phase for {} seconds", self.config.warm_up_duration.as_secs());
        
        let warm_up_users = std::cmp::min(10, self.config.concurrent_users);
        let semaphore = Arc::new(Semaphore::new(warm_up_users as usize));
        let mut tasks = Vec::new();

        for user_id in 0..warm_up_users {
            let semaphore = semaphore.clone();
            let config = self.config.clone();
            let client = self.client.clone();

            let task = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                
                let user_session = UserSession::generate_for_type(
                    UserType::Regular,
                    (Duration::from_millis(50), Duration::from_millis(200)),
                    config.warm_up_duration,
                );

                // Execute warm-up requests (don't count in stats)
                for action in user_session.actions {
                    let _ = Self::execute_action(
                        user_id,
                        &action,
                        &config.target_base_url,
                        &client,
                    ).await;
                    
                    sleep(Duration::from_millis(100)).await;
                }
            });

            tasks.push(task);
        }

        // Wait for warm-up to complete
        sleep(self.config.warm_up_duration).await;

        // Cancel remaining warm-up tasks
        for task in tasks {
            task.abort();
        }

        tracing::info!("Warm-up phase completed");
        Ok(())
    }

    async fn execute_user_session(
        user_id: u32,
        user_session: UserSession,
        config: &LoadTestConfig,
        client: &reqwest::Client,
        stats: Arc<Mutex<LoadTestStats>>,
        monitor: Option<Arc<LoadTestMonitor>>,
    ) {
        let session_start = Instant::now();
        
        for action in user_session.actions {
            if session_start.elapsed() >= config.test_duration {
                break;
            }

            let action_start = Instant::now();
            
            match Self::execute_action(user_id, &action, &config.target_base_url, client).await {
                Ok(response) => {
                    let response_time = action_start.elapsed();
                    
                    let mut stats_guard = stats.lock().unwrap();
                    stats_guard.total_requests += 1;
                    stats_guard.successful_requests += 1;
                    stats_guard.response_times.push(response_time);
                    
                    // Check for cache hits/misses
                    if let Some(cache_header) = response.headers().get("x-cache-status") {
                        if cache_header == "HIT" {
                            stats_guard.cache_hits += 1;
                        } else {
                            stats_guard.cache_misses += 1;
                        }
                    } else {
                        stats_guard.cache_misses += 1;
                    }
                    
                    // Check for SLA violations
                    if response_time > config.performance_thresholds.max_p95_response_time {
                        stats_guard.sla_violations += 1;
                    }
                    
                    drop(stats_guard);
                    
                    // Update monitoring
                    if let Some(monitor) = &monitor {
                        monitor.record_request_success(&action, response_time, response.status()).await;
                    }
                }
                Err(error) => {
                    let response_time = action_start.elapsed();
                    
                    let mut stats_guard = stats.lock().unwrap();
                    stats_guard.total_requests += 1;
                    stats_guard.failed_requests += 1;
                    stats_guard.response_times.push(response_time);
                    
                    *stats_guard.error_counts.entry(error.to_string()).or_insert(0) += 1;
                    drop(stats_guard);
                    
                    // Update monitoring
                    if let Some(monitor) = &monitor {
                        monitor.record_request_failure(&action, response_time, &error).await;
                    }
                }
            }

            // Think time
            let think_time = {
                let mut rng = thread_rng();
                let min_ms = user_session.think_time.as_millis() as u64;
                let max_ms = (user_session.think_time * 2).as_millis() as u64;
                Duration::from_millis(rng.gen_range(min_ms..=max_ms))
            };
            
            sleep(think_time).await;
        }
    }

    async fn execute_action(
        user_id: u32,
        action: &UserAction,
        base_url: &str,
        client: &reqwest::Client,
    ) -> Result<reqwest::Response, reqwest::Error> {
        match action {
            UserAction::Login { email, password } => {
                client.post(&format!("{}/api/auth/login", base_url))
                    .json(&serde_json::json!({
                        "email": email,
                        "password": password
                    }))
                    .send()
                    .await
            }
            UserAction::CheckPermissions { resource, action: perm_action } => {
                client.get(&format!("{}/api/auth/permissions", base_url))
                    .query(&[("resource", resource), ("action", perm_action)])
                    .send()
                    .await
            }
            UserAction::GetProfile => {
                client.get(&format!("{}/api/users/profile", base_url))
                    .send()
                    .await
            }
            UserAction::ListRoles => {
                client.get(&format!("{}/api/users/roles", base_url))
                    .send()
                    .await
            }
            UserAction::SearchUsers { query } => {
                client.get(&format!("{}/api/users/search", base_url))
                    .query(&[("q", query)])
                    .send()
                    .await
            }
            UserAction::ViewAuditLog { filters } => {
                client.get(&format!("{}/api/audit", base_url))
                    .query(&[("filters", filters)])
                    .send()
                    .await
            }
            UserAction::Logout => {
                client.post(&format!("{}/api/auth/logout", base_url))
                    .send()
                    .await
            }
        }
    }

    async fn compile_results(&self, execution_time: Duration) -> LoadTestResult<LoadTestResults> {
        let stats = self.stats.lock().unwrap();
        
        if stats.total_requests == 0 {
            return Err(LoadTestError::ExecutionFailed("No requests were executed".to_string()));
        }
        
        let mut response_times = stats.response_times.clone();
        response_times.sort();
        
        let total_requests = stats.total_requests;
        let successful_requests = stats.successful_requests;
        let failed_requests = stats.failed_requests;
        
        let average_response_time = if !response_times.is_empty() {
            let total_ms: u64 = response_times.iter().map(|d| d.as_millis() as u64).sum();
            Duration::from_millis(total_ms / response_times.len() as u64)
        } else {
            Duration::ZERO
        };
        
        let p50_response_time = if !response_times.is_empty() {
            response_times[response_times.len() * 50 / 100]
        } else {
            Duration::ZERO
        };
        
        let p95_response_time = if !response_times.is_empty() {
            response_times[response_times.len() * 95 / 100]
        } else {
            Duration::ZERO
        };
        
        let p99_response_time = if !response_times.is_empty() {
            response_times[response_times.len() * 99 / 100]
        } else {
            Duration::ZERO
        };
        
        let requests_per_second = total_requests as f64 / execution_time.as_secs_f64();
        let error_rate = failed_requests as f64 / total_requests as f64;
        
        let cache_hit_ratio = if stats.cache_hits + stats.cache_misses > 0 {
            stats.cache_hits as f64 / (stats.cache_hits + stats.cache_misses) as f64
        } else {
            0.0
        };
        
        // Get system resource usage from monitoring
        let (memory_usage_mb, cpu_utilization) = if let Some(monitor) = &self.monitor {
            monitor.get_resource_usage().await
        } else {
            (0.0, 0.0)
        };
        
        Ok(LoadTestResults {
            scenario_name: self.config.scenario_name.clone(),
            duration: execution_time,
            total_requests,
            successful_requests,
            failed_requests,
            average_response_time,
            p50_response_time,
            p95_response_time,
            p99_response_time,
            requests_per_second,
            error_rate,
            cache_hit_ratio,
            memory_usage_mb,
            cpu_utilization,
            concurrent_users: self.config.concurrent_users,
            sla_violations: stats.sla_violations,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn user_type_distribution_default_totals_one() {
        let distribution = UserTypeDistribution::default();
        let total = distribution.regular_users + distribution.admin_users + distribution.service_accounts;
        assert!((total - 1.0).abs() < f64::EPSILON);
    }

    #[test]
    fn user_type_distribution_generates_expected_types() {
        let distribution = UserTypeDistribution {
            regular_users: 1.0,
            admin_users: 0.0,
            service_accounts: 0.0,
        };
        
        // Should always return Regular when regular_users = 1.0
        for _ in 0..100 {
            assert!(matches!(distribution.get_user_type(), UserType::Regular));
        }
    }

    #[test]
    fn load_test_config_has_reasonable_defaults() {
        let config = LoadTestConfig::default();
        
        assert_eq!(config.concurrent_users, 100);
        assert_eq!(config.test_duration, Duration::from_secs(600));
        assert_eq!(config.ramp_up_time, Duration::from_secs(60));
        assert_eq!(config.target_base_url, "http://localhost:8080");
        assert!(config.enable_monitoring);
    }

    #[tokio::test]
    async fn load_test_runner_can_be_created() {
        let config = LoadTestConfig::default();
        let runner = LoadTestRunner::new(config);
        
        // Should create without panicking
        assert!(runner.stats.lock().unwrap().total_requests == 0);
    }

    #[tokio::test]
    async fn load_test_stats_track_basic_metrics() {
        let mut stats = LoadTestStats::default();
        
        stats.total_requests = 100;
        stats.successful_requests = 95;
        stats.failed_requests = 5;
        stats.response_times.push(Duration::from_millis(50));
        stats.cache_hits = 80;
        stats.cache_misses = 20;
        
        assert_eq!(stats.total_requests, 100);
        assert_eq!(stats.successful_requests, 95);
        assert_eq!(stats.failed_requests, 5);
        assert_eq!(stats.response_times.len(), 1);
        assert_eq!(stats.cache_hits, 80);
        assert_eq!(stats.cache_misses, 20);
    }
}