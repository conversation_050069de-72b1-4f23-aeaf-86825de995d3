//! Load Testing Infrastructure for AuthService
//! 
//! This module provides comprehensive load testing capabilities to validate
//! production-scale performance of the AuthService under realistic conditions.
//! 
//! ## Key Features
//! - Realistic user behavior simulation
//! - Multiple load test scenarios (normal, peak, stress, endurance, spike)
//! - Performance monitoring integration
//! - SLA validation and regression detection
//! - Automated reporting and baseline tracking

pub mod framework;
pub mod scenarios;
pub mod data_generator;
pub mod user_simulation;
pub mod monitoring_integration;
pub mod reporting;

pub use framework::*;
pub use scenarios::*;
pub use data_generator::*;
pub use user_simulation::*;
pub use monitoring_integration::*;
pub use reporting::*;

// Re-export common types for convenience
pub use std::time::Duration;
pub use tokio::time::Instant;

/// Load test execution results
#[derive(Debug, Clone)]
pub struct LoadTestResults {
    pub scenario_name: String,
    pub duration: Duration,
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub average_response_time: Duration,
    pub p50_response_time: Duration,
    pub p95_response_time: Duration,
    pub p99_response_time: Duration,
    pub requests_per_second: f64,
    pub error_rate: f64,
    pub cache_hit_ratio: f64,
    pub memory_usage_mb: f64,
    pub cpu_utilization: f64,
    pub concurrent_users: u32,
    pub sla_violations: u64,
}

impl LoadTestResults {
    /// Check if the load test met SLA requirements
    pub fn meets_sla(&self, sla_config: &SlaConfig) -> bool {
        self.p95_response_time <= sla_config.max_p95_response_time
            && self.error_rate <= sla_config.max_error_rate
            && self.cache_hit_ratio >= sla_config.min_cache_hit_ratio
    }
    
    /// Generate a performance report
    pub fn generate_report(&self) -> String {
        format!(
            "Load Test Results: {}\n\
             Duration: {:.2}s\n\
             Total Requests: {}\n\
             Success Rate: {:.2}%\n\
             RPS: {:.2}\n\
             P95 Response Time: {:.2}ms\n\
             Cache Hit Ratio: {:.2}%\n\
             SLA Violations: {}\n",
            self.scenario_name,
            self.duration.as_secs_f64(),
            self.total_requests,
            (self.successful_requests as f64 / self.total_requests as f64) * 100.0,
            self.requests_per_second,
            self.p95_response_time.as_millis() as f64,
            self.cache_hit_ratio * 100.0,
            self.sla_violations
        )
    }
}

/// SLA configuration for load testing
#[derive(Debug, Clone)]
pub struct SlaConfig {
    pub max_p95_response_time: Duration,
    pub max_error_rate: f64,
    pub min_cache_hit_ratio: f64,
    pub max_memory_growth_mb: f64,
    pub max_cpu_utilization: f64,
}

impl Default for SlaConfig {
    fn default() -> Self {
        Self {
            max_p95_response_time: Duration::from_millis(100),
            max_error_rate: 0.01, // 1%
            min_cache_hit_ratio: 0.80, // 80%
            max_memory_growth_mb: 100.0,
            max_cpu_utilization: 0.80, // 80%
        }
    }
}

/// Load test error types
#[derive(Debug, thiserror::Error)]
pub enum LoadTestError {
    #[error("Load test setup failed: {0}")]
    SetupFailed(String),
    
    #[error("Test execution failed: {0}")]
    ExecutionFailed(String),
    
    #[error("SLA violation detected: {0}")]
    SlaViolation(String),
    
    #[error("Performance regression detected: current {0}ms > baseline {1}ms")]
    PerformanceRegression(f64, f64),
    
    #[error("Resource exhaustion: {0}")]
    ResourceExhaustion(String),
    
    #[error("Test environment error: {0}")]
    EnvironmentError(String),
}

pub type LoadTestResult<T> = Result<T, LoadTestError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn load_test_results_meets_sla_when_within_thresholds() {
        let results = LoadTestResults {
            scenario_name: "test".to_string(),
            duration: Duration::from_secs(60),
            total_requests: 1000,
            successful_requests: 999,
            failed_requests: 1,
            average_response_time: Duration::from_millis(25),
            p50_response_time: Duration::from_millis(20),
            p95_response_time: Duration::from_millis(50),
            p99_response_time: Duration::from_millis(80),
            requests_per_second: 16.67,
            error_rate: 0.001,
            cache_hit_ratio: 0.85,
            memory_usage_mb: 45.2,
            cpu_utilization: 0.65,
            concurrent_users: 100,
            sla_violations: 0,
        };
        
        let sla_config = SlaConfig::default();
        assert!(results.meets_sla(&sla_config));
    }

    #[test]
    fn load_test_results_fails_sla_when_p95_exceeds_threshold() {
        let results = LoadTestResults {
            scenario_name: "test".to_string(),
            duration: Duration::from_secs(60),
            total_requests: 1000,
            successful_requests: 999,
            failed_requests: 1,
            average_response_time: Duration::from_millis(25),
            p50_response_time: Duration::from_millis(20),
            p95_response_time: Duration::from_millis(150), // Exceeds 100ms SLA
            p99_response_time: Duration::from_millis(200),
            requests_per_second: 16.67,
            error_rate: 0.001,
            cache_hit_ratio: 0.85,
            memory_usage_mb: 45.2,
            cpu_utilization: 0.65,
            concurrent_users: 100,
            sla_violations: 15,
        };
        
        let sla_config = SlaConfig::default();
        assert!(!results.meets_sla(&sla_config));
    }

    #[test]
    fn load_test_results_fails_sla_when_error_rate_exceeds_threshold() {
        let results = LoadTestResults {
            scenario_name: "test".to_string(),
            duration: Duration::from_secs(60),
            total_requests: 1000,
            successful_requests: 970,
            failed_requests: 30,
            average_response_time: Duration::from_millis(25),
            p50_response_time: Duration::from_millis(20),
            p95_response_time: Duration::from_millis(50),
            p99_response_time: Duration::from_millis(80),
            requests_per_second: 16.67,
            error_rate: 0.03, // 3% error rate exceeds 1% SLA
            cache_hit_ratio: 0.85,
            memory_usage_mb: 45.2,
            cpu_utilization: 0.65,
            concurrent_users: 100,
            sla_violations: 25,
        };
        
        let sla_config = SlaConfig::default();
        assert!(!results.meets_sla(&sla_config));
    }

    #[test]
    fn load_test_results_generates_comprehensive_report() {
        let results = LoadTestResults {
            scenario_name: "Normal Load Test".to_string(),
            duration: Duration::from_secs(300),
            total_requests: 5000,
            successful_requests: 4985,
            failed_requests: 15,
            average_response_time: Duration::from_millis(25),
            p50_response_time: Duration::from_millis(20),
            p95_response_time: Duration::from_millis(50),
            p99_response_time: Duration::from_millis(80),
            requests_per_second: 16.67,
            error_rate: 0.003,
            cache_hit_ratio: 0.85,
            memory_usage_mb: 45.2,
            cpu_utilization: 0.65,
            concurrent_users: 100,
            sla_violations: 2,
        };
        
        let report = results.generate_report();
        
        assert!(report.contains("Normal Load Test"));
        assert!(report.contains("Duration: 300.00s"));
        assert!(report.contains("Total Requests: 5000"));
        assert!(report.contains("Success Rate: 99.70%"));
        assert!(report.contains("RPS: 16.67"));
        assert!(report.contains("P95 Response Time: 50.00ms"));
        assert!(report.contains("Cache Hit Ratio: 85.00%"));
        assert!(report.contains("SLA Violations: 2"));
    }
}