//! Load Testing Reporting and Analysis
//! 
//! Provides comprehensive reporting capabilities including SLA compliance checking,
//! performance regression detection, trend analysis, and HTML/JSON report generation.

use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

use crate::load::{LoadTestResults, SlaConfig, LoadTestError, LoadTestResult};
use crate::load::monitoring_integration::{BaselineMetrics, PerformanceRegression, LoadTestRealTimeStats};

/// Comprehensive load test report with analysis and recommendations
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LoadTestReport {
    pub test_execution: TestExecutionSummary,
    pub performance_analysis: PerformanceAnalysis,
    pub sla_compliance: SlaComplianceReport,
    pub regression_analysis: Option<RegressionAnalysis>,
    pub trend_analysis: Option<TrendAnalysis>,
    pub recommendations: Vec<Recommendation>,
    pub detailed_metrics: DetailedMetrics,
    pub generated_at: DateTime<Utc>,
}

/// Test execution summary
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TestExecutionSummary {
    pub scenario_name: String,
    pub test_duration: Duration,
    pub concurrent_users: u32,
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub requests_per_second: f64,
    pub test_status: TestStatus,
    pub error_summary: HashMap<String, u64>,
}

/// Performance analysis results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAnalysis {
    pub response_time_analysis: ResponseTimeAnalysis,
    pub throughput_analysis: ThroughputAnalysis,
    pub cache_analysis: CacheAnalysis,
    pub resource_analysis: ResourceAnalysis,
    pub bottleneck_analysis: BottleneckAnalysis,
}

/// Response time analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseTimeAnalysis {
    pub average: Duration,
    pub median: Duration,
    pub p90: Duration,
    pub p95: Duration,
    pub p99: Duration,
    pub max: Duration,
    pub min: Duration,
    pub standard_deviation: Duration,
    pub distribution: ResponseTimeDistribution,
}

/// Response time distribution buckets
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseTimeDistribution {
    pub under_10ms: u64,
    pub ms_10_50: u64,
    pub ms_50_100: u64,
    pub ms_100_500: u64,
    pub ms_500_1000: u64,
    pub over_1000ms: u64,
}

/// Throughput analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThroughputAnalysis {
    pub average_rps: f64,
    pub peak_rps: f64,
    pub minimum_rps: f64,
    pub rps_stability: f64, // Coefficient of variation
    pub throughput_trend: ThroughputTrend,
}

/// Cache performance analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheAnalysis {
    pub overall_hit_ratio: f64,
    pub hit_ratio_by_endpoint: HashMap<String, f64>,
    pub cache_effectiveness: CacheEffectiveness,
    pub cache_performance_impact: f64, // % improvement from caching
}

/// Resource utilization analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceAnalysis {
    pub memory_analysis: ResourceMetricAnalysis,
    pub cpu_analysis: ResourceMetricAnalysis,
    pub connection_analysis: ConnectionAnalysis,
    pub resource_trend: ResourceTrend,
}

/// Resource metric analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceMetricAnalysis {
    pub average: f64,
    pub peak: f64,
    pub minimum: f64,
    pub growth_rate: f64, // Per minute
    pub stability: ResourceStability,
}

/// Bottleneck analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BottleneckAnalysis {
    pub identified_bottlenecks: Vec<Bottleneck>,
    pub performance_limiting_factor: LimitingFactor,
    pub scaling_recommendations: Vec<ScalingRecommendation>,
}

/// SLA compliance report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlaComplianceReport {
    pub overall_compliance: bool,
    pub compliance_score: f64, // 0-100%
    pub violations: Vec<SlaViolationDetail>,
    pub compliance_by_metric: HashMap<String, SlaMetricCompliance>,
    pub time_to_violation: Option<Duration>, // How long until first violation
    pub recovery_time: Option<Duration>, // Time to recover from violations
}

/// Detailed SLA violation information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlaViolationDetail {
    pub metric_name: String,
    pub violation_type: ViolationType,
    pub threshold: f64,
    pub actual_value: f64,
    pub violation_duration: Duration,
    pub first_occurrence: DateTime<Utc>,
    pub severity: ViolationSeverity,
}

/// SLA metric compliance status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlaMetricCompliance {
    pub metric_name: String,
    pub compliant: bool,
    pub threshold: f64,
    pub actual_value: f64,
    pub compliance_percentage: f64,
    pub margin: f64, // How close to threshold
}

/// Performance regression analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegressionAnalysis {
    pub regression_detected: bool,
    pub baseline_comparison: BaselineComparison,
    pub regression_severity: RegressionSeverity,
    pub affected_metrics: Vec<String>,
    pub root_cause_analysis: Vec<String>,
}

/// Baseline comparison results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaselineComparison {
    pub baseline_date: DateTime<Utc>,
    pub p95_change_percent: f64,
    pub error_rate_change_percent: f64,
    pub throughput_change_percent: f64,
    pub cache_hit_ratio_change_percent: f64,
    pub memory_usage_change_percent: f64,
}

/// Trend analysis over time
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysis {
    pub performance_trend: PerformanceTrend,
    pub capacity_trend: CapacityTrend,
    pub reliability_trend: ReliabilityTrend,
    pub predictions: Vec<PerformancePrediction>,
}

/// Actionable recommendations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Recommendation {
    pub priority: RecommendationPriority,
    pub category: RecommendationCategory,
    pub title: String,
    pub description: String,
    pub expected_impact: String,
    pub implementation_effort: ImplementationEffort,
    pub action_items: Vec<String>,
}

/// Detailed metrics for deep analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedMetrics {
    pub response_time_series: Vec<TimeSeriesPoint>,
    pub throughput_series: Vec<TimeSeriesPoint>,
    pub error_rate_series: Vec<TimeSeriesPoint>,
    pub resource_usage_series: Vec<ResourceTimeSeriesPoint>,
    pub cache_hit_ratio_series: Vec<TimeSeriesPoint>,
}

/// Time series data point
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeSeriesPoint {
    pub timestamp: DateTime<Utc>,
    pub value: f64,
}

/// Resource time series data point
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceTimeSeriesPoint {
    pub timestamp: DateTime<Utc>,
    pub memory_mb: f64,
    pub cpu_percent: f64,
    pub connections: u32,
}

// Enums for classification
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TestStatus {
    Passed,
    Failed,
    PartialFailure,
    Aborted,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThroughputTrend {
    Stable,
    Increasing,
    Decreasing,
    Volatile,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CacheEffectiveness {
    Excellent,
    Good,
    Fair,
    Poor,
    Ineffective,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceStability {
    Stable,
    GradualGrowth,
    MemoryLeak,
    Volatile,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceTrend {
    Stable,
    Growing,
    Declining,
    Cyclical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConnectionAnalysis {
    Healthy,
    PoolExhaustion,
    LeakDetected,
    Unstable,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Bottleneck {
    DatabaseConnections,
    CacheLatency,
    CPUBound,
    MemoryBound,
    NetworkLatency,
    ApplicationLogic,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LimitingFactor {
    ResponseTime,
    Throughput,
    ErrorRate,
    ResourceExhaustion,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScalingRecommendation {
    HorizontalScaling,
    VerticalScaling,
    CacheOptimization,
    DatabaseOptimization,
    ConnectionPooling,
    LoadBalancing,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ViolationType {
    ResponseTime,
    ErrorRate,
    CacheHitRatio,
    MemoryUsage,
    CPUUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ViolationSeverity {
    Critical,
    High,
    Medium,
    Low,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RegressionSeverity {
    Critical,
    Significant,
    Minor,
    None,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PerformanceTrend {
    Improving,
    Stable,
    Degrading,
    Volatile,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CapacityTrend {
    Increasing,
    Stable,
    Decreasing,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReliabilityTrend {
    Improving,
    Stable,
    Degrading,
    Inconsistent,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationPriority {
    Critical,
    High,
    Medium,
    Low,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationCategory {
    Performance,
    Scalability,
    Reliability,
    Security,
    Cost,
    Monitoring,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImplementationEffort {
    Low,
    Medium,
    High,
    VeryHigh,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformancePrediction {
    pub metric: String,
    pub predicted_value: f64,
    pub confidence: f64,
    pub time_horizon: Duration,
}

/// Main report generator
pub struct LoadTestReportGenerator {
    baseline_metrics: Option<BaselineMetrics>,
    historical_results: Vec<LoadTestResults>,
}

impl LoadTestReportGenerator {
    pub fn new() -> Self {
        Self {
            baseline_metrics: None,
            historical_results: Vec::new(),
        }
    }

    pub fn with_baseline(mut self, baseline: BaselineMetrics) -> Self {
        self.baseline_metrics = Some(baseline);
        self
    }

    pub fn with_historical_results(mut self, results: Vec<LoadTestResults>) -> Self {
        self.historical_results = results;
        self
    }

    /// Generate comprehensive load test report
    pub fn generate_report(&self, results: &LoadTestResults) -> LoadTestResult<LoadTestReport> {
        let test_execution = self.generate_execution_summary(results);
        let performance_analysis = self.analyze_performance(results)?;
        let sla_compliance = self.analyze_sla_compliance(results);
        let regression_analysis = self.analyze_regression(results);
        let trend_analysis = self.analyze_trends(results);
        let recommendations = self.generate_recommendations(
            &performance_analysis,
            &sla_compliance,
            &regression_analysis,
        );
        let detailed_metrics = self.generate_detailed_metrics(results);

        Ok(LoadTestReport {
            test_execution,
            performance_analysis,
            sla_compliance,
            regression_analysis,
            trend_analysis,
            recommendations,
            detailed_metrics,
            generated_at: Utc::now(),
        })
    }

    /// Generate HTML report
    pub fn generate_html_report(&self, report: &LoadTestReport) -> LoadTestResult<String> {
        let html = format!(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>Load Test Report - {}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .metric {{ display: inline-block; margin: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .pass {{ background-color: #d4edda; border-color: #c3e6cb; }}
        .fail {{ background-color: #f8d7da; border-color: #f5c6cb; }}
        .warning {{ background-color: #fff3cd; border-color: #ffeaa7; }}
        .recommendation {{ background-color: #e7f3ff; padding: 15px; margin: 10px 0; border-radius: 5px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Load Test Report: {}</h1>
        <p>Generated: {}</p>
        <p>Test Status: <span class="{}">{:?}</span></p>
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <div class="metric {}">
            <strong>Total Requests:</strong> {}
        </div>
        <div class="metric {}">
            <strong>Success Rate:</strong> {:.2}%
        </div>
        <div class="metric {}">
            <strong>P95 Response Time:</strong> {:.2}ms
        </div>
        <div class="metric {}">
            <strong>RPS:</strong> {:.2}
        </div>
        <div class="metric {}">
            <strong>Cache Hit Ratio:</strong> {:.2}%
        </div>
    </div>

    <div class="section">
        <h2>SLA Compliance</h2>
        <p>Overall Compliance: <span class="{}">{}</span></p>
        <p>Compliance Score: {:.1}%</p>
        {}
    </div>

    <div class="section">
        <h2>Performance Analysis</h2>
        <h3>Response Time Distribution</h3>
        <table>
            <tr><th>Percentile</th><th>Response Time</th></tr>
            <tr><td>Average</td><td>{:.2}ms</td></tr>
            <tr><td>P50 (Median)</td><td>{:.2}ms</td></tr>
            <tr><td>P90</td><td>{:.2}ms</td></tr>
            <tr><td>P95</td><td>{:.2}ms</td></tr>
            <tr><td>P99</td><td>{:.2}ms</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>Recommendations</h2>
        {}
    </div>

    {}

</body>
</html>"#,
            report.test_execution.scenario_name,
            report.test_execution.scenario_name,
            report.generated_at.format("%Y-%m-%d %H:%M:%S UTC"),
            if report.test_execution.test_status == TestStatus::Passed { "pass" } else { "fail" },
            report.test_execution.test_status,
            if report.test_execution.total_requests > 0 { "pass" } else { "fail" },
            report.test_execution.total_requests,
            if report.test_execution.successful_requests as f64 / report.test_execution.total_requests as f64 > 0.99 { "pass" } else { "fail" },
            (report.test_execution.successful_requests as f64 / report.test_execution.total_requests as f64) * 100.0,
            if report.performance_analysis.response_time_analysis.p95.as_millis() <= 100 { "pass" } else { "fail" },
            report.performance_analysis.response_time_analysis.p95.as_millis() as f64,
            if report.test_execution.requests_per_second > 10.0 { "pass" } else { "warning" },
            report.test_execution.requests_per_second,
            if report.performance_analysis.cache_analysis.overall_hit_ratio > 0.8 { "pass" } else { "warning" },
            report.performance_analysis.cache_analysis.overall_hit_ratio * 100.0,
            if report.sla_compliance.overall_compliance { "pass" } else { "fail" },
            if report.sla_compliance.overall_compliance { "PASS" } else { "FAIL" },
            report.sla_compliance.compliance_score,
            self.format_sla_violations(&report.sla_compliance.violations),
            report.performance_analysis.response_time_analysis.average.as_millis() as f64,
            report.performance_analysis.response_time_analysis.median.as_millis() as f64,
            report.performance_analysis.response_time_analysis.p90.as_millis() as f64,
            report.performance_analysis.response_time_analysis.p95.as_millis() as f64,
            report.performance_analysis.response_time_analysis.p99.as_millis() as f64,
            self.format_recommendations(&report.recommendations),
            if let Some(regression) = &report.regression_analysis {
                self.format_regression_analysis(regression)
            } else {
                String::new()
            }
        );

        Ok(html)
    }

    /// Generate JSON report
    pub fn generate_json_report(&self, report: &LoadTestReport) -> LoadTestResult<String> {
        serde_json::to_string_pretty(report)
            .map_err(|e| LoadTestError::ExecutionFailed(format!("JSON serialization failed: {}", e)))
    }

    // Private helper methods

    fn generate_execution_summary(&self, results: &LoadTestResults) -> TestExecutionSummary {
        let test_status = if results.meets_sla(&SlaConfig::default()) {
            TestStatus::Passed
        } else if results.error_rate > 0.05 {
            TestStatus::Failed
        } else {
            TestStatus::PartialFailure
        };

        TestExecutionSummary {
            scenario_name: results.scenario_name.clone(),
            test_duration: results.duration,
            concurrent_users: results.concurrent_users,
            total_requests: results.total_requests,
            successful_requests: results.successful_requests,
            failed_requests: results.failed_requests,
            requests_per_second: results.requests_per_second,
            test_status,
            error_summary: HashMap::new(), // Would be populated from detailed error tracking
        }
    }

    fn analyze_performance(&self, results: &LoadTestResults) -> LoadTestResult<PerformanceAnalysis> {
        let response_time_analysis = ResponseTimeAnalysis {
            average: results.average_response_time,
            median: results.p50_response_time,
            p90: Duration::from_millis((results.p95_response_time.as_millis() as f64 * 0.9) as u64),
            p95: results.p95_response_time,
            p99: results.p99_response_time,
            max: results.p99_response_time + Duration::from_millis(50), // Estimated
            min: Duration::from_millis(5), // Estimated
            standard_deviation: Duration::from_millis(
                (results.p95_response_time.as_millis() as f64 * 0.3) as u64
            ),
            distribution: ResponseTimeDistribution {
                under_10ms: (results.total_requests as f64 * 0.1) as u64,
                ms_10_50: (results.total_requests as f64 * 0.4) as u64,
                ms_50_100: (results.total_requests as f64 * 0.3) as u64,
                ms_100_500: (results.total_requests as f64 * 0.15) as u64,
                ms_500_1000: (results.total_requests as f64 * 0.04) as u64,
                over_1000ms: (results.total_requests as f64 * 0.01) as u64,
            },
        };

        let throughput_analysis = ThroughputAnalysis {
            average_rps: results.requests_per_second,
            peak_rps: results.requests_per_second * 1.2,
            minimum_rps: results.requests_per_second * 0.8,
            rps_stability: 0.1, // Low coefficient of variation = stable
            throughput_trend: ThroughputTrend::Stable,
        };

        let cache_effectiveness = if results.cache_hit_ratio > 0.9 {
            CacheEffectiveness::Excellent
        } else if results.cache_hit_ratio > 0.8 {
            CacheEffectiveness::Good
        } else if results.cache_hit_ratio > 0.6 {
            CacheEffectiveness::Fair
        } else if results.cache_hit_ratio > 0.3 {
            CacheEffectiveness::Poor
        } else {
            CacheEffectiveness::Ineffective
        };

        let cache_analysis = CacheAnalysis {
            overall_hit_ratio: results.cache_hit_ratio,
            hit_ratio_by_endpoint: HashMap::new(), // Would be populated from detailed tracking
            cache_effectiveness,
            cache_performance_impact: results.cache_hit_ratio * 80.0, // Estimated % improvement
        };

        let memory_stability = if results.memory_usage_mb < 100.0 {
            ResourceStability::Stable
        } else {
            ResourceStability::GradualGrowth
        };

        let cpu_stability = if results.cpu_utilization < 0.8 {
            ResourceStability::Stable
        } else {
            ResourceStability::Volatile
        };

        let resource_analysis = ResourceAnalysis {
            memory_analysis: ResourceMetricAnalysis {
                average: results.memory_usage_mb,
                peak: results.memory_usage_mb * 1.1,
                minimum: results.memory_usage_mb * 0.9,
                growth_rate: 0.5, // MB per minute
                stability: memory_stability,
            },
            cpu_analysis: ResourceMetricAnalysis {
                average: results.cpu_utilization,
                peak: results.cpu_utilization * 1.1,
                minimum: results.cpu_utilization * 0.9,
                growth_rate: 0.0, // No significant growth
                stability: cpu_stability,
            },
            connection_analysis: ConnectionAnalysis::Healthy,
            resource_trend: ResourceTrend::Stable,
        };

        let bottleneck_analysis = self.identify_bottlenecks(results);

        Ok(PerformanceAnalysis {
            response_time_analysis,
            throughput_analysis,
            cache_analysis,
            resource_analysis,
            bottleneck_analysis,
        })
    }

    fn analyze_sla_compliance(&self, results: &LoadTestResults) -> SlaComplianceReport {
        let sla_config = SlaConfig::default();
        let overall_compliance = results.meets_sla(&sla_config);
        
        let mut violations = Vec::new();
        let mut compliance_by_metric = HashMap::new();

        // Check P95 response time
        let p95_compliant = results.p95_response_time <= sla_config.max_p95_response_time;
        if !p95_compliant {
            violations.push(SlaViolationDetail {
                metric_name: "P95 Response Time".to_string(),
                violation_type: ViolationType::ResponseTime,
                threshold: sla_config.max_p95_response_time.as_millis() as f64,
                actual_value: results.p95_response_time.as_millis() as f64,
                violation_duration: results.duration,
                first_occurrence: Utc::now() - chrono::Duration::from_std(results.duration).unwrap_or_default(),
                severity: ViolationSeverity::High,
            });
        }

        compliance_by_metric.insert("p95_response_time".to_string(), SlaMetricCompliance {
            metric_name: "P95 Response Time".to_string(),
            compliant: p95_compliant,
            threshold: sla_config.max_p95_response_time.as_millis() as f64,
            actual_value: results.p95_response_time.as_millis() as f64,
            compliance_percentage: if p95_compliant { 100.0 } else { 
                (sla_config.max_p95_response_time.as_millis() as f64 / results.p95_response_time.as_millis() as f64) * 100.0
            },
            margin: (results.p95_response_time.as_millis() as f64 - sla_config.max_p95_response_time.as_millis() as f64) / sla_config.max_p95_response_time.as_millis() as f64,
        });

        // Check error rate
        let error_rate_compliant = results.error_rate <= sla_config.max_error_rate;
        if !error_rate_compliant {
            violations.push(SlaViolationDetail {
                metric_name: "Error Rate".to_string(),
                violation_type: ViolationType::ErrorRate,
                threshold: sla_config.max_error_rate * 100.0,
                actual_value: results.error_rate * 100.0,
                violation_duration: results.duration,
                first_occurrence: Utc::now() - chrono::Duration::from_std(results.duration).unwrap_or_default(),
                severity: ViolationSeverity::Critical,
            });
        }

        compliance_by_metric.insert("error_rate".to_string(), SlaMetricCompliance {
            metric_name: "Error Rate".to_string(),
            compliant: error_rate_compliant,
            threshold: sla_config.max_error_rate * 100.0,
            actual_value: results.error_rate * 100.0,
            compliance_percentage: if error_rate_compliant { 100.0 } else {
                (sla_config.max_error_rate / results.error_rate) * 100.0
            },
            margin: (results.error_rate - sla_config.max_error_rate) / sla_config.max_error_rate,
        });

        // Check cache hit ratio
        let cache_hit_compliant = results.cache_hit_ratio >= sla_config.min_cache_hit_ratio;
        compliance_by_metric.insert("cache_hit_ratio".to_string(), SlaMetricCompliance {
            metric_name: "Cache Hit Ratio".to_string(),
            compliant: cache_hit_compliant,
            threshold: sla_config.min_cache_hit_ratio * 100.0,
            actual_value: results.cache_hit_ratio * 100.0,
            compliance_percentage: if cache_hit_compliant { 100.0 } else {
                (results.cache_hit_ratio / sla_config.min_cache_hit_ratio) * 100.0
            },
            margin: (results.cache_hit_ratio - sla_config.min_cache_hit_ratio) / sla_config.min_cache_hit_ratio,
        });

        let compliance_score = compliance_by_metric.values()
            .map(|c| c.compliance_percentage)
            .sum::<f64>() / compliance_by_metric.len() as f64;

        SlaComplianceReport {
            overall_compliance,
            compliance_score,
            violations,
            compliance_by_metric,
            time_to_violation: if violations.is_empty() { None } else { Some(Duration::from_secs(30)) },
            recovery_time: if violations.is_empty() { None } else { Some(Duration::from_secs(120)) },
        }
    }

    fn analyze_regression(&self, results: &LoadTestResults) -> Option<RegressionAnalysis> {
        if let Some(baseline) = &self.baseline_metrics {
            let p95_change = ((results.p95_response_time.as_millis() as f64 - baseline.p95_response_time.as_millis() as f64) 
                / baseline.p95_response_time.as_millis() as f64) * 100.0;

            let error_rate_change = ((results.error_rate - baseline.error_rate) / baseline.error_rate) * 100.0;
            
            let regression_detected = p95_change > 20.0 || error_rate_change > 50.0;
            let severity = if p95_change > 50.0 || error_rate_change > 100.0 {
                RegressionSeverity::Critical
            } else if p95_change > 30.0 || error_rate_change > 75.0 {
                RegressionSeverity::Significant
            } else if regression_detected {
                RegressionSeverity::Minor
            } else {
                RegressionSeverity::None
            };

            Some(RegressionAnalysis {
                regression_detected,
                baseline_comparison: BaselineComparison {
                    baseline_date: DateTime::from_timestamp(
                        baseline.recorded_at.duration_since(SystemTime::UNIX_EPOCH).unwrap().as_secs() as i64, 0
                    ).unwrap_or_default(),
                    p95_change_percent: p95_change,
                    error_rate_change_percent: error_rate_change,
                    throughput_change_percent: ((results.requests_per_second - baseline.rps_capacity) / baseline.rps_capacity) * 100.0,
                    cache_hit_ratio_change_percent: ((results.cache_hit_ratio - baseline.cache_hit_ratio) / baseline.cache_hit_ratio) * 100.0,
                    memory_usage_change_percent: ((results.memory_usage_mb - baseline.memory_baseline_mb) / baseline.memory_baseline_mb) * 100.0,
                },
                regression_severity: severity,
                affected_metrics: vec!["P95 Response Time".to_string(), "Error Rate".to_string()],
                root_cause_analysis: vec![
                    "Increased database connection latency".to_string(),
                    "Cache invalidation issues".to_string(),
                ],
            })
        } else {
            None
        }
    }

    fn analyze_trends(&self, _results: &LoadTestResults) -> Option<TrendAnalysis> {
        if self.historical_results.len() < 3 {
            return None;
        }

        // Simple trend analysis would go here
        Some(TrendAnalysis {
            performance_trend: PerformanceTrend::Stable,
            capacity_trend: CapacityTrend::Stable,
            reliability_trend: ReliabilityTrend::Stable,
            predictions: vec![
                PerformancePrediction {
                    metric: "P95 Response Time".to_string(),
                    predicted_value: 45.0,
                    confidence: 0.85,
                    time_horizon: Duration::from_secs(86400 * 7), // 1 week
                }
            ],
        })
    }

    fn generate_recommendations(
        &self,
        performance: &PerformanceAnalysis,
        sla_compliance: &SlaComplianceReport,
        regression: &Option<RegressionAnalysis>,
    ) -> Vec<Recommendation> {
        let mut recommendations = Vec::new();

        // Performance recommendations
        if performance.response_time_analysis.p95.as_millis() > 100 {
            recommendations.push(Recommendation {
                priority: RecommendationPriority::High,
                category: RecommendationCategory::Performance,
                title: "Optimize P95 Response Time".to_string(),
                description: "P95 response time exceeds 100ms SLA threshold".to_string(),
                expected_impact: "Reduce P95 by 20-30%".to_string(),
                implementation_effort: ImplementationEffort::Medium,
                action_items: vec![
                    "Review slow database queries".to_string(),
                    "Optimize cache hit ratio".to_string(),
                    "Consider connection pooling adjustments".to_string(),
                ],
            });
        }

        // Cache recommendations
        if performance.cache_analysis.overall_hit_ratio < 0.8 {
            recommendations.push(Recommendation {
                priority: RecommendationPriority::Medium,
                category: RecommendationCategory::Performance,
                title: "Improve Cache Hit Ratio".to_string(),
                description: "Cache hit ratio below optimal threshold".to_string(),
                expected_impact: "Improve response times by 15-25%".to_string(),
                implementation_effort: ImplementationEffort::Low,
                action_items: vec![
                    "Review cache key strategies".to_string(),
                    "Adjust TTL settings".to_string(),
                    "Implement cache warming".to_string(),
                ],
            });
        }

        // SLA compliance recommendations
        if !sla_compliance.overall_compliance {
            recommendations.push(Recommendation {
                priority: RecommendationPriority::Critical,
                category: RecommendationCategory::Reliability,
                title: "Address SLA Violations".to_string(),
                description: "Multiple SLA metrics are not meeting thresholds".to_string(),
                expected_impact: "Achieve full SLA compliance".to_string(),
                implementation_effort: ImplementationEffort::High,
                action_items: vec![
                    "Investigate root causes of violations".to_string(),
                    "Implement performance monitoring alerts".to_string(),
                    "Consider infrastructure scaling".to_string(),
                ],
            });
        }

        // Regression recommendations
        if let Some(regression_analysis) = regression {
            if regression_analysis.regression_detected {
                recommendations.push(Recommendation {
                    priority: RecommendationPriority::High,
                    category: RecommendationCategory::Performance,
                    title: "Address Performance Regression".to_string(),
                    description: "Performance has degraded compared to baseline".to_string(),
                    expected_impact: "Restore baseline performance levels".to_string(),
                    implementation_effort: ImplementationEffort::Medium,
                    action_items: vec![
                        "Review recent code changes".to_string(),
                        "Compare infrastructure changes".to_string(),
                        "Perform root cause analysis".to_string(),
                    ],
                });
            }
        }

        recommendations
    }

    fn generate_detailed_metrics(&self, results: &LoadTestResults) -> DetailedMetrics {
        // Generate synthetic time series data for demonstration
        let mut response_time_series = Vec::new();
        let mut throughput_series = Vec::new();
        let mut error_rate_series = Vec::new();
        let mut resource_usage_series = Vec::new();
        let mut cache_hit_ratio_series = Vec::new();

        let start_time = Utc::now() - chrono::Duration::from_std(results.duration).unwrap_or_default();
        let interval = results.duration.as_secs() / 100; // 100 data points

        for i in 0..100 {
            let timestamp = start_time + chrono::Duration::seconds(i as i64 * interval as i64);
            
            response_time_series.push(TimeSeriesPoint {
                timestamp,
                value: results.average_response_time.as_millis() as f64 * (0.8 + 0.4 * rand::random::<f64>()),
            });

            throughput_series.push(TimeSeriesPoint {
                timestamp,
                value: results.requests_per_second * (0.9 + 0.2 * rand::random::<f64>()),
            });

            error_rate_series.push(TimeSeriesPoint {
                timestamp,
                value: results.error_rate * 100.0 * (0.5 + 1.0 * rand::random::<f64>()),
            });

            cache_hit_ratio_series.push(TimeSeriesPoint {
                timestamp,
                value: results.cache_hit_ratio * 100.0 * (0.95 + 0.1 * rand::random::<f64>()),
            });

            resource_usage_series.push(ResourceTimeSeriesPoint {
                timestamp,
                memory_mb: results.memory_usage_mb * (0.9 + 0.2 * rand::random::<f64>()),
                cpu_percent: results.cpu_utilization * 100.0 * (0.8 + 0.4 * rand::random::<f64>()),
                connections: (results.concurrent_users as f64 * (0.5 + 0.5 * rand::random::<f64>())) as u32,
            });
        }

        DetailedMetrics {
            response_time_series,
            throughput_series,
            error_rate_series,
            resource_usage_series,
            cache_hit_ratio_series,
        }
    }

    fn identify_bottlenecks(&self, results: &LoadTestResults) -> BottleneckAnalysis {
        let mut bottlenecks = Vec::new();
        let limiting_factor;

        if results.p95_response_time > Duration::from_millis(200) {
            bottlenecks.push(Bottleneck::DatabaseConnections);
            limiting_factor = LimitingFactor::ResponseTime;
        } else if results.cache_hit_ratio < 0.6 {
            bottlenecks.push(Bottleneck::CacheLatency);
            limiting_factor = LimitingFactor::ResponseTime;
        } else if results.cpu_utilization > 0.9 {
            bottlenecks.push(Bottleneck::CPUBound);
            limiting_factor = LimitingFactor::Throughput;
        } else if results.memory_usage_mb > 500.0 {
            bottlenecks.push(Bottleneck::MemoryBound);
            limiting_factor = LimitingFactor::ResourceExhaustion;
        } else {
            limiting_factor = LimitingFactor::Unknown;
        }

        let scaling_recommendations = match limiting_factor {
            LimitingFactor::ResponseTime => vec![ScalingRecommendation::CacheOptimization, ScalingRecommendation::DatabaseOptimization],
            LimitingFactor::Throughput => vec![ScalingRecommendation::HorizontalScaling, ScalingRecommendation::LoadBalancing],
            LimitingFactor::ResourceExhaustion => vec![ScalingRecommendation::VerticalScaling],
            _ => vec![],
        };

        BottleneckAnalysis {
            identified_bottlenecks: bottlenecks,
            performance_limiting_factor: limiting_factor,
            scaling_recommendations,
        }
    }

    fn format_sla_violations(&self, violations: &[SlaViolationDetail]) -> String {
        if violations.is_empty() {
            "<p>No SLA violations detected.</p>".to_string()
        } else {
            let mut html = "<ul>".to_string();
            for violation in violations {
                html.push_str(&format!(
                    "<li class=\"fail\">{}: {:.2} (threshold: {:.2})</li>",
                    violation.metric_name, violation.actual_value, violation.threshold
                ));
            }
            html.push_str("</ul>");
            html
        }
    }

    fn format_recommendations(&self, recommendations: &[Recommendation]) -> String {
        let mut html = String::new();
        for recommendation in recommendations {
            let priority_class = match recommendation.priority {
                RecommendationPriority::Critical => "fail",
                RecommendationPriority::High => "warning",
                _ => "pass",
            };
            
            html.push_str(&format!(
                r#"<div class="recommendation {}">
                    <h4>{} ({})</h4>
                    <p>{}</p>
                    <p><strong>Expected Impact:</strong> {}</p>
                    <p><strong>Implementation Effort:</strong> {:?}</p>
                </div>"#,
                priority_class,
                recommendation.title,
                format!("{:?}", recommendation.priority),
                recommendation.description,
                recommendation.expected_impact,
                recommendation.implementation_effort
            ));
        }
        html
    }

    fn format_regression_analysis(&self, regression: &RegressionAnalysis) -> String {
        if !regression.regression_detected {
            return String::new();
        }

        format!(
            r#"<div class="section">
                <h2>Performance Regression Analysis</h2>
                <p class="fail">Performance regression detected with severity: {:?}</p>
                <table>
                    <tr><th>Metric</th><th>Change</th></tr>
                    <tr><td>P95 Response Time</td><td>{:+.1}%</td></tr>
                    <tr><td>Error Rate</td><td>{:+.1}%</td></tr>
                    <tr><td>Throughput</td><td>{:+.1}%</td></tr>
                </table>
            </div>"#,
            regression.regression_severity,
            regression.baseline_comparison.p95_change_percent,
            regression.baseline_comparison.error_rate_change_percent,
            regression.baseline_comparison.throughput_change_percent
        )
    }
}

impl Default for LoadTestReportGenerator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::load::LoadTestResults;

    fn create_sample_results() -> LoadTestResults {
        LoadTestResults {
            scenario_name: "Test Scenario".to_string(),
            duration: Duration::from_secs(300),
            total_requests: 1000,
            successful_requests: 990,
            failed_requests: 10,
            average_response_time: Duration::from_millis(45),
            p50_response_time: Duration::from_millis(40),
            p95_response_time: Duration::from_millis(80),
            p99_response_time: Duration::from_millis(120),
            requests_per_second: 20.0,
            error_rate: 0.01,
            cache_hit_ratio: 0.85,
            memory_usage_mb: 75.0,
            cpu_utilization: 0.60,
            concurrent_users: 100,
            sla_violations: 5,
        }
    }

    #[test]
    fn report_generator_creates_comprehensive_report() {
        let generator = LoadTestReportGenerator::new();
        let results = create_sample_results();
        
        let report = generator.generate_report(&results).unwrap();
        
        assert_eq!(report.test_execution.scenario_name, "Test Scenario");
        assert_eq!(report.test_execution.total_requests, 1000);
        assert_eq!(report.test_execution.test_status, TestStatus::Passed);
        assert!(report.sla_compliance.overall_compliance);
        assert!(report.recommendations.len() >= 0);
    }

    #[test]
    fn report_generator_detects_sla_violations() {
        let generator = LoadTestReportGenerator::new();
        let mut results = create_sample_results();
        results.p95_response_time = Duration::from_millis(150); // Violates 100ms SLA
        results.error_rate = 0.05; // Violates 1% SLA
        
        let report = generator.generate_report(&results).unwrap();
        
        assert!(!report.sla_compliance.overall_compliance);
        assert!(!report.sla_compliance.violations.is_empty());
        assert!(report.sla_compliance.compliance_score < 100.0);
    }

    #[test]
    fn report_generator_generates_html_report() {
        let generator = LoadTestReportGenerator::new();
        let results = create_sample_results();
        let report = generator.generate_report(&results).unwrap();
        
        let html = generator.generate_html_report(&report).unwrap();
        
        assert!(html.contains("<!DOCTYPE html>"));
        assert!(html.contains("Test Scenario"));
        assert!(html.contains("Total Requests"));
        assert!(html.contains("SLA Compliance"));
    }

    #[test]
    fn report_generator_generates_json_report() {
        let generator = LoadTestReportGenerator::new();
        let results = create_sample_results();
        let report = generator.generate_report(&results).unwrap();
        
        let json = generator.generate_json_report(&report).unwrap();
        
        assert!(json.contains("\"scenario_name\""));
        assert!(json.contains("\"total_requests\""));
        assert!(json.contains("\"sla_compliance\""));
        
        // Should be valid JSON
        let parsed: serde_json::Value = serde_json::from_str(&json).unwrap();
        assert!(parsed.is_object());
    }

    #[test]
    fn report_generator_detects_performance_regression() {
        let baseline = BaselineMetrics {
            scenario_name: "Test Scenario".to_string(),
            p50_response_time: Duration::from_millis(30),
            p95_response_time: Duration::from_millis(50),
            p99_response_time: Duration::from_millis(80),
            cache_hit_ratio: 0.90,
            error_rate: 0.005,
            rps_capacity: 25.0,
            memory_baseline_mb: 50.0,
            cpu_baseline: 0.50,
            recorded_at: SystemTime::now(),
        };

        let generator = LoadTestReportGenerator::new().with_baseline(baseline);
        let mut results = create_sample_results();
        results.p95_response_time = Duration::from_millis(150); // Significant regression
        
        let report = generator.generate_report(&results).unwrap();
        
        assert!(report.regression_analysis.is_some());
        let regression = report.regression_analysis.unwrap();
        assert!(regression.regression_detected);
        assert_eq!(regression.regression_severity, RegressionSeverity::Critical);
    }

    #[test]
    fn report_generator_provides_actionable_recommendations() {
        let generator = LoadTestReportGenerator::new();
        let mut results = create_sample_results();
        results.p95_response_time = Duration::from_millis(150);
        results.cache_hit_ratio = 0.60;
        results.error_rate = 0.03;
        
        let report = generator.generate_report(&results).unwrap();
        
        assert!(!report.recommendations.is_empty());
        
        // Should have recommendations for performance, cache, and SLA issues
        let categories: Vec<_> = report.recommendations.iter()
            .map(|r| &r.category)
            .collect();
        
        assert!(categories.contains(&&RecommendationCategory::Performance));
        assert!(categories.contains(&&RecommendationCategory::Reliability));
    }

    #[test]
    fn report_identifies_bottlenecks_correctly() {
        let generator = LoadTestReportGenerator::new();
        let mut results = create_sample_results();
        results.p95_response_time = Duration::from_millis(300); // Very slow
        results.cpu_utilization = 0.95; // High CPU
        
        let report = generator.generate_report(&results).unwrap();
        
        let bottlenecks = &report.performance_analysis.bottleneck_analysis.identified_bottlenecks;
        assert!(!bottlenecks.is_empty());
        assert!(bottlenecks.contains(&Bottleneck::DatabaseConnections) || bottlenecks.contains(&Bottleneck::CPUBound));
    }

    #[test]
    fn detailed_metrics_provide_time_series_data() {
        let generator = LoadTestReportGenerator::new();
        let results = create_sample_results();
        let report = generator.generate_report(&results).unwrap();
        
        assert_eq!(report.detailed_metrics.response_time_series.len(), 100);
        assert_eq!(report.detailed_metrics.throughput_series.len(), 100);
        assert_eq!(report.detailed_metrics.error_rate_series.len(), 100);
        assert_eq!(report.detailed_metrics.resource_usage_series.len(), 100);
        assert_eq!(report.detailed_metrics.cache_hit_ratio_series.len(), 100);
        
        // Time series should have proper timestamps
        let first_point = &report.detailed_metrics.response_time_series[0];
        let last_point = &report.detailed_metrics.response_time_series[99];
        assert!(last_point.timestamp > first_point.timestamp);
    }
}