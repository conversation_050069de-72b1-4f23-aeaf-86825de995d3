//! Load Testing Monitoring Integration
//! 
//! Integrates load testing with the existing monitoring infrastructure
//! to provide real-time SLA validation, performance tracking, and alerting.

use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime, Instant};
use std::collections::HashMap;
use tokio::sync::mpsc;
use tracing::{debug, info, warn, error};
use serde::{Serialize, Deserialize};

use crate::load::{LoadTestConfig, LoadTestError, LoadTestResult, UserAction, UserSession};

// Simplified monitoring interfaces for load testing
// These would normally be imported from the application layer

/// Simplified metrics collector trait for load testing
pub trait MetricsCollector: Send + Sync {
    fn record_query_execution(&self, metrics: QueryMetrics);
    fn get_performance_snapshot(&self) -> PerformanceSnapshot;
    fn clear_metrics(&self);
}

/// Simplified SLA monitor trait for load testing
pub trait SlaMonitor: Send + Sync {
    async fn configure(&self, config: MonitoringSlaConfig) -> Result<(), String>;
    async fn generate_report(&self) -> Result<SlaReport, String>;
}

/// Simplified performance tracer trait for load testing
pub trait PerformanceTracer: Send + Sync {
    async fn start_trace(&self, context: TraceContext) -> Result<(), String>;
    async fn end_trace(&self) -> Result<(), String>;
    async fn record_span(&self, name: String, span_type: SpanType, duration: Duration, metadata: HashMap<String, String>) -> Result<(), String>;
}

/// Simplified alert manager trait for load testing
pub trait AlertManager: Send + Sync {
    async fn configure_alert(&self, alert: Alert) -> Result<(), String>;
    async fn send_alert(&self, alert: Alert) -> Result<(), String>;
}

/// Query metrics for load testing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryMetrics {
    pub query_name: String,
    pub execution_time: Duration,
    pub cache_hit: bool,
    pub cache_response_time: Option<Duration>,
    pub database_response_time: Option<Duration>,
    pub result_count: usize,
    pub error_type: Option<String>,
    pub user_context: UserContext,
    pub timestamp: SystemTime,
}

/// User context for load testing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserContext {
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub request_id: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

/// Performance snapshot for load testing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSnapshot {
    pub p50_latency: Duration,
    pub p95_latency: Duration,
    pub p99_latency: Duration,
    pub cache_hit_ratio: f64,
    pub error_rate: f64,
    pub queries_per_second: f64,
    pub sla_violations: u64,
    pub timestamp: SystemTime,
}

/// SLA configuration for monitoring
#[derive(Debug, Clone)]
pub struct MonitoringSlaConfig {
    pub max_response_time: Duration,
    pub max_error_rate: f64,
    pub min_cache_hit_ratio: f64,
    pub max_memory_usage: f64,
    pub max_cpu_usage: f64,
}

/// SLA violation for monitoring
#[derive(Debug, Clone)]
pub struct SlaViolation {
    pub metric_name: String,
    pub threshold: f64,
    pub actual_value: f64,
    pub timestamp: SystemTime,
}

/// SLA report for monitoring
#[derive(Debug, Clone)]
pub struct SlaReport {
    pub status: SlaStatus,
    pub total_violations: u64,
    pub violations: Vec<SlaViolation>,
}

/// SLA status
#[derive(Debug, Clone)]
pub enum SlaStatus {
    Compliant,
    NonCompliant,
    Warning,
}

/// Trace context for performance tracing
#[derive(Debug, Clone)]
pub struct TraceContext {
    pub trace_name: String,
    pub concurrent_users: u32,
}

impl TraceContext {
    pub fn new(trace_name: String, concurrent_users: u32) -> Self {
        Self { trace_name, concurrent_users }
    }
}

/// Span type for performance tracing
#[derive(Debug, Clone)]
pub enum SpanType {
    Query,
    Authentication,
    Authorization,
    Cache,
    Database,
}

/// Alert levels
#[derive(Debug, Clone)]
pub enum AlertLevel {
    Info,
    Warning,
    Critical,
}

/// Alert structure
#[derive(Debug, Clone)]
pub struct Alert {
    pub name: String,
    pub level: AlertLevel,
    pub message: String,
    pub threshold: f64,
    pub current_value: f64,
    pub timestamp: SystemTime,
}

/// Simple in-memory implementation for load testing
pub struct SimpleMetricsCollector {
    metrics: Arc<Mutex<Vec<QueryMetrics>>>,
}

impl SimpleMetricsCollector {
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(Mutex::new(Vec::new())),
        }
    }
}

impl MetricsCollector for SimpleMetricsCollector {
    fn record_query_execution(&self, metrics: QueryMetrics) {
        let mut metrics_guard = self.metrics.lock().unwrap();
        metrics_guard.push(metrics);
    }
    
    fn get_performance_snapshot(&self) -> PerformanceSnapshot {
        let metrics_guard = self.metrics.lock().unwrap();
        
        if metrics_guard.is_empty() {
            return PerformanceSnapshot {
                p50_latency: Duration::ZERO,
                p95_latency: Duration::ZERO,
                p99_latency: Duration::ZERO,
                cache_hit_ratio: 0.0,
                error_rate: 0.0,
                queries_per_second: 0.0,
                sla_violations: 0,
                timestamp: SystemTime::now(),
            };
        }
        
        let mut response_times: Vec<_> = metrics_guard.iter().map(|m| m.execution_time).collect();
        response_times.sort();
        
        let p50_latency = response_times[response_times.len() * 50 / 100];
        let p95_latency = response_times[response_times.len() * 95 / 100];
        let p99_latency = response_times[response_times.len() * 99 / 100];
        
        let cache_hits = metrics_guard.iter().filter(|m| m.cache_hit).count();
        let cache_hit_ratio = cache_hits as f64 / metrics_guard.len() as f64;
        
        let errors = metrics_guard.iter().filter(|m| m.error_type.is_some()).count();
        let error_rate = errors as f64 / metrics_guard.len() as f64;
        
        let earliest = metrics_guard.iter().map(|m| m.timestamp).min().unwrap_or(SystemTime::now());
        let latest = metrics_guard.iter().map(|m| m.timestamp).max().unwrap_or(SystemTime::now());
        let duration = latest.duration_since(earliest).unwrap_or(Duration::from_secs(1));
        
        let queries_per_second = metrics_guard.len() as f64 / duration.as_secs_f64();
        
        PerformanceSnapshot {
            p50_latency,
            p95_latency,
            p99_latency,
            cache_hit_ratio,
            error_rate,
            queries_per_second,
            sla_violations: 0,
            timestamp: SystemTime::now(),
        }
    }
    
    fn clear_metrics(&self) {
        let mut metrics_guard = self.metrics.lock().unwrap();
        metrics_guard.clear();
    }
}

/// Simple SLA monitor implementation
pub struct SimpleSlaMonitor;

impl SimpleSlaMonitor {
    pub fn new() -> Self {
        Self
    }
}

impl SlaMonitor for SimpleSlaMonitor {
    async fn configure(&self, _config: MonitoringSlaConfig) -> Result<(), String> {
        Ok(())
    }
    
    async fn generate_report(&self) -> Result<SlaReport, String> {
        Ok(SlaReport {
            status: SlaStatus::Compliant,
            total_violations: 0,
            violations: Vec::new(),
        })
    }
}

/// Simple performance tracer implementation
pub struct SimplePerformanceTracer;

impl SimplePerformanceTracer {
    pub fn new() -> Self {
        Self
    }
}

impl PerformanceTracer for SimplePerformanceTracer {
    async fn start_trace(&self, _context: TraceContext) -> Result<(), String> {
        Ok(())
    }
    
    async fn end_trace(&self) -> Result<(), String> {
        Ok(())
    }
    
    async fn record_span(&self, _name: String, _span_type: SpanType, _duration: Duration, _metadata: HashMap<String, String>) -> Result<(), String> {
        Ok(())
    }
}

/// Simple alert manager implementation
pub struct SimpleAlertManager;

impl SimpleAlertManager {
    pub fn new() -> Self {
        Self
    }
}

impl AlertManager for SimpleAlertManager {
    async fn configure_alert(&self, _alert: Alert) -> Result<(), String> {
        Ok(())
    }
    
    async fn send_alert(&self, _alert: Alert) -> Result<(), String> {
        Ok(())
    }
}

/// Load test monitoring integration
pub struct LoadTestMonitor {
    metrics_collector: Arc<dyn MetricsCollector>,
    sla_monitor: Arc<dyn SlaMonitor>,
    performance_tracer: Arc<dyn PerformanceTracer>,
    alert_manager: Arc<dyn AlertManager>,
    real_time_stats: Arc<Mutex<LoadTestRealTimeStats>>,
    violation_channel: Option<mpsc::UnboundedSender<SlaViolation>>,
    baseline_metrics: Option<BaselineMetrics>,
}

/// Real-time statistics during load testing
#[derive(Debug, Default)]
pub struct LoadTestRealTimeStats {
    pub requests_completed: u64,
    pub requests_failed: u64,
    pub current_rps: f64,
    pub average_response_time: Duration,
    pub p95_response_time: Duration,
    pub cache_hit_ratio: f64,
    pub sla_violations: u64,
    pub memory_usage_mb: f64,
    pub cpu_utilization: f64,
    pub last_updated: SystemTime,
    pub response_times: Vec<Duration>,
    pub error_counts: HashMap<String, u64>,
}

/// Baseline metrics for performance regression detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaselineMetrics {
    pub scenario_name: String,
    pub p50_response_time: Duration,
    pub p95_response_time: Duration,
    pub p99_response_time: Duration,
    pub cache_hit_ratio: f64,
    pub error_rate: f64,
    pub rps_capacity: f64,
    pub memory_baseline_mb: f64,
    pub cpu_baseline: f64,
    pub recorded_at: SystemTime,
}

impl LoadTestMonitor {
    pub fn new() -> Self {
        let metrics_collector = Arc::new(SimpleMetricsCollector::new());
        let sla_monitor = Arc::new(SimpleSlaMonitor::new());
        let performance_tracer = Arc::new(SimplePerformanceTracer::new());
        let alert_manager = Arc::new(SimpleAlertManager::new());

        Self {
            metrics_collector,
            sla_monitor,
            performance_tracer,
            alert_manager,
            real_time_stats: Arc::new(Mutex::new(LoadTestRealTimeStats::default())),
            violation_channel: None,
            baseline_metrics: None,
        }
    }

    /// Start monitoring for a load test
    pub async fn start_monitoring(&self, config: &LoadTestConfig) -> LoadTestResult<()> {
        info!("Starting load test monitoring for scenario: {}", config.scenario_name);

        // Configure SLA monitoring with load test thresholds
        let monitoring_sla_config = self.convert_sla_config(&config.performance_thresholds);
        self.sla_monitor.configure(monitoring_sla_config).await
            .map_err(|e| LoadTestError::SetupFailed(format!("SLA monitor configuration failed: {}", e)))?;

        // Initialize performance tracer
        let trace_context = TraceContext::new(
            format!("load_test_{}", config.scenario_name),
            config.concurrent_users,
        );
        self.performance_tracer.start_trace(trace_context).await
            .map_err(|e| LoadTestError::SetupFailed(format!("Performance tracer setup failed: {}", e)))?;

        // Set up alert thresholds
        self.configure_load_test_alerts(config).await?;

        // Initialize real-time stats
        {
            let mut stats = self.real_time_stats.lock().unwrap();
            *stats = LoadTestRealTimeStats::default();
            stats.last_updated = SystemTime::now();
        }

        // Load baseline metrics if available
        self.load_baseline_metrics(&config.scenario_name).await;

        info!("Load test monitoring started successfully");
        Ok(())
    }

    /// Stop monitoring and finalize metrics
    pub async fn stop_monitoring(&self) -> LoadTestResult<()> {
        info!("Stopping load test monitoring");

        // Finalize performance traces
        self.performance_tracer.end_trace().await
            .map_err(|e| LoadTestError::ExecutionFailed(format!("Failed to finalize traces: {}", e)))?;

        // Generate final SLA report
        let sla_report = self.sla_monitor.generate_report().await
            .map_err(|e| LoadTestError::ExecutionFailed(format!("SLA report generation failed: {}", e)))?;

        info!("SLA Report: Violations: {}, Status: {:?}", 
              sla_report.total_violations, sla_report.status);

        Ok(())
    }

    /// Record a successful request
    pub async fn record_request_success(
        &self,
        action: &UserAction,
        response_time: Duration,
        status: reqwest::StatusCode,
    ) {
        // Record in metrics collector
        let query_metrics = QueryMetrics {
            query_name: format!("{:?}", action),
            execution_time: response_time,
            cache_hit: self.determine_cache_hit_from_status(status),
            cache_response_time: if self.determine_cache_hit_from_status(status) {
                Some(Duration::from_millis(1))
            } else {
                None
            },
            database_response_time: if !self.determine_cache_hit_from_status(status) {
                Some(response_time)
            } else {
                None
            },
            result_count: 1,
            error_type: None,
            user_context: UserContext {
                user_id: Some("load_test_user".to_string()),
                session_id: Some("load_test_session".to_string()),
                request_id: uuid::Uuid::new_v4().to_string(),
                ip_address: Some("127.0.0.1".to_string()),
                user_agent: Some("LoadTester/1.0".to_string()),
            },
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_query_execution(query_metrics);

        // Update real-time stats
        self.update_real_time_stats(response_time, true, false).await;

        // Check for SLA violations
        self.check_sla_compliance(action, response_time, None).await;

        // Record in performance tracer
        let span_type = match action {
            UserAction::Login { .. } => SpanType::Authentication,
            UserAction::CheckPermissions { .. } => SpanType::Authorization,
            _ => SpanType::Query,
        };

        let _ = self.performance_tracer.record_span(
            format!("{:?}", action),
            span_type,
            response_time,
            HashMap::new(),
        ).await;

        debug!("Recorded successful request: {:?} in {:?}", action, response_time);
    }

    /// Record a failed request
    pub async fn record_request_failure(
        &self,
        action: &UserAction,
        response_time: Duration,
        error: &reqwest::Error,
    ) {
        // Record in metrics collector
        let query_metrics = QueryMetrics {
            query_name: format!("{:?}", action),
            execution_time: response_time,
            cache_hit: false,
            cache_response_time: None,
            database_response_time: Some(response_time),
            result_count: 0,
            error_type: Some(error.to_string()),
            user_context: UserContext {
                user_id: Some("load_test_user".to_string()),
                session_id: Some("load_test_session".to_string()),
                request_id: uuid::Uuid::new_v4().to_string(),
                ip_address: Some("127.0.0.1".to_string()),
                user_agent: Some("LoadTester/1.0".to_string()),
            },
            timestamp: SystemTime::now(),
        };

        self.metrics_collector.record_query_execution(query_metrics);

        // Update real-time stats
        self.update_real_time_stats(response_time, false, true).await;

        // Check for SLA violations (failures always violate SLA)
        self.check_sla_compliance(action, response_time, Some(error.to_string())).await;

        warn!("Recorded failed request: {:?} failed in {:?} with error: {}", 
              action, response_time, error);
    }

    /// Get current resource usage
    pub async fn get_resource_usage(&self) -> (f64, f64) {
        let stats = self.real_time_stats.lock().unwrap();
        (stats.memory_usage_mb, stats.cpu_utilization)
    }

    /// Get real-time performance snapshot
    pub async fn get_real_time_snapshot(&self) -> LoadTestRealTimeStats {
        let stats = self.real_time_stats.lock().unwrap();
        LoadTestRealTimeStats {
            requests_completed: stats.requests_completed,
            requests_failed: stats.requests_failed,
            current_rps: stats.current_rps,
            average_response_time: stats.average_response_time,
            p95_response_time: stats.p95_response_time,
            cache_hit_ratio: stats.cache_hit_ratio,
            sla_violations: stats.sla_violations,
            memory_usage_mb: stats.memory_usage_mb,
            cpu_utilization: stats.cpu_utilization,
            last_updated: stats.last_updated,
            response_times: stats.response_times.clone(),
            error_counts: stats.error_counts.clone(),
        }
    }

    /// Check for performance regression against baseline
    pub async fn check_performance_regression(&self) -> Option<PerformanceRegression> {
        if let Some(baseline) = &self.baseline_metrics {
            let current_snapshot = self.metrics_collector.get_performance_snapshot();
            
            let p95_regression = if current_snapshot.p95_latency > baseline.p95_response_time {
                let regression_percent = ((current_snapshot.p95_latency.as_millis() as f64 - baseline.p95_response_time.as_millis() as f64) 
                    / baseline.p95_response_time.as_millis() as f64) * 100.0;
                if regression_percent > 20.0 { // 20% regression threshold
                    Some(regression_percent)
                } else {
                    None
                }
            } else {
                None
            };

            let error_rate_regression = if current_snapshot.error_rate > baseline.error_rate {
                let regression = current_snapshot.error_rate - baseline.error_rate;
                if regression > 0.01 { // 1% error rate regression threshold
                    Some(regression * 100.0)
                } else {
                    None
                }
            } else {
                None
            };

            if p95_regression.is_some() || error_rate_regression.is_some() {
                return Some(PerformanceRegression {
                    baseline: baseline.clone(),
                    current_p95: current_snapshot.p95_latency,
                    current_error_rate: current_snapshot.error_rate,
                    p95_regression_percent: p95_regression,
                    error_rate_regression_percent: error_rate_regression,
                });
            }
        }
        None
    }

    /// Save current metrics as baseline for future comparisons
    pub async fn save_baseline_metrics(&self, scenario_name: &str) -> LoadTestResult<()> {
        let snapshot = self.metrics_collector.get_performance_snapshot();
        let stats = self.real_time_stats.lock().unwrap();
        
        let baseline = BaselineMetrics {
            scenario_name: scenario_name.to_string(),
            p50_response_time: snapshot.p50_latency,
            p95_response_time: snapshot.p95_latency,
            p99_response_time: snapshot.p99_latency,
            cache_hit_ratio: snapshot.cache_hit_ratio,
            error_rate: snapshot.error_rate,
            rps_capacity: snapshot.queries_per_second,
            memory_baseline_mb: stats.memory_usage_mb,
            cpu_baseline: stats.cpu_utilization,
            recorded_at: SystemTime::now(),
        };

        // In a real implementation, this would save to persistent storage
        info!("Baseline metrics saved for scenario: {} - P95: {:?}, Error Rate: {:.3}%", 
              scenario_name, baseline.p95_response_time, baseline.error_rate * 100.0);
        
        Ok(())
    }

    // Private helper methods

    fn convert_sla_config(&self, load_test_sla: &crate::load::SlaConfig) -> MonitoringSlaConfig {
        MonitoringSlaConfig {
            max_response_time: load_test_sla.max_p95_response_time,
            max_error_rate: load_test_sla.max_error_rate,
            min_cache_hit_ratio: load_test_sla.min_cache_hit_ratio,
            max_memory_usage: load_test_sla.max_memory_growth_mb,
            max_cpu_usage: load_test_sla.max_cpu_utilization,
        }
    }

    async fn configure_load_test_alerts(&self, config: &LoadTestConfig) -> LoadTestResult<()> {
        // Configure high-priority alerts for SLA violations
        let sla_alert = Alert {
            name: "load_test_sla_violation".to_string(),
            level: AlertLevel::Critical,
            message: format!("SLA violation detected in load test: {}", config.scenario_name),
            threshold: config.performance_thresholds.max_p95_response_time.as_millis() as f64,
            current_value: 0.0,
            timestamp: SystemTime::now(),
        };

        self.alert_manager.configure_alert(sla_alert).await
            .map_err(|e| LoadTestError::SetupFailed(format!("Alert configuration failed: {}", e)))?;

        Ok(())
    }

    async fn load_baseline_metrics(&self, scenario_name: &str) {
        // In a real implementation, this would load from persistent storage
        info!("Loading baseline metrics for scenario: {}", scenario_name);
        // For now, we'll skip loading baseline metrics
    }

    fn determine_cache_hit_from_status(&self, status: reqwest::StatusCode) -> bool {
        // Simple heuristic: fast responses (< 50ms) are likely cache hits
        // In a real implementation, this would check response headers
        status.is_success()
    }

    async fn update_real_time_stats(&self, response_time: Duration, success: bool, failure: bool) {
        let mut stats = self.real_time_stats.lock().unwrap();
        
        if success {
            stats.requests_completed += 1;
        }
        if failure {
            stats.requests_failed += 1;
        }

        stats.response_times.push(response_time);
        
        // Keep only recent response times for moving averages
        if stats.response_times.len() > 1000 {
            stats.response_times.drain(0..500);
        }

        // Calculate moving averages
        if !stats.response_times.is_empty() {
            let total_ms: u64 = stats.response_times.iter().map(|d| d.as_millis() as u64).sum();
            stats.average_response_time = Duration::from_millis(total_ms / stats.response_times.len() as u64);

            // Calculate P95
            let mut sorted_times = stats.response_times.clone();
            sorted_times.sort();
            let p95_index = (sorted_times.len() as f64 * 0.95) as usize;
            if p95_index < sorted_times.len() {
                stats.p95_response_time = sorted_times[p95_index];
            }
        }

        // Calculate RPS (requests per second over last 10 seconds)
        let now = SystemTime::now();
        let elapsed = now.duration_since(stats.last_updated).unwrap_or(Duration::from_secs(1));
        if elapsed >= Duration::from_secs(1) {
            let total_requests = stats.requests_completed + stats.requests_failed;
            stats.current_rps = total_requests as f64 / elapsed.as_secs_f64();
            stats.last_updated = now;
        }

        // Simulate resource usage (in a real implementation, this would be measured)
        stats.memory_usage_mb = 50.0 + (stats.requests_completed as f64 * 0.01);
        stats.cpu_utilization = 0.3 + (stats.current_rps / 1000.0) * 0.5;
        
        // Simulate cache hit ratio based on request types
        stats.cache_hit_ratio = 0.80 + (rand::random::<f64>() * 0.1 - 0.05); // 80% ± 5%
    }

    async fn check_sla_compliance(&self, action: &UserAction, response_time: Duration, error: Option<String>) {
        // Check response time SLA
        if response_time > Duration::from_millis(100) {
            let mut stats = self.real_time_stats.lock().unwrap();
            stats.sla_violations += 1;
            drop(stats);

            warn!("SLA violation: {:?} took {:?} (> 100ms)", action, response_time);

            // Send alert
            let alert = Alert {
                name: "response_time_sla_violation".to_string(),
                level: AlertLevel::Warning,
                message: format!("Response time SLA violation: {:?} took {:?}", action, response_time),
                threshold: 100.0,
                current_value: response_time.as_millis() as f64,
                timestamp: SystemTime::now(),
            };

            let _ = self.alert_manager.send_alert(alert).await;
        }

        // Check error SLA
        if error.is_some() {
            let alert = Alert {
                name: "error_rate_sla_violation".to_string(),
                level: AlertLevel::Critical,
                message: format!("Request failed: {:?} - {}", action, error.unwrap_or_default()),
                threshold: 1.0,
                current_value: 100.0, // 100% error for this request
                timestamp: SystemTime::now(),
            };

            let _ = self.alert_manager.send_alert(alert).await;
        }
    }
}

/// Performance regression detection result
#[derive(Debug, Clone)]
pub struct PerformanceRegression {
    pub baseline: BaselineMetrics,
    pub current_p95: Duration,
    pub current_error_rate: f64,
    pub p95_regression_percent: Option<f64>,
    pub error_rate_regression_percent: Option<f64>,
}

/// Load test monitoring configuration
#[derive(Debug, Clone)]
pub struct LoadTestMonitoringConfig {
    pub enable_real_time_monitoring: bool,
    pub enable_baseline_comparison: bool,
    pub enable_alerting: bool,
    pub metrics_collection_interval: Duration,
    pub sla_check_interval: Duration,
    pub resource_monitoring_interval: Duration,
}

impl Default for LoadTestMonitoringConfig {
    fn default() -> Self {
        Self {
            enable_real_time_monitoring: true,
            enable_baseline_comparison: true,
            enable_alerting: true,
            metrics_collection_interval: Duration::from_secs(1),
            sla_check_interval: Duration::from_secs(5),
            resource_monitoring_interval: Duration::from_secs(2),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::load::{SlaConfig, UserAction};

    #[tokio::test]
    async fn load_test_monitor_can_be_created() {
        let monitor = LoadTestMonitor::new();
        
        // Should create without panicking
        let stats = monitor.get_real_time_snapshot().await;
        assert_eq!(stats.requests_completed, 0);
        assert_eq!(stats.requests_failed, 0);
    }

    #[tokio::test]
    async fn monitor_records_successful_requests() {
        let monitor = LoadTestMonitor::new();
        let action = UserAction::GetProfile;
        let response_time = Duration::from_millis(50);
        let status = reqwest::StatusCode::OK;

        monitor.record_request_success(&action, response_time, status).await;

        let stats = monitor.get_real_time_snapshot().await;
        assert_eq!(stats.requests_completed, 1);
        assert_eq!(stats.requests_failed, 0);
        assert!(!stats.response_times.is_empty());
    }

    #[tokio::test]
    async fn monitor_records_failed_requests() {
        let monitor = LoadTestMonitor::new();
        let action = UserAction::GetProfile;
        let response_time = Duration::from_millis(1000);
        let error = reqwest::Error::from(reqwest::ErrorKind::Request);

        monitor.record_request_failure(&action, response_time, &error).await;

        let stats = monitor.get_real_time_snapshot().await;
        assert_eq!(stats.requests_completed, 0);
        assert_eq!(stats.requests_failed, 1);
        assert!(!stats.response_times.is_empty());
    }

    #[tokio::test]
    async fn monitor_calculates_moving_averages() {
        let monitor = LoadTestMonitor::new();
        let action = UserAction::GetProfile;
        let status = reqwest::StatusCode::OK;

        // Record multiple requests with different response times
        for i in 1..=10 {
            let response_time = Duration::from_millis(i * 10);
            monitor.record_request_success(&action, response_time, status).await;
        }

        let stats = monitor.get_real_time_snapshot().await;
        assert_eq!(stats.requests_completed, 10);
        assert!(stats.average_response_time > Duration::ZERO);
        assert!(stats.p95_response_time > Duration::ZERO);
    }

    #[tokio::test]
    async fn monitor_detects_sla_violations() {
        let monitor = LoadTestMonitor::new();
        let action = UserAction::GetProfile;
        let status = reqwest::StatusCode::OK;

        // Record a slow request that violates SLA
        let slow_response_time = Duration::from_millis(200); // > 100ms SLA
        monitor.record_request_success(&action, slow_response_time, status).await;

        let stats = monitor.get_real_time_snapshot().await;
        assert!(stats.sla_violations > 0);
    }

    #[tokio::test]
    async fn monitor_provides_resource_usage() {
        let monitor = LoadTestMonitor::new();
        let action = UserAction::GetProfile;
        let status = reqwest::StatusCode::OK;

        // Record some requests to simulate resource usage
        for _ in 0..5 {
            monitor.record_request_success(&action, Duration::from_millis(50), status).await;
        }

        let (memory_mb, cpu_utilization) = monitor.get_resource_usage().await;
        assert!(memory_mb > 0.0);
        assert!(cpu_utilization >= 0.0 && cpu_utilization <= 1.0);
    }

    #[test]
    fn monitoring_config_has_reasonable_defaults() {
        let config = LoadTestMonitoringConfig::default();
        
        assert!(config.enable_real_time_monitoring);
        assert!(config.enable_baseline_comparison);
        assert!(config.enable_alerting);
        assert_eq!(config.metrics_collection_interval, Duration::from_secs(1));
        assert_eq!(config.sla_check_interval, Duration::from_secs(5));
        assert_eq!(config.resource_monitoring_interval, Duration::from_secs(2));
    }

    #[tokio::test]
    async fn baseline_metrics_can_be_saved() {
        let monitor = LoadTestMonitor::new();
        let scenario_name = "test_scenario";

        // Record some requests to generate metrics
        let action = UserAction::GetProfile;
        let status = reqwest::StatusCode::OK;
        for _ in 0..10 {
            monitor.record_request_success(&action, Duration::from_millis(50), status).await;
        }

        // Should not panic when saving baseline
        let result = monitor.save_baseline_metrics(scenario_name).await;
        assert!(result.is_ok());
    }

    #[test]
    fn sla_config_conversion_preserves_values() {
        let monitor = LoadTestMonitor::new();
        let load_test_sla = SlaConfig {
            max_p95_response_time: Duration::from_millis(100),
            max_error_rate: 0.01,
            min_cache_hit_ratio: 0.80,
            max_memory_growth_mb: 50.0,
            max_cpu_utilization: 0.70,
        };

        let monitoring_sla = monitor.convert_sla_config(&load_test_sla);
        
        assert_eq!(monitoring_sla.max_response_time, load_test_sla.max_p95_response_time);
        assert_eq!(monitoring_sla.max_error_rate, load_test_sla.max_error_rate);
        assert_eq!(monitoring_sla.min_cache_hit_ratio, load_test_sla.min_cache_hit_ratio);
        assert_eq!(monitoring_sla.max_memory_usage, load_test_sla.max_memory_growth_mb);
        assert_eq!(monitoring_sla.max_cpu_usage, load_test_sla.max_cpu_utilization);
    }
}