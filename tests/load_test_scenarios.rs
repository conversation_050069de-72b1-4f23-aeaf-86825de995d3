//! Load Test Scenario Execution Tests
//! 
//! Tests that validate all 5 load testing scenarios execute correctly,
//! proper SLA validation, and error handling under various conditions.

use std::time::Duration;
use tokio::time::timeout;

// Import load testing infrastructure
use auth_service::tests::load::{
    LoadTestScenarios, SpecializedScenarios, ScenarioValidator,
    LoadTestRunner, LoadTestConfig, SlaConfig, LoadTestResults,
    LoadTestError, UserTypeDistribution,
    LoadTestReportGenerator, LoadTestReport,
    TestDataGenerator, TestDataConfig, LoadTestDataSetup,
};

/// Integration tests for load test scenarios
#[cfg(test)]
mod scenario_execution_tests {
    use super::*;

    #[tokio::test]
    async fn normal_load_scenario_executes_successfully() {
        let config = LoadTestScenarios::normal_load();
        
        // Validate scenario configuration
        assert!(ScenarioValidator::validate_config(&config).is_ok());
        
        // Verify scenario parameters
        assert_eq!(config.concurrent_users, 100);
        assert_eq!(config.test_duration, Duration::from_secs(600));
        assert_eq!(config.performance_thresholds.max_p95_response_time, Duration::from_millis(100));
        assert_eq!(config.performance_thresholds.max_error_rate, 0.01);
        assert_eq!(config.performance_thresholds.min_cache_hit_ratio, 0.80);
        
        // Verify user distribution
        let distribution = &config.user_type_distribution;
        let total = distribution.regular_users + distribution.admin_users + distribution.service_accounts;
        assert!((total - 1.0).abs() < 0.01);
        assert!(distribution.regular_users > distribution.admin_users);
    }

    #[tokio::test]
    async fn peak_load_scenario_has_higher_concurrency() {
        let config = LoadTestScenarios::peak_load();
        let normal_config = LoadTestScenarios::normal_load();
        
        // Peak load should have more concurrent users
        assert!(config.concurrent_users > normal_config.concurrent_users);
        assert_eq!(config.concurrent_users, 500);
        
        // Should have longer test duration
        assert!(config.test_duration > normal_config.test_duration);
        assert_eq!(config.test_duration, Duration::from_secs(900));
        
        // Should have longer ramp-up time for gradual scaling
        assert!(config.ramp_up_time > normal_config.ramp_up_time);
        
        // Should allow higher RPS
        assert!(config.max_requests_per_second.unwrap_or(0) > normal_config.max_requests_per_second.unwrap_or(0));
    }

    #[tokio::test]
    async fn stress_test_scenario_has_most_lenient_thresholds() {
        let stress_config = LoadTestScenarios::stress_test();
        let normal_config = LoadTestScenarios::normal_load();
        
        // Stress test should have more lenient SLA thresholds
        assert!(stress_config.performance_thresholds.max_p95_response_time > normal_config.performance_thresholds.max_p95_response_time);
        assert!(stress_config.performance_thresholds.max_error_rate > normal_config.performance_thresholds.max_error_rate);
        assert!(stress_config.performance_thresholds.min_cache_hit_ratio < normal_config.performance_thresholds.min_cache_hit_ratio);
        
        // Should have highest concurrency
        assert!(stress_config.concurrent_users > normal_config.concurrent_users);
        assert_eq!(stress_config.concurrent_users, 1000);
        
        // Should have no RPS limit to find breaking point
        assert!(stress_config.max_requests_per_second.is_none());
    }

    #[tokio::test]
    async fn endurance_test_scenario_has_longest_duration() {
        let endurance_config = LoadTestScenarios::endurance_test();
        let all_scenarios = LoadTestScenarios::all_scenarios();
        
        // Endurance test should have the longest duration
        for scenario in all_scenarios {
            if scenario.scenario_name != endurance_config.scenario_name {
                assert!(endurance_config.test_duration >= scenario.test_duration);
            }
        }
        
        assert_eq!(endurance_config.test_duration, Duration::from_secs(3600)); // 1 hour
        
        // Should have strict error rate for long-term stability
        assert!(endurance_config.performance_thresholds.max_error_rate <= 0.005);
        
        // Should have strict memory growth limits
        assert!(endurance_config.performance_thresholds.max_memory_growth_mb <= 100.0);
    }

    #[tokio::test]
    async fn spike_test_scenario_has_rapid_ramp_up() {
        let spike_config = LoadTestScenarios::spike_test();
        let all_scenarios = LoadTestScenarios::all_scenarios();
        
        // Spike test should have the shortest ramp-up time
        for scenario in all_scenarios {
            if scenario.scenario_name != spike_config.scenario_name {
                assert!(spike_config.ramp_up_time <= scenario.ramp_up_time);
            }
        }
        
        assert_eq!(spike_config.ramp_up_time, Duration::from_secs(10)); // Very fast spike
        
        // Should have high concurrency to simulate spike
        assert_eq!(spike_config.concurrent_users, 500);
        
        // Should have more lenient thresholds during spike
        assert!(spike_config.performance_thresholds.max_p95_response_time >= Duration::from_millis(200));
        assert!(spike_config.performance_thresholds.max_error_rate >= 0.02);
    }

    #[tokio::test]
    async fn all_scenarios_have_unique_names_and_characteristics() {
        let scenarios = LoadTestScenarios::all_scenarios();
        
        // Verify we have all 5 scenarios
        assert_eq!(scenarios.len(), 5);
        
        // Verify unique names
        let mut names = std::collections::HashSet::new();
        for scenario in &scenarios {
            assert!(names.insert(scenario.scenario_name.clone()));
        }
        
        // Verify all scenarios are valid
        for scenario in &scenarios {
            assert!(ScenarioValidator::validate_config(scenario).is_ok());
        }
        
        // Verify scenarios have different characteristics
        let concurrent_users: Vec<_> = scenarios.iter().map(|s| s.concurrent_users).collect();
        let durations: Vec<_> = scenarios.iter().map(|s| s.test_duration).collect();
        
        // Should have variety in concurrent users
        let min_users = *concurrent_users.iter().min().unwrap();
        let max_users = *concurrent_users.iter().max().unwrap();
        assert!(max_users > min_users * 2); // At least 2x difference
        
        // Should have variety in durations
        let min_duration = *durations.iter().min().unwrap();
        let max_duration = *durations.iter().max().unwrap();
        assert!(max_duration > min_duration * 2); // At least 2x difference
    }

    #[tokio::test]
    async fn specialized_scenarios_have_distinct_characteristics() {
        let db_intensive = SpecializedScenarios::database_intensive();
        let cache_intensive = SpecializedScenarios::cache_intensive();
        let security_intensive = SpecializedScenarios::security_intensive();
        let mobile_api = SpecializedScenarios::mobile_api_simulation();
        
        // Database intensive should favor admin users (complex queries)
        assert!(db_intensive.user_type_distribution.admin_users >= 0.4);
        assert!(db_intensive.performance_thresholds.min_cache_hit_ratio <= 0.70);
        
        // Cache intensive should have very high cache expectations
        assert!(cache_intensive.user_type_distribution.regular_users >= 0.85);
        assert!(cache_intensive.performance_thresholds.min_cache_hit_ratio >= 0.90);
        
        // Security intensive should have balanced user types with longer response times
        assert!(security_intensive.user_type_distribution.admin_users >= 0.25);
        assert!(security_intensive.performance_thresholds.max_p95_response_time >= Duration::from_millis(120));
        
        // Mobile API should simulate high concurrency with fast operations
        assert!(mobile_api.concurrent_users >= 800);
        assert!(mobile_api.user_type_distribution.regular_users >= 0.90);
        assert!(mobile_api.max_requests_per_second.unwrap_or(0) >= 1500);
    }

    #[tokio::test]
    async fn scenario_validator_catches_invalid_configurations() {
        // Test zero users
        let mut invalid_config = LoadTestScenarios::normal_load();
        invalid_config.concurrent_users = 0;
        assert!(ScenarioValidator::validate_config(&invalid_config).is_err());
        
        // Test zero duration
        let mut invalid_config = LoadTestScenarios::normal_load();
        invalid_config.test_duration = Duration::ZERO;
        assert!(ScenarioValidator::validate_config(&invalid_config).is_err());
        
        // Test ramp-up time >= test duration
        let mut invalid_config = LoadTestScenarios::normal_load();
        invalid_config.ramp_up_time = invalid_config.test_duration;
        assert!(ScenarioValidator::validate_config(&invalid_config).is_err());
        
        // Test empty base URL
        let mut invalid_config = LoadTestScenarios::normal_load();
        invalid_config.target_base_url = String::new();
        assert!(ScenarioValidator::validate_config(&invalid_config).is_err());
        
        // Test invalid user distribution (doesn't sum to 1.0)
        let mut invalid_config = LoadTestScenarios::normal_load();
        invalid_config.user_type_distribution = UserTypeDistribution {
            regular_users: 0.5,
            admin_users: 0.4,
            service_accounts: 0.2, // Total = 1.1
        };
        assert!(ScenarioValidator::validate_config(&invalid_config).is_err());
        
        // Test invalid think time range
        let mut invalid_config = LoadTestScenarios::normal_load();
        invalid_config.think_time_range = (Duration::from_secs(5), Duration::from_secs(2)); // min > max
        assert!(ScenarioValidator::validate_config(&invalid_config).is_err());
    }

    #[tokio::test]
    async fn scenario_validator_provides_environment_warnings() {
        let stress_config = LoadTestScenarios::stress_test(); // 1000 users
        
        // Test with limited resources
        let warnings = ScenarioValidator::check_environment_suitability(&stress_config, 2.0, 2);
        assert!(!warnings.is_empty());
        
        // Should warn about high CPU usage
        assert!(warnings.iter().any(|w| w.contains("CPU usage")));
        
        // Should warn about high memory usage
        assert!(warnings.iter().any(|w| w.contains("memory usage")));
        
        // Test with sufficient resources
        let warnings = ScenarioValidator::check_environment_suitability(&stress_config, 16.0, 16);
        // May still have warnings for very high load, but fewer
        
        // Test endurance test duration warning
        let endurance_config = LoadTestScenarios::endurance_test();
        let warnings = ScenarioValidator::check_environment_suitability(&endurance_config, 8.0, 8);
        assert!(warnings.iter().any(|w| w.contains("Long test duration")));
    }

    #[tokio::test]
    async fn load_test_runner_can_be_created_for_all_scenarios() {
        let scenarios = LoadTestScenarios::all_scenarios();
        
        for scenario in scenarios {
            // Should be able to create runner without panicking
            let runner = LoadTestRunner::new(scenario.clone());
            
            // Basic sanity check
            assert_eq!(runner.config.scenario_name, scenario.scenario_name);
            assert_eq!(runner.config.concurrent_users, scenario.concurrent_users);
        }
    }

    #[tokio::test]
    async fn load_test_results_meet_sla_under_normal_conditions() {
        // Create mock results that should pass SLA
        let results = LoadTestResults {
            scenario_name: "Normal Load Test".to_string(),
            duration: Duration::from_secs(600),
            total_requests: 5000,
            successful_requests: 4995,
            failed_requests: 5,
            average_response_time: Duration::from_millis(25),
            p50_response_time: Duration::from_millis(20),
            p95_response_time: Duration::from_millis(75), // Under 100ms SLA
            p99_response_time: Duration::from_millis(95),
            requests_per_second: 8.33,
            error_rate: 0.001, // 0.1% - under 1% SLA
            cache_hit_ratio: 0.85, // Over 80% SLA
            memory_usage_mb: 45.0,
            cpu_utilization: 0.65,
            concurrent_users: 100,
            sla_violations: 0,
        };
        
        let sla_config = SlaConfig::default();
        assert!(results.meets_sla(&sla_config));
    }

    #[tokio::test]
    async fn load_test_results_fail_sla_when_thresholds_exceeded() {
        // Create mock results that violate SLA
        let results = LoadTestResults {
            scenario_name: "Failed Load Test".to_string(),
            duration: Duration::from_secs(600),
            total_requests: 1000,
            successful_requests: 950,
            failed_requests: 50,
            average_response_time: Duration::from_millis(150),
            p50_response_time: Duration::from_millis(120),
            p95_response_time: Duration::from_millis(300), // Exceeds 100ms SLA
            p99_response_time: Duration::from_millis(500),
            requests_per_second: 1.67,
            error_rate: 0.05, // 5% - exceeds 1% SLA
            cache_hit_ratio: 0.60, // Below 80% SLA
            memory_usage_mb: 200.0,
            cpu_utilization: 0.95,
            concurrent_users: 100,
            sla_violations: 150,
        };
        
        let sla_config = SlaConfig::default();
        assert!(!results.meets_sla(&sla_config));
    }

    #[tokio::test]
    async fn load_test_report_generation_works() {
        let results = LoadTestResults {
            scenario_name: "Test Report Generation".to_string(),
            duration: Duration::from_secs(300),
            total_requests: 2000,
            successful_requests: 1990,
            failed_requests: 10,
            average_response_time: Duration::from_millis(35),
            p50_response_time: Duration::from_millis(30),
            p95_response_time: Duration::from_millis(70),
            p99_response_time: Duration::from_millis(90),
            requests_per_second: 6.67,
            error_rate: 0.005,
            cache_hit_ratio: 0.88,
            memory_usage_mb: 55.0,
            cpu_utilization: 0.58,
            concurrent_users: 100,
            sla_violations: 2,
        };
        
        let report_generator = LoadTestReportGenerator::new();
        let report = report_generator.generate_report(&results).unwrap();
        
        // Verify report structure
        assert_eq!(report.test_execution.scenario_name, "Test Report Generation");
        assert_eq!(report.test_execution.total_requests, 2000);
        assert!(report.sla_compliance.overall_compliance);
        assert!(report.sla_compliance.compliance_score > 90.0);
        
        // Should have minimal recommendations for passing test
        let critical_recommendations: Vec<_> = report.recommendations.iter()
            .filter(|r| matches!(r.priority, auth_service::tests::load::reporting::RecommendationPriority::Critical))
            .collect();
        assert!(critical_recommendations.is_empty());
    }

    #[tokio::test]
    async fn load_test_report_identifies_issues() {
        let results = LoadTestResults {
            scenario_name: "Problem Test".to_string(),
            duration: Duration::from_secs(300),
            total_requests: 1000,
            successful_requests: 970,
            failed_requests: 30,
            average_response_time: Duration::from_millis(180),
            p50_response_time: Duration::from_millis(150),
            p95_response_time: Duration::from_millis(350), // Violates SLA
            p99_response_time: Duration::from_millis(500),
            requests_per_second: 3.33,
            error_rate: 0.03, // Violates SLA
            cache_hit_ratio: 0.55, // Below optimal
            memory_usage_mb: 180.0,
            cpu_utilization: 0.92,
            concurrent_users: 100,
            sla_violations: 75,
        };
        
        let report_generator = LoadTestReportGenerator::new();
        let report = report_generator.generate_report(&results).unwrap();
        
        // Should detect SLA violations
        assert!(!report.sla_compliance.overall_compliance);
        assert!(report.sla_compliance.compliance_score < 80.0);
        assert!(!report.sla_compliance.violations.is_empty());
        
        // Should provide recommendations
        assert!(!report.recommendations.is_empty());
        
        // Should have critical or high priority recommendations
        let high_priority_recommendations: Vec<_> = report.recommendations.iter()
            .filter(|r| matches!(r.priority, 
                auth_service::tests::load::reporting::RecommendationPriority::Critical | 
                auth_service::tests::load::reporting::RecommendationPriority::High))
            .collect();
        assert!(!high_priority_recommendations.is_empty());
        
        // Should identify bottlenecks
        assert!(!report.performance_analysis.bottleneck_analysis.identified_bottlenecks.is_empty());
    }

    #[tokio::test]
    async fn test_data_generator_creates_realistic_data() {
        let config = TestDataConfig {
            user_count: 100,
            role_count: 10,
            permission_count: 20,
            session_count: 50,
            organization_count: 3,
            audit_log_entries: 200,
            data_seed: Some(42), // For reproducible tests
        };
        
        let mut generator = TestDataGenerator::new(config);
        let data_set = generator.generate();
        
        // Verify data counts
        assert_eq!(data_set.users.len(), 100);
        assert_eq!(data_set.roles.len(), 10);
        assert_eq!(data_set.permissions.len(), 20);
        assert_eq!(data_set.sessions.len(), 50);
        assert_eq!(data_set.organizations.len(), 3);
        
        // Verify user distribution (70% regular, 20% admin, 10% service)
        let admin_users = data_set.get_admin_users();
        let regular_users = data_set.get_regular_users();
        let service_users = data_set.get_service_users();
        
        assert!(!admin_users.is_empty());
        assert!(!regular_users.is_empty());
        assert!(!service_users.is_empty());
        
        // Rough distribution check (within reasonable bounds due to randomization)
        assert!(admin_users.len() >= 15 && admin_users.len() <= 25); // ~20%
        assert!(regular_users.len() >= 65 && regular_users.len() <= 75); // ~70%
        assert!(service_users.len() >= 5 && service_users.len() <= 15); // ~10%
        
        // Verify credentials are available for all users
        assert_eq!(data_set.user_credentials.len(), data_set.users.len());
        for user in &data_set.users {
            assert!(data_set.get_user_credentials(&user.email).is_some());
        }
        
        // Verify random user selection works
        let random_user = data_set.get_random_active_user();
        assert!(random_user.is_some());
        assert!(random_user.unwrap().is_active);
    }

    #[tokio::test]
    async fn scenario_execution_by_name_works() {
        // Test valid scenario names
        let valid_scenarios = ["normal", "peak", "stress", "endurance", "spike"];
        
        for scenario_name in &valid_scenarios {
            // Should not panic when creating the scenario
            // Note: We can't actually execute due to no running server in tests
            match LoadTestScenarios::execute_scenario(scenario_name).await {
                Err(LoadTestError::SetupFailed(_)) | 
                Err(LoadTestError::ExecutionFailed(_)) |
                Err(LoadTestError::EnvironmentError(_)) => {
                    // Expected errors when no server is running
                }
                Ok(_) => {
                    // Unexpected success (would mean server is running)
                    panic!("Unexpected success - server should not be running in tests");
                }
                Err(other) => {
                    panic!("Unexpected error type: {:?}", other);
                }
            }
        }
        
        // Test invalid scenario name
        match LoadTestScenarios::execute_scenario("invalid_scenario").await {
            Err(LoadTestError::SetupFailed(msg)) => {
                assert!(msg.contains("Unknown scenario"));
            }
            other => {
                panic!("Expected SetupFailed for unknown scenario, got: {:?}", other);
            }
        }
    }

    #[tokio::test]
    async fn load_test_error_handling_works() {
        // Test various error conditions
        
        // Invalid configuration should be caught
        let mut invalid_config = LoadTestScenarios::normal_load();
        invalid_config.concurrent_users = 0;
        
        let validation_error = ScenarioValidator::validate_config(&invalid_config);
        assert!(validation_error.is_err());
        
        // Test error enum functionality
        let setup_error = LoadTestError::SetupFailed("Test setup failure".to_string());
        assert!(setup_error.to_string().contains("Test setup failure"));
        
        let execution_error = LoadTestError::ExecutionFailed("Test execution failure".to_string());
        assert!(execution_error.to_string().contains("Test execution failure"));
        
        let sla_error = LoadTestError::SlaViolation("Test SLA violation".to_string());
        assert!(sla_error.to_string().contains("Test SLA violation"));
        
        let regression_error = LoadTestError::PerformanceRegression(150.0, 100.0);
        assert!(regression_error.to_string().contains("150"));
        assert!(regression_error.to_string().contains("100"));
    }
}

/// Mock server utilities for testing
#[cfg(test)]
mod mock_server_utils {
    use super::*;
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use warp::Filter;

    /// Mock server for load testing
    pub struct MockAuthServer {
        pub port: u16,
        pub request_count: Arc<Mutex<u64>>,
        pub response_delay: Duration,
        pub error_rate: f64,
    }

    impl MockAuthServer {
        pub fn new(port: u16) -> Self {
            Self {
                port,
                request_count: Arc::new(Mutex::new(0)),
                response_delay: Duration::from_millis(10),
                error_rate: 0.0,
            }
        }

        pub fn with_delay(mut self, delay: Duration) -> Self {
            self.response_delay = delay;
            self
        }

        pub fn with_error_rate(mut self, error_rate: f64) -> Self {
            self.error_rate = error_rate;
            self
        }

        pub async fn start(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
            let request_count = self.request_count.clone();
            let response_delay = self.response_delay;
            let error_rate = self.error_rate;

            // Mock login endpoint
            let login = warp::path("api")
                .and(warp::path("auth"))
                .and(warp::path("login"))
                .and(warp::post())
                .and_then(move || {
                    let request_count = request_count.clone();
                    async move {
                        let mut count = request_count.lock().await;
                        *count += 1;
                        
                        tokio::time::sleep(response_delay).await;
                        
                        if rand::random::<f64>() < error_rate {
                            Err(warp::reject::custom(MockServerError))
                        } else {
                            Ok(warp::reply::json(&serde_json::json!({
                                "token": "mock_token",
                                "user_id": "mock_user_id"
                            })))
                        }
                    }
                });

            // Mock profile endpoint
            let profile = warp::path("api")
                .and(warp::path("users"))
                .and(warp::path("profile"))
                .and(warp::get())
                .and_then(move || {
                    let request_count = request_count.clone();
                    async move {
                        let mut count = request_count.lock().await;
                        *count += 1;
                        
                        tokio::time::sleep(response_delay).await;
                        
                        if rand::random::<f64>() < error_rate {
                            Err(warp::reject::custom(MockServerError))
                        } else {
                            Ok(warp::reply::with_header(
                                warp::reply::json(&serde_json::json!({
                                    "user_id": "mock_user_id",
                                    "email": "<EMAIL>",
                                    "roles": ["user"]
                                })),
                                "X-Cache-Status",
                                "HIT"
                            ))
                        }
                    }
                });

            let routes = login.or(profile);
            
            warp::serve(routes)
                .run(([127, 0, 0, 1], self.port))
                .await;
                
            Ok(())
        }

        pub async fn get_request_count(&self) -> u64 {
            *self.request_count.lock().await
        }
    }

    #[derive(Debug)]
    struct MockServerError;
    impl warp::reject::Reject for MockServerError {}

    #[tokio::test]
    async fn mock_server_basic_functionality() {
        let server = MockAuthServer::new(0) // Use ephemeral port
            .with_delay(Duration::from_millis(5))
            .with_error_rate(0.1);

        // Start server in background
        let server_handle = tokio::spawn(async move {
            server.start().await
        });

        // Give server time to start
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Test basic HTTP client functionality
        let client = reqwest::Client::new();
        let response = client
            .post("http://127.0.0.1:3030/api/auth/login")
            .json(&serde_json::json!({
                "email": "<EMAIL>",
                "password": "password123"
            }))
            .send()
            .await;

        // May succeed or fail depending on mock server setup
        // This test mainly verifies the mock server can be created and started
        server_handle.abort();
    }
}

/// Performance benchmarks for load testing infrastructure
#[cfg(test)]
mod performance_benchmarks {
    use super::*;
    use std::time::Instant;

    #[tokio::test]
    async fn scenario_generation_performance() {
        let start = Instant::now();
        
        // Generate all scenarios multiple times
        for _ in 0..100 {
            let _scenarios = LoadTestScenarios::all_scenarios();
        }
        
        let duration = start.elapsed();
        
        // Should be very fast (< 10ms for 100 iterations)
        assert!(duration < Duration::from_millis(10));
    }

    #[tokio::test]
    async fn test_data_generation_performance() {
        let config = TestDataConfig {
            user_count: 1000,
            role_count: 20,
            permission_count: 50,
            session_count: 500,
            organization_count: 5,
            audit_log_entries: 1000,
            data_seed: Some(42),
        };
        
        let start = Instant::now();
        let mut generator = TestDataGenerator::new(config);
        let _data_set = generator.generate();
        let duration = start.elapsed();
        
        // Should generate 1000 users with related data in reasonable time (< 1s)
        assert!(duration < Duration::from_secs(1));
        println!("Generated 1000 users with related data in {:?}", duration);
    }

    #[tokio::test]
    async fn report_generation_performance() {
        let results = LoadTestResults {
            scenario_name: "Performance Test".to_string(),
            duration: Duration::from_secs(600),
            total_requests: 10000,
            successful_requests: 9950,
            failed_requests: 50,
            average_response_time: Duration::from_millis(45),
            p50_response_time: Duration::from_millis(40),
            p95_response_time: Duration::from_millis(85),
            p99_response_time: Duration::from_millis(120),
            requests_per_second: 16.67,
            error_rate: 0.005,
            cache_hit_ratio: 0.88,
            memory_usage_mb: 75.0,
            cpu_utilization: 0.65,
            concurrent_users: 100,
            sla_violations: 5,
        };
        
        let start = Instant::now();
        let report_generator = LoadTestReportGenerator::new();
        let report = report_generator.generate_report(&results).unwrap();
        let _html = report_generator.generate_html_report(&report).unwrap();
        let _json = report_generator.generate_json_report(&report).unwrap();
        let duration = start.elapsed();
        
        // Report generation should be fast (< 100ms)
        assert!(duration < Duration::from_millis(100));
        println!("Generated comprehensive report in {:?}", duration);
    }
}